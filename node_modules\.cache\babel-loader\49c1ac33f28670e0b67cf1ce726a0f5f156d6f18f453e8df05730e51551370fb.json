{"ast": null, "code": "import * as echarts from \"echarts\";\nimport { EconomicDevelopment } from '@/api/index.js';\nexport default {\n  name: '<PERSON><PERSON><PERSON><PERSON>',\n  props: {\n    colorPalette: Object\n  },\n  data() {\n    return {\n      uid: null,\n      // 设置uid唯一\n      myChart: '',\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      progress: 0,\n      yData: 200000,\n      oftenLivePeople: 0\n    };\n  },\n  created() {\n    this.uid = Math.floor(Math.random() * 100) + 0.2;\n  },\n  beforeDestroy() {\n    if (this.myChart) {\n      this.myChart.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.dashesPieConfig();\n      }, 200);\n    });\n  },\n  methods: {\n    dashesPieConfig() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        var dom = document.getElementById(this.uid);\n        if (this.myChart != null && this.myChart != \"\" && this.myChart != undefined) {\n          this.myChart.dispose();\n        }\n        this.myChart = echarts.init(dom);\n        this.myChart.clear();\n        EconomicDevelopment().then(res => {\n          for (let v of res.data.extra) {\n            if (v.edName == \"常住人口\") {\n              if (v.unit.indexOf('万') != -1) {\n                this.progress = Math.floor(v.gdp * 10000);\n                this.oftenLivePeople = Math.floor(v.gdp * 10000);\n              }\n            }\n          }\n          if (this.colorPalette.name == '常驻人口') {\n            this.progress = 19.2 * 10000;\n            this.$nextTick(() => {\n              this.setOption();\n            });\n          } else {\n            this.progress = 18.3 * 10000;\n            this.setOption();\n          }\n        });\n      });\n    },\n    setOption() {\n      var xdata = this.progress; //接收x数据\n      var ydata = this.yData; //接收y数据\n      var num = 100; //定义小块个数\n      var rate = xdata / ydata; //完成率\n\n      //定义图表option\n      var option = {\n        tooltip: {\n          show: false\n        },\n        title: {\n          text: xdata,\n          //中间标题\n          x: \"11%\",\n          y: \"20%\",\n          // center: ['25%', '25%'],\n          textStyle: {\n            textAlign: 'center',\n            color: this.colorPalette.color,\n            fontSize: 21,\n            //中间标题文字大小设置\n            fontWeight: 'bold' //中间标题文字大小设置 不同大小可使用不同的字体或颜色\n          }\n        },\n        series: [{\n          //内部环形\n          name: \"内部环形\",\n          type: \"pie\",\n          radius: [\"38%\", \"30%\"],\n          // center: [\"50%\", \"50%\"],\n          center: ['25%', '25%'],\n          clockwise: false,\n          data: [{\n            value: 1,\n            name: ''\n          }],\n          startAngle: 180,\n          hoverAnimation: false,\n          legendHoverLink: false,\n          label: {\n            show: false\n          },\n          labelLine: {\n            show: false\n          },\n          animation: false,\n          itemStyle: {\n            color: this.colorPalette.innerRingColor,\n            shadowBlur: 20\n          }\n        }, {\n          name: 'decorationTwo',\n          type: 'pie',\n          color: [this.colorPalette.color,, 'rgba(255,255,255,0)'],\n          center: ['25%', '25%'],\n          radius: [\"45%\", \"40%\"],\n          hoverAnimation: false,\n          lable: {\n            show: false,\n            emphasis: {\n              show: false\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: new Array(num).fill(1).map((v, i) => {\n            if (i < rate * num) {\n              return {\n                name: '',\n                value: 1\n              };\n            } else {\n              return {\n                name: '',\n                value: 1,\n                itemStyle: {\n                  color: \"rgba(255,255,255,0)\"\n                }\n              };\n            }\n          })\n        }]\n      };\n      this.myChart.setOption(option, true);\n      window.onresize = this.myChart.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "EconomicDevelopment", "name", "props", "colorPalette", "Object", "data", "uid", "myChart", "style", "width", "height", "progress", "yData", "oftenLivePeople", "created", "Math", "floor", "random", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "setTimeout", "dashesPieConfig", "methods", "Promise", "resolve", "then", "dom", "document", "getElementById", "undefined", "dispose", "init", "res", "v", "extra", "ed<PERSON>ame", "unit", "indexOf", "gdp", "setOption", "xdata", "ydata", "num", "rate", "option", "tooltip", "show", "title", "text", "x", "y", "textStyle", "textAlign", "color", "fontSize", "fontWeight", "series", "type", "radius", "center", "clockwise", "value", "startAngle", "hoverAnimation", "legendHoverLink", "label", "labelLine", "animation", "itemStyle", "innerRingColor", "<PERSON><PERSON><PERSON><PERSON>", "lable", "emphasis", "Array", "fill", "map", "i", "window", "onresize", "resize"], "sources": ["src/components/comprehensive/DashesPie.vue"], "sourcesContent": ["<template>\r\n    <div class=\"Dashes<PERSON>ie\">\r\n\r\n        <div :id=\"uid\" :style=\"style\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { EconomicDevelopment } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: '<PERSON><PERSON><PERSON><PERSON>',\r\n\r\n    props: {\r\n        colorPalette: Object,\r\n    },\r\n\r\n    data() {\r\n        return {\r\n            uid: null, // 设置uid唯一\r\n            myChart: '',\r\n            style: {\r\n                width: '100%',\r\n                height: '100%',\r\n            },\r\n            progress: 0,\r\n            yData: 200000,\r\n            oftenLivePeople: 0,\r\n\r\n        };\r\n    },\r\n    created() {\r\n        this.uid = Math.floor(Math.random() * 100) + 0.2;\r\n    },\r\n    beforeDestroy() {\r\n        if (this.myChart) {\r\n            this.myChart.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            setTimeout(() => {\r\n                this.dashesPieConfig()\r\n\r\n            }, 200)\r\n\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n\r\n        dashesPieConfig() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n\r\n                var dom = document.getElementById(this.uid);\r\n                if (this.myChart != null && this.myChart != \"\" && this.myChart != undefined) {\r\n                    this.myChart.dispose();\r\n                }\r\n\r\n                this.myChart = echarts.init(dom);\r\n                this.myChart.clear();\r\n                EconomicDevelopment().then(res => {\r\n                    for (let v of res.data.extra) {\r\n\r\n                        if (v.edName == \"常住人口\") {\r\n                            if (v.unit.indexOf('万') != -1) {\r\n                                this.progress = Math.floor(v.gdp * 10000)\r\n                                this.oftenLivePeople = Math.floor(v.gdp * 10000)\r\n                            }\r\n                        }\r\n\r\n                    }\r\n                    if (this.colorPalette.name == '常驻人口') {\r\n                        this.progress = 19.2 * 10000\r\n                        this.$nextTick(() => {\r\n                            this.setOption()\r\n\r\n                        });\r\n\r\n\r\n                    } else {\r\n                        this.progress = 18.3 * 10000\r\n                        this.setOption()\r\n\r\n                    }\r\n                })\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            var xdata = this.progress; //接收x数据\r\n            var ydata = this.yData; //接收y数据\r\n            var num = 100; //定义小块个数\r\n            var rate = xdata / ydata; //完成率\r\n\r\n\r\n            //定义图表option\r\n            var option = {\r\n\r\n                tooltip: {\r\n                    show: false,\r\n                },\r\n                title: {\r\n                    text: xdata, //中间标题\r\n                    x: \"11%\",\r\n                    y: \"20%\",\r\n                    // center: ['25%', '25%'],\r\n                    textStyle: {\r\n                        textAlign: 'center',\r\n                        color: this.colorPalette.color,\r\n                        fontSize: 21, //中间标题文字大小设置\r\n                        fontWeight: 'bold', //中间标题文字大小设置 不同大小可使用不同的字体或颜色\r\n                    },\r\n                },\r\n                series: [\r\n\r\n                    { //内部环形\r\n                        name: \"内部环形\",\r\n                        type: \"pie\",\r\n                        radius: [\"38%\", \"30%\"],\r\n                        // center: [\"50%\", \"50%\"],\r\n                        center: ['25%', '25%'],\r\n                        clockwise: false,\r\n                        data: [{ value: 1, name: '' }],\r\n                        startAngle: 180,\r\n                        hoverAnimation: false,\r\n                        legendHoverLink: false,\r\n                        label: {\r\n                            show: false,\r\n                        },\r\n                        labelLine: {\r\n                            show: false,\r\n\r\n                        },\r\n                        animation: false,\r\n                        itemStyle: {\r\n                            color: this.colorPalette.innerRingColor,\r\n                            shadowBlur: 20,\r\n\r\n                        },\r\n\r\n                    },\r\n                    {\r\n                        name: 'decorationTwo',\r\n                        type: 'pie',\r\n                        color: [this.colorPalette.color, , 'rgba(255,255,255,0)'],\r\n                        center: ['25%', '25%'],\r\n                        radius: [\"45%\", \"40%\"],\r\n                        hoverAnimation: false,\r\n                        lable: {\r\n                            show: false,\r\n                            emphasis: {\r\n                                show: false\r\n                            }\r\n                        },\r\n                        labelLine: {\r\n                            show: false\r\n                        },\r\n                        data: new Array(num).fill(1).map((v, i) => {\r\n                            if (i < rate * num) {\r\n                                return {\r\n                                    name: '',\r\n                                    value: 1,\r\n                                }\r\n                            } else {\r\n                                return {\r\n                                    name: '',\r\n                                    value: 1,\r\n                                    itemStyle: {\r\n                                        color: \"rgba(255,255,255,0)\",\r\n                                    },\r\n                                }\r\n                            }\r\n                        }),\r\n                    },\r\n                ],\r\n\r\n            }\r\n            this.myChart.setOption(option, true)\r\n            window.onresize = this.myChart.resize;\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.DashesPie {\r\n    /* background-color: pink; */\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n</style>"], "mappings": "AAQA,YAAAA,OAAA;AACA,SAAAC,mBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACAC,YAAA,EAAAC;EACA;EAEAC,KAAA;IACA;MACAC,GAAA;MAAA;MACAC,OAAA;MACAC,KAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,QAAA;MACAC,KAAA;MACAC,eAAA;IAEA;EACA;EACAC,QAAA;IACA,KAAAR,GAAA,GAAAS,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;EACA;EACAC,cAAA;IACA,SAAAX,OAAA;MACA,KAAAA,OAAA,CAAAY,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACAC,UAAA;QACA,KAAAC,eAAA;MAEA;IAGA;EACA;EAEAC,OAAA;IAEAD,gBAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QAEA,IAAAC,GAAA,GAAAC,QAAA,CAAAC,cAAA,MAAAxB,GAAA;QACA,SAAAC,OAAA,iBAAAA,OAAA,eAAAA,OAAA,IAAAwB,SAAA;UACA,KAAAxB,OAAA,CAAAyB,OAAA;QACA;QAEA,KAAAzB,OAAA,GAAAR,OAAA,CAAAkC,IAAA,CAAAL,GAAA;QACA,KAAArB,OAAA,CAAAY,KAAA;QACAnB,mBAAA,GAAA2B,IAAA,CAAAO,GAAA;UACA,SAAAC,CAAA,IAAAD,GAAA,CAAA7B,IAAA,CAAA+B,KAAA;YAEA,IAAAD,CAAA,CAAAE,MAAA;cACA,IAAAF,CAAA,CAAAG,IAAA,CAAAC,OAAA;gBACA,KAAA5B,QAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAmB,CAAA,CAAAK,GAAA;gBACA,KAAA3B,eAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAmB,CAAA,CAAAK,GAAA;cACA;YACA;UAEA;UACA,SAAArC,YAAA,CAAAF,IAAA;YACA,KAAAU,QAAA;YACA,KAAAU,SAAA;cACA,KAAAoB,SAAA;YAEA;UAGA;YACA,KAAA9B,QAAA;YACA,KAAA8B,SAAA;UAEA;QACA;MAGA;IACA;IACAA,UAAA;MACA,IAAAC,KAAA,QAAA/B,QAAA;MACA,IAAAgC,KAAA,QAAA/B,KAAA;MACA,IAAAgC,GAAA;MACA,IAAAC,IAAA,GAAAH,KAAA,GAAAC,KAAA;;MAGA;MACA,IAAAG,MAAA;QAEAC,OAAA;UACAC,IAAA;QACA;QACAC,KAAA;UACAC,IAAA,EAAAR,KAAA;UAAA;UACAS,CAAA;UACAC,CAAA;UACA;UACAC,SAAA;YACAC,SAAA;YACAC,KAAA,OAAApD,YAAA,CAAAoD,KAAA;YACAC,QAAA;YAAA;YACAC,UAAA;UACA;QACA;QACAC,MAAA,GAEA;UAAA;UACAzD,IAAA;UACA0D,IAAA;UACAC,MAAA;UACA;UACAC,MAAA;UACAC,SAAA;UACAzD,IAAA;YAAA0D,KAAA;YAAA9D,IAAA;UAAA;UACA+D,UAAA;UACAC,cAAA;UACAC,eAAA;UACAC,KAAA;YACAnB,IAAA;UACA;UACAoB,SAAA;YACApB,IAAA;UAEA;UACAqB,SAAA;UACAC,SAAA;YACAf,KAAA,OAAApD,YAAA,CAAAoE,cAAA;YACAC,UAAA;UAEA;QAEA,GACA;UACAvE,IAAA;UACA0D,IAAA;UACAJ,KAAA,QAAApD,YAAA,CAAAoD,KAAA;UACAM,MAAA;UACAD,MAAA;UACAK,cAAA;UACAQ,KAAA;YACAzB,IAAA;YACA0B,QAAA;cACA1B,IAAA;YACA;UACA;UACAoB,SAAA;YACApB,IAAA;UACA;UACA3C,IAAA,MAAAsE,KAAA,CAAA/B,GAAA,EAAAgC,IAAA,IAAAC,GAAA,EAAA1C,CAAA,EAAA2C,CAAA;YACA,IAAAA,CAAA,GAAAjC,IAAA,GAAAD,GAAA;cACA;gBACA3C,IAAA;gBACA8D,KAAA;cACA;YACA;cACA;gBACA9D,IAAA;gBACA8D,KAAA;gBACAO,SAAA;kBACAf,KAAA;gBACA;cACA;YACA;UACA;QACA;MAGA;MACA,KAAAhD,OAAA,CAAAkC,SAAA,CAAAK,MAAA;MACAiC,MAAA,CAAAC,QAAA,QAAAzE,OAAA,CAAA0E,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}