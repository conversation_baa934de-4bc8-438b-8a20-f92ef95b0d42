{"ast": null, "code": "import { getCimLayer } from '@/api/userMenu';\nimport { getVideoTreeList, getQueryListByOrg, getVideoQueryListById } from '@/api/index.js';\nimport { mapState } from 'vuex';\nimport { number } from 'echarts';\nexport default {\n  name: 'videoPlayerTree',\n  props: {},\n  data() {\n    return {\n      dataList: [],\n      typeEquipmentAll: [],\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      newArr: [],\n      checkedKeys: [],\n      inputValue: '',\n      isAllSelect: false,\n      isSelected: false,\n      changeAllNode: [],\n      isSearch: false,\n      typeList: ['沿街', '道路', '重点区域', '社会面视频', '门前三包'],\n      checkboxGroup: [],\n      valueType: '',\n      keyword: '',\n      doorTag: '',\n      radio: 0,\n      selectedOption: []\n    };\n  },\n  watch: {\n    TypeVideoAll(nv) {},\n    checkedKeys(nv) {}\n  },\n  computed: {\n    ...mapState(\"action\", [\"TypeVideoAll\", 'isFull'])\n  },\n  activated() {\n    this.$store.commit(\"action/getTabsValue\", '按部门分类');\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initData();\n    });\n  },\n  beforeDestroy() {\n    this.$store.commit(\"action/clearAll\", false);\n  },\n  methods: {\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    handleNodeClick(val) {\n      this.deletePoi();\n      this.$store.commit(\"action/getVideoPlayerBox\", false);\n      this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n      // console.log(val, 'ceshi')\n      if (val.single) {\n        this.getLatLon({\n          id: val.data\n        }, true);\n      }\n    },\n    changeRadio(val) {\n      this.getLatLon({\n        id: val\n      }, true);\n    },\n    handleCheckboxChange(val) {\n      if (val.length > 1) {\n        this.selectedOption = [val[val.length - 1]];\n      }\n      if (this.selectedOption.length > 0) {\n        this.getLatLon({\n          id: this.selectedOption[0]\n        }, true);\n      } else {\n        this.$eventBus.$emit('senTxtToUe', '清除');\n      }\n    },\n    async initData(params = {}) {\n      getVideoTreeList(params).then(res => {\n        if (res.status == 200) {\n          let list = res.data.extra;\n          list.forEach((ele, index) => {\n            if (this.keyword != '') {\n              ele.disabled = true;\n            }\n            this.addParams(ele, index);\n          });\n          this.typeEquipmentAll = list;\n        }\n      });\n    },\n    // 搜索框\n    inputChange(val) {\n      this.keyword = val;\n      this.$eventBus.$emit('senTxtToUe', '清除');\n      let params = {\n        keyword: val,\n        streetType: this.valueType,\n        displayTag: this.doorTag\n      };\n      if (val != '') {\n        this.isSearch = true;\n      } else {\n        this.isSearch = false;\n      }\n      this.initData(params);\n    },\n    // 下拉框\n    changeValueSelect(val) {\n      let str = val[val.length - 1];\n      this.checkboxGroup = [str];\n      let params = '';\n      if (str == '门前三包') {\n        this.valueType = '';\n        this.doorTag = '门前三包';\n        params = {\n          keyword: this.keyword,\n          streetType: '',\n          displayTag: this.doorTag\n        };\n      } else {\n        this.valueType = str;\n        this.doorTag = '';\n        params = {\n          keyword: this.keyword,\n          streetType: str == \"社会面视频\" ? \"区域视频\" : str\n        };\n      }\n      this.initData(params);\n    },\n    // handleNodeClick(val, flag) {\n    //     console.log(val, flag, \"cehsi1\")\n\n    //     if (!this.isSearch) {\n    //         if (flag && !val.single) {\n    //             // 全部选中\n    //             this.isAllSelect = true;\n\n    //             this.getDepList({ organization: val.label }, flag)\n    //         } else {\n    //             val.flag = flag;\n    //             this.changeAllNode.push(val)\n    //         }\n\n    //     } else {\n    //         if (val.single) {\n    //             val.flag = flag;\n    //             this.changeAllNode.push(val)\n\n    //         }\n    //     }\n\n    // },\n    // 点击节点\n    handleCheckAll(node, val) {\n      let currentNode = node;\n      if (!this.isAllSelect) {\n        // 点击第一层\n        if (typeof currentNode.data == 'string') {\n          this.changeAllNode.forEach(item => {\n            if (item.label == currentNode.label) {\n              // 全选取消\n              currentNode.flag = item.flag;\n              this.getDepList({\n                organization: currentNode.label\n              }, currentNode.flag);\n              return;\n            }\n          });\n        } else {\n          // 点击第二层\n          this.changeAllNode.forEach((item, i) => {\n            if (item.label == currentNode.label) {\n              this.getLatLon({\n                id: this.changeAllNode[i].data\n              }, this.changeAllNode[i].flag);\n            }\n          });\n        }\n      }\n      this.isAllSelect = false;\n      this.changeAllNode = [];\n    },\n    // 根据id查询\n    async getLatLon(params = {}, flag) {\n      await getVideoQueryListById(params).then(res => {\n        if (res.status == 200) {\n          let list = res.data.extra;\n          if (flag == undefined) {\n            this.$store.commit('action/getVideoDetailsExpand', list);\n          } else if (flag == 'play') {\n            let par = {\n              ...list,\n              name: list.channalName\n            };\n            this.$store.commit(\"action/getVideoPlayerList\", par);\n          } else {\n            // 给ue发送撒点\n            list.name = list.deviceName;\n            list.imageX = '0.18';\n            list.imageY = '0.18';\n            let params = {\n              \"mode\": \"add\",\n              \"sources\": [list]\n            };\n            this.$eventBus.$emit('senTxtToUe', params);\n            console.log('params', params);\n          }\n        }\n      });\n    },\n    // 根据组织查询\n    async getDepList(params = {}, flag) {\n      await getQueryListByOrg(params).then(res => {\n        if (res.status == 200) {\n          let list = res.data.extra;\n          // 给ue发送撒点\n          let params = {\n            name: '区域监控',\n            data: list,\n            flag: flag\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n          console.log('params', params);\n          // return res.data.extra\n        }\n      });\n    },\n    // 递归添加参数\n    addParams(val, index) {\n      if (!val.children.length) {\n        val.single = true;\n      } else {\n        val.single = false;\n        val.data = index + '-1';\n        val.children.forEach(ele => {\n          this.addParams(ele);\n        });\n      }\n      return val;\n    },\n    closeTree() {\n      this.$store.commit(\"action/clearAll\", false);\n    },\n    // 打开监控\n    openPlay(data) {\n      this.getLatLon({\n        id: data.data\n      }, 'play');\n      this.$store.commit(\"action/getIsShowNumInitChange\", true);\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n    },\n    // 打开信息\n    openInfo(val) {\n      // this.$store.commit(\"action/getInformationBox\", val)\n      this.getLatLon({\n        id: val.data\n      });\n      this.$store.commit(\"action/getVideoInfoFlag\", true);\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getVideoTreeList", "getQueryListByOrg", "getVideoQueryListById", "mapState", "number", "name", "props", "data", "dataList", "typeEquipmentAll", "defaultProps", "children", "label", "newArr", "checked<PERSON>eys", "inputValue", "isAllSelect", "isSelected", "changeAllNode", "isSearch", "typeList", "checkboxGroup", "valueType", "keyword", "doorTag", "radio", "selectedOption", "watch", "TypeVideoAll", "nv", "computed", "activated", "$store", "commit", "mounted", "$nextTick", "initData", "<PERSON><PERSON><PERSON><PERSON>", "methods", "deletePoi", "params", "$eventBus", "$emit", "handleNodeClick", "val", "single", "getLatLon", "id", "changeRadio", "handleCheckboxChange", "length", "then", "res", "status", "list", "extra", "for<PERSON>ach", "ele", "index", "disabled", "addParams", "inputChange", "streetType", "displayTag", "changeValueSelect", "str", "handleCheckAll", "node", "currentNode", "item", "flag", "getDepList", "organization", "i", "undefined", "par", "channal<PERSON>ame", "deviceName", "imageX", "imageY", "console", "log", "closeTree", "openPlay", "openInfo"], "sources": ["src/components/videoTypeOption/videoTabsList/ownVideoPlayer.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container_box\" :class=\"isFull ? ' ' : 'allScreen'\">\r\n        <div class=\"keyWordsSearch\">\r\n            <div class=\"input\">\r\n                <el-input placeholder=\"请输入关键词搜索\" @change=\"inputChange\" v-model=\"inputValue\" :clearable=\"true\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n            </div>\r\n            <div class=\"selectType\">\r\n                <div>\r\n                    <el-checkbox-group v-model=\"checkboxGroup\" size=\"medium\" @change=\"changeValueSelect\">\r\n                        <el-checkbox :label=\"item\" border v-for=\"(item, value) in typeList\" :key=\"value\"></el-checkbox>\r\n                    </el-checkbox-group>\r\n                </div>\r\n            </div>\r\n\r\n        </div>\r\n        <!-- <div class=\"radio\">\r\n            <el-radio-group v-model=\"radio\" @change=\"changeRadio\">\r\n                <el-radio :label=\"604\">建滔广场东南角-AR全景</el-radio>\r\n                <el-radio :label=\"2557\">东城大道金融大道南卡口枪向北</el-radio>\r\n            </el-radio-group>\r\n        </div> -->\r\n        <!-- <div class=\"radio\">\r\n            <el-checkbox-group v-model=\"selectedOption\" @change=\"handleCheckboxChange\">\r\n                <el-checkbox :label=\"604\">建滔广场东南角-AR全景</el-checkbox>\r\n                <el-checkbox :label=\"2557\">东城大道金融大道南卡口枪向北</el-checkbox>\r\n            </el-checkbox-group>\r\n        </div> -->\r\n        <div class=\"treeShow\">\r\n            <el-tree ref=\"Tree\" :data=\"typeEquipmentAll\" node-key=\"data\" :default-expanded-keys=\"checkedKeys\"\r\n                :props=\"defaultProps\" @node-click=\"handleNodeClick\">\r\n\r\n                <span slot-scope=\"{node,data}\" class=\"slotTxt\">\r\n                    <div class=\"pr10 over-ellipsis\">\r\n                        <a v-if=\"node.level == 1\" href=\"javascript:;\" class=\"tree-a\" :title=\"data.org_name\">\r\n                            {{ data.label }} ({{ data.children.length }})\r\n                        </a>\r\n\r\n                        <a href=\"javascript:;\" class=\"tree-a\" :title=\"data.label\" v-else>\r\n                            {{ data.label }}\r\n                        </a>\r\n                    </div>\r\n\r\n                    <div class=\"rightIcon\" v-if=\"data.single\">\r\n                        <!--\r\n                         <div class=\"point\" v-if=\"checkNodeData?.org_name == data.org_name\">\r\n                            <img src=\"@/assets/images/mainPics/i_info.png\" alt=\"\" @click=\"openInfo(true)\">\r\n                        </div> \r\n                        -->\r\n                        <div class=\"poin\" @click=\"openInfo(data)\">\r\n                            <img src=\"@/assets/images/mainPics/i_info.png\" alt=\"\" width=\"30px\">\r\n\r\n                        </div>\r\n                        <div class=\"poin\" @click.stop=\"openPlay(data)\">\r\n                            <img src=\"@/assets/images/mainPics/i_play.png\" alt=\"\" width=\"30px\">\r\n\r\n                        </div>\r\n                    </div>\r\n                </span>\r\n            </el-tree>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCimLayer } from '@/api/userMenu'\r\nimport { getVideoTreeList, getQueryListByOrg, getVideoQueryListById } from '@/api/index.js'\r\nimport { mapState } from 'vuex'\r\nimport { number } from 'echarts';\r\n\r\nexport default {\r\n    name: 'videoPlayerTree',\r\n    props: {\r\n\r\n    },\r\n    data() {\r\n        return {\r\n            dataList: [],\r\n            typeEquipmentAll: [],\r\n            defaultProps: {\r\n                children: 'children',\r\n                label: 'label'\r\n            },\r\n            newArr: [],\r\n            checkedKeys: [],\r\n            inputValue: '',\r\n            isAllSelect: false,\r\n            isSelected: false,\r\n            changeAllNode: [],\r\n            isSearch: false,\r\n            typeList: ['沿街', '道路', '重点区域', '社会面视频', '门前三包'],\r\n            checkboxGroup: [],\r\n            valueType: '',\r\n            keyword: '',\r\n            doorTag: '',\r\n            radio: 0,\r\n            selectedOption: [],\r\n        };\r\n    },\r\n    watch: {\r\n        TypeVideoAll(nv) {\r\n\r\n        },\r\n        checkedKeys(nv) {\r\n        }\r\n        \r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"TypeVideoAll\", 'isFull'\r\n        ]),\r\n    },\r\n    activated() {\r\n        this.$store.commit(\"action/getTabsValue\", '按部门分类')\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.initData()\r\n\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n        this.$store.commit(\"action/clearAll\", false)\r\n\r\n    },\r\n\r\n    methods: {\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        handleNodeClick(val) {\r\n            this.deletePoi();\r\n            this.$store.commit(\"action/getVideoPlayerBox\", false)\r\n            this.$eventBus.$emit('senTxtToUe', \"全局视角\");\r\n            // console.log(val, 'ceshi')\r\n            if (val.single) {\r\n                this.getLatLon({ id: val.data }, true)\r\n\r\n            }\r\n        },\r\n        changeRadio(val) {\r\n            this.getLatLon({ id: val }, true)\r\n        },\r\n        handleCheckboxChange(val) {\r\n            if (val.length > 1) {\r\n                this.selectedOption = [val[val.length - 1]];\r\n            }\r\n            if (this.selectedOption.length > 0) {\r\n                this.getLatLon({ id: this.selectedOption[0] }, true)\r\n            } else {\r\n                this.$eventBus.$emit('senTxtToUe', '清除')\r\n            }\r\n        },\r\n        async initData(params = {}) {\r\n            getVideoTreeList(params).then(res => {\r\n                if (res.status == 200) {\r\n                    let list = res.data.extra;\r\n                    list.forEach((ele, index) => {\r\n                        if (this.keyword != '') {\r\n                            ele.disabled = true\r\n                        }\r\n                        this.addParams(ele, index)\r\n                    })\r\n                    this.typeEquipmentAll = list\r\n\r\n                }\r\n            })\r\n        },\r\n        // 搜索框\r\n        inputChange(val) {\r\n            this.keyword = val\r\n            this.$eventBus.$emit('senTxtToUe', '清除')\r\n\r\n            let params = {\r\n                keyword: val,\r\n                streetType: this.valueType,\r\n                displayTag: this.doorTag\r\n            }\r\n            if (val != '') {\r\n                this.isSearch = true\r\n            } else {\r\n                this.isSearch = false\r\n            }\r\n            this.initData(params)\r\n        },\r\n        // 下拉框\r\n        changeValueSelect(val) {\r\n            let str=val[val.length-1];\r\n            this.checkboxGroup=[str];\r\n            let params = ''\r\n            if (str=='门前三包') {\r\n                this.valueType = ''\r\n                this.doorTag = '门前三包'\r\n                params = {\r\n                    keyword: this.keyword,\r\n                    streetType: '',\r\n                    displayTag: this.doorTag\r\n                }\r\n            } else {\r\n                this.valueType = str;\r\n                this.doorTag = ''\r\n                params = {\r\n                    keyword: this.keyword,\r\n                    streetType: str==\"社会面视频\"?\"区域视频\":str\r\n                }\r\n            }\r\n            this.initData(params)\r\n        },\r\n        // handleNodeClick(val, flag) {\r\n        //     console.log(val, flag, \"cehsi1\")\r\n\r\n\r\n        //     if (!this.isSearch) {\r\n        //         if (flag && !val.single) {\r\n        //             // 全部选中\r\n        //             this.isAllSelect = true;\r\n\r\n        //             this.getDepList({ organization: val.label }, flag)\r\n        //         } else {\r\n        //             val.flag = flag;\r\n        //             this.changeAllNode.push(val)\r\n        //         }\r\n\r\n        //     } else {\r\n        //         if (val.single) {\r\n        //             val.flag = flag;\r\n        //             this.changeAllNode.push(val)\r\n\r\n        //         }\r\n        //     }\r\n\r\n        // },\r\n        // 点击节点\r\n        handleCheckAll(node, val) {\r\n            let currentNode = node;\r\n            if (!this.isAllSelect) {\r\n                // 点击第一层\r\n                if (typeof currentNode.data == 'string') {\r\n                    this.changeAllNode.forEach(item => {\r\n                        if (item.label == currentNode.label) {\r\n                            // 全选取消\r\n                            currentNode.flag = item.flag\r\n                            this.getDepList({ organization: currentNode.label }, currentNode.flag)\r\n                            return\r\n                        }\r\n                    })\r\n                } else {\r\n                    // 点击第二层\r\n                    this.changeAllNode.forEach((item, i) => {\r\n                        if (item.label == currentNode.label) {\r\n\r\n                            this.getLatLon({ id: this.changeAllNode[i].data }, this.changeAllNode[i].flag)\r\n                        }\r\n                    })\r\n\r\n                }\r\n\r\n            }\r\n            this.isAllSelect = false\r\n            this.changeAllNode = []\r\n\r\n\r\n        },\r\n        // 根据id查询\r\n        async getLatLon(params = {}, flag) {\r\n\r\n            await getVideoQueryListById(params).then(res => {\r\n                if (res.status == 200) {\r\n                    let list = res.data.extra;\r\n                    if (flag == undefined) {\r\n\r\n                        this.$store.commit('action/getVideoDetailsExpand', list)\r\n                    } else if (flag == 'play') {\r\n                        let par = {\r\n                            ...list,\r\n                            name: list.channalName,\r\n                        }\r\n                        this.$store.commit(\"action/getVideoPlayerList\", par)\r\n\r\n                    } else {\r\n\r\n                        // 给ue发送撒点\r\n                        list.name = list.deviceName;\r\n                        list.imageX = '0.18'\r\n                        list.imageY = '0.18'\r\n                        let params = {\r\n                            \"mode\": \"add\",\r\n                            \"sources\": [list]\r\n                        }\r\n                        this.$eventBus.$emit('senTxtToUe', params)\r\n                        console.log('params', params)\r\n                    }\r\n\r\n                }\r\n            })\r\n        },\r\n        // 根据组织查询\r\n        async getDepList(params = {}, flag) {\r\n            await getQueryListByOrg(params).then(res => {\r\n\r\n                if (res.status == 200) {\r\n                    let list = res.data.extra\r\n                    // 给ue发送撒点\r\n                    let params = {\r\n                        name: '区域监控',\r\n                        data: list,\r\n                        flag: flag\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params)\r\n\r\n                    console.log('params', params)\r\n                    // return res.data.extra\r\n                }\r\n            })\r\n        },\r\n\r\n        // 递归添加参数\r\n        addParams(val, index) {\r\n            if (!val.children.length) {\r\n                val.single = true;\r\n            } else {\r\n                val.single = false;\r\n                val.data = index + '-1'\r\n                val.children.forEach(ele => {\r\n                    this.addParams(ele)\r\n                })\r\n            }\r\n            return val\r\n        },\r\n\r\n\r\n\r\n\r\n        closeTree() {\r\n            this.$store.commit(\"action/clearAll\", false)\r\n\r\n        },\r\n        // 打开监控\r\n        openPlay(data) {\r\n            this.getLatLon({ id: data.data }, 'play')\r\n            this.$store.commit(\"action/getIsShowNumInitChange\", true)\r\n\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n\r\n        },\r\n        // 打开信息\r\n        openInfo(val) {\r\n            // this.$store.commit(\"action/getInformationBox\", val)\r\n            this.getLatLon({ id: val.data })\r\n            this.$store.commit(\"action/getVideoInfoFlag\", true)\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.radio {\r\n\r\n    ::v-deep .el-checkbox__label {\r\n        font-size: 28px;\r\n        font-weight: 200;\r\n    }\r\n\r\n    ::v-deep .el-checkbox {\r\n        white-space: wrap;\r\n        display: block;\r\n        color: #fff;\r\n        margin-top: 30px;\r\n        margin-left: 60px;\r\n    }\r\n\r\n    ::v-deep .el-checkbox__inner {\r\n        width: 26px;\r\n        height: 26px;\r\n        background-color: rgba(0, 142, 221, 0.1);\r\n    }\r\n\r\n    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {\r\n        border: 1px solid #dce1e2;\r\n        background-color: rgba(0, 142, 221, 0.1);\r\n    }\r\n\r\n    ::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {\r\n        color: #fff;\r\n    }\r\n\r\n    ::v-deep .el-checkbox__inner::after {\r\n        width: 9px;\r\n        height: 9px;\r\n        top: 5px;\r\n    }\r\n\r\n    ::v-deep .el-radio__input.is-checked+.el-radio__label {\r\n        color: #fff;\r\n    }\r\n\r\n    ::v-deep .el-radio {\r\n        color: #fff;\r\n        white-space: wrap;\r\n        margin-left: 60px;\r\n        font-weight: 300;\r\n        margin-top: 20px;\r\n    }\r\n\r\n    ::v-deep .el-radio__inner {\r\n        width: 23px;\r\n        height: 23px;\r\n        background-color: rgba(0, 142, 221, 0.1);\r\n    }\r\n\r\n    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n        background: rgba(128, 140, 146, 0.6);\r\n    }\r\n\r\n    ::v-deep .el-radio__inner::after {\r\n        background-color: rgba(128, 140, 146, 0.6);\r\n    }\r\n\r\n    ::v-deep .el-radio__label {\r\n        font-size: 28px;\r\n        padding-left: 20px;\r\n    }\r\n}\r\n\r\n.allScreen {\r\n    display: none;\r\n}\r\n\r\n.container_box {\r\n    // background: url('@/assets/images/mainPics/bgicon6.png') no-repeat center;\r\n    // background-size: 100% 100%;\r\n\r\n\r\n    .treeShow {\r\n        width: 100%;\r\n        height: 1254px;\r\n        overflow: auto;\r\n        padding: 0 10px;\r\n        box-sizing: border-box;\r\n\r\n        .slotTxt {\r\n            display: flex\r\n        }\r\n\r\n        .rightIcon {\r\n            position: absolute;\r\n            right: 20px;\r\n            display: flex;\r\n\r\n            .point {\r\n                margin-right: 30px;\r\n            }\r\n        }\r\n\r\n        .over-ellipsis {\r\n            display: block;\r\n            width: 62%;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            -webkit-line-clamp: 1;\r\n\r\n            .tree-a {\r\n                color: #fff;\r\n                text-decoration: none;\r\n                font-size: 28px;\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n\r\n\r\n\r\n\r\n    .keyWordsSearch {\r\n        margin: 10px 0;\r\n        padding: 0 20px;\r\n\r\n        .selectType {\r\n            margin-top: 20px;\r\n        }\r\n\r\n        ::v-deep .el-input {\r\n            .el-input__suffix {\r\n                right: 40px;\r\n\r\n                .el-icon-circle-close:before {\r\n                    font-size: 28px;\r\n                    padding-top: 10px;\r\n\r\n                }\r\n            }\r\n\r\n            .el-input__clear {\r\n                margin-top: 10px;\r\n                padding-right: 10px;\r\n            }\r\n\r\n            .el-input__prefix {\r\n                right: 5px;\r\n                left: auto;\r\n                padding: 0 10px;\r\n                font-size: 35px;\r\n                cursor: pointer;\r\n                font-weight: bold;\r\n                padding-top: 10px;\r\n            }\r\n\r\n\r\n            .el-input__inner {\r\n                background-color: transparent;\r\n                color: #fff;\r\n                height: 65px;\r\n                font-size: 28px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .selectType {\r\n        ::v-deep .el-checkbox-group {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n        }\r\n\r\n        ::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {\r\n            color: #6db6ff\r\n        }\r\n\r\n        ::v-deep .el-checkbox__label {\r\n            color: #fff;\r\n            padding-left: 0;\r\n            line-height: 30px !important;\r\n            font-size: 25px !important;\r\n        }\r\n\r\n        ::v-deep .el-checkbox__input {\r\n            display: none;\r\n        }\r\n\r\n        ::v-deep .el-checkbox.is-bordered.el-checkbox--small {\r\n            padding: 4px 8px;\r\n        }\r\n\r\n        ::v-deep .el-checkbox {\r\n            margin-right: 0;\r\n            height: 50px;\r\n        }\r\n    }\r\n\r\n    ::v-deep .el-tree {\r\n        background: transparent;\r\n        color: #fff;\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n            margin-left: 20px;\r\n            width: 25px;\r\n            height: 25px;\r\n        }\r\n\r\n        /* 对勾样式 */\r\n        .el-checkbox__inner::after {\r\n            left: 10px;\r\n            top: 5px;\r\n        }\r\n\r\n        .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n            transform: rotate(50deg) scaleY(1.8);\r\n        }\r\n\r\n        .el-checkbox__input.is-disabled {\r\n            display: none;\r\n        }\r\n\r\n        .el-tree-node.is-focusable {\r\n            margin-top: 15px;\r\n            font-size: 27px !important;\r\n\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n        }\r\n\r\n        img {\r\n            margin-right: 5px;\r\n        }\r\n\r\n        .el-tree-node:focus>.el-tree-node__content {\r\n            background: linear-gradient(160deg, rgba(0, 163, 255, 0.25) 22%, rgba(0, 142, 221, 0.2) 57%, rgba(0, 133, 255, 0.26) 100%);\r\n            border-radius: 6px 6px 6px 6px;\r\n            border: 1px solid;\r\n            border-image: linear-gradient(160deg, rgba(0, 178, 255, 1), rgba(0, 142, 221, 0.77), rgba(0, 133, 255, 0.26)) 1 1;\r\n        }\r\n\r\n        >.el-tree-node>.el-tree-node__content {\r\n            position: relative;\r\n            width: 99%;\r\n            height: 60px;\r\n            box-sizing: border-box;\r\n\r\n            &:hover {\r\n                background: linear-gradient(160deg, rgba(0, 163, 255, 0.25) 22%, rgba(0, 142, 221, 0.2) 57%, rgba(0, 133, 255, 0.26) 100%);\r\n                border-radius: 6px 6px 6px 6px;\r\n                border: 1px solid;\r\n                border-image: linear-gradient(160deg, rgba(0, 178, 255, 1), rgba(0, 142, 221, 0.77), rgba(0, 133, 255, 0.26)) 1 1;\r\n            }\r\n\r\n\r\n            >.el-icon-caret-right {\r\n                // position: absolute;\r\n                // right: 45px;\r\n                font-size: 16px;\r\n                color: #fff;\r\n            }\r\n\r\n            .el-icon-caret-right:before {\r\n                content: \"\\e6df\";\r\n            }\r\n\r\n            .el-tree-node__label {\r\n                font-size: .9rem;\r\n                font-weight: bold;\r\n            }\r\n        }\r\n\r\n        .el-tree-node__children .el-tree-node__content {\r\n            background: rgba(255, 255, 255, 0);\r\n            height: 60px;\r\n\r\n            .el-tree-node__label {\r\n                font-size: .8rem;\r\n                font-weight: 400;\r\n            }\r\n\r\n        }\r\n\r\n\r\n\r\n\r\n    }\r\n}\r\n\r\n/* 整个滚动条 */\r\n::-webkit-scrollbar {\r\n    position: absolute;\r\n    width: 5px !important;\r\n    height: 10px !important;\r\n}\r\n\r\n/* 滚动条上的滚动滑块 */\r\n::-webkit-scrollbar-thumb {\r\n    background: rgba(27, 137, 225, 0.4);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 滚动条上的滚动滑块悬浮效果 */\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(27, 137, 225, 0.4);\r\n\r\n}\r\n\r\n/* 滚动条没有滑块的轨道部分 */\r\n::-webkit-scrollbar-track-piece {\r\n    background-color: rgba(255, 255, 255, .3);\r\n}\r\n\r\n/*滚动条上的按钮(上下箭头)  */\r\n::-webkit-scrollbar-button {\r\n    background-color: transparent;\r\n}\r\n</style>"], "mappings": "AAkEA,SAAAA,WAAA;AACA,SAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA;AACA,SAAAC,QAAA;AACA,SAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,KAAA,GAEA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,gBAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,MAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,aAAA;MACAC,SAAA;MACAC,OAAA;MACAC,OAAA;MACAC,KAAA;MACAC,cAAA;IACA;EACA;EACAC,KAAA;IACAC,aAAAC,EAAA,GAEA;IACAf,YAAAe,EAAA,GACA;EAEA;EACAC,QAAA;IACA,GAAA3B,QAAA,YACA,yBACA;EACA;EACA4B,UAAA;IACA,KAAAC,MAAA,CAAAC,MAAA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,QAAA;IAEA;EACA;EACAC,cAAA;IACA,KAAAL,MAAA,CAAAC,MAAA;EAEA;EAEAK,OAAA;IACAC,UAAA;MACA,IAAAC,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAG,gBAAAC,GAAA;MACA,KAAAL,SAAA;MACA,KAAAP,MAAA,CAAAC,MAAA;MACA,KAAAQ,SAAA,CAAAC,KAAA;MACA;MACA,IAAAE,GAAA,CAAAC,MAAA;QACA,KAAAC,SAAA;UAAAC,EAAA,EAAAH,GAAA,CAAArC;QAAA;MAEA;IACA;IACAyC,YAAAJ,GAAA;MACA,KAAAE,SAAA;QAAAC,EAAA,EAAAH;MAAA;IACA;IACAK,qBAAAL,GAAA;MACA,IAAAA,GAAA,CAAAM,MAAA;QACA,KAAAxB,cAAA,IAAAkB,GAAA,CAAAA,GAAA,CAAAM,MAAA;MACA;MACA,SAAAxB,cAAA,CAAAwB,MAAA;QACA,KAAAJ,SAAA;UAAAC,EAAA,OAAArB,cAAA;QAAA;MACA;QACA,KAAAe,SAAA,CAAAC,KAAA;MACA;IACA;IACA,MAAAN,SAAAI,MAAA;MACAxC,gBAAA,CAAAwC,MAAA,EAAAW,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAA7C,IAAA,CAAAgD,KAAA;UACAD,IAAA,CAAAE,OAAA,EAAAC,GAAA,EAAAC,KAAA;YACA,SAAAnC,OAAA;cACAkC,GAAA,CAAAE,QAAA;YACA;YACA,KAAAC,SAAA,CAAAH,GAAA,EAAAC,KAAA;UACA;UACA,KAAAjD,gBAAA,GAAA6C,IAAA;QAEA;MACA;IACA;IACA;IACAO,YAAAjB,GAAA;MACA,KAAArB,OAAA,GAAAqB,GAAA;MACA,KAAAH,SAAA,CAAAC,KAAA;MAEA,IAAAF,MAAA;QACAjB,OAAA,EAAAqB,GAAA;QACAkB,UAAA,OAAAxC,SAAA;QACAyC,UAAA,OAAAvC;MACA;MACA,IAAAoB,GAAA;QACA,KAAAzB,QAAA;MACA;QACA,KAAAA,QAAA;MACA;MACA,KAAAiB,QAAA,CAAAI,MAAA;IACA;IACA;IACAwB,kBAAApB,GAAA;MACA,IAAAqB,GAAA,GAAArB,GAAA,CAAAA,GAAA,CAAAM,MAAA;MACA,KAAA7B,aAAA,IAAA4C,GAAA;MACA,IAAAzB,MAAA;MACA,IAAAyB,GAAA;QACA,KAAA3C,SAAA;QACA,KAAAE,OAAA;QACAgB,MAAA;UACAjB,OAAA,OAAAA,OAAA;UACAuC,UAAA;UACAC,UAAA,OAAAvC;QACA;MACA;QACA,KAAAF,SAAA,GAAA2C,GAAA;QACA,KAAAzC,OAAA;QACAgB,MAAA;UACAjB,OAAA,OAAAA,OAAA;UACAuC,UAAA,EAAAG,GAAA,uBAAAA;QACA;MACA;MACA,KAAA7B,QAAA,CAAAI,MAAA;IACA;IACA;IACA;;IAGA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA0B,eAAAC,IAAA,EAAAvB,GAAA;MACA,IAAAwB,WAAA,GAAAD,IAAA;MACA,UAAAnD,WAAA;QACA;QACA,WAAAoD,WAAA,CAAA7D,IAAA;UACA,KAAAW,aAAA,CAAAsC,OAAA,CAAAa,IAAA;YACA,IAAAA,IAAA,CAAAzD,KAAA,IAAAwD,WAAA,CAAAxD,KAAA;cACA;cACAwD,WAAA,CAAAE,IAAA,GAAAD,IAAA,CAAAC,IAAA;cACA,KAAAC,UAAA;gBAAAC,YAAA,EAAAJ,WAAA,CAAAxD;cAAA,GAAAwD,WAAA,CAAAE,IAAA;cACA;YACA;UACA;QACA;UACA;UACA,KAAApD,aAAA,CAAAsC,OAAA,EAAAa,IAAA,EAAAI,CAAA;YACA,IAAAJ,IAAA,CAAAzD,KAAA,IAAAwD,WAAA,CAAAxD,KAAA;cAEA,KAAAkC,SAAA;gBAAAC,EAAA,OAAA7B,aAAA,CAAAuD,CAAA,EAAAlE;cAAA,QAAAW,aAAA,CAAAuD,CAAA,EAAAH,IAAA;YACA;UACA;QAEA;MAEA;MACA,KAAAtD,WAAA;MACA,KAAAE,aAAA;IAGA;IACA;IACA,MAAA4B,UAAAN,MAAA,OAAA8B,IAAA;MAEA,MAAApE,qBAAA,CAAAsC,MAAA,EAAAW,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAA7C,IAAA,CAAAgD,KAAA;UACA,IAAAe,IAAA,IAAAI,SAAA;YAEA,KAAA1C,MAAA,CAAAC,MAAA,iCAAAqB,IAAA;UACA,WAAAgB,IAAA;YACA,IAAAK,GAAA;cACA,GAAArB,IAAA;cACAjD,IAAA,EAAAiD,IAAA,CAAAsB;YACA;YACA,KAAA5C,MAAA,CAAAC,MAAA,8BAAA0C,GAAA;UAEA;YAEA;YACArB,IAAA,CAAAjD,IAAA,GAAAiD,IAAA,CAAAuB,UAAA;YACAvB,IAAA,CAAAwB,MAAA;YACAxB,IAAA,CAAAyB,MAAA;YACA,IAAAvC,MAAA;cACA;cACA,YAAAc,IAAA;YACA;YACA,KAAAb,SAAA,CAAAC,KAAA,eAAAF,MAAA;YACAwC,OAAA,CAAAC,GAAA,WAAAzC,MAAA;UACA;QAEA;MACA;IACA;IACA;IACA,MAAA+B,WAAA/B,MAAA,OAAA8B,IAAA;MACA,MAAArE,iBAAA,CAAAuC,MAAA,EAAAW,IAAA,CAAAC,GAAA;QAEA,IAAAA,GAAA,CAAAC,MAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAA7C,IAAA,CAAAgD,KAAA;UACA;UACA,IAAAf,MAAA;YACAnC,IAAA;YACAE,IAAA,EAAA+C,IAAA;YACAgB,IAAA,EAAAA;UACA;UACA,KAAA7B,SAAA,CAAAC,KAAA,eAAAF,MAAA;UAEAwC,OAAA,CAAAC,GAAA,WAAAzC,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAoB,UAAAhB,GAAA,EAAAc,KAAA;MACA,KAAAd,GAAA,CAAAjC,QAAA,CAAAuC,MAAA;QACAN,GAAA,CAAAC,MAAA;MACA;QACAD,GAAA,CAAAC,MAAA;QACAD,GAAA,CAAArC,IAAA,GAAAmD,KAAA;QACAd,GAAA,CAAAjC,QAAA,CAAA6C,OAAA,CAAAC,GAAA;UACA,KAAAG,SAAA,CAAAH,GAAA;QACA;MACA;MACA,OAAAb,GAAA;IACA;IAKAsC,UAAA;MACA,KAAAlD,MAAA,CAAAC,MAAA;IAEA;IACA;IACAkD,SAAA5E,IAAA;MACA,KAAAuC,SAAA;QAAAC,EAAA,EAAAxC,IAAA,CAAAA;MAAA;MACA,KAAAyB,MAAA,CAAAC,MAAA;MAEA,KAAAD,MAAA,CAAAC,MAAA;IAEA;IACA;IACAmD,SAAAxC,GAAA;MACA;MACA,KAAAE,SAAA;QAAAC,EAAA,EAAAH,GAAA,CAAArC;MAAA;MACA,KAAAyB,MAAA,CAAAC,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}