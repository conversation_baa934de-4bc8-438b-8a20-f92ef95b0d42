{"ast": null, "code": "import playUeShow from '@/components/play.vue';\nimport { getVideoUrl } from '@/api/hzVideo.js';\nimport bufferSearch from '@/components/bufferSearch.vue';\nimport pointPop from '@/views/dataScreen/fileType/pointPop.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'Home',\n  components: {\n    playUeShow,\n    bufferSearch,\n    pointPop\n  },\n  data() {\n    return {\n      isStreet: false\n    };\n  },\n  watch: {\n    userVideoToken(nv) {\n      if (nv == null) {\n        this.$store.dispatch('getTokenAuthentication');\n      }\n    },\n    // 接受ue信息 \n    dataChannelText(nv) {\n      switch (nv.typename) {\n        // case 'data':  \n        //   if (nv.ape_id != '') {\n        //     this.$store.commit(\"action/getVideoPlayerList\", nv)\n        //     this.$store.commit(\"action/getIsShowNumInitChange\", false)\n        //     this.$store.commit(\"action/getVideoPlayerBox\", true)\n        //   } else {\n        //     this.$message.error('暂无视频点位')\n        //   }\n        //   break;\n        case 'street':\n          this.$store.commit(\"action/getTabsValue\", nv.channalName);\n          break;\n        case 'vr':\n          let {\n            channal_code\n          } = nv;\n          this.getVideoUrl(channal_code, 3);\n          break;\n        case 'videom':\n          nv.ape_id = nv.install_position;\n          nv.channalName = nv.device_name;\n          const {\n            install_position,\n            device_name,\n            ...data\n          } = nv;\n          nv = data;\n          if (nv.ape_id != '') {\n            this.$store.commit(\"action/getVideoPlayerList\", nv);\n            this.$store.commit(\"action/getVideoPlayerBox\", true);\n            this.$store.commit(\"action/getIsShowNumInitChange\", true);\n          } else {\n            this.$message.error('暂无视频点位');\n          }\n          break;\n        default:\n          break;\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      bufferShow: state => state.action.bufferShow\n    }),\n    ...mapState(['userVideoToken', 'isShowPlay', 'dataChannelText'])\n  },\n  created() {\n    this.$store.dispatch('getTokenAuthentication');\n    this.keepPlayVideo();\n    //     setTimeout(()=>{\n    // this.getVideoUrl('32058358001310267217',2)\n    //     },2000)\n  },\n  mounted() {\n    this.$eventBus.$emit(\"senTxtToUe\", \"首页\");\n  },\n  methods: {\n    keepPlayVideo() {\n      setTimeout(() => {\n        this.$store.dispatch('getTokenAuthentication');\n      }, 5400000);\n    },\n    // 获取设备视频流\n    async getVideoUrl(channel_code, stream_mode) {\n      await getVideoUrl(channel_code, stream_mode).then(res => {\n        let url = res.data.data;\n        this.$eventBus.$emit('senTxtToUe', {\n          url: url\n        });\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["playUeShow", "getVideoUrl", "bufferSearch", "pointPop", "mapState", "name", "components", "data", "isStreet", "watch", "userVideoToken", "nv", "$store", "dispatch", "dataChannelText", "typename", "commit", "channal<PERSON>ame", "channal_code", "ape_id", "install_position", "device_name", "$message", "error", "computed", "bufferShow", "state", "action", "created", "keepPlayVideo", "mounted", "$eventBus", "$emit", "methods", "setTimeout", "channel_code", "stream_mode", "then", "res", "url"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"\">\r\n    <div class=\"uiContainer\" v-if=\"isShowPlay == '3d'\">\r\n      <playUeShow ref=\"play\"></playUeShow>\r\n      <bufferSearch v-if=\"bufferShow\" />\r\n\r\n    </div>\r\n    <!-- 事件告警 -->\r\n    <pointPop></pointPop>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport playUeShow from '@/components/play.vue'\r\nimport { getVideoUrl } from '@/api/hzVideo.js';\r\nimport bufferSearch from '@/components/bufferSearch.vue'\r\nimport pointPop from '@/views/dataScreen/fileType/pointPop.vue'\r\n\r\nimport { mapState } from 'vuex'\r\n\r\nexport default {\r\n  name: 'Home',\r\n  components: {\r\n    playUeShow,\r\n    bufferSearch,\r\n    pointPop,\r\n\r\n  },\r\n  data() {\r\n    return {\r\n      isStreet: false,\r\n    }\r\n  },\r\n  watch: {\r\n    userVideoToken(nv) {\r\n      if (nv == null) {\r\n        this.$store.dispatch('getTokenAuthentication')\r\n      }\r\n    },\r\n    // 接受ue信息 \r\n    dataChannelText(nv) {\r\n      switch (nv.typename) {\r\n        // case 'data':  \r\n        //   if (nv.ape_id != '') {\r\n        //     this.$store.commit(\"action/getVideoPlayerList\", nv)\r\n        //     this.$store.commit(\"action/getIsShowNumInitChange\", false)\r\n        //     this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n        //   } else {\r\n        //     this.$message.error('暂无视频点位')\r\n        //   }\r\n        //   break;\r\n        case 'street':\r\n          this.$store.commit(\"action/getTabsValue\", nv.channalName)\r\n          break;\r\n        case 'vr':\r\n          let { channal_code } = nv;\r\n          this.getVideoUrl(channal_code, 3)\r\n          break;\r\n        case 'videom':\r\n          nv.ape_id = nv.install_position\r\n          nv.channalName = nv.device_name\r\n          const { install_position, device_name, ...data } = nv\r\n          nv = data\r\n          if (nv.ape_id != '') {\r\n            this.$store.commit(\"action/getVideoPlayerList\", nv)\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n            this.$store.commit(\"action/getIsShowNumInitChange\", true)\r\n          } else {\r\n            this.$message.error('暂无视频点位')\r\n          }\r\n          break\r\n        default:\r\n          break;\r\n      }\r\n\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      bufferShow: state => state.action.bufferShow,\r\n    }),\r\n    ...mapState(['userVideoToken', 'isShowPlay', 'dataChannelText'])\r\n  },\r\n  created() {\r\n    this.$store.dispatch('getTokenAuthentication')\r\n    this.keepPlayVideo()\r\n    //     setTimeout(()=>{\r\n    // this.getVideoUrl('32058358001310267217',2)\r\n    //     },2000)\r\n  },\r\n  mounted() {\r\n    this.$eventBus.$emit(\"senTxtToUe\", \"首页\")\r\n\r\n  },\r\n  methods: {\r\n\r\n    keepPlayVideo() {\r\n      setTimeout(() => {\r\n\r\n        this.$store.dispatch('getTokenAuthentication')\r\n      }, 5400000)\r\n    },\r\n    // 获取设备视频流\r\n    async getVideoUrl(channel_code, stream_mode) {\r\n      await getVideoUrl(channel_code, stream_mode).then(res => {\r\n        let url = res.data.data;\r\n\r\n        this.$eventBus.$emit('senTxtToUe', {\r\n          url: url\r\n        })\r\n      })\r\n    },\r\n  },\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.uiContainer {\r\n  position: fixed;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100vh;\r\n\r\n}\r\n</style>"], "mappings": "AAaA,OAAAA,UAAA;AACA,SAAAC,WAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AAEA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,UAAA;IACAE,YAAA;IACAC;EAEA;EACAI,KAAA;IACA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAC,eAAAC,EAAA;MACA,IAAAA,EAAA;QACA,KAAAC,MAAA,CAAAC,QAAA;MACA;IACA;IACA;IACAC,gBAAAH,EAAA;MACA,QAAAA,EAAA,CAAAI,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA,KAAAH,MAAA,CAAAI,MAAA,wBAAAL,EAAA,CAAAM,WAAA;UACA;QACA;UACA;YAAAC;UAAA,IAAAP,EAAA;UACA,KAAAV,WAAA,CAAAiB,YAAA;UACA;QACA;UACAP,EAAA,CAAAQ,MAAA,GAAAR,EAAA,CAAAS,gBAAA;UACAT,EAAA,CAAAM,WAAA,GAAAN,EAAA,CAAAU,WAAA;UACA;YAAAD,gBAAA;YAAAC,WAAA;YAAA,GAAAd;UAAA,IAAAI,EAAA;UACAA,EAAA,GAAAJ,IAAA;UACA,IAAAI,EAAA,CAAAQ,MAAA;YACA,KAAAP,MAAA,CAAAI,MAAA,8BAAAL,EAAA;YACA,KAAAC,MAAA,CAAAI,MAAA;YACA,KAAAJ,MAAA,CAAAI,MAAA;UACA;YACA,KAAAM,QAAA,CAAAC,KAAA;UACA;UACA;QACA;UACA;MACA;IAEA;EACA;EACAC,QAAA;IACA,GAAApB,QAAA;MACAqB,UAAA,EAAAC,KAAA,IAAAA,KAAA,CAAAC,MAAA,CAAAF;IACA;IACA,GAAArB,QAAA;EACA;EACAwB,QAAA;IACA,KAAAhB,MAAA,CAAAC,QAAA;IACA,KAAAgB,aAAA;IACA;IACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA,CAAAC,KAAA;EAEA;EACAC,OAAA;IAEAJ,cAAA;MACAK,UAAA;QAEA,KAAAtB,MAAA,CAAAC,QAAA;MACA;IACA;IACA;IACA,MAAAZ,YAAAkC,YAAA,EAAAC,WAAA;MACA,MAAAnC,WAAA,CAAAkC,YAAA,EAAAC,WAAA,EAAAC,IAAA,CAAAC,GAAA;QACA,IAAAC,GAAA,GAAAD,GAAA,CAAA/B,IAAA,CAAAA,IAAA;QAEA,KAAAwB,SAAA,CAAAC,KAAA;UACAO,GAAA,EAAAA;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}