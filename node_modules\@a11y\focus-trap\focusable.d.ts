/**
 * Returns whether the element is hidden.
 * @param $elem
 */
export declare function isHidden($elem: HTMLElement): boolean;
/**
 * Returns whether the element is disabled.
 * @param $elem
 */
export declare function isDisabled($elem: HTMLElement): boolean;
/**
 * Determines whether an element is focusable.
 * Read more here: https://stackoverflow.com/questions/1599660/which-html-elements-can-receive-focus/1600194#1600194
 * Or here: https://stackoverflow.com/questions/18261595/how-to-check-if-a-dom-element-is-focusable
 * @param $elem
 */
export declare function isFocusable($elem: HTMLElement): boolean;
