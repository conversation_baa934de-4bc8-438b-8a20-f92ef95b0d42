{"ast": null, "code": "import { openFullscreen, exitFullscreen, getWindowRatio } from '@/utils/fullScreen';\nimport canvasPlayerList from './canvasPlayerList.vue';\nimport moment from 'moment';\n// import { mapGetters } from \"vuex\";\nimport { getWaterMarkSet, setWaterMark } from \"@/api/video\";\nexport default {\n  name: 'v-dialog',\n  components: {\n    canvasPlayerList\n  },\n  props: {\n    headerFlag: {\n      type: Boolean,\n      default: true\n    },\n    nameFieldDisplay: {\n      type: String,\n      default: '0'\n    },\n    /*\r\n        弹窗标题;\r\n    */\n    title: {\n      type: String,\n      default() {\n        return '';\n      }\n    },\n    /*\r\n        整个播放区域的样式设置;\r\n    */\n    playerListStyle: {\n      type: Object,\n      default() {\n        return {};\n      }\n    },\n    /*\r\n        是否显示弹窗底部\r\n    */\n    showFooter: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    showSwitch: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    /*\r\n        播放窗口的样式\r\n    */\n    playerModalStyle: {\n      type: Object,\n      default() {\n        return {};\n      }\n    },\n    initNum: {\n      type: Number,\n      default() {\n        return 4;\n      }\n    },\n    prefix: {\n      type: String,\n      default() {\n        return 'vDialogWay';\n      }\n    },\n    // 待播放列表;\n    cameras: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    // 是否需要轮播操作组;\n    showLoop: {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    },\n    // 轮播分组列表\n    loopGroup: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    loopDefaultValue: {\n      type: String\n    },\n    // 轮巡时间列表\n    loopTimeList: {\n      type: Array,\n      default() {\n        return [{\n          label: \"1分钟\",\n          value: 60 * 1000\n        }, {\n          label: \"5分钟\",\n          value: 5 * 60 * 1000\n        }, {\n          label: \"10分钟\",\n          value: 10 * 60 * 1000\n        }];\n      }\n    },\n    bottomList: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    // 标记当前调用组件的状态;\n    current: {\n      type: String,\n      default() {\n        return '';\n      }\n    },\n    // 是否能删除\n    canDelete: {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    }\n  },\n  computed: {\n    // ...mapGetters(['userinfo']),\n    // 弹出窗的id 属性;\n    modalId() {\n      return `${this.prefix}_video-dialog-container`;\n    },\n    /*\r\n        整个弹窗的高度和宽度\r\n    */\n    modalStyle() {\n      let ratio = getWindowRatio() / 100;\n      let width = window.screen.width / ratio;\n      let height = window.screen.height / ratio;\n      let scale = ratio === 1 ? '1,1' : width + ',' + height;\n      return {\n        ...this.customModalStyle,\n        ...this.playerModalStyle\n      };\n    },\n    /*\r\n        el-dialog__body的高度(去除了头部和底部);\r\n        在全屏的时候不能考虑缩放比例,直接使用window.screen.width window.screen.height 设置;\r\n    */\n    bodyHeight() {\n      // let height = window.screen.height;\n      let height = this.current_height;\n      let total = 50 + (this.showFooter ? 50 : 0);\n      let allHeight = this.isFull ? height : parseInt(this.modalStyle.height);\n      return allHeight - total;\n    },\n    /*\r\n    el-dialog__body的宽度,全屏时窗口的宽度;\r\n    */\n    bodyWidth() {\n      // let width = window.screen.width;\n      let width = this.current_width;\n      return this.isFull ? width : parseInt(this.modalStyle.width);\n    },\n    /*\r\n        播放区域的宽度和高度\r\n    */\n    canvasPlayListStyle() {\n      return {\n        width: this.bodyWidth - 30 - (this.showSwitch ? this.switchFlag ? 198 : 30 : 0),\n        height: this.bodyHeight - 30\n      };\n    }\n  },\n  directives: {\n    'dialogDrag': {\n      // 属性名称dialogDrags，前面加v- 使用\n      bind(el, binding, vnode, oldVnode) {\n        const {\n          value\n        } = binding;\n        let minWidth = 400;\n        let minHeight = 300;\n        let dragHeader = value && value.dragHeader ? value.dragHeader : '.el-dialog__header';\n        let dragDomSelector = value && value.dragDom ? value.dragDom : '.el-dialog';\n        const dialogHeaderEl = el.querySelector(dragHeader);\n        const dragDom = el;\n        dialogHeaderEl.style.cssText += \";cursor:move;\";\n        const sty = function () {\n          if (window.document.currentStyle) {\n            return (dom, attr) => dom.currentStyle[attr];\n          } else {\n            return (dom, attr) => getComputedStyle(dom, false)[attr];\n          }\n        }();\n        dialogHeaderEl.onmousedown = e => {\n          if (vnode.context.isFull) {\n            return false; // 如果是全屏时不处理\n          }\n          // 鼠标按下，计算当前元素距离可视区的距离\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\n          const screenWidth = document.body.clientWidth; // body当前宽度\n          const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取)\n          const dragDomWidth = dragDom.offsetWidth; // 对话框宽度\n          const dragDomheight = dragDom.offsetHeight; // 对话框高度\n          // 获取到的值带px 正则匹配替换\n          let styL = sty(dragDom, \"left\");\n          let styT = sty(dragDom, \"top\");\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\n          if (styL.includes(\"%\")) {\n            styL = +document.body.clientWidth * (+styL.replace(/\\%/g, \"\") / 100);\n            styT = +document.body.clientHeight * (+styT.replace(/\\%/g, \"\") / 100);\n          } else {\n            styL = +styL.replace(/\\px/g, \"\");\n            styT = +styT.replace(/\\px/g, \"\");\n          }\n          document.onmousemove = function (e) {\n            // 通过事件委托，计算移动的距离\n            let left = e.clientX - disX + styL;\n            let top = e.clientY - disY + styT;\n\n            // 边界处理\n            if (left < 335) {\n              left = 335;\n            }\n            if (left > screenWidth + 335 - dragDomWidth) {\n              left = screenWidth + 335 - dragDomWidth;\n            }\n            if (top < 0) {\n              top = 0;\n            }\n            if (top > screenHeight - dragDomheight) {\n              top = screenHeight - dragDomheight;\n            }\n\n            // 移动当前元素\n            dragDom.style.cssText += `;left:${left}px;top:${top}px;`;\n            vnode.context.exitModelStyle = {\n              top: top,\n              left: left\n            };\n          };\n          document.onmouseup = function (e) {\n            document.onmousemove = null;\n            document.onmouseup = null;\n          };\n        };\n      }\n    }\n  },\n  data() {\n    return {\n      hideAllFunc: false,\n      choseObj: {},\n      markOptions: [],\n      loading: false,\n      markValue1: 100,\n      markValue2: 14,\n      checkList: [],\n      showStatus: false,\n      // 当前的弹窗打开关闭状态\n      currentWin: 4,\n      // 当前是几分屏,\n      isFull: false,\n      // 当前弹窗是否是全屏的状态;\n      is_mini: false,\n      // 当前弹窗是否是缩小状态\n      customModalStyle: {\n        width: '860px',\n        // 整个弹窗的宽度\n        height: '750px',\n        //整个弹窗的高度\n        top: '15vh',\n        // 弹窗距离顶部的距离\n        dialog_body_padding: '15px' // el-dialog__body 的padding\n      },\n      exitModelStyle: {\n        left: '50%',\n        top: '50%'\n      },\n      // 轮播开关\n      loopFlag: false,\n      // 播放列表是否展开\n      switchFlag: true,\n      // 开始轮播时间;\n      loopTime: 60 * 1000,\n      //this.loopTimeList && this.loopTimeList[0] && this.loopTimeList[0]['value']?this.loopTimeList[0]['value']:0,\n      playerArray: [],\n      loopTimer: null,\n      notLoopArray: [],\n      // 还没有开始轮播的点位;\n      saveVisible: false,\n      tabIndex: 0,\n      groupName: '',\n      folderTreeCode: '',\n      carouseOptions: [],\n      saveLoading: false,\n      markVisible: false,\n      current_height: 0,\n      // 当前高度\n      current_width: 0 // 当前宽度\n    };\n  },\n  watch: {\n    saveVisible: {\n      handler(newVal) {\n        if (newVal == false) {\n          this.choseObj = {};\n        }\n      }\n    },\n    showStatus: {\n      handler(newVal) {\n        if (newVal == false) {\n          this.cameras.forEach(item => {\n            item.chose = false;\n          });\n        }\n      }\n    }\n  },\n  methods: {\n    hideFunction() {\n      this.hideAllFunc = true;\n    },\n    change() {\n      if (!/^[1-9]\\d*$/.test(this.markValue2)) {\n        this.markValue2 = Number(this.markValue2.toFixed(0));\n      }\n      this.renderWaterMarkText();\n    },\n    saveWatermarkOptions() {\n      this.loading = true;\n      let arr = this.markOptions;\n      let promiseArr = [];\n      arr.forEach(item => {\n        if (item.dictLabel == 'font_transparence') {\n          item.dictValue = this.markValue1;\n        } else if (item.dictLabel == 'font_size') {\n          item.dictValue = this.markValue2;\n        }\n        promiseArr.push(setWaterMark(item));\n      });\n      Promise.all(promiseArr).then(res => {\n        this.loading = false;\n        if (res[0]?.code == 0 && res[1]?.code == 0) {\n          this.$message({\n            type: 'success',\n            message: `保存成功`\n          });\n          this.markVisible = false;\n          // this.$refs['canvasPlayerList'].resetWatermark()\n          if (this.$parent.$refs.video1) {\n            this.$parent.$refs.video1.$refs['canvasPlayerList'].resetWatermark();\n          }\n          if (this.$parent.$refs.video4) {\n            this.$parent.$refs.video4.$refs['canvasPlayerList'].resetWatermark();\n          }\n          if (this.$parent.$refs.canvasPlay) {\n            this.$parent.$refs.canvasPlay.resetWatermark();\n          }\n        }\n      });\n    },\n    // 水印参数初始化\n    renderWaterMarkText() {\n      // let waterCanvas = document.getElementById('waterMark');\n      let waterCanvas = this.$refs.waterMark;\n      let ctx = waterCanvas.getContext('2d');\n      // 清除画布\n      let width = waterCanvas.width;\n      let height = waterCanvas.height;\n      if (ctx) {\n        ctx.clearRect(0, 0, width, height);\n      }\n      // let name = this.userinfo.sysUser.nickName\n      const name = \"南星渎商业三期\";\n      let time = moment().format(\"YYYY-MM-DD HH:mm:ss\");\n      let points = [{\n        x: width / 4,\n        y: height / 4\n      }, {\n        x: 3 * width / 4,\n        y: height / 4\n      }, {\n        x: width / 4,\n        y: 3 * height / 4\n      }, {\n        x: 3 * width / 4,\n        y: 3 * height / 4\n      }\n      // {// 中心点\n      //     x: width / 2,\n      //     y: height / 2\n      // }\n      ];\n      for (let i = 0; i < points.length; i++) {\n        let {\n          x,\n          y\n        } = points[i];\n        this._renderCanvasText(waterCanvas, name, x, y);\n        this._renderCanvasText(waterCanvas, time, x, y);\n      }\n    },\n    // 绘制水印\n    _renderCanvasText: function (waterCanvas, text, x, y, fix) {\n      let _ctx = waterCanvas.getContext('2d');\n      if (_ctx) {\n        let metrics = _ctx.measureText(text);\n        let strWidth = metrics.width;\n        let actualHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;\n        _ctx.save();\n        _ctx.font = `${this.markValue2}px 微软雅黑`;\n        _ctx.fillStyle = `rgba(255,255,255,${(this.markValue1 / 100).toFixed(2)})`;\n        let yHeight = fix ? y + strWidth / 2 - actualHeight : y + strWidth / 2;\n        _ctx.rotate(-15 * Math.PI / 180);\n        _ctx.fillText(text, x - strWidth / 2 - strWidth / 2, yHeight);\n        _ctx.restore();\n      }\n    },\n    // 保存\n    save(isone) {\n      const check_ids = this.cameras.filter(item => {\n        return item.cameraCode && item.chose;\n      });\n      if (!isone && check_ids.length === 0) {\n        this.$message.error('请勾选点位');\n        return false;\n      }\n      this.saveVisible = true;\n      this.saveLoading = true;\n      let params = {\n        \"onlineStatusList\": [],\n        \"cameraShareList\": [],\n        \"orgAreaList\": [],\n        \"pageNum\": 1,\n        \"searchType\": \"click\"\n      };\n      queryCarouseAndCameras(params).then(res => {\n        this.carouseOptions = res.data.map(item => {\n          return {\n            label: item.folderName,\n            value: item.folderTreeCode,\n            children: item.childrenCamera\n          };\n        });\n        this.saveLoading = false;\n      });\n    },\n    // 新增分组\n    addGroup() {\n      // 保存到新分组\n      if (this.tabIndex == 0) {\n        if (!this.groupName) {\n          this.$message.error('请输入分组名称');\n          return;\n        }\n        let params = {\n          \"onlineStatusList\": [],\n          \"cameraShareList\": [],\n          \"orgAreaList\": [],\n          \"pageNum\": 1,\n          \"searchType\": \"click\"\n        };\n        addCarouse({\n          folderName: this.groupName\n        }).then(response => {\n          if (response.code == 0) {\n            queryCarouseAndCameras(params).then(res => {\n              if (res.code == 0) {\n                res.data.forEach(item => {\n                  if (item.folderName == this.groupName) {\n                    this.folderTreeCode = item.folderTreeCode;\n                    let cameraList = [];\n                    if (this.choseObj.cameraCode) {\n                      cameraList.push(this.choseObj.cameraCode);\n                    } else {\n                      this.cameras.forEach(item => {\n                        if (item.cameraCode && item.chose) {\n                          cameraList.push(item.cameraCode);\n                        }\n                      });\n                    }\n                    batchAddCarouseMerge({\n                      folderTreeCode: this.folderTreeCode,\n                      cameraCodeList: cameraList\n                    }).then(res => {\n                      if (res.code == 0) {\n                        this.groupName = '';\n                        this.folderTreeCode = '';\n                        this.$message.success('添加成功');\n                        this.tabIndex = 0;\n                        this.cameras.forEach(item => {\n                          item.chose = false;\n                        });\n                        this.saveVisible = false;\n                      }\n                    });\n                  }\n                });\n              }\n            });\n          }\n        });\n      } else if (this.tabIndex == 1) {\n        // 保存到已有分组\n        let choseChildren = [];\n        this.carouseOptions.forEach(item => {\n          if (this.folderTreeCode == item.value) {\n            choseChildren = item.children;\n          }\n        });\n        let childrenArr = [];\n        choseChildren.forEach(item => {\n          childrenArr.push(item.cameraCode);\n        });\n        let cameraList = [];\n        if (this.choseObj.cameraCode) {\n          cameraList.push(this.choseObj.cameraCode);\n        } else {\n          this.cameras.forEach(item => {\n            if (item.chose) {\n              cameraList.push(item.cameraCode);\n            }\n          });\n        }\n        let cameraCodeList = [...new Set(childrenArr.concat(cameraList))];\n        batchAddCarouseMerge({\n          folderTreeCode: this.folderTreeCode,\n          cameraCodeList\n        }).then(res => {\n          if (res.code == 0) {\n            this.folderTreeCode = '';\n            this.tabIndex = 0;\n            this.$message.success('添加成功');\n            this.cameras.forEach(item => {\n              item.chose = false;\n            });\n            this.saveVisible = false;\n          }\n        });\n      }\n    },\n    // 删除选中设备\n    delCamera(index) {\n      this.$confirm(\"是否删除设备\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        center: true,\n        type: \"warning\",\n        customClass: \"global-message-del-box\"\n      }).then(() => {\n        this.$emit('delCamera', index);\n      });\n    },\n    // 添加一个设备到轮播\n    saveOne(obj) {\n      this.choseObj = obj;\n      this.save(true);\n    },\n    init() {\n      // 初始化playerArray 数组;并且默认播放前n路;\n      for (let i = 0; i < this.initNum; i++) {\n        let item = this.cameras[i] ? this.cameras[i] : {};\n        this.playerArray.push({\n          ...item,\n          cameraCode: !this.showLoop && this.cameras[i] && this.cameras[i]['cameraCode'] ? this.cameras[i]['cameraCode'] : '',\n          cameraName: !this.showLoop && this.cameras[i] && this.cameras[i]['cameraName'] ? this.cameras[i]['cameraName'] : '',\n          id: `${this.prefix}_${i}`,\n          type: true\n        });\n        // 默认选中的项;\n        if (!this.showLoop) {\n          if (this.cameras[i] && this.cameras[i]['cameraCode']) {\n            this.checkList.push(this.cameras[i]['cameraCode']);\n          }\n        }\n      }\n      ;\n    },\n    // 打开弹窗\n    open() {\n      if (this.showStatus) {\n        this.replacePlayer(0);\n      } else {\n        this.init();\n        // this.$set(this, 'showStatus', true);\n        this.showStatus = true;\n        this.$nextTick(() => {\n          this.$refs['canvasPlayerList'].play();\n        });\n      }\n    },\n    refresh() {\n      this.playerArray = [];\n      this.checkList = [];\n      this.init();\n      this.$nextTick(() => {\n        this.$refs['canvasPlayerList'].play();\n      });\n    },\n    // 关闭弹窗;\n    close() {\n      if (this.isFull) {\n        // this.$set(this, 'isFull', false);\n        this.isFull = false;\n        this.is_mini = false;\n        exitFullscreen();\n      }\n      this.$refs['canvasPlayerList'].stop();\n      // this.$set(this, 'checkList', []);\n      // this.$set(this, 'playerArray', []);\n      // this.$set(this, 'showStatus', false);\n      // this.$set(this, 'switchFlag', true);\n      // this.$set(this, 'loopFlag', false);\n      this.checkList = [];\n      this.playerArray = [];\n      this.showStatus = false;\n      this.switchFlag = true;\n      this.loopFlag = false;\n      if (this.loopTimer) {\n        clearInterval(this.loopTimer);\n        this.loopTimer = null;\n      }\n      // this.$set(this, 'loopTime', 60 * 1000);\n      // this.$set(this, 'notLoopArray', []);\n      this.loopTime = 60 * 1000;\n      this.notLoopArray = [];\n      this.$emit('close', true);\n    },\n    // 清空播放数据;\n    clearAll() {\n      if (this.isFull) {\n        // this.$set(this, 'isFull', false);\n        this.isFull = false;\n        this.is_mini = false;\n        exitFullscreen();\n      }\n      this.$refs['canvasPlayerList'].stop();\n      // this.$set(this, 'checkList', []);\n      // this.$set(this, 'playerArray', []);\n      // //this.$set(this, 'showStatus', false);\n      // this.$set(this, 'switchFlag', true);\n      // this.$set(this, 'loopFlag', false);\n      this.checkList = [];\n      this.playerArray = [];\n      this.switchFlag = true;\n      this.loopFlag = false;\n      if (this.loopTimer) {\n        clearInterval(this.loopTimer);\n        this.loopTimer = null;\n      }\n      // this.$set(this, 'loopTime', 60 * 1000);\n      // this.$set(this, 'notLoopArray', []);\n      this.loopTime = 60 * 1000;\n      this.notLoopArray = [];\n    },\n    // 关闭\n    closePlayer(cameraCode) {\n      if (this.checkList.includes(cameraCode)) {\n        // 如果在播放列表中,则是取消播放\n        let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\n        let cancelItem = this.playerArray[cancelIndex];\n        this.checkList.splice(cancelIndex, 1, '');\n        this.playerArray.splice(cancelIndex, 1, {\n          cameraCode: '',\n          cameraName: '',\n          id: cancelItem['id'],\n          type: true\n        });\n        this.$refs['canvasPlayerList'].replacePlayer(cancelIndex);\n      }\n    },\n    // 全屏切换\n    fullScreen() {\n      var t = window.devicePixelRatio;\n      let id = this.modalId;\n      let dom = document.querySelector(`#${id}`);\n      if (this.isFull) {\n        // this.$set(this, 'isFull', false);\n        this.isFull = false;\n        const {\n          left,\n          top\n        } = this.exitModelStyle;\n        document.querySelector('.video-dialog-container').style.cssText += `;left:${left}px;top:${top}px;`;\n        exitFullscreen();\n      } else {\n        openFullscreen(id);\n        // this.$set(this, 'isFull', true);\n        this.isFull = true;\n      }\n      // 切换屏幕显示状态;\n      this.$refs['canvasPlayerList'].resize();\n      // 当全屏和取消全屏的状态切换;\n      // this.$emit('fullChange',this.isFull,this.bodyHeight);\n    },\n    // 视频水印设置\n    openWaterMarkSet() {\n      this.markVisible = true;\n      getWaterMarkSet().then(res => {\n        this.markOptions = res.data;\n        this.markOptions.forEach(item => {\n          if (item.dictLabel == 'font_transparence') {\n            this.markValue1 = Number(item.dictValue);\n          } else if (item.dictLabel == 'font_size') {\n            this.markValue2 = Number(item.dictValue);\n          }\n        });\n        this.$nextTick(() => {\n          this.renderWaterMarkText();\n        });\n      });\n    },\n    // 缩小和普通弹窗间切换\n    changeMiniOrMedium() {\n      const is_mini = !this.is_mini;\n      if (this.isFull) {\n        this.fullScreen();\n      }\n      this.$nextTick(() => {\n        this.is_mini = is_mini;\n      });\n    },\n    isOpen() {\n      return this.showStatus;\n    },\n    // 最小化(暂定)\n    small() {},\n    // 1路和4路切换;\n    handleChangeWin(num) {\n      if (!this.loopFlag) {\n        // this.$set(this, 'currentWin', num);\n        this.currentWin = num;\n        this.$refs['canvasPlayerList'].handleChangeWin(num);\n      }\n    },\n    // 模态窗的状态\n    getModal() {\n      return {\n        showStatus: this.showStatus,\n        //当前的弹窗是否已经打开\n        currentWin: this.currentWin,\n        //当前是1路播放还是4路播放;\n        currentActiveIndex: this.$refs['canvasPlayerList'].getCurrentActiveIndex() //当前选中的是第几个窗口\n      };\n    },\n    // 替换\n    replacePlayer(index) {\n      // this.$set(this.playerArray, index, {\n      //   cameraCode: this.cameras[index]['cameraCode'],\n      //   cameraName: this.cameras[index]['cameraName'],\n      //   id: this.playerArray[index]['id'],\n      //   type: true\n      // })\n      this.playerArray.splice(index, 1, {\n        cameraCode: this.cameras[index]['cameraCode'],\n        cameraName: this.cameras[index]['cameraName'],\n        id: this.playerArray[index]['id'],\n        type: true\n      });\n      this.$nextTick(() => {\n        this.$refs['canvasPlayerList'].replacePlayer(index);\n      });\n    },\n    // 轮播初始化;\n    runInit() {\n      // this.$set(this, 'notLoopArray', JSON.parse(JSON.stringify(this.cameras)));\n      this.notLoopArray = JSON.parse(JSON.stringify(this.cameras));\n    },\n    // 启动轮播或者是停止轮播,目前是全列表轮播;\n    run() {\n      let _that = this;\n      // 启动轮播\n      if (this.loopTimer) {\n        clearInterval(_that.loopTimer);\n        this.loopTimer = null;\n      }\n      if (this.loopFlag) {\n        // 开始播放\n        // 轮播初始化;\n        this.runInit();\n        this.runLoop(true);\n        this.loopTimer = setInterval(() => {\n          this.runLoop(false);\n        }, this.loopTime);\n      } else {// 暂停播放\n      }\n    },\n    runLoop(init) {\n      // 需要进行轮播的集合;\n      let loopArray = this.notLoopArray.splice(0, this.currentWin);\n      for (let i = 0; i < this.playerArray.length; i++) {\n        // this.$set(this.playerArray, i, {\n        //   cameraCode: loopArray[i] && loopArray[i]['cameraCode'] ? loopArray[i]['cameraCode'] : '',\n        //   cameraName: loopArray[i] && loopArray[i]['cameraName'] ? loopArray[i]['cameraName'] : '',\n        //   id: this.playerArray[i]['id'],\n        //   type: true\n        // })\n        this.playerArray.splice(i, 1, {\n          cameraCode: loopArray[i] && loopArray[i]['cameraCode'] ? loopArray[i]['cameraCode'] : '',\n          cameraName: loopArray[i] && loopArray[i]['cameraName'] ? loopArray[i]['cameraName'] : '',\n          id: this.playerArray[i]['id'],\n          type: true\n        });\n      }\n      // this.$set(this, 'checkList', loopArray.map(item => item['cameraCode']));\n      this.checkList = loopArray.map(item => item['cameraCode']);\n      // 开始播放;\n      if (init) {\n        this.$refs['canvasPlayerList'].play();\n      } else {\n        this.playerArray.forEach((item, index) => {\n          this.$refs['canvasPlayerList'].replacePlayer(index);\n        });\n      }\n      // 将正在播放的追加到未播放的尾部;\n      // this.$set(this, 'notLoopArray', [...this.notLoopArray, ...loopArray]);\n      this.notLoopArray = [...this.notLoopArray, ...loopArray];\n    },\n    // 是否开始轮播控制\n    handleLoopClick() {\n      // this.$set(this, 'loopFlag', !this.loopFlag);\n      this.loopFlag = !this.loopFlag;\n      this.$nextTick(() => {\n        this.run();\n      });\n    },\n    // 是否收起侧边点位列表\n    handleSwitchClick() {\n      // this.$set(this, 'switchFlag', !this.switchFlag);\n      this.switchFlag = !this.switchFlag;\n    },\n    // 轮播组切换事件,调用时监听此事件 更新待播放列表;\n    handleLoopGroupChange(val) {\n      // 切换时应该将状态都清空,\n      // this.$set(this, 'notLoopArray', []);\n      this.notLoopArray = [];\n      this.$refs['canvasPlayerList'].stop();\n      this.$emit('loopGroupChange', val);\n    },\n    //轮播时间切换事件,调用时监听此事件;\n    handleLoopTimeChange() {},\n    handleCloseItem({\n      cameraCode\n    }) {\n      let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\n      if (cancelIndex != -1) {\n        let cancelItem = this.playerArray[cancelIndex];\n        this.checkList.splice(cancelIndex, 1, '');\n        this.playerArray.splice(cancelIndex, 1, {\n          cameraCode: '',\n          cameraName: '',\n          id: cancelItem['id'],\n          type: true\n        });\n      }\n    },\n    // 单个点位的点击事件\n    handleCheckItemChange({\n      cameraCode,\n      cameraName\n    }) {\n      if (!this.loopFlag) {\n        // 1.判断当前点位是否在播放列表中\n        if (this.checkList.includes(cameraCode)) {\n          // 如果在播放列表中,则是取消播放\n          let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\n          let cancelItem = this.playerArray[cancelIndex];\n          this.checkList.splice(cancelIndex, 1, '');\n          this.playerArray.splice(cancelIndex, 1, {\n            cameraCode: '',\n            cameraName: '',\n            id: cancelItem['id'],\n            type: true\n          });\n          this.$refs['canvasPlayerList'].replacePlayer(cancelIndex);\n        } else {\n          //替换播放\n          let replaceIndex = -1;\n          let isAllPlay = this.playerArray.every(item => item['cameraCode']);\n          if (isAllPlay) {\n            let failIndex = this.playerArray.findIndex(item => !item['type']);\n            if (failIndex != -1) {\n              replaceIndex = failIndex;\n            } else {\n              replaceIndex = this.$refs['canvasPlayerList'].getCurrentActiveIndex();\n            }\n          } else {\n            replaceIndex = this.playerArray.findIndex(item => item['cameraCode'] === '');\n          }\n          if (replaceIndex != -1) {\n            let replaceItem = this.playerArray[replaceIndex];\n            // 替换操作\n            this.checkList.splice(replaceIndex, 1, cameraCode);\n            this.playerArray.splice(replaceIndex, 1, {\n              cameraCode: cameraCode,\n              cameraName: cameraName,\n              id: replaceItem['id'],\n              type: true\n            });\n            this.$refs['canvasPlayerList'].replacePlayer(replaceIndex);\n          }\n        }\n      }\n    },\n    // 当前的播放状态;\n    handlePlayType(cameraCode, type) {\n      let index = this.playerArray.findIndex(item => item['cameraCode'] == cameraCode);\n      if (index != -1) {\n        // this.$set(this.playerArray[index], 'type', type);\n        this.playerArray[index]['type'] = type;\n      }\n    },\n    exitFullscreen(e) {\n      if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {\n        // this.$set(this, 'isFull', false);\n        this.isFull = false;\n        exitFullscreen();\n        this.$refs['canvasPlayerList'].resize();\n        this.$emit('fullChange', this.isFull, this.bodyHeight);\n      }\n    },\n    resizeChange() {\n      if (this.isFull) {\n        this.current_height = document.body.clientHeight;\n        this.current_width = document.body.clientWidth;\n      }\n    },\n    // 自定义按钮的点击事件\n    handleBtnClick(player, index) {\n      this.$emit('handleBtnClick', player, index);\n    }\n  },\n  mounted() {\n    let _that = this;\n    this.current_height = window.screen.height;\n    this.current_width = window.screen.width;\n    if (document.addEventListener) {\n      document.addEventListener('webkitfullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('mozfullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('fullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('MSFullscreenChange', _that.exitFullscreen, false);\n      window.addEventListener('resize', _that.resizeChange, false);\n    }\n  },\n  beforeDestroy() {\n    let _that = this;\n    if (document.removeEventListener) {\n      document.removeEventListener('webkitfullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('mozfullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('fullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('MSFullscreenChange', _that.exitFullscreen);\n      window.removeEventListener('resize', _that.resizeChange);\n    }\n    if (this.loopTimer) {\n      clearInterval(this.loopTimer);\n    }\n  }\n};", "map": {"version": 3, "names": ["openFullscreen", "exitFullscreen", "getWindowRatio", "canvasPlayerList", "moment", "getWaterMarkSet", "setWaterMark", "name", "components", "props", "headerFlag", "type", "Boolean", "default", "nameFieldDisplay", "String", "title", "player<PERSON>ist<PERSON><PERSON><PERSON>", "Object", "showFooter", "showSwitch", "playerModalStyle", "initNum", "Number", "prefix", "cameras", "Array", "showLoop", "loopGroup", "loopDefaultValue", "loopTimeList", "label", "value", "bottomList", "current", "canDelete", "computed", "modalId", "modalStyle", "ratio", "width", "window", "screen", "height", "scale", "customModalStyle", "bodyHeight", "current_height", "total", "allHeight", "isFull", "parseInt", "bodyWidth", "current_width", "canvasPlayListStyle", "switchFlag", "directives", "bind", "el", "binding", "vnode", "oldVnode", "min<PERSON><PERSON><PERSON>", "minHeight", "dragHeader", "dragDomSelector", "dragDom", "dialogHeaderEl", "querySelector", "style", "cssText", "sty", "document", "currentStyle", "dom", "attr", "getComputedStyle", "onmousedown", "e", "context", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "screenWidth", "body", "clientWidth", "screenHeight", "documentElement", "clientHeight", "dragD<PERSON><PERSON><PERSON>th", "offsetWidth", "dragDomheight", "offsetHeight", "styL", "styT", "includes", "replace", "<PERSON><PERSON><PERSON><PERSON>", "left", "top", "exitModelStyle", "onmouseup", "data", "hideAllFunc", "<PERSON><PERSON><PERSON><PERSON>", "markOptions", "loading", "markValue1", "markValue2", "checkList", "showStatus", "currentWin", "is_mini", "dialog_body_padding", "loopFlag", "loopTime", "<PERSON><PERSON><PERSON><PERSON>", "loopTimer", "notLoopArray", "saveVisible", "tabIndex", "groupName", "folderTreeCode", "carouseOptions", "saveLoading", "markVisible", "watch", "handler", "newVal", "for<PERSON>ach", "item", "chose", "methods", "hideFunction", "change", "test", "toFixed", "renderWaterMarkText", "saveWatermarkOptions", "arr", "promiseArr", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "push", "Promise", "all", "then", "res", "code", "$message", "message", "$parent", "$refs", "video1", "resetWatermark", "video4", "canvasPlay", "waterCanvas", "waterMark", "ctx", "getContext", "clearRect", "time", "format", "points", "x", "y", "i", "length", "_renderCanvasText", "text", "fix", "_ctx", "metrics", "measureText", "str<PERSON>idth", "actualHeight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "save", "font", "fillStyle", "yHeight", "rotate", "Math", "PI", "fillText", "restore", "isone", "check_ids", "filter", "cameraCode", "error", "params", "queryCarouseAndCameras", "map", "folderName", "children", "childrenCamera", "addGroup", "addCarouse", "response", "cameraList", "batchAddCarouseMerge", "cameraCodeList", "success", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenArr", "Set", "concat", "delCamera", "index", "$confirm", "confirmButtonText", "cancelButtonText", "center", "customClass", "$emit", "saveOne", "obj", "init", "cameraName", "id", "open", "replacePlayer", "$nextTick", "play", "refresh", "close", "stop", "clearInterval", "clearAll", "closePlayer", "cancelIndex", "findIndex", "cancelItem", "splice", "fullScreen", "t", "devicePixelRatio", "resize", "openWaterMarkSet", "changeMiniOrMedium", "isOpen", "small", "handleChangeWin", "num", "getModal", "currentActiveIndex", "getCurrentActiveIndex", "runInit", "JSON", "parse", "stringify", "run", "_that", "run<PERSON><PERSON>", "setInterval", "loopArray", "handleLoopClick", "handleSwitchClick", "handleLoopGroupChange", "val", "handleLoopTimeChange", "handleCloseItem", "handleCheckItemChange", "replaceIndex", "isAllPlay", "every", "failIndex", "replaceItem", "handlePlayType", "webkitIsFullScreen", "mozFullScreen", "msFullscreenElement", "resizeChange", "handleBtnClick", "player", "mounted", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/components/rslPlayer/components/new-dialog.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"headerFlag\" :id=\"modalId\" class=\"video-dialog-container\" :class=\"is_mini ? 'mini-video-dialog' : ''\" v-show=\"showStatus\"\r\n    :style=\"{ ...modalStyle }\" v-dialogDrag=\"{\r\n      dragDom: '.video-dialog-container',\r\n      dragHeader: '.el-dialog__header',\r\n    }\">\r\n    <div class=\"video-dialog\">\r\n      <!--弹窗头部-->\r\n      <div class=\"el-dialog__header\" @click=\"close\">\r\n        <!--弹窗标题-->\r\n        <div class=\"el-dialog__title\">\r\n          {{ title }}\r\n        </div>\r\n        <!--弹窗操作组-->\r\n        <div class=\"el-dialog__btns\">\r\n          <!-- <i class=\"minus-view el-icon-setting\" @click=\"openWaterMarkSet\" v-if=\"!hideAllFunc\" /> -->\r\n          <!-- <el-icon class=\"minus-view\" @click=\"openWaterMarkSet\" v-if=\"!hideAllFunc\">\r\n            <Setting />\r\n          </el-icon> -->\r\n          <!-- <i\r\n              class=\"minus-view el-icon-minus\"\r\n              @click=\"changeMiniOrMedium\"\r\n              v-if=\"!hideAllFunc\"\r\n            /> -->\r\n          <span :class=\"['fullOrSmall', isFull ? 'isFull' : '']\" @click.stop=\"fullScreen\" v-if=\"!hideAllFunc\"> </span>\r\n          <!--关闭-->\r\n          <!-- <i class=\"el-icon-close\" @click=\"close\"></i> -->\r\n          <div class=\"close-icon\" @click=\"close\">\r\n            <!-- <el-icon>\r\n              <Close />\r\n            </el-icon> -->\r\n            <i class=\"el-icon-close\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"el-dialog__body\" :style=\"{ height: (bodyHeight - 22) + 'px' }\">\r\n        <div class=\"el-dialog__body-container\">\r\n          <!--左侧插槽-->\r\n          <div class=\"canvas-player-slot\" v-show=\"showSwitch && switchFlag\">\r\n            <slot name=\"canvas-player-slot\">\r\n              <div class=\"canvas-player-cameras\">\r\n                <div class=\"canavs-player-cameras-title\">\r\n                  设备名称\r\n                  <!-- <el-button type=\"primary\" size=\"mini\" @click=\"save()\"\r\n                      >添加轮播</el-button\r\n                    > -->\r\n                </div>\r\n                <div class=\"canvas-player-camera-container\">\r\n                  <el-scrollbar style=\"height: 100%\">\r\n                    <div :class=\"[\r\n                      'player-item',\r\n                      checkList.includes(camera.cameraCode)\r\n                        ? 'player-item-active'\r\n                        : '',\r\n                    ]\" v-for=\"(camera, index) in cameras\" :key=\"index\">\r\n                      <div class=\"select\" style=\"margin-left: 10px\">\r\n                        <!-- <el-checkbox\r\n                            v-model=\"camera.chose\"\r\n                            @change=\"$forceUpdate()\"\r\n                          ></el-checkbox> -->\r\n                      </div>\r\n                      <div class=\"player-item-icon\" style=\"width: 20px\"></div>\r\n                      <el-tooltip class=\"item\" :open-delay=\"1000\" effect=\"dark\"\r\n                        :content=\"nameFieldDisplay === '0' ? camera.cameraName || camera.aliasName : camera.aliasName || camera.cameraName\">\r\n                        <div class=\"player-item-title\" @click=\"handleCheckItemChange(camera)\">\r\n                          {{ nameFieldDisplay === '0' ? camera.cameraName || camera.aliasName : camera.aliasName ||\r\n                            camera.cameraName }}\r\n                        </div>\r\n                      </el-tooltip>\r\n                      <!-- <div class=\"deleteIcon\" v-if=\"canDelete\" @click.stop=\"delCamera(index)\"><i\r\n                                                      class=\"el-icon-delete\"></i> </div> -->\r\n                      <!-- <div class=\"deleteIcon\" @click.stop=\"saveOne(camera)\">\r\n                          <i class=\"el-icon-circle-plus-outline\"></i>\r\n                        </div> -->\r\n                    </div>\r\n                  </el-scrollbar>\r\n                </div>\r\n              </div>\r\n            </slot>\r\n          </div>\r\n          <!--左侧列表的显示和隐藏-->\r\n          <!-- <div class=\"canvas-player-padding\" :style=\"{ width: switchFlag ? '8px' : '30px' }\" v-show=\"showSwitch\">\r\n            <div class=\"canvas-player-cameras-switch\" :style=\"{ left: switchFlag ? '-15px' : '0px' }\">\r\n              <img class=\"img\" @click=\"handleSwitchClick\" :src=\"switchFlag\r\n                ? openIcon\r\n                : closeIcon\r\n                \" />\r\n            </div>\r\n          </div> -->\r\n          <!--多路视频播放组件-->\r\n          <div class=\"canvas-player-list-container\">\r\n            <canvas-player-list :current=\"current\" :player-list-style=\"playerListStyle\" :player-array=\"playerArray\"\r\n              :init-num=\"initNum\" :prefix=\"prefix\" :height=\"canvasPlayListStyle.height + 'px'\"\r\n              :width=\"canvasPlayListStyle.width + 'px'\" ref=\"canvasPlayerList\" :bottom-list=\"bottomList\"\r\n              :name-field-display=\"nameFieldDisplay\" @playType=\"handlePlayType\" @handleBtnClick=\"handleBtnClick\"\r\n              @closeItem=\"handleCloseItem\" :isFull=\"isFull\" :hideAllFunc=\"hideAllFunc\">\r\n            </canvas-player-list>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"el-dialog__footer\" v-if=\"showFooter\">\r\n        <!--slot-->\r\n        <div class=\"el-dialog__footer-slot\">\r\n          <!--底部插槽-->\r\n          <slot name=\"dialog_footer_slot\">\r\n            <div class=\"loop-set-container\" v-if=\"showLoop\">\r\n              <span class=\"loop-set-title loop-set-item\">视频轮巡:</span>\r\n              <div class=\"loop-set-item\" v-if=\"loopGroup && loopGroup.length > 0\">\r\n                <el-select :disabled=\"loopFlag\" :value=\"loopDefaultValue\" @change=\"handleLoopGroupChange\" filterable\r\n                  :size=\"'mini'\" style=\"width: 100px; height: 32px\">\r\n                  <el-option v-for=\"item in loopGroup\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n              <div class=\"loop-set-item\" v-if=\"loopTimeList && loopTimeList.length > 0\">\r\n                <el-select :disabled=\"loopFlag\" v-model=\"loopTime\" @change=\"handleLoopTimeChange\" filterable\r\n                  :size=\"'mini'\" style=\"width: 100px; height: 32px\">\r\n                  <el-option v-for=\"item in loopTimeList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n              <div class=\"loop-set-item\">\r\n                <div class=\"loop-btn\">\r\n                  <!-- <img\r\n                      @click=\"handleLoopClick\"\r\n                      :src=\"\r\n                        loopFlag\r\n                          ? require('@/assets/image/video/player/loop-stop.png')\r\n                          : require('@/assets/image/video/player/loop-start.png')\r\n                      \"\r\n                      class=\"img\"\r\n                    /> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </slot>\r\n        </div>\r\n        <!--分屏切换-->\r\n        <div class=\"el-dialog__footer-change-container\">\r\n          <div :class=\"[\r\n            'change-item',\r\n            currentWin === 1 ? 'change-item-active' : '',\r\n          ]\" @click=\"handleChangeWin(1)\">\r\n            <i class=\"el-icon-one\"></i>\r\n          </div>\r\n          <div :class=\"[\r\n            'change-item',\r\n            currentWin === 4 ? 'change-item-active' : '',\r\n          ]\" @click=\"handleChangeWin(4)\">\r\n            <!-- <i class=\"el-icon-menu\"></i> -->\r\n            <el-icon>\r\n              <Menu />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div v-show=\"is_mini\" class=\"el-dialog__header play-btns\">\r\n      <div class=\"play-title\">{{ title }}</div>\r\n      <div class=\"btn\" @click=\"is_mini = false\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { openFullscreen, exitFullscreen, getWindowRatio } from '@/utils/fullScreen'\r\nimport canvasPlayerList from './canvasPlayerList.vue'\r\nimport moment from 'moment';\r\n// import { mapGetters } from \"vuex\";\r\nimport {\r\n  getWaterMarkSet,\r\n  setWaterMark\r\n} from \"@/api/video\";\r\nexport default {\r\n  name: 'v-dialog',\r\n  components: {\r\n    canvasPlayerList\r\n  },\r\n  props: {\r\n    headerFlag:{\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    nameFieldDisplay: {\r\n      type: String,\r\n      default: '0'\r\n    },\r\n    /*\r\n        弹窗标题;\r\n    */\r\n    title: {\r\n      type: String,\r\n      default() {\r\n        return ''\r\n      }\r\n    },\r\n    /*\r\n        整个播放区域的样式设置;\r\n    */\r\n    playerListStyle: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    /*\r\n        是否显示弹窗底部\r\n    */\r\n    showFooter: {\r\n      type: Boolean,\r\n      default() {\r\n        return true;\r\n      }\r\n    },\r\n    showSwitch: {\r\n      type: Boolean,\r\n      default() {\r\n        return true\r\n      }\r\n    },\r\n    /*\r\n        播放窗口的样式\r\n    */\r\n    playerModalStyle: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    initNum: {\r\n      type: Number,\r\n      default() {\r\n        return 4\r\n      }\r\n    },\r\n    prefix: {\r\n      type: String,\r\n      default() {\r\n        return 'vDialogWay'\r\n      }\r\n    },\r\n    // 待播放列表;\r\n    cameras: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      }\r\n    },\r\n    // 是否需要轮播操作组;\r\n    showLoop: {\r\n      type: Boolean,\r\n      default() {\r\n        return false;\r\n      },\r\n    },\r\n    // 轮播分组列表\r\n    loopGroup: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      }\r\n    },\r\n    loopDefaultValue: {\r\n      type: String,\r\n    },\r\n    // 轮巡时间列表\r\n    loopTimeList: {\r\n      type: Array,\r\n      default() {\r\n        return [\r\n          {\r\n            label: \"1分钟\",\r\n            value: 60 * 1000\r\n          },\r\n          {\r\n            label: \"5分钟\",\r\n            value: 5 * 60 * 1000\r\n          },\r\n          {\r\n            label: \"10分钟\",\r\n            value: 10 * 60 * 1000\r\n          },\r\n        ]\r\n      }\r\n    },\r\n    bottomList: {\r\n      type: Array,\r\n      default() {\r\n        return []\r\n      }\r\n    },\r\n    // 标记当前调用组件的状态;\r\n    current: {\r\n      type: String,\r\n      default() {\r\n        return '';\r\n      }\r\n    },\r\n    // 是否能删除\r\n    canDelete: {\r\n      type: Boolean,\r\n      default() {\r\n        return false\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // ...mapGetters(['userinfo']),\r\n    // 弹出窗的id 属性;\r\n    modalId() {\r\n      return `${this.prefix}_video-dialog-container`;\r\n    },\r\n    /*\r\n        整个弹窗的高度和宽度\r\n    */\r\n    modalStyle() {\r\n      let ratio = getWindowRatio() / 100;\r\n      let width = window.screen.width / ratio;\r\n      let height = window.screen.height / ratio;\r\n      let scale = ratio === 1 ? '1,1' : (width + ',' + height)\r\n      return {\r\n        ...this.customModalStyle,\r\n        ...this.playerModalStyle,\r\n      }\r\n    },\r\n    /*\r\n        el-dialog__body的高度(去除了头部和底部);\r\n        在全屏的时候不能考虑缩放比例,直接使用window.screen.width window.screen.height 设置;\r\n    */\r\n    bodyHeight() {\r\n      // let height = window.screen.height;\r\n      let height = this.current_height\r\n      let total = 50 + (this.showFooter ? 50 : 0);\r\n      let allHeight = this.isFull ? height : parseInt(this.modalStyle.height);\r\n      return allHeight - total;\r\n    },\r\n    /*\r\n    el-dialog__body的宽度,全屏时窗口的宽度;\r\n    */\r\n    bodyWidth() {\r\n      // let width = window.screen.width;\r\n      let width = this.current_width\r\n      return this.isFull ? width : parseInt(this.modalStyle.width);\r\n    },\r\n    /*\r\n        播放区域的宽度和高度\r\n    */\r\n    canvasPlayListStyle() {\r\n      return {\r\n        width: this.bodyWidth - 30 - (this.showSwitch ? ((this.switchFlag ? 198 : 30)) : 0),\r\n        height: this.bodyHeight - 30\r\n      }\r\n    }\r\n  },\r\n  directives: {\r\n    'dialogDrag': {\r\n      // 属性名称dialogDrags，前面加v- 使用\r\n      bind(el, binding, vnode, oldVnode) {\r\n        const { value } = binding;\r\n        let minWidth = 400;\r\n        let minHeight = 300;\r\n        let dragHeader = value && value.dragHeader ? value.dragHeader : '.el-dialog__header';\r\n        let dragDomSelector = value && value.dragDom ? value.dragDom : '.el-dialog';\r\n        const dialogHeaderEl = el.querySelector(dragHeader);\r\n        const dragDom = el;\r\n        dialogHeaderEl.style.cssText += \";cursor:move;\";\r\n        const sty = (function () {\r\n          if (window.document.currentStyle) {\r\n            return (dom, attr) => dom.currentStyle[attr];\r\n          } else {\r\n            return (dom, attr) => getComputedStyle(dom, false)[attr];\r\n          }\r\n        })();\r\n        dialogHeaderEl.onmousedown = (e) => {\r\n          if (vnode.context.isFull) {\r\n            return false // 如果是全屏时不处理\r\n          }\r\n          // 鼠标按下，计算当前元素距离可视区的距离\r\n          const disX = e.clientX - dialogHeaderEl.offsetLeft;\r\n          const disY = e.clientY - dialogHeaderEl.offsetTop;\r\n\r\n          const screenWidth = document.body.clientWidth; // body当前宽度\r\n          const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取)\r\n          const dragDomWidth = dragDom.offsetWidth; // 对话框宽度\r\n          const dragDomheight = dragDom.offsetHeight; // 对话框高度\r\n          // 获取到的值带px 正则匹配替换\r\n          let styL = sty(dragDom, \"left\");\r\n          let styT = sty(dragDom, \"top\");\r\n          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n          if (styL.includes(\"%\")) {\r\n            styL = +document.body.clientWidth * (+styL.replace(/\\%/g, \"\") / 100);\r\n            styT = +document.body.clientHeight * (+styT.replace(/\\%/g, \"\") / 100);\r\n          } else {\r\n            styL = +styL.replace(/\\px/g, \"\");\r\n            styT = +styT.replace(/\\px/g, \"\");\r\n          }\r\n          document.onmousemove = function (e) {\r\n            // 通过事件委托，计算移动的距离\r\n            let left = e.clientX - disX + styL;\r\n            let top = e.clientY - disY + styT;\r\n\r\n            // 边界处理\r\n            if (left < 335) {\r\n              left = 335;\r\n            }\r\n\r\n            if (left > screenWidth + 335 - dragDomWidth) {\r\n              left = screenWidth + 335 - dragDomWidth;\r\n            }\r\n\r\n            if (top < 0) {\r\n              top = 0;\r\n            }\r\n            if (top > screenHeight - dragDomheight) {\r\n              top = screenHeight - dragDomheight;\r\n            }\r\n\r\n            // 移动当前元素\r\n            dragDom.style.cssText += `;left:${left}px;top:${top}px;`;\r\n            vnode.context.exitModelStyle = {\r\n              top: top,\r\n              left: left\r\n            }\r\n          };\r\n\r\n          document.onmouseup = function (e) {\r\n            document.onmousemove = null;\r\n            document.onmouseup = null;\r\n          };\r\n        };\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      hideAllFunc: false,\r\n      choseObj: {},\r\n      markOptions: [],\r\n      loading: false,\r\n      markValue1: 100,\r\n      markValue2: 14,\r\n      checkList: [],\r\n      showStatus: false,// 当前的弹窗打开关闭状态\r\n      currentWin: 4,// 当前是几分屏,\r\n      isFull: false,// 当前弹窗是否是全屏的状态;\r\n      is_mini: false, // 当前弹窗是否是缩小状态\r\n      customModalStyle: {\r\n        width: '860px',// 整个弹窗的宽度\r\n        height: '750px',//整个弹窗的高度\r\n        top: '15vh',// 弹窗距离顶部的距离\r\n        dialog_body_padding: '15px'// el-dialog__body 的padding\r\n      },\r\n      exitModelStyle: {\r\n        left: '50%',\r\n        top: '50%'\r\n      },\r\n      // 轮播开关\r\n      loopFlag: false,\r\n      // 播放列表是否展开\r\n      switchFlag: true,\r\n      // 开始轮播时间;\r\n      loopTime: 60 * 1000,//this.loopTimeList && this.loopTimeList[0] && this.loopTimeList[0]['value']?this.loopTimeList[0]['value']:0,\r\n      playerArray: [],\r\n      loopTimer: null,\r\n      notLoopArray: [],// 还没有开始轮播的点位;\r\n      saveVisible: false,\r\n      tabIndex: 0,\r\n      groupName: '',\r\n      folderTreeCode: '',\r\n      carouseOptions: [],\r\n      saveLoading: false,\r\n      markVisible: false,\r\n      current_height: 0, // 当前高度\r\n      current_width: 0 // 当前宽度\r\n    }\r\n  },\r\n  watch: {\r\n    saveVisible: {\r\n      handler(newVal) {\r\n        if (newVal == false) {\r\n          this.choseObj = {}\r\n        }\r\n      },\r\n    },\r\n    showStatus: {\r\n      handler(newVal) {\r\n        if (newVal == false) {\r\n          this.cameras.forEach(item => {\r\n            item.chose = false\r\n          })\r\n        }\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    hideFunction() {\r\n      this.hideAllFunc = true\r\n    },\r\n    change() {\r\n      if (!(/^[1-9]\\d*$/.test(this.markValue2))) {\r\n        this.markValue2 = Number(this.markValue2.toFixed(0))\r\n      }\r\n      this.renderWaterMarkText()\r\n    },\r\n    saveWatermarkOptions() {\r\n      this.loading = true\r\n      let arr = this.markOptions\r\n      let promiseArr = []\r\n      arr.forEach(item => {\r\n        if (item.dictLabel == 'font_transparence') {\r\n          item.dictValue = this.markValue1\r\n        } else if (item.dictLabel == 'font_size') {\r\n          item.dictValue = this.markValue2\r\n        }\r\n        promiseArr.push(setWaterMark(item))\r\n      })\r\n      Promise.all(promiseArr).then(res => {\r\n        this.loading = false\r\n        if (res[0]?.code == 0 && res[1]?.code == 0) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: `保存成功`\r\n          })\r\n          this.markVisible = false\r\n          // this.$refs['canvasPlayerList'].resetWatermark()\r\n          if (this.$parent.$refs.video1) {\r\n            this.$parent.$refs.video1.$refs['canvasPlayerList'].resetWatermark()\r\n          }\r\n          if (this.$parent.$refs.video4) {\r\n            this.$parent.$refs.video4.$refs['canvasPlayerList'].resetWatermark()\r\n          }\r\n          if (this.$parent.$refs.canvasPlay) {\r\n            this.$parent.$refs.canvasPlay.resetWatermark()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 水印参数初始化\r\n    renderWaterMarkText() {\r\n      // let waterCanvas = document.getElementById('waterMark');\r\n      let waterCanvas = this.$refs.waterMark\r\n      let ctx = waterCanvas.getContext('2d');\r\n      // 清除画布\r\n      let width = waterCanvas.width;\r\n      let height = waterCanvas.height;\r\n      if (ctx) {\r\n        ctx.clearRect(0, 0, width, height);\r\n      }\r\n      // let name = this.userinfo.sysUser.nickName\r\n      const name = \"南星渎商业三期\";\r\n      let time = moment().format(\"YYYY-MM-DD HH:mm:ss\");\r\n      let points = [\r\n        {\r\n          x: width / 4,\r\n          y: height / 4\r\n        },\r\n        {\r\n          x: 3 * width / 4,\r\n          y: height / 4\r\n        },\r\n        {\r\n          x: width / 4,\r\n          y: 3 * height / 4\r\n        },\r\n        {\r\n          x: 3 * width / 4,\r\n          y: 3 * height / 4\r\n        },\r\n        // {// 中心点\r\n        //     x: width / 2,\r\n        //     y: height / 2\r\n        // }\r\n      ];\r\n      for (let i = 0; i < points.length; i++) {\r\n        let { x, y } = points[i];\r\n        this._renderCanvasText(waterCanvas, name, x, y);\r\n        this._renderCanvasText(waterCanvas, time, x, y);\r\n      }\r\n    },\r\n    // 绘制水印\r\n    _renderCanvasText: function (waterCanvas, text, x, y, fix) {\r\n      let _ctx = waterCanvas.getContext('2d');\r\n      if (_ctx) {\r\n        let metrics = _ctx.measureText(text);\r\n        let strWidth = metrics.width;\r\n        let actualHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;\r\n        _ctx.save();\r\n        _ctx.font = `${this.markValue2}px 微软雅黑`;\r\n        _ctx.fillStyle = `rgba(255,255,255,${(this.markValue1 / 100).toFixed(2)})`;\r\n        let yHeight = fix ? (y + strWidth / 2 - actualHeight) : (y + strWidth / 2);\r\n        _ctx.rotate((-15 * Math.PI / 180));\r\n        _ctx.fillText(text, x - strWidth / 2 - strWidth / 2, yHeight);\r\n        _ctx.restore();\r\n      }\r\n    },\r\n    // 保存\r\n    save(isone) {\r\n      const check_ids = this.cameras.filter(item => {\r\n        return item.cameraCode && item.chose\r\n      })\r\n      if (!isone && check_ids.length === 0) {\r\n        this.$message.error('请勾选点位')\r\n        return false\r\n      }\r\n      this.saveVisible = true\r\n      this.saveLoading = true\r\n      let params = { \"onlineStatusList\": [], \"cameraShareList\": [], \"orgAreaList\": [], \"pageNum\": 1, \"searchType\": \"click\" }\r\n      queryCarouseAndCameras(params).then(res => {\r\n        this.carouseOptions = res.data.map(item => {\r\n          return {\r\n            label: item.folderName,\r\n            value: item.folderTreeCode,\r\n            children: item.childrenCamera\r\n          }\r\n        })\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    // 新增分组\r\n    addGroup() {\r\n      // 保存到新分组\r\n      if (this.tabIndex == 0) {\r\n        if (!this.groupName) {\r\n          this.$message.error('请输入分组名称')\r\n          return\r\n        }\r\n        let params = { \"onlineStatusList\": [], \"cameraShareList\": [], \"orgAreaList\": [], \"pageNum\": 1, \"searchType\": \"click\" }\r\n        addCarouse({ folderName: this.groupName }).then((response) => {\r\n          if (response.code == 0) {\r\n            queryCarouseAndCameras(params).then(res => {\r\n              if (res.code == 0) {\r\n                res.data.forEach(item => {\r\n                  if (item.folderName == this.groupName) {\r\n                    this.folderTreeCode = item.folderTreeCode\r\n                    let cameraList = []\r\n                    if (this.choseObj.cameraCode) {\r\n                      cameraList.push(this.choseObj.cameraCode)\r\n                    } else {\r\n                      this.cameras.forEach(item => {\r\n                        if (item.cameraCode && item.chose) {\r\n                          cameraList.push(item.cameraCode)\r\n                        }\r\n                      })\r\n                    }\r\n                    batchAddCarouseMerge({ folderTreeCode: this.folderTreeCode, cameraCodeList: cameraList }).then(res => {\r\n                      if (res.code == 0) {\r\n                        this.groupName = ''\r\n                        this.folderTreeCode = ''\r\n                        this.$message.success('添加成功')\r\n                        this.tabIndex = 0\r\n                        this.cameras.forEach(item => {\r\n                          item.chose = false\r\n                        })\r\n                        this.saveVisible = false\r\n                      }\r\n                    })\r\n                  }\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n      } else if (this.tabIndex == 1) {\r\n        // 保存到已有分组\r\n        let choseChildren = []\r\n        this.carouseOptions.forEach(item => {\r\n          if (this.folderTreeCode == item.value) {\r\n            choseChildren = item.children\r\n          }\r\n        })\r\n        let childrenArr = []\r\n        choseChildren.forEach(item => {\r\n          childrenArr.push(item.cameraCode)\r\n        })\r\n        let cameraList = []\r\n        if (this.choseObj.cameraCode) {\r\n          cameraList.push(this.choseObj.cameraCode)\r\n        } else {\r\n          this.cameras.forEach(item => {\r\n            if (item.chose) {\r\n              cameraList.push(item.cameraCode)\r\n            }\r\n          })\r\n        }\r\n        let cameraCodeList = [...new Set(childrenArr.concat(cameraList))]\r\n        batchAddCarouseMerge({ folderTreeCode: this.folderTreeCode, cameraCodeList }).then(res => {\r\n          if (res.code == 0) {\r\n            this.folderTreeCode = ''\r\n            this.tabIndex = 0\r\n            this.$message.success('添加成功')\r\n            this.cameras.forEach(item => {\r\n              item.chose = false\r\n            })\r\n            this.saveVisible = false\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 删除选中设备\r\n    delCamera(index) {\r\n      this.$confirm(\"是否删除设备\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        center: true,\r\n        type: \"warning\",\r\n        customClass: \"global-message-del-box\",\r\n      }).then(() => {\r\n        this.$emit('delCamera', index)\r\n      })\r\n    },\r\n    // 添加一个设备到轮播\r\n    saveOne(obj) {\r\n      this.choseObj = obj\r\n      this.save(true)\r\n    },\r\n    init() {\r\n      // 初始化playerArray 数组;并且默认播放前n路;\r\n      for (let i = 0; i < this.initNum; i++) {\r\n        let item = this.cameras[i] ? this.cameras[i] : {};\r\n        this.playerArray.push({\r\n          ...item,\r\n          cameraCode: !this.showLoop && this.cameras[i] && this.cameras[i]['cameraCode'] ? this.cameras[i]['cameraCode'] : '',\r\n          cameraName: !this.showLoop && this.cameras[i] && this.cameras[i]['cameraName'] ? this.cameras[i]['cameraName'] : '',\r\n          id: `${this.prefix}_${i}`,\r\n          type: true\r\n        });\r\n        // 默认选中的项;\r\n        if (!this.showLoop) {\r\n          if (this.cameras[i] && this.cameras[i]['cameraCode']) {\r\n            this.checkList.push(this.cameras[i]['cameraCode']);\r\n          }\r\n        }\r\n      };\r\n    },\r\n    // 打开弹窗\r\n    open() {\r\n      if (this.showStatus) {\r\n        this.replacePlayer(0)\r\n      } else {\r\n        this.init();\r\n        // this.$set(this, 'showStatus', true);\r\n        this.showStatus = true;\r\n        this.$nextTick(() => {\r\n          this.$refs['canvasPlayerList'].play();\r\n        })\r\n      }\r\n    },\r\n    refresh() {\r\n      this.playerArray = []\r\n      this.checkList = []\r\n      this.init();\r\n      this.$nextTick(() => {\r\n        this.$refs['canvasPlayerList'].play();\r\n      })\r\n    },\r\n    // 关闭弹窗;\r\n    close() {\r\n      if (this.isFull) {\r\n        // this.$set(this, 'isFull', false);\r\n        this.isFull = false;\r\n        this.is_mini = false\r\n        exitFullscreen();\r\n      }\r\n      this.$refs['canvasPlayerList'].stop();\r\n      // this.$set(this, 'checkList', []);\r\n      // this.$set(this, 'playerArray', []);\r\n      // this.$set(this, 'showStatus', false);\r\n      // this.$set(this, 'switchFlag', true);\r\n      // this.$set(this, 'loopFlag', false);\r\n      this.checkList = []\r\n      this.playerArray = []\r\n      this.showStatus = false\r\n      this.switchFlag = true\r\n      this.loopFlag = false\r\n      if (this.loopTimer) {\r\n        clearInterval(this.loopTimer);\r\n        this.loopTimer = null\r\n      }\r\n      // this.$set(this, 'loopTime', 60 * 1000);\r\n      // this.$set(this, 'notLoopArray', []);\r\n      this.loopTime = 60 * 1000\r\n      this.notLoopArray = []\r\n      this.$emit('close', true);\r\n    },\r\n    // 清空播放数据;\r\n    clearAll() {\r\n      if (this.isFull) {\r\n        // this.$set(this, 'isFull', false);\r\n        this.isFull = false;\r\n        this.is_mini = false\r\n        exitFullscreen();\r\n      }\r\n      this.$refs['canvasPlayerList'].stop();\r\n      // this.$set(this, 'checkList', []);\r\n      // this.$set(this, 'playerArray', []);\r\n      // //this.$set(this, 'showStatus', false);\r\n      // this.$set(this, 'switchFlag', true);\r\n      // this.$set(this, 'loopFlag', false);\r\n      this.checkList = []\r\n      this.playerArray = []\r\n      this.switchFlag = true\r\n      this.loopFlag = false\r\n      if (this.loopTimer) {\r\n        clearInterval(this.loopTimer);\r\n        this.loopTimer = null\r\n      }\r\n      // this.$set(this, 'loopTime', 60 * 1000);\r\n      // this.$set(this, 'notLoopArray', []);\r\n      this.loopTime = 60 * 1000\r\n      this.notLoopArray = []\r\n    },\r\n    // 关闭\r\n    closePlayer(cameraCode) {\r\n      if (this.checkList.includes(cameraCode)) {\r\n        // 如果在播放列表中,则是取消播放\r\n        let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\r\n        let cancelItem = this.playerArray[cancelIndex];\r\n        this.checkList.splice(cancelIndex, 1, '');\r\n        this.playerArray.splice(cancelIndex, 1, {\r\n          cameraCode: '',\r\n          cameraName: '',\r\n          id: cancelItem['id'],\r\n          type: true\r\n        });\r\n        this.$refs['canvasPlayerList'].replacePlayer(cancelIndex);\r\n      }\r\n    },\r\n    // 全屏切换\r\n    fullScreen() {\r\n      var t = window.devicePixelRatio;\r\n      let id = this.modalId;\r\n      let dom = document.querySelector(`#${id}`);\r\n      if (this.isFull) {\r\n        // this.$set(this, 'isFull', false);\r\n        this.isFull = false;\r\n        const { left, top } = this.exitModelStyle\r\n        document.querySelector('.video-dialog-container').style.cssText += `;left:${left}px;top:${top}px;`;\r\n        exitFullscreen();\r\n      } else {\r\n        openFullscreen(id);\r\n        // this.$set(this, 'isFull', true);\r\n        this.isFull = true;\r\n      }\r\n      // 切换屏幕显示状态;\r\n      this.$refs['canvasPlayerList'].resize();\r\n      // 当全屏和取消全屏的状态切换;\r\n      // this.$emit('fullChange',this.isFull,this.bodyHeight);\r\n    },\r\n    // 视频水印设置\r\n    openWaterMarkSet() {\r\n      this.markVisible = true\r\n      getWaterMarkSet().then((res) => {\r\n        this.markOptions = res.data\r\n        this.markOptions.forEach(item => {\r\n          if (item.dictLabel == 'font_transparence') {\r\n            this.markValue1 = Number(item.dictValue)\r\n          } else if (item.dictLabel == 'font_size') {\r\n            this.markValue2 = Number(item.dictValue)\r\n          }\r\n        })\r\n        this.$nextTick(() => {\r\n          this.renderWaterMarkText()\r\n        })\r\n      });\r\n    },\r\n    // 缩小和普通弹窗间切换\r\n    changeMiniOrMedium() {\r\n      const is_mini = !this.is_mini\r\n      if (this.isFull) {\r\n        this.fullScreen()\r\n      }\r\n      this.$nextTick(() => {\r\n        this.is_mini = is_mini\r\n      })\r\n    },\r\n    isOpen() {\r\n      return this.showStatus;\r\n    },\r\n    // 最小化(暂定)\r\n    small() {\r\n\r\n    },\r\n    // 1路和4路切换;\r\n    handleChangeWin(num) {\r\n      if (!this.loopFlag) {\r\n        // this.$set(this, 'currentWin', num);\r\n        this.currentWin = num\r\n        this.$refs['canvasPlayerList'].handleChangeWin(num);\r\n      }\r\n    },\r\n    // 模态窗的状态\r\n    getModal() {\r\n      return {\r\n        showStatus: this.showStatus,//当前的弹窗是否已经打开\r\n        currentWin: this.currentWin,//当前是1路播放还是4路播放;\r\n        currentActiveIndex: this.$refs['canvasPlayerList'].getCurrentActiveIndex()//当前选中的是第几个窗口\r\n      }\r\n    },\r\n    // 替换\r\n    replacePlayer(index) {\r\n      // this.$set(this.playerArray, index, {\r\n      //   cameraCode: this.cameras[index]['cameraCode'],\r\n      //   cameraName: this.cameras[index]['cameraName'],\r\n      //   id: this.playerArray[index]['id'],\r\n      //   type: true\r\n      // })\r\n      this.playerArray.splice(index, 1, {\r\n        cameraCode: this.cameras[index]['cameraCode'],\r\n        cameraName: this.cameras[index]['cameraName'],\r\n        id: this.playerArray[index]['id'],\r\n        type: true\r\n      })\r\n\r\n      this.$nextTick(() => {\r\n        this.$refs['canvasPlayerList'].replacePlayer(index);\r\n      });\r\n    },\r\n    // 轮播初始化;\r\n    runInit() {\r\n      // this.$set(this, 'notLoopArray', JSON.parse(JSON.stringify(this.cameras)));\r\n      this.notLoopArray = JSON.parse(JSON.stringify(this.cameras))\r\n    },\r\n    // 启动轮播或者是停止轮播,目前是全列表轮播;\r\n    run() {\r\n      let _that = this;\r\n      // 启动轮播\r\n      if (this.loopTimer) {\r\n        clearInterval(_that.loopTimer);\r\n        this.loopTimer = null;\r\n      }\r\n      if (this.loopFlag) {// 开始播放\r\n        // 轮播初始化;\r\n        this.runInit();\r\n        this.runLoop(true);\r\n        this.loopTimer = setInterval(() => {\r\n          this.runLoop(false);\r\n        }, this.loopTime)\r\n      } else { // 暂停播放\r\n\r\n      }\r\n    },\r\n    runLoop(init) {\r\n      // 需要进行轮播的集合;\r\n      let loopArray = this.notLoopArray.splice(0, this.currentWin);\r\n      for (let i = 0; i < this.playerArray.length; i++) {\r\n        // this.$set(this.playerArray, i, {\r\n        //   cameraCode: loopArray[i] && loopArray[i]['cameraCode'] ? loopArray[i]['cameraCode'] : '',\r\n        //   cameraName: loopArray[i] && loopArray[i]['cameraName'] ? loopArray[i]['cameraName'] : '',\r\n        //   id: this.playerArray[i]['id'],\r\n        //   type: true\r\n        // })\r\n        this.playerArray.splice(i, 1, {\r\n          cameraCode: loopArray[i] && loopArray[i]['cameraCode'] ? loopArray[i]['cameraCode'] : '',\r\n          cameraName: loopArray[i] && loopArray[i]['cameraName'] ? loopArray[i]['cameraName'] : '',\r\n          id: this.playerArray[i]['id'],\r\n          type: true\r\n        })\r\n      }\r\n      // this.$set(this, 'checkList', loopArray.map(item => item['cameraCode']));\r\n      this.checkList = loopArray.map(item => item['cameraCode'])\r\n      // 开始播放;\r\n      if (init) {\r\n        this.$refs['canvasPlayerList'].play();\r\n      } else {\r\n        this.playerArray.forEach((item, index) => {\r\n          this.$refs['canvasPlayerList'].replacePlayer(index);\r\n        });\r\n      }\r\n      // 将正在播放的追加到未播放的尾部;\r\n      // this.$set(this, 'notLoopArray', [...this.notLoopArray, ...loopArray]);\r\n      this.notLoopArray = [...this.notLoopArray, ...loopArray]\r\n    },\r\n    // 是否开始轮播控制\r\n    handleLoopClick() {\r\n      // this.$set(this, 'loopFlag', !this.loopFlag);\r\n      this.loopFlag = !this.loopFlag\r\n      this.$nextTick(() => {\r\n        this.run();\r\n      })\r\n    },\r\n    // 是否收起侧边点位列表\r\n    handleSwitchClick() {\r\n      // this.$set(this, 'switchFlag', !this.switchFlag);\r\n      this.switchFlag = !this.switchFlag\r\n    },\r\n    // 轮播组切换事件,调用时监听此事件 更新待播放列表;\r\n    handleLoopGroupChange(val) {\r\n      // 切换时应该将状态都清空,\r\n      // this.$set(this, 'notLoopArray', []);\r\n      this.notLoopArray = []\r\n      this.$refs['canvasPlayerList'].stop();\r\n      this.$emit('loopGroupChange', val);\r\n    },\r\n    //轮播时间切换事件,调用时监听此事件;\r\n    handleLoopTimeChange() {\r\n\r\n    },\r\n    handleCloseItem({ cameraCode }) {\r\n      let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\r\n      if (cancelIndex != -1) {\r\n        let cancelItem = this.playerArray[cancelIndex];\r\n        this.checkList.splice(cancelIndex, 1, '');\r\n        this.playerArray.splice(cancelIndex, 1, {\r\n          cameraCode: '',\r\n          cameraName: '',\r\n          id: cancelItem['id'],\r\n          type: true\r\n        });\r\n      }\r\n    },\r\n    // 单个点位的点击事件\r\n    handleCheckItemChange({ cameraCode, cameraName }) {\r\n      if (!this.loopFlag) {\r\n        // 1.判断当前点位是否在播放列表中\r\n        if (this.checkList.includes(cameraCode)) {\r\n          // 如果在播放列表中,则是取消播放\r\n          let cancelIndex = this.checkList.findIndex(item => item === cameraCode);\r\n          let cancelItem = this.playerArray[cancelIndex];\r\n          this.checkList.splice(cancelIndex, 1, '');\r\n          this.playerArray.splice(cancelIndex, 1, {\r\n            cameraCode: '',\r\n            cameraName: '',\r\n            id: cancelItem['id'],\r\n            type: true\r\n          });\r\n          this.$refs['canvasPlayerList'].replacePlayer(cancelIndex);\r\n        } else {\r\n          //替换播放\r\n          let replaceIndex = -1;\r\n          let isAllPlay = this.playerArray.every(item => item['cameraCode']);\r\n          if (isAllPlay) {\r\n            let failIndex = this.playerArray.findIndex(item => !item['type']);\r\n            if (failIndex != -1) {\r\n              replaceIndex = failIndex;\r\n            } else {\r\n              replaceIndex = this.$refs['canvasPlayerList'].getCurrentActiveIndex();\r\n            }\r\n          } else {\r\n            replaceIndex = this.playerArray.findIndex(item => item['cameraCode'] === '');\r\n          }\r\n          if (replaceIndex != -1) {\r\n            let replaceItem = this.playerArray[replaceIndex];\r\n            // 替换操作\r\n            this.checkList.splice(replaceIndex, 1, cameraCode);\r\n            this.playerArray.splice(replaceIndex, 1, {\r\n              cameraCode: cameraCode,\r\n              cameraName: cameraName,\r\n              id: replaceItem['id'],\r\n              type: true\r\n            });\r\n            this.$refs['canvasPlayerList'].replacePlayer(replaceIndex);\r\n          }\r\n        }\r\n      }\r\n\r\n    },\r\n    // 当前的播放状态;\r\n    handlePlayType(cameraCode, type) {\r\n      let index = this.playerArray.findIndex(item => item['cameraCode'] == cameraCode);\r\n      if (index != -1) {\r\n        // this.$set(this.playerArray[index], 'type', type);\r\n        this.playerArray[index]['type'] = type\r\n      }\r\n    },\r\n    exitFullscreen(e) {\r\n      if (!document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {\r\n        // this.$set(this, 'isFull', false);\r\n        this.isFull = false;\r\n        exitFullscreen();\r\n        this.$refs['canvasPlayerList'].resize();\r\n        this.$emit('fullChange', this.isFull, this.bodyHeight);\r\n      }\r\n    },\r\n    resizeChange() {\r\n      if (this.isFull) {\r\n        this.current_height = document.body.clientHeight\r\n        this.current_width = document.body.clientWidth\r\n      }\r\n    },\r\n    // 自定义按钮的点击事件\r\n    handleBtnClick(player, index) {\r\n      this.$emit('handleBtnClick', player, index);\r\n    }\r\n  },\r\n  mounted() {\r\n    let _that = this;\r\n    this.current_height = window.screen.height\r\n    this.current_width = window.screen.width\r\n    if (document.addEventListener) {\r\n      document.addEventListener('webkitfullscreenchange', _that.exitFullscreen, false);\r\n      document.addEventListener('mozfullscreenchange', _that.exitFullscreen, false);\r\n      document.addEventListener('fullscreenchange', _that.exitFullscreen, false);\r\n      document.addEventListener('MSFullscreenChange', _that.exitFullscreen, false);\r\n      window.addEventListener('resize', _that.resizeChange, false);\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    let _that = this;\r\n    if (document.removeEventListener) {\r\n      document.removeEventListener('webkitfullscreenchange', _that.exitFullscreen);\r\n      document.removeEventListener('mozfullscreenchange', _that.exitFullscreen);\r\n      document.removeEventListener('fullscreenchange', _that.exitFullscreen);\r\n      document.removeEventListener('MSFullscreenChange', _that.exitFullscreen);\r\n      window.removeEventListener('resize', _that.resizeChange);\r\n    }\r\n    if (this.loopTimer) {\r\n      clearInterval(this.loopTimer)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.video-dialog-container {\r\n  pointer-events: stroke;\r\n  // background-color: #5f88b0;\r\n  background: rgba(10, 55, 88, 0.85);\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 3px;\r\n  box-sizing: border-box;\r\n  position: fixed;\r\n  top: 15vh;\r\n  z-index: 2000;\r\n  /*水平居中*/\r\n  left: 50%;\r\n  margin-left: -335px;\r\n\r\n  // transform: translateX(-50%);\r\n  // 弹窗头部样式\r\n  .video-dialog {\r\n    width: 100%;\r\n    height: 100%;\r\n    // background: rgba(10, 55, 88, 0.85);\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // background-color: #5f88b0;\r\n    background: linear-gradient(360deg, #5ad3fba8 0%, rgba(126, 220, 251, 0) 100%, rgba(90, 211, 251, 0) 100%);\r\n    border-bottom: 1px solid #5f88b0;\r\n    height: 35px;\r\n    line-height: 35px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    border-radius: 0px;\r\n    padding: 10px 14px;\r\n    margin: 0;\r\n  }\r\n\r\n  // 弹窗标题样式\r\n  .el-dialog__title {\r\n    font-size: 23px;\r\n    font-family: Source Han Sans SC-Regular, Source Han Sans SC;\r\n    font-weight: 400;\r\n    color: #1FC6FF;\r\n    padding-left: 0px;\r\n    border-left: none;\r\n    display: flex;\r\n  }\r\n\r\n  .el-dialog__btns {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .close-icon {\r\n      width: 30px;\r\n      height: 31px;\r\n      line-height: 31px;\r\n      margin-left: 20px;\r\n      margin-right: 10px;\r\n\r\n      i {\r\n        cursor: pointer;\r\n        color: #ccc;\r\n        font-size: 20px;\r\n      }\r\n    }\r\n\r\n    .minus-view {\r\n      margin-right: 15px;\r\n    }\r\n\r\n    .fullOrSmall {\r\n      cursor: pointer;\r\n      display: inline-block;\r\n      width: 20px;\r\n      height: 20px;\r\n      background-repeat: no-repeat;\r\n      background-position: center;\r\n      background-size: 20px 20px;\r\n      background-image: url('@/assets/images/video/fullscreen.png');\r\n\r\n      &.isFull {\r\n        background-image: url('@/assets/images/video/exitfull.png');\r\n      }\r\n    }\r\n  }\r\n\r\n  // 弹窗体样式\r\n  .el-dialog__body {\r\n    padding: 15px;\r\n    // background-color: #3b516f;\r\n    // background: rgba(10, 55, 88, 0.85);\r\n\r\n    .el-dialog__body-container {\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      flex-flow: row nowrap;\r\n      justify-content: center;\r\n\r\n      .canvas-player-slot {\r\n        width: 190px;\r\n\r\n        // 左侧的样式\r\n        .canvas-player-cameras {\r\n          width: 100%;\r\n          height: 100%;\r\n          border: 1px solid #dcdee0;\r\n          border-radius: 4px 4px 0px 0px;\r\n          position: relative;\r\n        }\r\n\r\n        .canavs-player-cameras-title {\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: bold;\r\n          line-height: 40px;\r\n          color: #333333;\r\n          opacity: 1;\r\n          padding: 0 16px;\r\n          border-bottom: 1px solid #dcdee0;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n\r\n        .canvas-player-camera-container {\r\n          width: 100%;\r\n          height: calc(100% - 40px);\r\n        }\r\n\r\n        .player-item {\r\n          height: 32px;\r\n          display: flex;\r\n          flex-flow: row nowrap;\r\n          justify-content: flex-start;\r\n          align-items: center;\r\n          cursor: pointer;\r\n\r\n          .player-item-icon {\r\n            background-image: url('@/assets/images/video/player-active.png')\r\n          }\r\n        }\r\n\r\n        .deleteIcon {\r\n          height: 100%;\r\n          width: 32px;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .player-item-active {\r\n          .player-item-icon {\r\n            background-image: url('@/assets/images/video/player.png')\r\n          }\r\n\r\n          .player-item-title {\r\n            color: #4b87ff;\r\n          }\r\n        }\r\n\r\n        .player-item-icon {\r\n          width: 40px;\r\n          height: 100%;\r\n          background-repeat: no-repeat;\r\n          background-size: 16px 16px;\r\n          background-position: center;\r\n        }\r\n\r\n        .player-item-title {\r\n          color: #000;\r\n          font-size: 14px;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      // 间距;\r\n      .canvas-player-padding {\r\n        position: relative;\r\n\r\n        .canvas-player-cameras-switch {\r\n          position: absolute;\r\n          width: 15px;\r\n          height: 65px;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n\r\n          .img {\r\n            width: 100%;\r\n            height: 100%;\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n\r\n      .canvas-player-list-container {\r\n        flex: 1;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 弹窗底部样式\r\n  .el-dialog__footer {\r\n    height: 50px;\r\n    background-color: #fff;\r\n    border-top: 1px solid #e9e9e9;\r\n    padding: 0 15px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    align-items: center;\r\n  }\r\n\r\n  .el-dialog__footer-slot {\r\n    flex: 1;\r\n  }\r\n\r\n  //\r\n  .loop-set-container {\r\n    display: flex;\r\n    flex-flow: row nowrap;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n\r\n    .loop-set-title {\r\n      font-size: 14px;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      color: #2f2e3f;\r\n    }\r\n\r\n    .loop-set-item {\r\n      margin-right: 10px;\r\n\r\n      & :deep(.el-select--mini) {\r\n        margin-top: 5px;\r\n      }\r\n    }\r\n\r\n    .loop-btn {\r\n      width: 32px;\r\n      height: 32px;\r\n\r\n      .img {\r\n        width: 100%;\r\n        height: 100%;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 多路切换的样式\r\n  .el-dialog__footer-change-container {\r\n    display: flex;\r\n    flex-flow: row nowrap;\r\n    align-items: center;\r\n\r\n    .change-item {\r\n      width: 30px;\r\n      height: 30px;\r\n      border-style: solid;\r\n      border-radius: 5px;\r\n      border-width: 1px;\r\n      border-color: #dcdee0;\r\n      background-color: #fff;\r\n      cursor: pointer;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-left: 10px;\r\n\r\n      &.change-item-active {\r\n        border-color: #4b87ff;\r\n        background-color: #f1f6ff;\r\n      }\r\n    }\r\n\r\n    i {\r\n      width: 15px;\r\n      height: 15px;\r\n      color: #4b87ff;\r\n      width: 14px;\r\n      height: 14px;\r\n    }\r\n\r\n    .el-icon-one {\r\n      background-color: #4b87ff;\r\n    }\r\n  }\r\n\r\n  .video-list {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.mini-video-dialog {\r\n  visibility: hidden;\r\n\r\n  .play-btns {\r\n    visibility: visible;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    position: fixed;\r\n    bottom: 50px;\r\n    right: 200px;\r\n    width: 222px;\r\n    height: 40px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 6px 0px rgba(118, 147, 175, 0.25);\r\n    border-radius: 4px;\r\n    padding: 8px 12px;\r\n\r\n    .play-title {\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      font-weight: 600;\r\n      color: #0e1726;\r\n      flex: 1;\r\n      overflow: hidden; //超出的文本隐藏\r\n      text-overflow: ellipsis; //用省略号显示\r\n      white-space: nowrap; //不换行\r\n    }\r\n\r\n    .btn {\r\n      background-image: url(\"@/assets/images/video/fangda.png\");\r\n      background-size: 100%;\r\n      background-position: center;\r\n      background-repeat: no-repeat;\r\n      width: 22px;\r\n      height: 22px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.dialogTitle {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  line-height: 32px;\r\n\r\n  .dialogTab {\r\n    color: #4b87ff;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n:deep(.el-select) {\r\n  width: 100%;\r\n}\r\n\r\n.waterMark {\r\n  height: 300px;\r\n  position: relative;\r\n\r\n  .waterText {\r\n    position: absolute;\r\n    top: 0px;\r\n    left: 0px;\r\n    // width: 100%;\r\n    // height: 100%;\r\n  }\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.btn-main-box {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.aliasName {\r\n  color: darkgray;\r\n  font-size: 14px;\r\n  border: darkgray 1px solid;\r\n  border-radius: 10px;\r\n}\r\n</style>\r\n  "], "mappings": "AAoKA,SAAAA,cAAA,EAAAC,cAAA,EAAAC,cAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,MAAA;AACA;AACA,SACAC,eAAA,EACAC,YAAA,QACA;AACA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,gBAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;AACA;AACA;IACAG,KAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAI,eAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAM,UAAA;MACAR,IAAA,EAAAC,OAAA;MACAC,QAAA;QACA;MACA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAC,OAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAQ,gBAAA;MACAV,IAAA,EAAAO,MAAA;MACAL,QAAA;QACA;MACA;IACA;IACAS,OAAA;MACAX,IAAA,EAAAY,MAAA;MACAV,QAAA;QACA;MACA;IACA;IACAW,MAAA;MACAb,IAAA,EAAAI,MAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAY,OAAA;MACAd,IAAA,EAAAe,KAAA;MACAb,QAAA;QACA;MACA;IACA;IACA;IACAc,QAAA;MACAhB,IAAA,EAAAC,OAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACAe,SAAA;MACAjB,IAAA,EAAAe,KAAA;MACAb,QAAA;QACA;MACA;IACA;IACAgB,gBAAA;MACAlB,IAAA,EAAAI;IACA;IACA;IACAe,YAAA;MACAnB,IAAA,EAAAe,KAAA;MACAb,QAAA;QACA,QACA;UACAkB,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,EACA;MACA;IACA;IACAC,UAAA;MACAtB,IAAA,EAAAe,KAAA;MACAb,QAAA;QACA;MACA;IACA;IACA;IACAqB,OAAA;MACAvB,IAAA,EAAAI,MAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAsB,SAAA;MACAxB,IAAA,EAAAC,OAAA;MACAC,QAAA;QACA;MACA;IACA;EACA;EACAuB,QAAA;IACA;IACA;IACAC,QAAA;MACA,eAAAb,MAAA;IACA;IACA;AACA;AACA;IACAc,WAAA;MACA,IAAAC,KAAA,GAAArC,cAAA;MACA,IAAAsC,KAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAF,KAAA,GAAAD,KAAA;MACA,IAAAI,MAAA,GAAAF,MAAA,CAAAC,MAAA,CAAAC,MAAA,GAAAJ,KAAA;MACA,IAAAK,KAAA,GAAAL,KAAA,iBAAAC,KAAA,SAAAG,MAAA;MACA;QACA,QAAAE,gBAAA;QACA,QAAAxB;MACA;IACA;IACA;AACA;AACA;AACA;IACAyB,WAAA;MACA;MACA,IAAAH,MAAA,QAAAI,cAAA;MACA,IAAAC,KAAA,cAAA7B,UAAA;MACA,IAAA8B,SAAA,QAAAC,MAAA,GAAAP,MAAA,GAAAQ,QAAA,MAAAb,UAAA,CAAAK,MAAA;MACA,OAAAM,SAAA,GAAAD,KAAA;IACA;IACA;AACA;AACA;IACAI,UAAA;MACA;MACA,IAAAZ,KAAA,QAAAa,aAAA;MACA,YAAAH,MAAA,GAAAV,KAAA,GAAAW,QAAA,MAAAb,UAAA,CAAAE,KAAA;IACA;IACA;AACA;AACA;IACAc,oBAAA;MACA;QACAd,KAAA,OAAAY,SAAA,cAAAhC,UAAA,QAAAmC,UAAA;QACAZ,MAAA,OAAAG,UAAA;MACA;IACA;EACA;EACAU,UAAA;IACA;MACA;MACAC,KAAAC,EAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,QAAA;QACA;UAAA7B;QAAA,IAAA2B,OAAA;QACA,IAAAG,QAAA;QACA,IAAAC,SAAA;QACA,IAAAC,UAAA,GAAAhC,KAAA,IAAAA,KAAA,CAAAgC,UAAA,GAAAhC,KAAA,CAAAgC,UAAA;QACA,IAAAC,eAAA,GAAAjC,KAAA,IAAAA,KAAA,CAAAkC,OAAA,GAAAlC,KAAA,CAAAkC,OAAA;QACA,MAAAC,cAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAJ,UAAA;QACA,MAAAE,OAAA,GAAAR,EAAA;QACAS,cAAA,CAAAE,KAAA,CAAAC,OAAA;QACA,MAAAC,GAAA;UACA,IAAA9B,MAAA,CAAA+B,QAAA,CAAAC,YAAA;YACA,QAAAC,GAAA,EAAAC,IAAA,KAAAD,GAAA,CAAAD,YAAA,CAAAE,IAAA;UACA;YACA,QAAAD,GAAA,EAAAC,IAAA,KAAAC,gBAAA,CAAAF,GAAA,SAAAC,IAAA;UACA;QACA;QACAR,cAAA,CAAAU,WAAA,GAAAC,CAAA;UACA,IAAAlB,KAAA,CAAAmB,OAAA,CAAA7B,MAAA;YACA;UACA;UACA;UACA,MAAA8B,IAAA,GAAAF,CAAA,CAAAG,OAAA,GAAAd,cAAA,CAAAe,UAAA;UACA,MAAAC,IAAA,GAAAL,CAAA,CAAAM,OAAA,GAAAjB,cAAA,CAAAkB,SAAA;UAEA,MAAAC,WAAA,GAAAd,QAAA,CAAAe,IAAA,CAAAC,WAAA;UACA,MAAAC,YAAA,GAAAjB,QAAA,CAAAkB,eAAA,CAAAC,YAAA;UACA,MAAAC,YAAA,GAAA1B,OAAA,CAAA2B,WAAA;UACA,MAAAC,aAAA,GAAA5B,OAAA,CAAA6B,YAAA;UACA;UACA,IAAAC,IAAA,GAAAzB,GAAA,CAAAL,OAAA;UACA,IAAA+B,IAAA,GAAA1B,GAAA,CAAAL,OAAA;UACA;UACA,IAAA8B,IAAA,CAAAE,QAAA;YACAF,IAAA,IAAAxB,QAAA,CAAAe,IAAA,CAAAC,WAAA,KAAAQ,IAAA,CAAAG,OAAA;YACAF,IAAA,IAAAzB,QAAA,CAAAe,IAAA,CAAAI,YAAA,KAAAM,IAAA,CAAAE,OAAA;UACA;YACAH,IAAA,IAAAA,IAAA,CAAAG,OAAA;YACAF,IAAA,IAAAA,IAAA,CAAAE,OAAA;UACA;UACA3B,QAAA,CAAA4B,WAAA,aAAAtB,CAAA;YACA;YACA,IAAAuB,IAAA,GAAAvB,CAAA,CAAAG,OAAA,GAAAD,IAAA,GAAAgB,IAAA;YACA,IAAAM,GAAA,GAAAxB,CAAA,CAAAM,OAAA,GAAAD,IAAA,GAAAc,IAAA;;YAEA;YACA,IAAAI,IAAA;cACAA,IAAA;YACA;YAEA,IAAAA,IAAA,GAAAf,WAAA,SAAAM,YAAA;cACAS,IAAA,GAAAf,WAAA,SAAAM,YAAA;YACA;YAEA,IAAAU,GAAA;cACAA,GAAA;YACA;YACA,IAAAA,GAAA,GAAAb,YAAA,GAAAK,aAAA;cACAQ,GAAA,GAAAb,YAAA,GAAAK,aAAA;YACA;;YAEA;YACA5B,OAAA,CAAAG,KAAA,CAAAC,OAAA,aAAA+B,IAAA,UAAAC,GAAA;YACA1C,KAAA,CAAAmB,OAAA,CAAAwB,cAAA;cACAD,GAAA,EAAAA,GAAA;cACAD,IAAA,EAAAA;YACA;UACA;UAEA7B,QAAA,CAAAgC,SAAA,aAAA1B,CAAA;YACAN,QAAA,CAAA4B,WAAA;YACA5B,QAAA,CAAAgC,SAAA;UACA;QACA;MACA;IACA;EACA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACAC,UAAA;MAAA;MACAC,UAAA;MAAA;MACAhE,MAAA;MAAA;MACAiE,OAAA;MAAA;MACAtE,gBAAA;QACAL,KAAA;QAAA;QACAG,MAAA;QAAA;QACA2D,GAAA;QAAA;QACAc,mBAAA;MACA;MACAb,cAAA;QACAF,IAAA;QACAC,GAAA;MACA;MACA;MACAe,QAAA;MACA;MACA9D,UAAA;MACA;MACA+D,QAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,YAAA;MAAA;MACAC,WAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,cAAA;MACAC,WAAA;MACAC,WAAA;MACAjF,cAAA;MAAA;MACAM,aAAA;IACA;EACA;EACA4E,KAAA;IACAP,WAAA;MACAQ,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAxB,QAAA;QACA;MACA;IACA;IACAM,UAAA;MACAiB,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAA1G,OAAA,CAAA2G,OAAA,CAAAC,IAAA;YACAA,IAAA,CAAAC,KAAA;UACA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,aAAA;MACA,KAAA9B,WAAA;IACA;IACA+B,OAAA;MACA,kBAAAC,IAAA,MAAA3B,UAAA;QACA,KAAAA,UAAA,GAAAxF,MAAA,MAAAwF,UAAA,CAAA4B,OAAA;MACA;MACA,KAAAC,mBAAA;IACA;IACAC,qBAAA;MACA,KAAAhC,OAAA;MACA,IAAAiC,GAAA,QAAAlC,WAAA;MACA,IAAAmC,UAAA;MACAD,GAAA,CAAAV,OAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAW,SAAA;UACAX,IAAA,CAAAY,SAAA,QAAAnC,UAAA;QACA,WAAAuB,IAAA,CAAAW,SAAA;UACAX,IAAA,CAAAY,SAAA,QAAAlC,UAAA;QACA;QACAgC,UAAA,CAAAG,IAAA,CAAA5I,YAAA,CAAA+H,IAAA;MACA;MACAc,OAAA,CAAAC,GAAA,CAAAL,UAAA,EAAAM,IAAA,CAAAC,GAAA;QACA,KAAAzC,OAAA;QACA,IAAAyC,GAAA,KAAAC,IAAA,SAAAD,GAAA,KAAAC,IAAA;UACA,KAAAC,QAAA;YACA7I,IAAA;YACA8I,OAAA;UACA;UACA,KAAAzB,WAAA;UACA;UACA,SAAA0B,OAAA,CAAAC,KAAA,CAAAC,MAAA;YACA,KAAAF,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAD,KAAA,qBAAAE,cAAA;UACA;UACA,SAAAH,OAAA,CAAAC,KAAA,CAAAG,MAAA;YACA,KAAAJ,OAAA,CAAAC,KAAA,CAAAG,MAAA,CAAAH,KAAA,qBAAAE,cAAA;UACA;UACA,SAAAH,OAAA,CAAAC,KAAA,CAAAI,UAAA;YACA,KAAAL,OAAA,CAAAC,KAAA,CAAAI,UAAA,CAAAF,cAAA;UACA;QACA;MACA;IACA;IACA;IACAjB,oBAAA;MACA;MACA,IAAAoB,WAAA,QAAAL,KAAA,CAAAM,SAAA;MACA,IAAAC,GAAA,GAAAF,WAAA,CAAAG,UAAA;MACA;MACA,IAAA3H,KAAA,GAAAwH,WAAA,CAAAxH,KAAA;MACA,IAAAG,MAAA,GAAAqH,WAAA,CAAArH,MAAA;MACA,IAAAuH,GAAA;QACAA,GAAA,CAAAE,SAAA,OAAA5H,KAAA,EAAAG,MAAA;MACA;MACA;MACA,MAAApC,IAAA;MACA,IAAA8J,IAAA,GAAAjK,MAAA,GAAAkK,MAAA;MACA,IAAAC,MAAA,IACA;QACAC,CAAA,EAAAhI,KAAA;QACAiI,CAAA,EAAA9H,MAAA;MACA,GACA;QACA6H,CAAA,MAAAhI,KAAA;QACAiI,CAAA,EAAA9H,MAAA;MACA,GACA;QACA6H,CAAA,EAAAhI,KAAA;QACAiI,CAAA,MAAA9H,MAAA;MACA,GACA;QACA6H,CAAA,MAAAhI,KAAA;QACAiI,CAAA,MAAA9H,MAAA;MACA;MACA;MACA;MACA;MACA;MAAA,CACA;MACA,SAAA+H,CAAA,MAAAA,CAAA,GAAAH,MAAA,CAAAI,MAAA,EAAAD,CAAA;QACA;UAAAF,CAAA;UAAAC;QAAA,IAAAF,MAAA,CAAAG,CAAA;QACA,KAAAE,iBAAA,CAAAZ,WAAA,EAAAzJ,IAAA,EAAAiK,CAAA,EAAAC,CAAA;QACA,KAAAG,iBAAA,CAAAZ,WAAA,EAAAK,IAAA,EAAAG,CAAA,EAAAC,CAAA;MACA;IACA;IACA;IACAG,iBAAA,WAAAA,CAAAZ,WAAA,EAAAa,IAAA,EAAAL,CAAA,EAAAC,CAAA,EAAAK,GAAA;MACA,IAAAC,IAAA,GAAAf,WAAA,CAAAG,UAAA;MACA,IAAAY,IAAA;QACA,IAAAC,OAAA,GAAAD,IAAA,CAAAE,WAAA,CAAAJ,IAAA;QACA,IAAAK,QAAA,GAAAF,OAAA,CAAAxI,KAAA;QACA,IAAA2I,YAAA,GAAAH,OAAA,CAAAI,uBAAA,GAAAJ,OAAA,CAAAK,wBAAA;QACAN,IAAA,CAAAO,IAAA;QACAP,IAAA,CAAAQ,IAAA,WAAAxE,UAAA;QACAgE,IAAA,CAAAS,SAAA,6BAAA1E,UAAA,QAAA6B,OAAA;QACA,IAAA8C,OAAA,GAAAX,GAAA,GAAAL,CAAA,GAAAS,QAAA,OAAAC,YAAA,GAAAV,CAAA,GAAAS,QAAA;QACAH,IAAA,CAAAW,MAAA,OAAAC,IAAA,CAAAC,EAAA;QACAb,IAAA,CAAAc,QAAA,CAAAhB,IAAA,EAAAL,CAAA,GAAAU,QAAA,OAAAA,QAAA,MAAAO,OAAA;QACAV,IAAA,CAAAe,OAAA;MACA;IACA;IACA;IACAR,KAAAS,KAAA;MACA,MAAAC,SAAA,QAAAvK,OAAA,CAAAwK,MAAA,CAAA5D,IAAA;QACA,OAAAA,IAAA,CAAA6D,UAAA,IAAA7D,IAAA,CAAAC,KAAA;MACA;MACA,KAAAyD,KAAA,IAAAC,SAAA,CAAArB,MAAA;QACA,KAAAnB,QAAA,CAAA2C,KAAA;QACA;MACA;MACA,KAAAzE,WAAA;MACA,KAAAK,WAAA;MACA,IAAAqE,MAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACAC,sBAAA,CAAAD,MAAA,EAAA/C,IAAA,CAAAC,GAAA;QACA,KAAAxB,cAAA,GAAAwB,GAAA,CAAA7C,IAAA,CAAA6F,GAAA,CAAAjE,IAAA;UACA;YACAtG,KAAA,EAAAsG,IAAA,CAAAkE,UAAA;YACAvK,KAAA,EAAAqG,IAAA,CAAAR,cAAA;YACA2E,QAAA,EAAAnE,IAAA,CAAAoE;UACA;QACA;QACA,KAAA1E,WAAA;MACA;IACA;IACA;IACA2E,SAAA;MACA;MACA,SAAA/E,QAAA;QACA,UAAAC,SAAA;UACA,KAAA4B,QAAA,CAAA2C,KAAA;UACA;QACA;QACA,IAAAC,MAAA;UAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAO,UAAA;UAAAJ,UAAA,OAAA3E;QAAA,GAAAyB,IAAA,CAAAuD,QAAA;UACA,IAAAA,QAAA,CAAArD,IAAA;YACA8C,sBAAA,CAAAD,MAAA,EAAA/C,IAAA,CAAAC,GAAA;cACA,IAAAA,GAAA,CAAAC,IAAA;gBACAD,GAAA,CAAA7C,IAAA,CAAA2B,OAAA,CAAAC,IAAA;kBACA,IAAAA,IAAA,CAAAkE,UAAA,SAAA3E,SAAA;oBACA,KAAAC,cAAA,GAAAQ,IAAA,CAAAR,cAAA;oBACA,IAAAgF,UAAA;oBACA,SAAAlG,QAAA,CAAAuF,UAAA;sBACAW,UAAA,CAAA3D,IAAA,MAAAvC,QAAA,CAAAuF,UAAA;oBACA;sBACA,KAAAzK,OAAA,CAAA2G,OAAA,CAAAC,IAAA;wBACA,IAAAA,IAAA,CAAA6D,UAAA,IAAA7D,IAAA,CAAAC,KAAA;0BACAuE,UAAA,CAAA3D,IAAA,CAAAb,IAAA,CAAA6D,UAAA;wBACA;sBACA;oBACA;oBACAY,oBAAA;sBAAAjF,cAAA,OAAAA,cAAA;sBAAAkF,cAAA,EAAAF;oBAAA,GAAAxD,IAAA,CAAAC,GAAA;sBACA,IAAAA,GAAA,CAAAC,IAAA;wBACA,KAAA3B,SAAA;wBACA,KAAAC,cAAA;wBACA,KAAA2B,QAAA,CAAAwD,OAAA;wBACA,KAAArF,QAAA;wBACA,KAAAlG,OAAA,CAAA2G,OAAA,CAAAC,IAAA;0BACAA,IAAA,CAAAC,KAAA;wBACA;wBACA,KAAAZ,WAAA;sBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA,gBAAAC,QAAA;QACA;QACA,IAAAsF,aAAA;QACA,KAAAnF,cAAA,CAAAM,OAAA,CAAAC,IAAA;UACA,SAAAR,cAAA,IAAAQ,IAAA,CAAArG,KAAA;YACAiL,aAAA,GAAA5E,IAAA,CAAAmE,QAAA;UACA;QACA;QACA,IAAAU,WAAA;QACAD,aAAA,CAAA7E,OAAA,CAAAC,IAAA;UACA6E,WAAA,CAAAhE,IAAA,CAAAb,IAAA,CAAA6D,UAAA;QACA;QACA,IAAAW,UAAA;QACA,SAAAlG,QAAA,CAAAuF,UAAA;UACAW,UAAA,CAAA3D,IAAA,MAAAvC,QAAA,CAAAuF,UAAA;QACA;UACA,KAAAzK,OAAA,CAAA2G,OAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,KAAA;cACAuE,UAAA,CAAA3D,IAAA,CAAAb,IAAA,CAAA6D,UAAA;YACA;UACA;QACA;QACA,IAAAa,cAAA,WAAAI,GAAA,CAAAD,WAAA,CAAAE,MAAA,CAAAP,UAAA;QACAC,oBAAA;UAAAjF,cAAA,OAAAA,cAAA;UAAAkF;QAAA,GAAA1D,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAA1B,cAAA;YACA,KAAAF,QAAA;YACA,KAAA6B,QAAA,CAAAwD,OAAA;YACA,KAAAvL,OAAA,CAAA2G,OAAA,CAAAC,IAAA;cACAA,IAAA,CAAAC,KAAA;YACA;YACA,KAAAZ,WAAA;UACA;QACA;MACA;IACA;IACA;IACA2F,UAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,MAAA;QACA/M,IAAA;QACAgN,WAAA;MACA,GAAAtE,IAAA;QACA,KAAAuE,KAAA,cAAAN,KAAA;MACA;IACA;IACA;IACAO,QAAAC,GAAA;MACA,KAAAnH,QAAA,GAAAmH,GAAA;MACA,KAAAxC,IAAA;IACA;IACAyC,KAAA;MACA;MACA,SAAArD,CAAA,MAAAA,CAAA,QAAApJ,OAAA,EAAAoJ,CAAA;QACA,IAAArC,IAAA,QAAA5G,OAAA,CAAAiJ,CAAA,SAAAjJ,OAAA,CAAAiJ,CAAA;QACA,KAAAnD,WAAA,CAAA2B,IAAA;UACA,GAAAb,IAAA;UACA6D,UAAA,QAAAvK,QAAA,SAAAF,OAAA,CAAAiJ,CAAA,UAAAjJ,OAAA,CAAAiJ,CAAA,uBAAAjJ,OAAA,CAAAiJ,CAAA;UACAsD,UAAA,QAAArM,QAAA,SAAAF,OAAA,CAAAiJ,CAAA,UAAAjJ,OAAA,CAAAiJ,CAAA,uBAAAjJ,OAAA,CAAAiJ,CAAA;UACAuD,EAAA,UAAAzM,MAAA,IAAAkJ,CAAA;UACA/J,IAAA;QACA;QACA;QACA,UAAAgB,QAAA;UACA,SAAAF,OAAA,CAAAiJ,CAAA,UAAAjJ,OAAA,CAAAiJ,CAAA;YACA,KAAA1D,SAAA,CAAAkC,IAAA,MAAAzH,OAAA,CAAAiJ,CAAA;UACA;QACA;MACA;MAAA;IACA;IACA;IACAwD,KAAA;MACA,SAAAjH,UAAA;QACA,KAAAkH,aAAA;MACA;QACA,KAAAJ,IAAA;QACA;QACA,KAAA9G,UAAA;QACA,KAAAmH,SAAA;UACA,KAAAzE,KAAA,qBAAA0E,IAAA;QACA;MACA;IACA;IACAC,QAAA;MACA,KAAA/G,WAAA;MACA,KAAAP,SAAA;MACA,KAAA+G,IAAA;MACA,KAAAK,SAAA;QACA,KAAAzE,KAAA,qBAAA0E,IAAA;MACA;IACA;IACA;IACAE,MAAA;MACA,SAAArL,MAAA;QACA;QACA,KAAAA,MAAA;QACA,KAAAiE,OAAA;QACAlH,cAAA;MACA;MACA,KAAA0J,KAAA,qBAAA6E,IAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAxH,SAAA;MACA,KAAAO,WAAA;MACA,KAAAN,UAAA;MACA,KAAA1D,UAAA;MACA,KAAA8D,QAAA;MACA,SAAAG,SAAA;QACAiH,aAAA,MAAAjH,SAAA;QACA,KAAAA,SAAA;MACA;MACA;MACA;MACA,KAAAF,QAAA;MACA,KAAAG,YAAA;MACA,KAAAmG,KAAA;IACA;IACA;IACAc,SAAA;MACA,SAAAxL,MAAA;QACA;QACA,KAAAA,MAAA;QACA,KAAAiE,OAAA;QACAlH,cAAA;MACA;MACA,KAAA0J,KAAA,qBAAA6E,IAAA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAxH,SAAA;MACA,KAAAO,WAAA;MACA,KAAAhE,UAAA;MACA,KAAA8D,QAAA;MACA,SAAAG,SAAA;QACAiH,aAAA,MAAAjH,SAAA;QACA,KAAAA,SAAA;MACA;MACA;MACA;MACA,KAAAF,QAAA;MACA,KAAAG,YAAA;IACA;IACA;IACAkH,YAAAzC,UAAA;MACA,SAAAlF,SAAA,CAAAd,QAAA,CAAAgG,UAAA;QACA;QACA,IAAA0C,WAAA,QAAA5H,SAAA,CAAA6H,SAAA,CAAAxG,IAAA,IAAAA,IAAA,KAAA6D,UAAA;QACA,IAAA4C,UAAA,QAAAvH,WAAA,CAAAqH,WAAA;QACA,KAAA5H,SAAA,CAAA+H,MAAA,CAAAH,WAAA;QACA,KAAArH,WAAA,CAAAwH,MAAA,CAAAH,WAAA;UACA1C,UAAA;UACA8B,UAAA;UACAC,EAAA,EAAAa,UAAA;UACAnO,IAAA;QACA;QACA,KAAAgJ,KAAA,qBAAAwE,aAAA,CAAAS,WAAA;MACA;IACA;IACA;IACAI,WAAA;MACA,IAAAC,CAAA,GAAAxM,MAAA,CAAAyM,gBAAA;MACA,IAAAjB,EAAA,QAAA5L,OAAA;MACA,IAAAqC,GAAA,GAAAF,QAAA,CAAAJ,aAAA,KAAA6J,EAAA;MACA,SAAA/K,MAAA;QACA;QACA,KAAAA,MAAA;QACA;UAAAmD,IAAA;UAAAC;QAAA,SAAAC,cAAA;QACA/B,QAAA,CAAAJ,aAAA,4BAAAC,KAAA,CAAAC,OAAA,aAAA+B,IAAA,UAAAC,GAAA;QACArG,cAAA;MACA;QACAD,cAAA,CAAAiO,EAAA;QACA;QACA,KAAA/K,MAAA;MACA;MACA;MACA,KAAAyG,KAAA,qBAAAwF,MAAA;MACA;MACA;IACA;IACA;IACAC,iBAAA;MACA,KAAApH,WAAA;MACA3H,eAAA,GAAAgJ,IAAA,CAAAC,GAAA;QACA,KAAA1C,WAAA,GAAA0C,GAAA,CAAA7C,IAAA;QACA,KAAAG,WAAA,CAAAwB,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAW,SAAA;YACA,KAAAlC,UAAA,GAAAvF,MAAA,CAAA8G,IAAA,CAAAY,SAAA;UACA,WAAAZ,IAAA,CAAAW,SAAA;YACA,KAAAjC,UAAA,GAAAxF,MAAA,CAAA8G,IAAA,CAAAY,SAAA;UACA;QACA;QACA,KAAAmF,SAAA;UACA,KAAAxF,mBAAA;QACA;MACA;IACA;IACA;IACAyG,mBAAA;MACA,MAAAlI,OAAA,SAAAA,OAAA;MACA,SAAAjE,MAAA;QACA,KAAA8L,UAAA;MACA;MACA,KAAAZ,SAAA;QACA,KAAAjH,OAAA,GAAAA,OAAA;MACA;IACA;IACAmI,OAAA;MACA,YAAArI,UAAA;IACA;IACA;IACAsI,MAAA,GAEA;IACA;IACAC,gBAAAC,GAAA;MACA,UAAApI,QAAA;QACA;QACA,KAAAH,UAAA,GAAAuI,GAAA;QACA,KAAA9F,KAAA,qBAAA6F,eAAA,CAAAC,GAAA;MACA;IACA;IACA;IACAC,SAAA;MACA;QACAzI,UAAA,OAAAA,UAAA;QAAA;QACAC,UAAA,OAAAA,UAAA;QAAA;QACAyI,kBAAA,OAAAhG,KAAA,qBAAAiG,qBAAA;MACA;IACA;IACA;IACAzB,cAAAb,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA/F,WAAA,CAAAwH,MAAA,CAAAzB,KAAA;QACApB,UAAA,OAAAzK,OAAA,CAAA6L,KAAA;QACAU,UAAA,OAAAvM,OAAA,CAAA6L,KAAA;QACAW,EAAA,OAAA1G,WAAA,CAAA+F,KAAA;QACA3M,IAAA;MACA;MAEA,KAAAyN,SAAA;QACA,KAAAzE,KAAA,qBAAAwE,aAAA,CAAAb,KAAA;MACA;IACA;IACA;IACAuC,QAAA;MACA;MACA,KAAApI,YAAA,GAAAqI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvO,OAAA;IACA;IACA;IACAwO,IAAA;MACA,IAAAC,KAAA;MACA;MACA,SAAA1I,SAAA;QACAiH,aAAA,CAAAyB,KAAA,CAAA1I,SAAA;QACA,KAAAA,SAAA;MACA;MACA,SAAAH,QAAA;QAAA;QACA;QACA,KAAAwI,OAAA;QACA,KAAAM,OAAA;QACA,KAAA3I,SAAA,GAAA4I,WAAA;UACA,KAAAD,OAAA;QACA,QAAA7I,QAAA;MACA;MAAA;IAGA;IACA6I,QAAApC,IAAA;MACA;MACA,IAAAsC,SAAA,QAAA5I,YAAA,CAAAsH,MAAA,SAAA7H,UAAA;MACA,SAAAwD,CAAA,MAAAA,CAAA,QAAAnD,WAAA,CAAAoD,MAAA,EAAAD,CAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAAnD,WAAA,CAAAwH,MAAA,CAAArE,CAAA;UACAwB,UAAA,EAAAmE,SAAA,CAAA3F,CAAA,KAAA2F,SAAA,CAAA3F,CAAA,kBAAA2F,SAAA,CAAA3F,CAAA;UACAsD,UAAA,EAAAqC,SAAA,CAAA3F,CAAA,KAAA2F,SAAA,CAAA3F,CAAA,kBAAA2F,SAAA,CAAA3F,CAAA;UACAuD,EAAA,OAAA1G,WAAA,CAAAmD,CAAA;UACA/J,IAAA;QACA;MACA;MACA;MACA,KAAAqG,SAAA,GAAAqJ,SAAA,CAAA/D,GAAA,CAAAjE,IAAA,IAAAA,IAAA;MACA;MACA,IAAA0F,IAAA;QACA,KAAApE,KAAA,qBAAA0E,IAAA;MACA;QACA,KAAA9G,WAAA,CAAAa,OAAA,EAAAC,IAAA,EAAAiF,KAAA;UACA,KAAA3D,KAAA,qBAAAwE,aAAA,CAAAb,KAAA;QACA;MACA;MACA;MACA;MACA,KAAA7F,YAAA,YAAAA,YAAA,KAAA4I,SAAA;IACA;IACA;IACAC,gBAAA;MACA;MACA,KAAAjJ,QAAA,SAAAA,QAAA;MACA,KAAA+G,SAAA;QACA,KAAA6B,GAAA;MACA;IACA;IACA;IACAM,kBAAA;MACA;MACA,KAAAhN,UAAA,SAAAA,UAAA;IACA;IACA;IACAiN,sBAAAC,GAAA;MACA;MACA;MACA,KAAAhJ,YAAA;MACA,KAAAkC,KAAA,qBAAA6E,IAAA;MACA,KAAAZ,KAAA,oBAAA6C,GAAA;IACA;IACA;IACAC,qBAAA,GAEA;IACAC,gBAAA;MAAAzE;IAAA;MACA,IAAA0C,WAAA,QAAA5H,SAAA,CAAA6H,SAAA,CAAAxG,IAAA,IAAAA,IAAA,KAAA6D,UAAA;MACA,IAAA0C,WAAA;QACA,IAAAE,UAAA,QAAAvH,WAAA,CAAAqH,WAAA;QACA,KAAA5H,SAAA,CAAA+H,MAAA,CAAAH,WAAA;QACA,KAAArH,WAAA,CAAAwH,MAAA,CAAAH,WAAA;UACA1C,UAAA;UACA8B,UAAA;UACAC,EAAA,EAAAa,UAAA;UACAnO,IAAA;QACA;MACA;IACA;IACA;IACAiQ,sBAAA;MAAA1E,UAAA;MAAA8B;IAAA;MACA,UAAA3G,QAAA;QACA;QACA,SAAAL,SAAA,CAAAd,QAAA,CAAAgG,UAAA;UACA;UACA,IAAA0C,WAAA,QAAA5H,SAAA,CAAA6H,SAAA,CAAAxG,IAAA,IAAAA,IAAA,KAAA6D,UAAA;UACA,IAAA4C,UAAA,QAAAvH,WAAA,CAAAqH,WAAA;UACA,KAAA5H,SAAA,CAAA+H,MAAA,CAAAH,WAAA;UACA,KAAArH,WAAA,CAAAwH,MAAA,CAAAH,WAAA;YACA1C,UAAA;YACA8B,UAAA;YACAC,EAAA,EAAAa,UAAA;YACAnO,IAAA;UACA;UACA,KAAAgJ,KAAA,qBAAAwE,aAAA,CAAAS,WAAA;QACA;UACA;UACA,IAAAiC,YAAA;UACA,IAAAC,SAAA,QAAAvJ,WAAA,CAAAwJ,KAAA,CAAA1I,IAAA,IAAAA,IAAA;UACA,IAAAyI,SAAA;YACA,IAAAE,SAAA,QAAAzJ,WAAA,CAAAsH,SAAA,CAAAxG,IAAA,KAAAA,IAAA;YACA,IAAA2I,SAAA;cACAH,YAAA,GAAAG,SAAA;YACA;cACAH,YAAA,QAAAlH,KAAA,qBAAAiG,qBAAA;YACA;UACA;YACAiB,YAAA,QAAAtJ,WAAA,CAAAsH,SAAA,CAAAxG,IAAA,IAAAA,IAAA;UACA;UACA,IAAAwI,YAAA;YACA,IAAAI,WAAA,QAAA1J,WAAA,CAAAsJ,YAAA;YACA;YACA,KAAA7J,SAAA,CAAA+H,MAAA,CAAA8B,YAAA,KAAA3E,UAAA;YACA,KAAA3E,WAAA,CAAAwH,MAAA,CAAA8B,YAAA;cACA3E,UAAA,EAAAA,UAAA;cACA8B,UAAA,EAAAA,UAAA;cACAC,EAAA,EAAAgD,WAAA;cACAtQ,IAAA;YACA;YACA,KAAAgJ,KAAA,qBAAAwE,aAAA,CAAA0C,YAAA;UACA;QACA;MACA;IAEA;IACA;IACAK,eAAAhF,UAAA,EAAAvL,IAAA;MACA,IAAA2M,KAAA,QAAA/F,WAAA,CAAAsH,SAAA,CAAAxG,IAAA,IAAAA,IAAA,kBAAA6D,UAAA;MACA,IAAAoB,KAAA;QACA;QACA,KAAA/F,WAAA,CAAA+F,KAAA,YAAA3M,IAAA;MACA;IACA;IACAV,eAAA6E,CAAA;MACA,KAAAN,QAAA,CAAA2M,kBAAA,KAAA3M,QAAA,CAAA4M,aAAA,KAAA5M,QAAA,CAAA6M,mBAAA;QACA;QACA,KAAAnO,MAAA;QACAjD,cAAA;QACA,KAAA0J,KAAA,qBAAAwF,MAAA;QACA,KAAAvB,KAAA,oBAAA1K,MAAA,OAAAJ,UAAA;MACA;IACA;IACAwO,aAAA;MACA,SAAApO,MAAA;QACA,KAAAH,cAAA,GAAAyB,QAAA,CAAAe,IAAA,CAAAI,YAAA;QACA,KAAAtC,aAAA,GAAAmB,QAAA,CAAAe,IAAA,CAAAC,WAAA;MACA;IACA;IACA;IACA+L,eAAAC,MAAA,EAAAlE,KAAA;MACA,KAAAM,KAAA,mBAAA4D,MAAA,EAAAlE,KAAA;IACA;EACA;EACAmE,QAAA;IACA,IAAAvB,KAAA;IACA,KAAAnN,cAAA,GAAAN,MAAA,CAAAC,MAAA,CAAAC,MAAA;IACA,KAAAU,aAAA,GAAAZ,MAAA,CAAAC,MAAA,CAAAF,KAAA;IACA,IAAAgC,QAAA,CAAAkN,gBAAA;MACAlN,QAAA,CAAAkN,gBAAA,2BAAAxB,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAkN,gBAAA,wBAAAxB,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAkN,gBAAA,qBAAAxB,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAkN,gBAAA,uBAAAxB,KAAA,CAAAjQ,cAAA;MACAwC,MAAA,CAAAiP,gBAAA,WAAAxB,KAAA,CAAAoB,YAAA;IACA;EACA;EACAK,cAAA;IACA,IAAAzB,KAAA;IACA,IAAA1L,QAAA,CAAAoN,mBAAA;MACApN,QAAA,CAAAoN,mBAAA,2BAAA1B,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAoN,mBAAA,wBAAA1B,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAoN,mBAAA,qBAAA1B,KAAA,CAAAjQ,cAAA;MACAuE,QAAA,CAAAoN,mBAAA,uBAAA1B,KAAA,CAAAjQ,cAAA;MACAwC,MAAA,CAAAmP,mBAAA,WAAA1B,KAAA,CAAAoB,YAAA;IACA;IACA,SAAA9J,SAAA;MACAiH,aAAA,MAAAjH,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}