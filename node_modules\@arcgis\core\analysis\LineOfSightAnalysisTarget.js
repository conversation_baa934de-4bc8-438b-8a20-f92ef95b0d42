/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import{Clonable as r}from"../core/Clonable.js";import{equalsMaybe as s}from"../core/maybe.js";import{property as t}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as e}from"../core/accessorSupport/decorators/subclass.js";import i from"../geometry/Point.js";let p=class extends r{constructor(o){super(o),this.position=null}equals(o){return s(this.position,o.position)&&s(this.intersection,o.intersection)}};o([t({type:i})],p.prototype,"position",void 0),o([t()],p.prototype,"intersection",void 0),p=o([e("esri.analysis.LineOfSightAnalysisTarget")],p);const c=p;export{c as default};
