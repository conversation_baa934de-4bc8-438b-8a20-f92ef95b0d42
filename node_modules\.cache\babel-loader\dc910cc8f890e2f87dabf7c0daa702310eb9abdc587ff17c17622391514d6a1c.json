{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"screen-adapter\"\n  }, [_c(\"div\", {\n    staticClass: \"content-wrap\",\n    style: _vm.style\n  }, [_vm._t(\"default\")], 2)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "_t", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/ScreenView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"screen-adapter\" }, [\n    _c(\n      \"div\",\n      { staticClass: \"content-wrap\", style: _vm.style },\n      [_vm._t(\"default\")],\n      2\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,cAAc;IAAEC,KAAK,EAAEJ,GAAG,CAACI;EAAM,CAAC,EACjD,CAACJ,GAAG,CAACK,EAAE,CAAC,SAAS,CAAC,CAAC,EACnB,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBP,MAAM,CAACQ,aAAa,GAAG,IAAI;AAE3B,SAASR,MAAM,EAAEO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}