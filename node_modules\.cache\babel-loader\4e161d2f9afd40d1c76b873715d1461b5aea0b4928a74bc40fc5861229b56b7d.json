{"ast": null, "code": "import videoPlayerTree from '@/components/videoTypeOption/videoPlayerTree';\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue';\nimport informationType from '@/components/videoTypeOption/informationType.vue';\nimport getVideoInfo from '@/components/videoTypeOption/getVideoInfo.vue';\n\n// 获取vuex中action文件的值\nimport { mapState } from 'vuex';\nexport default {\n  name: 'videoPlayerSteam',\n  components: {\n    videoPlayerTree,\n    singleHlsVideo,\n    informationType,\n    getVideoInfo\n  },\n  data() {\n    return {\n      winPlayer: 4,\n      allStyle: {\n        width: '42rem',\n        height: '25rem'\n      },\n      minWidth: '545px'\n    };\n  },\n  computed: {\n    ...mapState(\"action\", [\"playerList\", \"tabsList\"]),\n    ...mapState(['aspectRatio'])\n  },\n  watch: {},\n  created() {},\n  mounted() {\n    this.$store.commit(\"action/getVideoListTree\", true);\n  },\n  methods: {\n    handleStyle(newStyle) {\n      this.allStyle = {\n        ...this.allStyle,\n        ...newStyle\n      };\n    },\n    // 撒点点击单个监控播放\n    getUeVideoFun(nv) {\n      if (nv) {\n        let params = {\n          ...nv,\n          ape_id: nv.id\n        };\n        this.$store.$commit('action/getVideoPlayerList', params);\n        this.$store.commit(\"action/getVideoPlayerBox\", true);\n        this.$store.commit(\"action/getIsShowNumInitChange\", false);\n\n        // this.winPlayer = 1\n        // this.radio = nv.name\n        // this.areaTitInfo = params\n        // this.titleList = ['区域监控']\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["videoPlayerTree", "singleHlsVideo", "informationType", "getVideoInfo", "mapState", "name", "components", "data", "winPlayer", "allStyle", "width", "height", "min<PERSON><PERSON><PERSON>", "computed", "watch", "created", "mounted", "$store", "commit", "methods", "handleStyle", "newStyle", "getUeVideoFun", "nv", "params", "ape_id", "id", "$commit"], "sources": ["src/views/mainPage/components/videoPlayerSteam.vue"], "sourcesContent": ["<template>\r\n    <div class=\"videoBoxCom\">\r\n        <videoPlayerTree v-if=\"playerList.videoListTree\"></videoPlayerTree>\r\n        <singleHlsVideo v-if=\"playerList.videoPlayerBox\" :allStyle=\"allStyle\" @updateStyle=\"handleStyle\" :minWidth=\"minWidth\"></singleHlsVideo>\r\n\r\n        <informationType v-if=\"playerList.informationBox\"></informationType>\r\n        <getVideoInfo v-if=\"playerList.videoInfoFlag\"></getVideoInfo>\r\n    </div>\r\n</template>\r\n<script>\r\nimport videoPlayerTree from '@/components/videoTypeOption/videoPlayerTree'\r\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue'\r\nimport informationType from '@/components/videoTypeOption/informationType.vue'\r\nimport getVideoInfo from '@/components/videoTypeOption/getVideoInfo.vue'\r\n\r\n// 获取vuex中action文件的值\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'videoPlayerSteam',\r\n    components: {\r\n        videoPlayerTree,\r\n        singleHlsVideo,\r\n        informationType,\r\n        getVideoInfo\r\n    },\r\n    data() {\r\n        return {\r\n            winPlayer: 4,\r\n            allStyle: {\r\n                width: '42rem',\r\n                height: '25rem',\r\n            },\r\n            minWidth: '545px',\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"playerList\",\r\n            \"tabsList\"\r\n        ]),\r\n        ...mapState(['aspectRatio'])\r\n    },\r\n    watch: {\r\n       \r\n    },\r\n\r\n    created(){\r\n       \r\n    },\r\n    mounted() {\r\n        this.$store.commit(\"action/getVideoListTree\", true)\r\n       \r\n    },\r\n    methods: {\r\n        handleStyle(newStyle){\r\n            this.allStyle = { ...this.allStyle, ...newStyle };\r\n        },\r\n        // 撒点点击单个监控播放\r\n        getUeVideoFun(nv) {\r\n\r\n            if (nv) {\r\n                let params = {\r\n                    ...nv,\r\n                    ape_id: nv.id,\r\n                }\r\n                this.$store.$commit('action/getVideoPlayerList', params)\r\n\r\n                this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n                this.$store.commit(\"action/getIsShowNumInitChange\", false)\r\n\r\n                // this.winPlayer = 1\r\n                // this.radio = nv.name\r\n                // this.areaTitInfo = params\r\n                // this.titleList = ['区域监控']\r\n            }\r\n        },\r\n    },\r\n\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.videoBoxCom{\r\n    z-index: 88;\r\n   \r\n}\r\n</style>"], "mappings": "AAUA,OAAAA,eAAA;AACA,OAAAC,cAAA;AACA,OAAAC,eAAA;AACA,OAAAC,YAAA;;AAEA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,eAAA;IACAC,cAAA;IACAC,eAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,SAAA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAT,QAAA,YACA,cACA,WACA;IACA,GAAAA,QAAA;EACA;EACAU,KAAA,GAEA;EAEAC,QAAA,GAEA;EACAC,QAAA;IACA,KAAAC,MAAA,CAAAC,MAAA;EAEA;EACAC,OAAA;IACAC,YAAAC,QAAA;MACA,KAAAZ,QAAA;QAAA,QAAAA,QAAA;QAAA,GAAAY;MAAA;IACA;IACA;IACAC,cAAAC,EAAA;MAEA,IAAAA,EAAA;QACA,IAAAC,MAAA;UACA,GAAAD,EAAA;UACAE,MAAA,EAAAF,EAAA,CAAAG;QACA;QACA,KAAAT,MAAA,CAAAU,OAAA,8BAAAH,MAAA;QAEA,KAAAP,MAAA,CAAAC,MAAA;QACA,KAAAD,MAAA,CAAAC,MAAA;;QAEA;QACA;QACA;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}