{"ast": null, "code": "import axios from 'axios';\nimport store from '@/store/index.js';\nlet http = axios.create({\n  // 通用请求前缀\n  baseURL: process.env.VUE_APP_API_hz_BASE_URL,\n  timeout: 20000\n  // withCredentials: true,\n});\n// 添加请求拦截器\nhttp.interceptors.request.use(function (config) {\n  if (!config.data) {\n    config.data = true;\n  }\n  // let headers = {\n  //     \"User\": \"usercode:01\",\n  //     \"Authorization\": store.state.userVideoToken ? store.state.userVideoToken.access_token : \"null\",\n  // }\n  config.headers['Content-Type'] = 'application/json';\n  config.headers.User = 'usercode:01';\n  config.headers.Authorization = store.state.userVideoToken ? store.state.userVideoToken.access_token : 'null';\n  // config.headers = headers;\n  return config;\n}, function (error) {\n  // 对请求错误做些什么\n  return Promise.reject(error);\n});\n\n// 添加响应拦截器\nhttp.interceptors.response.use(function (res) {\n  // 2xx 范围内的状态码都会触发该函数。\n  // 对响应数据做点什么\n  // const code = res.data.code;\n\n  // 判断后端返回的错误信息\n  // if (code === 500) {\n  //     return Promise.reject(new Error(msg))\n  // } else if (code === 601) {\n  //     return Promise.reject(new Error(msg))\n  // } else if (code !== 200) {\n  //     return Promise.reject('error')\n  // } else {\n  //     return Promise.resolve(res)\n  // }\n  return res;\n}, function (error) {\n  // 超出 2xx 范围的状态码都会触发该函数。\n  // 对响应错误做点什么\n  // let { message } = error;\n  // if (message == \"Network Error\") {\n  //     message = \"后端接口连接异常\";\n  // } else if (message.includes(\"timeout\")) {\n  //     message = \"系统接口请求超时\";\n  // } else if (message.includes(\"Request failed with status code\")) {\n  //     message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\n  // }\n  return error;\n});\nexport default http;", "map": {"version": 3, "names": ["axios", "store", "http", "create", "baseURL", "process", "env", "VUE_APP_API_hz_BASE_URL", "timeout", "interceptors", "request", "use", "config", "data", "headers", "User", "Authorization", "state", "userVideoToken", "access_token", "error", "Promise", "reject", "response", "res"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/api/getHZVideo.js"], "sourcesContent": ["import axios from 'axios';\r\nimport store from '@/store/index.js';\r\n\r\nlet http = axios.create({\r\n    // 通用请求前缀\r\n    baseURL: process.env.VUE_APP_API_hz_BASE_URL,\r\n    timeout: 20000,\r\n    // withCredentials: true,\r\n});\r\n// 添加请求拦截器\r\nhttp.interceptors.request.use(\r\n    function (config) {\r\n        if (!config.data) {\r\n            config.data = true\r\n        }\r\n        // let headers = {\r\n        //     \"User\": \"usercode:01\",\r\n        //     \"Authorization\": store.state.userVideoToken ? store.state.userVideoToken.access_token : \"null\",\r\n        // }\r\n        config.headers['Content-Type'] = 'application/json';\r\n        config.headers.User = 'usercode:01'; \r\n        config.headers.Authorization = store.state.userVideoToken ? store.state.userVideoToken.access_token:'null';\r\n        // config.headers = headers;\r\n        return config;\r\n    },\r\n    function (error) {\r\n        // 对请求错误做些什么\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// 添加响应拦截器\r\nhttp.interceptors.response.use(\r\n    function (res) {\r\n        // 2xx 范围内的状态码都会触发该函数。\r\n        // 对响应数据做点什么\r\n        // const code = res.data.code;\r\n\r\n        // 判断后端返回的错误信息\r\n        // if (code === 500) {\r\n        //     return Promise.reject(new Error(msg))\r\n        // } else if (code === 601) {\r\n        //     return Promise.reject(new Error(msg))\r\n        // } else if (code !== 200) {\r\n        //     return Promise.reject('error')\r\n        // } else {\r\n        //     return Promise.resolve(res)\r\n        // }\r\n        return res;\r\n\r\n    },\r\n    function (error) {\r\n        // 超出 2xx 范围的状态码都会触发该函数。\r\n        // 对响应错误做点什么\r\n        // let { message } = error;\r\n        // if (message == \"Network Error\") {\r\n        //     message = \"后端接口连接异常\";\r\n        // } else if (message.includes(\"timeout\")) {\r\n        //     message = \"系统接口请求超时\";\r\n        // } else if (message.includes(\"Request failed with status code\")) {\r\n        //     message = \"系统接口\" + message.substr(message.length - 3) + \"异常\";\r\n        // }\r\n        return error;\r\n    }\r\n)\r\n\r\nexport default http;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,IAAIC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAAC;EACpB;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,uBAAuB;EAC5CC,OAAO,EAAE;EACT;AACJ,CAAC,CAAC;AACF;AACAN,IAAI,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzB,UAAUC,MAAM,EAAE;EACd,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;IACdD,MAAM,CAACC,IAAI,GAAG,IAAI;EACtB;EACA;EACA;EACA;EACA;EACAD,MAAM,CAACE,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EACnDF,MAAM,CAACE,OAAO,CAACC,IAAI,GAAG,aAAa;EACnCH,MAAM,CAACE,OAAO,CAACE,aAAa,GAAGf,KAAK,CAACgB,KAAK,CAACC,cAAc,GAAGjB,KAAK,CAACgB,KAAK,CAACC,cAAc,CAACC,YAAY,GAAC,MAAM;EAC1G;EACA,OAAOP,MAAM;AACjB,CAAC,EACD,UAAUQ,KAAK,EAAE;EACb;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAlB,IAAI,CAACO,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC1B,UAAUa,GAAG,EAAE;EACX;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOA,GAAG;AAEd,CAAC,EACD,UAAUJ,KAAK,EAAE;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK;AAChB,CACJ,CAAC;AAED,eAAelB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}