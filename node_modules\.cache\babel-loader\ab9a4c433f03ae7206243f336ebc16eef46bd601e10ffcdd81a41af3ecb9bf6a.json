{"ast": null, "code": "import flvjs from 'flv.js';\nimport rslPlayer from \"@/components/rslPlayer/components/new-dialog.vue\";\nimport { getCameraList } from '@/api/userMenu.js';\nimport canvasPlayerSingle from '@/components/rslPlayer/components/canvasPlayerSingle.vue';\nimport areaVideoPlay from '@/components/hlvJsVideo/hlsCreated.vue';\nimport videoTypeOption from '@/components/videoTypeOption/videoTypeOption.vue';\nimport { videoEncoderList } from '@/api/index.js';\nexport default {\n  name: \"allVideoStream\",\n  components: {\n    rslPlayer,\n    canvasPlayerSingle,\n    areaVideoPlay,\n    videoTypeOption\n  },\n  props: {\n    devicesList: {\n      type: Array,\n      default: () => []\n    },\n    hlsSingleVideo: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    },\n    currentPage: {\n      type: Number,\n      default: 10\n    }\n  },\n  data() {\n    return {\n      player: null,\n      flvPlayer: null,\n      url: \"\",\n      count: 1,\n      // 当前点击标识\n      flvPlayerList: [],\n      list: [],\n      dialogVisible: true,\n      radio: '',\n      checkList: [],\n      changeLampPost: false,\n      titleList: ['高空监控', '区域监控'],\n      isFalseList: ['绿地大道裕华园10号楼-高空', '滨江雅苑6#楼顶1-高空', '青青家园2#楼顶东南角-高空', '滨江御花园15#楼顶-高空', '可逸兰亭-高空', '万科南区79#楼顶-高空', '滨江雅苑1#楼顶-高空', '嘉宝梦&叔兜晨6#楼顶-高空', '梅苑里-高空', '万科花园南区107#楼顶-高空', '两岸新天地4#楼顶-高空', '浦岸花园2号楼-高空', '德馨嘉苑17#楼顶-高空', '海峡两岸4#楼顶北-高空', '金捷路嘉宝梦之晨7号楼-高空', '滨江雅苑6#楼顶2-高空', '象屿怡园2#楼顶-高空', '德馨嘉苑3号楼楼顶西南角-高空', '花桥裕花园7号楼楼顶东北角-高空', '青青家园4号楼楼顶西北角-高空', '可逸兰亭3号楼楼顶西南角-高空', '中科创新广场1幢A座西北角-高空', '象屿嘉园1号楼楼顶东南角-高空'],\n      selectedTitle: '区域监控',\n      flvCount: 6,\n      currentWin: 4,\n      //高空监控几路\n      areaTitInfo: null,\n      totalVideoList: 0,\n      isSearch: false,\n      videoTypeName: '',\n      pageSizeNum: 5,\n      winPlayer: 6,\n      //区域监控几路\n      isAction: false\n    };\n  },\n  watch: {\n    dialogVisible(newVal) {\n      if (!newVal) {\n        this.selectedTitle = '';\n        this.loopShift();\n        this.$parent.AuthorityValue = '';\n        if (this.selectedTitle == '高空监控') this.$refs.playerShow.closeBaseVideo();\n      }\n    },\n    // 监控类型切换\n    selectedTitle(nv, oldV) {\n      if (oldV == '高空监控') {\n        this.$refs.playerShow.closeBaseVideo();\n      } else if (oldV == '水务监控') {}\n      this.init(nv);\n    }\n  },\n  computed: {\n    // 弹出窗的id 属性;\n    modalId() {\n      return `video-container`;\n    }\n  },\n  mounted() {\n    this.pageSizeNum = this.currentPage;\n    this.$nextTick(() => {\n      this.init(this.selectedTitle);\n      // this.$refs.areaVideoPlay.getDeviceListInfo(this.currentPage)\n    });\n  },\n  beforeDestroy() {},\n  methods: {\n    // 监控类型\n    handleCommand(command) {\n      this.selectedTitle = command;\n    },\n    // 水务监控数据\n    async initPlayerList() {\n      await getCameraList().then(res => {\n        this.list = res.data.extra;\n        this.radio = this.list[0].stationName;\n        this.url = this.list[0].flv;\n      });\n    },\n    // 高空监控数据\n    initSkyData() {\n      this.list.forEach(item => {\n        item.disabled = true;\n      });\n      this.isFalseList.forEach(val => {\n        this.list.forEach(item => {\n          Object.assign(item, {\n            stationName: item.cameraName\n          });\n          if (val == item.cameraName) {\n            item.disabled = false;\n          }\n        });\n      });\n    },\n    // 区域监控数据\n    getPlayerInfoFun(val) {\n      this.list = [];\n      this.list = val.data;\n      // this.list.map(item => {\n      //     item.stationName = item.name\n      // })\n\n      this.totalVideoList = val.paging.total_num;\n    },\n    // \n    init(val) {\n      switch (val) {\n        case '水务监控':\n          this.list = [];\n          this.initPlayerList();\n          setTimeout(() => {\n            this.createVideo();\n          }, 500);\n          break;\n        case '高空监控':\n          this.list = [];\n          if (this.devicesList.length != 0) {\n            this.list = this.devicesList;\n            this.initSkyData();\n            let selectVideo = this.list[0];\n            setTimeout(() => {\n              this.$refs.playerShow.baseVideo(selectVideo.cameraCode);\n              this.radio = selectVideo.stationName;\n            }, 500);\n            this.loopShift();\n          }\n          break;\n        case '区域监控':\n          this.loopShift();\n          this.$nextTick(() => {\n            this.$refs.areaVideoPlay.getDeviceListInfo(this.currentPage);\n          });\n          break;\n        default:\n      }\n    },\n    //   左侧菜单按钮\n    clickHandleItem(data, index) {\n      switch (this.selectedTitle) {\n        case '水务监控':\n          this.url = data.flv;\n          if (this.flvPlayerList.length > 5) {\n            this.destoryVideo(this.flvPlayerList[0]);\n            this.flvPlayerList.shift();\n          }\n          this.$nextTick(() => {\n            this.createVideo();\n          });\n          this.count > 5 ? this.count = 1 : this.count++;\n          break;\n        case '高空监控':\n          this.$refs.playerShow.addBaseVideo(data.cameraCode);\n          break;\n        case '区域监控':\n          this.areaTitInfo = data;\n          break;\n        default:\n      }\n    },\n    // 停止水务监控播放\n    loopShift() {\n      if (this.flvPlayerList.length > 0) {\n        for (var i of this.flvPlayerList) {\n          this.destoryVideo(i);\n        }\n        this.flvPlayerList = [];\n        this.count = 1;\n      }\n    },\n    createVideo() {\n      if (flvjs.isSupported()) {\n        var videoElement = document.getElementById(\"videoElement\" + this.count);\n        this.flvPlayer = flvjs.createPlayer({\n          type: \"flv\",\n          isLive: true,\n          hasAudio: false,\n          url: this.url\n        }, {\n          enableWorker: false,\n          //不启用分离线程\n          enableStashBuffer: false,\n          //关闭IO隐藏缓冲区\n          reuseRedirectedURL: false,\n          //重用301/302重定向url，用于随后的请求，如查找、重新连接等。\n          autoCleanupSourceBuffer: true //自动清除缓存\n        });\n        this.flvPlayer.attachMediaElement(videoElement);\n        if (this.url != \"\" && this.url != null) {\n          this.flvPlayer.load();\n          this.flvPlayer.on(flvjs.Events.SCRIPTDATA_ARRIVED, res => {\n            // this.flvPlayer.play()\n          });\n        }\n      }\n\n      //定时方法是为了用户离开页面视频是实时播放的,暂停按钮无用\n      setTimeout(() => {\n        if (videoElement.buffered.length > 0) {\n          const end = videoElement.buffered.end(0); // 视频结尾时间\n          const current = videoElement.currentTime; //  视频当前时间\n          const diff = end - current; // 相差时间\n          // console.log(diff);\n          const diffCritical = 4; // 这里设定了超过4秒以上就进行跳转\n          const diffSpeedUp = 1; // 这里设置了超过1秒以上则进行视频加速播放\n          const maxPlaybackRate = 4; // 自定义设置允许的最大播放速度\n          let playbackRate = 1.0; // 播放速度\n          if (diff > diffCritical) {\n            //  this.flvPlayer.load();\n            // console.log(\"相差超过4秒，进行跳转\");\n            videoElement.currentTime = end - 1.5;\n            playbackRate = Math.max(1, Math.min(diffCritical, 16));\n          } else if (diff > diffSpeedUp) {\n            // console.log(\"相差超过1秒，进行加速\");\n            playbackRate = Math.max(1, Math.min(diff, maxPlaybackRate, 16));\n          }\n          videoElement.playbackRate = playbackRate;\n          if (videoElement.paused || videoElement.ended) {\n            setTimeout(() => {\n              // videoElement.play()\n            }, 200);\n          } else {\n            videoElement.pause();\n          }\n        }\n        if (videoElement.buffered.length) {\n          let end = this.flvPlayer.buffered.end(0); //获取当前buffered值\n          let diff = end - this.flvPlayer.currentTime; //获取buffered与currentTime的差值\n          if (diff >= 0.5) {\n            // 如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定\n            this.flvPlayer.currentTime = this.flvPlayer.buffered.end(0); //手动跳帧\n          }\n        }\n      }, 500);\n      this.flvPlayer.on(flvjs.Events.ERROR, (errType, errDetail) => {\n        if (this.flvPlayer) {\n          this.reloadVideo(this.flvPlayer);\n        }\n      });\n      this.flvPlayerList.push(this.flvPlayer);\n    },\n    reloadVideo(flvPlayer) {\n      this.destoryVideo(flvPlayer);\n      this.createVideo();\n    },\n    destoryVideo(flvPlayer) {\n      flvPlayer.pause();\n      flvPlayer.unload();\n      flvPlayer.detachMediaElement();\n      flvPlayer.destroy();\n      flvPlayer = null;\n    },\n    // 1路和4路切换\n    handleChangeWin(num) {\n      this.currentWin = num;\n    },\n    // 区域监控分页显示, 页数变动\n    handleCurrentChange(val) {\n      if (!this.isSearch) {\n        let params = {\n          type: '区域监控',\n          flag: false\n        };\n        // this.$eventBus.$emit('senTxtToUe', params)\n        this.pageSizeNum = val;\n        this.$emit('changePageSize', val);\n        this.$refs.areaVideoPlay.getDeviceListInfo(val);\n      } else {\n        this.getTypeVideoList(this.videoTypeName, val);\n      }\n    },\n    // 区域监控监控类型选择\n    getKeyWords(val) {\n      if (!val) {\n        this.isSearch = false;\n        this.init(this.selectedTitle);\n      } else {\n        this.isSearch = true;\n        this.videoTypeName = val;\n        this.getTypeVideoList(val, 1);\n      }\n    },\n    // 搜索类型获取区域监控\n    getTypeVideoList(val, pageNum) {\n      videoEncoderList(val, pageNum).then(res => {\n        this.list = res.data.extra.records;\n        this.totalVideoList = res.data.extra.total;\n      });\n    },\n    // 撒点点击单个监控播放\n    getUeVideoFun(nv) {\n      if (nv != {}) {\n        let params = {\n          ...nv,\n          ape_id: nv.id\n        };\n        this.winPlayer = 1;\n        this.radio = nv.name;\n        this.areaTitInfo = params;\n        this.titleList = ['区域监控'];\n      }\n    },\n    changeIsAction(val) {\n      this.isAction = val;\n    },\n    ChangeWinPlayer(val) {\n      this.winPlayer = val;\n    }\n  }\n};", "map": {"version": 3, "names": ["flvjs", "rslPlayer", "getCameraList", "canvasPlayerSingle", "areaVideoPlay", "videoTypeOption", "videoEncoderList", "name", "components", "props", "devicesList", "type", "Array", "default", "hlsSingleVideo", "Object", "currentPage", "Number", "data", "player", "flvPlayer", "url", "count", "flvPlayerList", "list", "dialogVisible", "radio", "checkList", "changeLampPost", "titleList", "isFalseList", "<PERSON><PERSON><PERSON><PERSON>", "flvCount", "currentWin", "areaTitInfo", "totalVideoList", "isSearch", "videoTypeName", "pageSizeNum", "winPlayer", "isAction", "watch", "newVal", "loopShift", "$parent", "AuthorityValue", "$refs", "playerShow", "closeBaseVideo", "nv", "oldV", "init", "computed", "modalId", "mounted", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handleCommand", "command", "initPlayerList", "then", "res", "extra", "stationName", "flv", "initSkyData", "for<PERSON>ach", "item", "disabled", "val", "assign", "cameraName", "getPlayerInfoFun", "paging", "total_num", "setTimeout", "createVideo", "length", "selectVideo", "baseVideo", "cameraCode", "getDeviceListInfo", "clickHandleItem", "index", "destoryVideo", "shift", "addBaseVideo", "i", "isSupported", "videoElement", "document", "getElementById", "createPlayer", "isLive", "hasAudio", "enableWorker", "enableStashBuffer", "reuseRedirectedURL", "autoCleanupSourceBuffer", "attachMediaElement", "load", "on", "Events", "SCRIPTDATA_ARRIVED", "buffered", "end", "current", "currentTime", "diff", "diffCritical", "diffSpeedUp", "maxPlaybackRate", "playbackRate", "Math", "max", "min", "paused", "ended", "pause", "ERROR", "errType", "errDetail", "reloadVideo", "push", "unload", "detachMediaElement", "destroy", "handleChangeWin", "num", "handleCurrentChange", "params", "flag", "$emit", "getTypeVideoList", "getKeyWords", "pageNum", "records", "total", "getUeVideoFun", "ape_id", "id", "changeIsAction", "ChangeWinPlayer"], "sources": ["src/views/mainPage/components/allVideoStream.vue"], "sourcesContent": ["<template>\r\n    <div class=\"flvDaLog\">\r\n        <el-dialog class=\"videoDialog\" :visible.sync=\"dialogVisible\" width=\"55%\" append-to-body>\r\n            <div class=\"titleList\">\r\n                <el-dropdown trigger=\"click\" @command=\"handleCommand\">\r\n                    <span class=\"el-dropdown-link\">\r\n                        {{ selectedTitle }}<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                    </span>\r\n                    <el-dropdown-menu slot=\"dropdown\">\r\n                        <el-dropdown-item v-for=\"(item, index) of  titleList \" :key=\"index\" :command=\"item\">{{ item\r\n                            }}</el-dropdown-item>\r\n                    </el-dropdown-menu>\r\n                </el-dropdown>\r\n            </div>\r\n            <div class=\"videoCon\">\r\n                <div class=\"leftPlaces\" v-if=\"selectedTitle != '区域监控'\">\r\n                    <el-radio v-for=\"( item, index ) of  list \" :key=\"index\" v-model=\"radio\" :label=\"item.stationName\"\r\n                        :class=\"radio == item.stationName ? 'radioAction' : ''\"\r\n                        :disabled=\"selectedTitle == '高空监控' ? item.disabled : false\" @input=\"clickHandleItem(item)\">\r\n                        <span>{{ item.stationName }}</span>\r\n                    </el-radio>\r\n                </div>\r\n                <div class=\"leftPlaces\" v-else>\r\n                    <div class=\"searchType\">\r\n                        <videoTypeOption @sendKeyWords=\"getKeyWords\"></videoTypeOption>\r\n\r\n                    </div>\r\n                    <el-radio v-if=\"!isSearch\" v-for=\"( item, index ) of  list \" :key=\"index\" v-model=\"radio\"\r\n                        :label=\"item.name\" :class=\"radio == item.name ? 'radioAction' : ''\"\r\n                        @input=\"clickHandleItem(item)\" :disabled=\"item.is_online == 1 ? false : true\">\r\n                        <span>{{ item.name }}</span>\r\n                    </el-radio>\r\n                    <el-radio v-if=\"isSearch\" v-for=\"( item, index ) of  list \" :key=\"index\" v-model=\"radio\"\r\n                        :label=\"item.deviceName\" :class=\"radio == item.deviceName ? 'radioAction' : ''\"\r\n                        @input=\"clickHandleItem(item)\" :disabled=\"item.devicePort == 1 ? false : true\">\r\n                        <span>{{ item.deviceName }}</span>\r\n                    </el-radio>\r\n                    <div class=\"search\">\r\n                        <div class=\"inputTxt\" style=\"width: 60%;\">\r\n                            <div class=\"pageSelect\">\r\n                                <el-pagination :current-page.sync=\"pageSizeNum\" :page-size=\"10\"\r\n                                    layout=\"total, prev, pager, next\" :total=\"totalVideoList\" :pager-count=\"5\" small\r\n                                    @current-change=\"handleCurrentChange\">\r\n                                </el-pagination>\r\n\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n\r\n                <!-- 水务监控 -->\r\n                <!-- <div class=\"rightVideoShow\" v-if=\"selectedTitle == '水务监控'\">\r\n                    <div class=\"watervideoShowMany\" v-for=\"( i, index ) of  flvCount \">\r\n                        <video :key=\"index\" autoplay controls muted :id=\"'videoElement' + i\"></video>\r\n                    </div>\r\n                </div> -->\r\n\r\n                <!-- 高空监控 -->\r\n                <div class=\"canvas-player-list-container rightVideoShow\" v-if=\"selectedTitle == '高空监控'\" :id=\"modalId\">\r\n                    <div class=\"numInitChange\">\r\n                        <span @click=\"handleChangeWin(1)\" :class=\"{ 'selectedSpn': currentWin == 1 }\">1路</span>\r\n                        <span @click=\"handleChangeWin(4)\" :class=\"{ 'selectedSpn': currentWin == 4 }\">4路</span>\r\n                        <span @click=\"handleChangeWin(9)\" :class=\"{ 'selectedSpn': currentWin == 9 }\">9路</span>\r\n                    </div>\r\n                    <div class=\"videoList\">\r\n                        <canvas-player-single ref=\"playerShow\" :list=\"list\" :currentWin=\"currentWin\" :modalId=\"modalId\"\r\n                            @handleDblclick=\"handleChangeWin\"></canvas-player-single>\r\n\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 区域监控(华智) -->\r\n                <div class=\"areaRightVideo rightVideoShow\" v-if=\"selectedTitle == '区域监控'\">\r\n                    <div class=\"numInitChange\">\r\n                        <span @click=\"ChangeWinPlayer(1)\" :class=\"{ 'selectedSpn': winPlayer == 1 }\">1路</span>\r\n                        <span @click=\"ChangeWinPlayer(4)\" :class=\"{ 'selectedSpn': winPlayer == 4 }\">4路</span>\r\n                        <span @click=\"ChangeWinPlayer(6)\" :class=\"{ 'selectedSpn': winPlayer == 6 }\">6路</span>\r\n                    </div>\r\n                    <areaVideoPlay ref=\"areaVideoPlay\" @selectAreaPlayer=\"getPlayerInfoFun\" \r\n                    @sendIsAction = \"changeIsAction\"\r\n                    :areaTitInfo=\"areaTitInfo\"\r\n                        :winPlayer=\"winPlayer\"\r\n                        :isAction = isAction\r\n                        >\r\n                    </areaVideoPlay>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport flvjs from 'flv.js'\r\nimport rslPlayer from \"@/components/rslPlayer/components/new-dialog.vue\";\r\nimport { getCameraList } from '@/api/userMenu.js'\r\nimport canvasPlayerSingle from '@/components/rslPlayer/components/canvasPlayerSingle.vue'\r\nimport areaVideoPlay from '@/components/hlvJsVideo/hlsCreated.vue'\r\nimport videoTypeOption from '@/components/videoTypeOption/videoTypeOption.vue'\r\nimport { videoEncoderList } from '@/api/index.js'\r\nexport default {\r\n    name: \"allVideoStream\",\r\n    components: {\r\n        rslPlayer,\r\n        canvasPlayerSingle,\r\n        areaVideoPlay,\r\n        videoTypeOption\r\n    },\r\n    props: {\r\n        devicesList: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        hlsSingleVideo: {\r\n            type: Object,\r\n            default: () => {\r\n                return {}\r\n            }\r\n        },\r\n        currentPage: {\r\n            type: Number,\r\n            default: 10\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            player: null,\r\n            flvPlayer: null,\r\n            url: \"\",\r\n            count: 1, // 当前点击标识\r\n            flvPlayerList: [],\r\n            list: [],\r\n            dialogVisible: true,\r\n            radio: '',\r\n            checkList: [],\r\n            changeLampPost: false,\r\n            titleList: ['高空监控', '区域监控'],\r\n            isFalseList: ['绿地大道裕华园10号楼-高空', '滨江雅苑6#楼顶1-高空', '青青家园2#楼顶东南角-高空', '滨江御花园15#楼顶-高空', '可逸兰亭-高空', '万科南区79#楼顶-高空', '滨江雅苑1#楼顶-高空', '嘉宝梦&叔兜晨6#楼顶-高空', '梅苑里-高空', '万科花园南区107#楼顶-高空', '两岸新天地4#楼顶-高空', '浦岸花园2号楼-高空', '德馨嘉苑17#楼顶-高空', '海峡两岸4#楼顶北-高空', '金捷路嘉宝梦之晨7号楼-高空', '滨江雅苑6#楼顶2-高空', '象屿怡园2#楼顶-高空', '德馨嘉苑3号楼楼顶西南角-高空', '花桥裕花园7号楼楼顶东北角-高空', '青青家园4号楼楼顶西北角-高空', '可逸兰亭3号楼楼顶西南角-高空', '中科创新广场1幢A座西北角-高空', '象屿嘉园1号楼楼顶东南角-高空'],\r\n            selectedTitle: '区域监控',\r\n            flvCount: 6,\r\n            currentWin: 4, //高空监控几路\r\n            areaTitInfo: null,\r\n            totalVideoList: 0,\r\n            isSearch: false,\r\n            videoTypeName: '',\r\n            pageSizeNum: 5,\r\n            winPlayer: 6,//区域监控几路\r\n            isAction:false,\r\n        }\r\n    },\r\n    watch: {\r\n     \r\n        dialogVisible(newVal) {\r\n        \r\n            if (!newVal) {\r\n                this.selectedTitle = ''\r\n                this.loopShift();\r\n                this.$parent.AuthorityValue = ''\r\n\r\n                if (this.selectedTitle == '高空监控') this.$refs.playerShow.closeBaseVideo();\r\n            }\r\n        },\r\n\r\n        // 监控类型切换\r\n        selectedTitle(nv, oldV) {\r\n            if (oldV == '高空监控') {\r\n                this.$refs.playerShow.closeBaseVideo()\r\n            } else if (oldV == '水务监控') {\r\n            }\r\n            this.init(nv);\r\n\r\n        },\r\n\r\n\r\n\r\n    },\r\n    computed: {\r\n        // 弹出窗的id 属性;\r\n        modalId() {\r\n            return `video-container`;\r\n        }\r\n    },\r\n\r\n\r\n\r\n    mounted() {\r\n\r\n        this.pageSizeNum = this.currentPage\r\n        this.$nextTick(() => {\r\n            this.init(this.selectedTitle)\r\n            // this.$refs.areaVideoPlay.getDeviceListInfo(this.currentPage)\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 监控类型\r\n        handleCommand(command) {\r\n            this.selectedTitle = command\r\n        },\r\n        // 水务监控数据\r\n        async initPlayerList() {\r\n            await getCameraList().then(res => {\r\n                this.list = res.data.extra;\r\n                this.radio = this.list[0].stationName;\r\n                this.url = this.list[0].flv;\r\n\r\n            })\r\n        },\r\n        // 高空监控数据\r\n        initSkyData() {\r\n            this.list.forEach(item => {\r\n                item.disabled = true;\r\n            })\r\n            this.isFalseList.forEach(val => {\r\n                this.list.forEach(item => {\r\n                    Object.assign(item, { stationName: item.cameraName })\r\n                    if (val == item.cameraName) {\r\n                        item.disabled = false;\r\n                    }\r\n                })\r\n            })\r\n        },\r\n        // 区域监控数据\r\n        getPlayerInfoFun(val) {\r\n            this.list = []\r\n            this.list = val.data\r\n            // this.list.map(item => {\r\n            //     item.stationName = item.name\r\n            // })\r\n\r\n            this.totalVideoList = val.paging.total_num\r\n        },\r\n        // \r\n        init(val) {\r\n\r\n            switch (val) {\r\n                case '水务监控':\r\n                    this.list = []\r\n                    this.initPlayerList()\r\n                    setTimeout(() => {\r\n                        this.createVideo()\r\n                    }, 500)\r\n                    break;\r\n                case '高空监控':\r\n                    this.list = []\r\n                    if (this.devicesList.length != 0) {\r\n                        this.list = this.devicesList\r\n                        this.initSkyData()\r\n                        let selectVideo = this.list[0]\r\n                        setTimeout(() => {\r\n                            this.$refs.playerShow.baseVideo(selectVideo.cameraCode)\r\n                            this.radio = selectVideo.stationName;\r\n                        }, 500)\r\n                        this.loopShift()\r\n                    }\r\n\r\n                    break;\r\n                case '区域监控':\r\n                    this.loopShift()\r\n                    this.$nextTick(() => {\r\n                        this.$refs.areaVideoPlay.getDeviceListInfo(this.currentPage)\r\n\r\n                    })\r\n\r\n                    break;\r\n\r\n                default:\r\n            }\r\n\r\n        },\r\n        //   左侧菜单按钮\r\n        clickHandleItem(data, index) {\r\n            switch (this.selectedTitle) {\r\n                case '水务监控':\r\n                    this.url = data.flv;\r\n                    if (this.flvPlayerList.length > 5) {\r\n                        this.destoryVideo(this.flvPlayerList[0])\r\n                        this.flvPlayerList.shift();\r\n                    }\r\n                    this.$nextTick(() => {\r\n                        this.createVideo();\r\n                    })\r\n                    this.count > 5 ? (this.count = 1) : this.count++;\r\n                    break;\r\n                case '高空监控':\r\n                    this.$refs.playerShow.addBaseVideo(data.cameraCode)\r\n                    break;\r\n                case '区域监控':\r\n                    this.areaTitInfo = data\r\n                    break\r\n                default:\r\n            }\r\n\r\n        },\r\n\r\n        // 停止水务监控播放\r\n        loopShift() {\r\n            if (this.flvPlayerList.length > 0) {\r\n                for (var i of this.flvPlayerList) {\r\n                    this.destoryVideo(i)\r\n                }\r\n                this.flvPlayerList = []\r\n                this.count = 1\r\n            }\r\n        },\r\n\r\n        createVideo() {\r\n            if (flvjs.isSupported()) {\r\n\r\n                var videoElement = document.getElementById(\"videoElement\" + this.count);\r\n                this.flvPlayer = flvjs.createPlayer(\r\n                    {\r\n                        type: \"flv\",\r\n                        isLive: true,\r\n                        hasAudio: false,\r\n                        url: this.url\r\n                    },\r\n                    {\r\n                        enableWorker: false, //不启用分离线程\r\n                        enableStashBuffer: false, //关闭IO隐藏缓冲区\r\n                        reuseRedirectedURL: false, //重用301/302重定向url，用于随后的请求，如查找、重新连接等。\r\n                        autoCleanupSourceBuffer: true //自动清除缓存\r\n                    }\r\n                );\r\n\r\n                this.flvPlayer.attachMediaElement(videoElement);\r\n                if (this.url != \"\" && this.url != null) {\r\n                    this.flvPlayer.load();\r\n\r\n                    this.flvPlayer.on(flvjs.Events.SCRIPTDATA_ARRIVED, (res) => {\r\n                        // this.flvPlayer.play()\r\n                    })\r\n                }\r\n            }\r\n\r\n            //定时方法是为了用户离开页面视频是实时播放的,暂停按钮无用\r\n            setTimeout(() => {\r\n                if (videoElement.buffered.length > 0) {\r\n                    const end = videoElement.buffered.end(0); // 视频结尾时间\r\n                    const current = videoElement.currentTime; //  视频当前时间\r\n                    const diff = end - current; // 相差时间\r\n                    // console.log(diff);\r\n                    const diffCritical = 4; // 这里设定了超过4秒以上就进行跳转\r\n                    const diffSpeedUp = 1; // 这里设置了超过1秒以上则进行视频加速播放\r\n                    const maxPlaybackRate = 4; // 自定义设置允许的最大播放速度\r\n                    let playbackRate = 1.0; // 播放速度\r\n                    if (diff > diffCritical) {\r\n                        //  this.flvPlayer.load();\r\n                        // console.log(\"相差超过4秒，进行跳转\");\r\n                        videoElement.currentTime = end - 1.5;\r\n                        playbackRate = Math.max(1, Math.min(diffCritical, 16));\r\n                    } else if (diff > diffSpeedUp) {\r\n\r\n\r\n                        // console.log(\"相差超过1秒，进行加速\");\r\n                        playbackRate = Math.max(1, Math.min(diff, maxPlaybackRate, 16));\r\n                    }\r\n                    videoElement.playbackRate = playbackRate;\r\n                    if (videoElement.paused || videoElement.ended) {\r\n                        setTimeout(() => {\r\n                            // videoElement.play()\r\n                        }, 200)\r\n\r\n                    } else {\r\n                        videoElement.pause();\r\n                    }\r\n                }\r\n                if (videoElement.buffered.length) {\r\n                    let end = this.flvPlayer.buffered.end(0);//获取当前buffered值\r\n                    let diff = end - this.flvPlayer.currentTime;//获取buffered与currentTime的差值\r\n                    if (diff >= 0.5) {\r\n\r\n                        // 如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定\r\n                        this.flvPlayer.currentTime = this.flvPlayer.buffered.end(0);//手动跳帧\r\n                    }\r\n                }\r\n            }, 500);\r\n\r\n            this.flvPlayer.on(flvjs.Events.ERROR, (errType, errDetail) => {\r\n                if (this.flvPlayer) {\r\n                    this.reloadVideo(this.flvPlayer);\r\n                }\r\n            });\r\n            this.flvPlayerList.push(this.flvPlayer);\r\n        },\r\n        reloadVideo(flvPlayer) {\r\n            this.destoryVideo(flvPlayer);\r\n            this.createVideo();\r\n        },\r\n\r\n        destoryVideo(flvPlayer) {\r\n            flvPlayer.pause();\r\n            flvPlayer.unload();\r\n            flvPlayer.detachMediaElement();\r\n            flvPlayer.destroy();\r\n            flvPlayer = null;\r\n\r\n        },\r\n\r\n\r\n        // 1路和4路切换\r\n        handleChangeWin(num) {\r\n            this.currentWin = num\r\n        },\r\n        // 区域监控分页显示, 页数变动\r\n        handleCurrentChange(val) {\r\n            if (!this.isSearch) {\r\n                let params = {\r\n                    type: '区域监控',\r\n                    flag: false\r\n                }\r\n                // this.$eventBus.$emit('senTxtToUe', params)\r\n                this.pageSizeNum = val\r\n                this.$emit('changePageSize', val)\r\n                this.$refs.areaVideoPlay.getDeviceListInfo(val)\r\n            } else {\r\n                this.getTypeVideoList(this.videoTypeName, val)\r\n            }\r\n\r\n        },\r\n        // 区域监控监控类型选择\r\n        getKeyWords(val) {\r\n            if (!val) {\r\n                this.isSearch = false\r\n                this.init(this.selectedTitle)\r\n\r\n            } else {\r\n                this.isSearch = true;\r\n                this.videoTypeName = val;\r\n                this.getTypeVideoList(val, 1)\r\n            }\r\n\r\n        },\r\n        // 搜索类型获取区域监控\r\n        getTypeVideoList(val, pageNum) {\r\n            videoEncoderList(val, pageNum).then(res => {\r\n                this.list = res.data.extra.records \r\n                this.totalVideoList = res.data.extra.total\r\n            })\r\n        },\r\n        // 撒点点击单个监控播放\r\n        getUeVideoFun(nv){\r\n            \r\n            if (nv != {}) {\r\n                let params = {\r\n                    ...nv,\r\n                    ape_id: nv.id,\r\n                }\r\n\r\n                this.winPlayer = 1\r\n                this.radio = nv.name\r\n                this.areaTitInfo = params\r\n                this.titleList = ['区域监控']\r\n            }\r\n        },\r\n\r\n        changeIsAction(val){\r\n            this.isAction = val\r\n        },\r\n        ChangeWinPlayer(val){\r\n            this.winPlayer = val\r\n        } \r\n\r\n    },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n::v-deep .el-dialog {\r\n    /* background-color: transparent; */\r\n    background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n    background-size: 100% 100%;\r\n\r\n    .el-dialog__header {\r\n        padding: 3rem 4rem 0;\r\n\r\n        .el-dialog__title {\r\n            color: #fff;\r\n            font-size: 1.4rem;\r\n\r\n        }\r\n\r\n        .el-dialog__close {\r\n            color: #fff;\r\n            padding: 1.6rem 1rem 0;\r\n        }\r\n    }\r\n\r\n    .el-dialog__body {\r\n        padding-bottom: 40px;\r\n    }\r\n}\r\n\r\n.titleList {\r\n    height: 10px;\r\n\r\n    .el-dropdown {\r\n        width: 100px;\r\n        text-align: center;\r\n        left: 2rem;\r\n        top: -1.5rem;\r\n        font-size: 1.1rem;\r\n    }\r\n\r\n    .el-dropdown-link {\r\n\r\n        cursor: pointer;\r\n        color: #409EFF;\r\n    }\r\n\r\n    .el-icon-arrow-down {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .demonstration {\r\n        display: block;\r\n        color: #8492a6;\r\n        font-size: 14px;\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n.videoCon {\r\n    display: flex;\r\n    width: 100%;\r\n    position: relative;\r\n\r\n    .leftPlaces {\r\n        width: 30%;\r\n        height: 30rem;\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n        padding-left: 2rem;\r\n\r\n        ::v-deep .el-radio__input {\r\n            display: none;\r\n\r\n        }\r\n\r\n        ::v-deep .el-radio {\r\n            color: #fff;\r\n        }\r\n\r\n        ::v-deep .el-radio .el-radio__label {\r\n            font-size: 17px;\r\n        }\r\n\r\n        .el-radio {\r\n            width: 100%;\r\n            height: 3rem;\r\n            line-height: 3rem;\r\n        }\r\n\r\n        .radioAction {\r\n            background-color: rgba(27, 137, 225, 0.2);\r\n        }\r\n\r\n        .el-dropdown-link {\r\n            cursor: pointer;\r\n            color: #409EFF;\r\n        }\r\n\r\n        .el-icon-arrow-down {\r\n            font-size: 12px;\r\n        }\r\n\r\n        .demonstration {\r\n            display: block;\r\n            color: #8492a6;\r\n            font-size: 14px;\r\n            margin-bottom: 20px;\r\n        }\r\n\r\n        .search {\r\n            position: absolute;\r\n            bottom: -4%;\r\n            left: 3%;\r\n\r\n            .pageSelect {\r\n                color: #fff;\r\n\r\n                ::v-deep .el-pagination__total {\r\n                    color: #fff;\r\n                }\r\n\r\n                ::v-deep .el-pager li {\r\n                    background: transparent !important;\r\n                }\r\n\r\n                ::v-deep .el-pagination {\r\n                    color: #fff;\r\n                }\r\n\r\n                ::v-deep .el-pagination .btn-next,\r\n                ::v-deep .el-pagination .btn-prev {\r\n                    background: transparent !important;\r\n                    color: #fff;\r\n                }\r\n\r\n                ::v-deep .el-pagination--small .more::before,\r\n                ::v-deep .el-pagination--small li.more::before {\r\n                    color: #fff !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .rightVideoShow {\r\n        width: 70%;\r\n\r\n    }\r\n\r\n    .el-overlay {\r\n        margin-left: 2rem;\r\n    }\r\n\r\n    .canvas-player-list-container {\r\n        position: relative;\r\n    }\r\n\r\n    .fullOrSmall {\r\n        position: absolute;\r\n        right: 10px;\r\n        top: -3px;\r\n        z-index: 200;\r\n        cursor: pointer;\r\n        display: inline-block;\r\n        width: 20px;\r\n        height: 20px;\r\n        background-repeat: no-repeat;\r\n        background-position: center;\r\n        background-size: 20px 20px;\r\n        background-image: url('@/assets/images/video/fullscreen.png');\r\n\r\n        &.isFull {\r\n            background-image: url('@/assets/images/video/exitfull.png');\r\n        }\r\n    }\r\n\r\n    .rightVideoShow {\r\n        position: absolute;\r\n        top: 43%;\r\n        left: 35%;\r\n        transform: translateY(-50%);\r\n        width: 60%;\r\n        height: 90%;\r\n        display: flex;\r\n        justify-content: space-evenly;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .numInitChange {\r\n            position: absolute;\r\n            top: -16px;\r\n            display: block;\r\n            color: #fff;\r\n\r\n            >span {\r\n                display: inline-block;\r\n                padding: 2px 3px;\r\n                border: 1px solid #fff;\r\n                cursor: pointer;\r\n            }\r\n\r\n            .selectedSpn {\r\n                background-color: rgba(9, 171, 212, .3);\r\n            }\r\n        }\r\n\r\n        .videoList {\r\n            width: 100%;\r\n            height: 100%;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .watervideoShowMany {\r\n            width: 30%;\r\n            height: 47%;\r\n\r\n            video {\r\n                object-fit: fill;\r\n                width: 100%;\r\n                height: 100%;\r\n\r\n            }\r\n        }\r\n\r\n\r\n\r\n        // 播放按钮\r\n        video::-webkit-media-controls-play-button {\r\n            display: none !important;\r\n        }\r\n\r\n        // 当前播放时间\r\n        video::-webkit-media-controls-current-time-display {\r\n            display: none !important;\r\n        }\r\n\r\n        // 剩余时间\r\n        video::-webkit-media-controls-time-remaining-display {\r\n            display: none !important;\r\n        }\r\n\r\n        // 音量按钮\r\n        video::-webkit-media-controls-volume-control-container {\r\n            display: none !important;\r\n        }\r\n\r\n        // // 全屏\r\n        // video::-webkit-media-controls-fullscreen-button {\r\n        //     display: none !important;\r\n        // }\r\n\r\n        // 时间轴\r\n        video::-webkit-media-controls-timeline {\r\n            display: none !important;\r\n        }\r\n\r\n\r\n\r\n    }\r\n}\r\n\r\n/* 整个滚动条 */\r\n::-webkit-scrollbar {\r\n    position: absolute;\r\n    width: 5px !important;\r\n    height: 10px !important;\r\n}\r\n\r\n/* 滚动条上的滚动滑块 */\r\n::-webkit-scrollbar-thumb {\r\n    background: rgba(27, 137, 225, 0.4);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 滚动条上的滚动滑块悬浮效果 */\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(27, 137, 225, 0.4);\r\n\r\n}\r\n\r\n/* 滚动条没有滑块的轨道部分 */\r\n::-webkit-scrollbar-track-piece {\r\n    background: rgba(27, 137, 225, 0.2);\r\n\r\n}\r\n\r\n/*滚动条上的按钮(上下箭头)  */\r\n::-webkit-scrollbar-button {\r\n    background-color: transparent;\r\n}\r\n</style>"], "mappings": "AA8FA,OAAAA,KAAA;AACA,OAAAC,SAAA;AACA,SAAAC,aAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,eAAA;AACA,SAAAC,gBAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAP,SAAA;IACAE,kBAAA;IACAC,aAAA;IACAC;EACA;EACAI,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAC,cAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;QACA;MACA;IACA;IACAG,WAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,KAAA;IACA;MACAC,MAAA;MACAC,SAAA;MACAC,GAAA;MACAC,KAAA;MAAA;MACAC,aAAA;MACAC,IAAA;MACAC,aAAA;MACAC,KAAA;MACAC,SAAA;MACAC,cAAA;MACAC,SAAA;MACAC,WAAA;MACAC,aAAA;MACAC,QAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MACAC,cAAA;MACAC,QAAA;MACAC,aAAA;MACAC,WAAA;MACAC,SAAA;MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IAEAhB,cAAAiB,MAAA;MAEA,KAAAA,MAAA;QACA,KAAAX,aAAA;QACA,KAAAY,SAAA;QACA,KAAAC,OAAA,CAAAC,cAAA;QAEA,SAAAd,aAAA,iBAAAe,KAAA,CAAAC,UAAA,CAAAC,cAAA;MACA;IACA;IAEA;IACAjB,cAAAkB,EAAA,EAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAJ,KAAA,CAAAC,UAAA,CAAAC,cAAA;MACA,WAAAE,IAAA,aACA;MACA,KAAAC,IAAA,CAAAF,EAAA;IAEA;EAIA;EACAG,QAAA;IACA;IACAC,QAAA;MACA;IACA;EACA;EAIAC,QAAA;IAEA,KAAAhB,WAAA,QAAAtB,WAAA;IACA,KAAAuC,SAAA;MACA,KAAAJ,IAAA,MAAApB,aAAA;MACA;IACA;EACA;EACAyB,cAAA,GAGA;EAEAC,OAAA;IACA;IACAC,cAAAC,OAAA;MACA,KAAA5B,aAAA,GAAA4B,OAAA;IACA;IACA;IACA,MAAAC,eAAA;MACA,MAAA1D,aAAA,GAAA2D,IAAA,CAAAC,GAAA;QACA,KAAAtC,IAAA,GAAAsC,GAAA,CAAA5C,IAAA,CAAA6C,KAAA;QACA,KAAArC,KAAA,QAAAF,IAAA,IAAAwC,WAAA;QACA,KAAA3C,GAAA,QAAAG,IAAA,IAAAyC,GAAA;MAEA;IACA;IACA;IACAC,YAAA;MACA,KAAA1C,IAAA,CAAA2C,OAAA,CAAAC,IAAA;QACAA,IAAA,CAAAC,QAAA;MACA;MACA,KAAAvC,WAAA,CAAAqC,OAAA,CAAAG,GAAA;QACA,KAAA9C,IAAA,CAAA2C,OAAA,CAAAC,IAAA;UACArD,MAAA,CAAAwD,MAAA,CAAAH,IAAA;YAAAJ,WAAA,EAAAI,IAAA,CAAAI;UAAA;UACA,IAAAF,GAAA,IAAAF,IAAA,CAAAI,UAAA;YACAJ,IAAA,CAAAC,QAAA;UACA;QACA;MACA;IACA;IACA;IACAI,iBAAAH,GAAA;MACA,KAAA9C,IAAA;MACA,KAAAA,IAAA,GAAA8C,GAAA,CAAApD,IAAA;MACA;MACA;MACA;;MAEA,KAAAiB,cAAA,GAAAmC,GAAA,CAAAI,MAAA,CAAAC,SAAA;IACA;IACA;IACAxB,KAAAmB,GAAA;MAEA,QAAAA,GAAA;QACA;UACA,KAAA9C,IAAA;UACA,KAAAoC,cAAA;UACAgB,UAAA;YACA,KAAAC,WAAA;UACA;UACA;QACA;UACA,KAAArD,IAAA;UACA,SAAAd,WAAA,CAAAoE,MAAA;YACA,KAAAtD,IAAA,QAAAd,WAAA;YACA,KAAAwD,WAAA;YACA,IAAAa,WAAA,QAAAvD,IAAA;YACAoD,UAAA;cACA,KAAA9B,KAAA,CAAAC,UAAA,CAAAiC,SAAA,CAAAD,WAAA,CAAAE,UAAA;cACA,KAAAvD,KAAA,GAAAqD,WAAA,CAAAf,WAAA;YACA;YACA,KAAArB,SAAA;UACA;UAEA;QACA;UACA,KAAAA,SAAA;UACA,KAAAY,SAAA;YACA,KAAAT,KAAA,CAAA1C,aAAA,CAAA8E,iBAAA,MAAAlE,WAAA;UAEA;UAEA;QAEA;MACA;IAEA;IACA;IACAmE,gBAAAjE,IAAA,EAAAkE,KAAA;MACA,aAAArD,aAAA;QACA;UACA,KAAAV,GAAA,GAAAH,IAAA,CAAA+C,GAAA;UACA,SAAA1C,aAAA,CAAAuD,MAAA;YACA,KAAAO,YAAA,MAAA9D,aAAA;YACA,KAAAA,aAAA,CAAA+D,KAAA;UACA;UACA,KAAA/B,SAAA;YACA,KAAAsB,WAAA;UACA;UACA,KAAAvD,KAAA,YAAAA,KAAA,YAAAA,KAAA;UACA;QACA;UACA,KAAAwB,KAAA,CAAAC,UAAA,CAAAwC,YAAA,CAAArE,IAAA,CAAA+D,UAAA;UACA;QACA;UACA,KAAA/C,WAAA,GAAAhB,IAAA;UACA;QACA;MACA;IAEA;IAEA;IACAyB,UAAA;MACA,SAAApB,aAAA,CAAAuD,MAAA;QACA,SAAAU,CAAA,SAAAjE,aAAA;UACA,KAAA8D,YAAA,CAAAG,CAAA;QACA;QACA,KAAAjE,aAAA;QACA,KAAAD,KAAA;MACA;IACA;IAEAuD,YAAA;MACA,IAAA7E,KAAA,CAAAyF,WAAA;QAEA,IAAAC,YAAA,GAAAC,QAAA,CAAAC,cAAA,uBAAAtE,KAAA;QACA,KAAAF,SAAA,GAAApB,KAAA,CAAA6F,YAAA,CACA;UACAlF,IAAA;UACAmF,MAAA;UACAC,QAAA;UACA1E,GAAA,OAAAA;QACA,GACA;UACA2E,YAAA;UAAA;UACAC,iBAAA;UAAA;UACAC,kBAAA;UAAA;UACAC,uBAAA;QACA,CACA;QAEA,KAAA/E,SAAA,CAAAgF,kBAAA,CAAAV,YAAA;QACA,SAAArE,GAAA,eAAAA,GAAA;UACA,KAAAD,SAAA,CAAAiF,IAAA;UAEA,KAAAjF,SAAA,CAAAkF,EAAA,CAAAtG,KAAA,CAAAuG,MAAA,CAAAC,kBAAA,EAAA1C,GAAA;YACA;UAAA,CACA;QACA;MACA;;MAEA;MACAc,UAAA;QACA,IAAAc,YAAA,CAAAe,QAAA,CAAA3B,MAAA;UACA,MAAA4B,GAAA,GAAAhB,YAAA,CAAAe,QAAA,CAAAC,GAAA;UACA,MAAAC,OAAA,GAAAjB,YAAA,CAAAkB,WAAA;UACA,MAAAC,IAAA,GAAAH,GAAA,GAAAC,OAAA;UACA;UACA,MAAAG,YAAA;UACA,MAAAC,WAAA;UACA,MAAAC,eAAA;UACA,IAAAC,YAAA;UACA,IAAAJ,IAAA,GAAAC,YAAA;YACA;YACA;YACApB,YAAA,CAAAkB,WAAA,GAAAF,GAAA;YACAO,YAAA,GAAAC,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,CAAAN,YAAA;UACA,WAAAD,IAAA,GAAAE,WAAA;YAGA;YACAE,YAAA,GAAAC,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,CAAAP,IAAA,EAAAG,eAAA;UACA;UACAtB,YAAA,CAAAuB,YAAA,GAAAA,YAAA;UACA,IAAAvB,YAAA,CAAA2B,MAAA,IAAA3B,YAAA,CAAA4B,KAAA;YACA1C,UAAA;cACA;YAAA,CACA;UAEA;YACAc,YAAA,CAAA6B,KAAA;UACA;QACA;QACA,IAAA7B,YAAA,CAAAe,QAAA,CAAA3B,MAAA;UACA,IAAA4B,GAAA,QAAAtF,SAAA,CAAAqF,QAAA,CAAAC,GAAA;UACA,IAAAG,IAAA,GAAAH,GAAA,QAAAtF,SAAA,CAAAwF,WAAA;UACA,IAAAC,IAAA;YAEA;YACA,KAAAzF,SAAA,CAAAwF,WAAA,QAAAxF,SAAA,CAAAqF,QAAA,CAAAC,GAAA;UACA;QACA;MACA;MAEA,KAAAtF,SAAA,CAAAkF,EAAA,CAAAtG,KAAA,CAAAuG,MAAA,CAAAiB,KAAA,GAAAC,OAAA,EAAAC,SAAA;QACA,SAAAtG,SAAA;UACA,KAAAuG,WAAA,MAAAvG,SAAA;QACA;MACA;MACA,KAAAG,aAAA,CAAAqG,IAAA,MAAAxG,SAAA;IACA;IACAuG,YAAAvG,SAAA;MACA,KAAAiE,YAAA,CAAAjE,SAAA;MACA,KAAAyD,WAAA;IACA;IAEAQ,aAAAjE,SAAA;MACAA,SAAA,CAAAmG,KAAA;MACAnG,SAAA,CAAAyG,MAAA;MACAzG,SAAA,CAAA0G,kBAAA;MACA1G,SAAA,CAAA2G,OAAA;MACA3G,SAAA;IAEA;IAGA;IACA4G,gBAAAC,GAAA;MACA,KAAAhG,UAAA,GAAAgG,GAAA;IACA;IACA;IACAC,oBAAA5D,GAAA;MACA,UAAAlC,QAAA;QACA,IAAA+F,MAAA;UACAxH,IAAA;UACAyH,IAAA;QACA;QACA;QACA,KAAA9F,WAAA,GAAAgC,GAAA;QACA,KAAA+D,KAAA,mBAAA/D,GAAA;QACA,KAAAxB,KAAA,CAAA1C,aAAA,CAAA8E,iBAAA,CAAAZ,GAAA;MACA;QACA,KAAAgE,gBAAA,MAAAjG,aAAA,EAAAiC,GAAA;MACA;IAEA;IACA;IACAiE,YAAAjE,GAAA;MACA,KAAAA,GAAA;QACA,KAAAlC,QAAA;QACA,KAAAe,IAAA,MAAApB,aAAA;MAEA;QACA,KAAAK,QAAA;QACA,KAAAC,aAAA,GAAAiC,GAAA;QACA,KAAAgE,gBAAA,CAAAhE,GAAA;MACA;IAEA;IACA;IACAgE,iBAAAhE,GAAA,EAAAkE,OAAA;MACAlI,gBAAA,CAAAgE,GAAA,EAAAkE,OAAA,EAAA3E,IAAA,CAAAC,GAAA;QACA,KAAAtC,IAAA,GAAAsC,GAAA,CAAA5C,IAAA,CAAA6C,KAAA,CAAA0E,OAAA;QACA,KAAAtG,cAAA,GAAA2B,GAAA,CAAA5C,IAAA,CAAA6C,KAAA,CAAA2E,KAAA;MACA;IACA;IACA;IACAC,cAAA1F,EAAA;MAEA,IAAAA,EAAA;QACA,IAAAkF,MAAA;UACA,GAAAlF,EAAA;UACA2F,MAAA,EAAA3F,EAAA,CAAA4F;QACA;QAEA,KAAAtG,SAAA;QACA,KAAAb,KAAA,GAAAuB,EAAA,CAAA1C,IAAA;QACA,KAAA2B,WAAA,GAAAiG,MAAA;QACA,KAAAtG,SAAA;MACA;IACA;IAEAiH,eAAAxE,GAAA;MACA,KAAA9B,QAAA,GAAA8B,GAAA;IACA;IACAyE,gBAAAzE,GAAA;MACA,KAAA/B,SAAA,GAAA+B,GAAA;IACA;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}