{"ast": null, "code": "export default {\n  name: 'WaterEventInfo',\n  props: {\n    classList: {\n      type: Object,\n      default: () => {\n        return {\n          fonSize: 'fz37',\n          width: 'w30'\n        };\n      }\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  components: {},\n  data() {\n    return {};\n  },\n  watch: {},\n  computed: {},\n  mounted() {},\n  methods: {\n    closeFun() {\n      this.$parent.closeFun();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "classList", "type", "Object", "default", "fonSize", "width", "title", "String", "components", "data", "watch", "computed", "mounted", "methods", "closeFun", "$parent"], "sources": ["src/components/popTitle/popTitle.vue"], "sourcesContent": ["<template>\r\n    <div class=\"box-card\">\r\n        <div class=\"topMain\">\r\n            <div class=\"titleTxt\">\r\n                <div class=\"txt fz37\" :class=\"classList.fonSize\">{{ title }}</div>\r\n            </div>\r\n            <div class=\"delete w30\" @click=\"closeFun\"  :class=\"classList.width\">\r\n                <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\" width=\"100%\">\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'WaterEventInfo',\r\n    props: {\r\n        classList: {\r\n            type: Object,\r\n            default: () => { \r\n                return {\r\n                    fonSize: 'fz37',\r\n                    width: 'w30'\r\n                }\r\n            }\r\n        },\r\n        title:{\r\n            type:String,\r\n            default:''\r\n        }\r\n    },\r\n    components: {\r\n\r\n    },\r\n    data() {\r\n        return {\r\n\r\n        }\r\n    },\r\n\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    methods: {\r\n        closeFun(){\r\n            this.$parent.closeFun()\r\n        }\r\n    },\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n \r\n</style>"], "mappings": "AAeA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;UACAC,OAAA;UACAC,KAAA;QACA;MACA;IACA;IACAC,KAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,UAAA,GAEA;EACAC,KAAA;IACA,QAEA;EACA;EAEAC,KAAA,GAEA;EACAC,QAAA,GAEA;EACAC,QAAA,GAEA;EACAC,OAAA;IACAC,SAAA;MACA,KAAAC,OAAA,CAAAD,QAAA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}