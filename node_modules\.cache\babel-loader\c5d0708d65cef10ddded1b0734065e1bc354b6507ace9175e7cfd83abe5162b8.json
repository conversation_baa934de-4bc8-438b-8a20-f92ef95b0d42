{"ast": null, "code": "import { mapState } from 'vuex';\nimport HZVideoPlayer from '@/components/videoTypeOption/videoTabsList/HZVideoPlayer.vue';\nimport ownVideoPlayer from '@/components/videoTypeOption/videoTabsList/ownVideoPlayer.vue';\nimport ParkingTreeList from '@/components/videoTypeOption/videoTabsList/ParkingTreeList.vue';\nimport ZhongYangPark from '@/components/videoTypeOption/videoTabsList/ZhongYangPark.vue';\nexport default {\n  name: 'videoPlayerTree',\n  components: {\n    HZVideoPlayer,\n    ownVideoPlayer,\n    ParkingTreeList,\n    ZhongYangPark\n  },\n  props: {},\n  data() {\n    return {\n      setComponent: '',\n      currentTabsValue: '',\n      tabsList: ['按部门分类', '智慧停车', '道路']\n    };\n  },\n  watch: {\n    setComponent() {\n      this.$store.commit(\"action/clearPopAll\", false);\n    },\n    tabsValue: {\n      handler(newVal) {\n        if (newVal == '道路-国际创新港' || newVal == '道路-中央公园' || newVal == \"道路-花家湾\") {\n          this.changeValue('道路');\n        } else {\n          this.changeValue(newVal);\n        }\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState('action', ['tabsValue'])\n  },\n  mounted() {\n    // this.$store.commit(\"action/getTabsList\", ['按部门分类', '智慧停车','道路-中央公园'])\n    // this.$store.commit(\"action/getTabsValue\", '道路-中央公园')\n  },\n  created() {},\n  destroyed() {\n    this.$store.commit(\"action/getTabsValue\", this.tabsList[0]);\n  },\n  updated() {},\n  beforeDestroy() {\n    this.$eventBus.$emit('senTxtToUe', '清除');\n  },\n  methods: {\n    closeTree() {\n      this.$store.commit(\"action/clearAll\", false);\n    },\n    changeValue(item) {\n      // this.$eventBus.$emit('senTxtToUe', '清除')\n      this.currentTabsValue = item;\n      switch (item) {\n        case '按部门分类':\n          this.setComponent = 'ownVideoPlayer';\n          break;\n        case '智慧停车':\n          this.setComponent = 'ParkingTreeList';\n          break;\n        case '道路':\n          this.setComponent = 'ZhongYangPark';\n          break;\n        default:\n          break;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "HZVideoPlayer", "ownVideoPlayer", "ParkingTreeList", "ZhongYangPark", "name", "components", "props", "data", "setComponent", "currentTabsValue", "tabsList", "watch", "$store", "commit", "tabsValue", "handler", "newVal", "changeValue", "immediate", "computed", "mounted", "created", "destroyed", "updated", "<PERSON><PERSON><PERSON><PERSON>", "$eventBus", "$emit", "methods", "closeTree", "item"], "sources": ["src/components/videoTypeOption/videoPlayerTree.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <div class=\"tabs disFlex\">\r\n            <div v-for=\"(item, index) of tabsList\" :key=\"index\" class=\"tabItems\"\r\n                :class=\"{ 'activeTab': currentTabsValue == item }\" @click=\"changeValue(item)\">\r\n                {{ item }}\r\n            </div>\r\n\r\n        </div>\r\n        <keep-alive>\r\n            <component :is=\"setComponent\"></component>\r\n        </keep-alive>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { mapState } from 'vuex'\r\nimport HZVideoPlayer from '@/components/videoTypeOption/videoTabsList/HZVideoPlayer.vue'\r\nimport ownVideoPlayer from '@/components/videoTypeOption/videoTabsList/ownVideoPlayer.vue'\r\nimport ParkingTreeList from '@/components/videoTypeOption/videoTabsList/ParkingTreeList.vue'\r\nimport ZhongYangPark from '@/components/videoTypeOption/videoTabsList/ZhongYangPark.vue'\r\n\r\nexport default {\r\n    name: 'videoPlayerTree',\r\n    components: {\r\n        HZVideoPlayer,\r\n        ownVideoPlayer,\r\n        ParkingTreeList,\r\n        ZhongYangPark\r\n\r\n    },\r\n    props: {\r\n\r\n    },\r\n    data() {\r\n        return {\r\n            setComponent: '',\r\n            currentTabsValue: '',\r\n            tabsList: ['按部门分类', '智慧停车', '道路'],\r\n        };\r\n    },\r\n    watch: {\r\n        setComponent() {\r\n            this.$store.commit(\"action/clearPopAll\", false)\r\n        },\r\n        tabsValue: {\r\n            handler(newVal) {\r\n                if (newVal == '道路-国际创新港' || newVal == '道路-中央公园' || newVal == \"道路-花家湾\") {\r\n                    this.changeValue('道路')\r\n                } else {\r\n                    this.changeValue(newVal)\r\n                }\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState('action', ['tabsValue'])\r\n    },\r\n\r\n    mounted() {\r\n        // this.$store.commit(\"action/getTabsList\", ['按部门分类', '智慧停车','道路-中央公园'])\r\n        // this.$store.commit(\"action/getTabsValue\", '道路-中央公园')\r\n\r\n    },\r\n    created() {\r\n\r\n    },\r\n    destroyed() {\r\n        this.$store.commit(\"action/getTabsValue\", this.tabsList[0])\r\n\r\n    },\r\n    updated() {\r\n\r\n    },\r\n    beforeDestroy() {\r\n        this.$eventBus.$emit('senTxtToUe', '清除')\r\n\r\n    },\r\n\r\n    methods: {\r\n        closeTree() {\r\n            this.$store.commit(\"action/clearAll\", false)\r\n\r\n        },\r\n        changeValue(item) {\r\n            // this.$eventBus.$emit('senTxtToUe', '清除')\r\n            this.currentTabsValue = item\r\n            switch (item) {\r\n                case '按部门分类':\r\n                    this.setComponent = 'ownVideoPlayer'\r\n                    break;\r\n                case '智慧停车':\r\n                    this.setComponent = 'ParkingTreeList'\r\n                    break;\r\n\r\n                case '道路':\r\n                    this.setComponent = 'ZhongYangPark'\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.container {\r\n    width: 100%;\r\n    height: 1580px;\r\n\r\n    .tabs {\r\n        user-select: none;\r\n        padding: 10px;\r\n        font-size: 28px;\r\n\r\n        >div {\r\n            cursor: pointer;\r\n            margin: 0 5px;\r\n            width: fit-content;\r\n        }\r\n\r\n        .activeTab {\r\n            color: rgb(37, 134, 236)\r\n        }\r\n    }\r\n\r\n    ::v-deep .container_box .treeShow .over-ellipsis {\r\n        width: fit-content !important;\r\n    }\r\n\r\n    ::v-deep .container_box .treeShow .slotTxt {\r\n        width: 70%;\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAiBA,SAAAA,QAAA;AACA,OAAAC,aAAA;AACA,OAAAC,cAAA;AACA,OAAAC,eAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,aAAA;IACAC,cAAA;IACAC,eAAA;IACAC;EAEA;EACAG,KAAA,GAEA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,gBAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAH,aAAA;MACA,KAAAI,MAAA,CAAAC,MAAA;IACA;IACAC,SAAA;MACAC,QAAAC,MAAA;QACA,IAAAA,MAAA,kBAAAA,MAAA,iBAAAA,MAAA;UACA,KAAAC,WAAA;QACA;UACA,KAAAA,WAAA,CAAAD,MAAA;QACA;MACA;MACAE,SAAA;IACA;EACA;EACAC,QAAA;IACA,GAAApB,QAAA;EACA;EAEAqB,QAAA;IACA;IACA;EAAA,CAEA;EACAC,QAAA,GAEA;EACAC,UAAA;IACA,KAAAV,MAAA,CAAAC,MAAA,6BAAAH,QAAA;EAEA;EACAa,QAAA,GAEA;EACAC,cAAA;IACA,KAAAC,SAAA,CAAAC,KAAA;EAEA;EAEAC,OAAA;IACAC,UAAA;MACA,KAAAhB,MAAA,CAAAC,MAAA;IAEA;IACAI,YAAAY,IAAA;MACA;MACA,KAAApB,gBAAA,GAAAoB,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAArB,YAAA;UACA;QACA;UACA,KAAAA,YAAA;UACA;QAEA;UACA,KAAAA,YAAA;UACA;QACA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}