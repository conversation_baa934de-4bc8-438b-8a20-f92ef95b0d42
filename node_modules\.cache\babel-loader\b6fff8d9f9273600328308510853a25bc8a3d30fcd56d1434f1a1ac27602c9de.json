{"ast": null, "code": "import { typeConfig } from './config';\nimport { getMonitorCount, IntegratedIntelligentRod } from '@/api/bigScreen.js';\nimport { getLatLng, getAlarmList, getSiteList, getCountStatistic, getQuerySiteList, getQueryDeviceList, getQueryPortList, getFireAlarm } from '@/api/chargingPiles.js';\nimport { getkeyArea } from '@/api/index.js';\nimport Dialog from '@/components/Dialog.vue';\nimport DiaLogPdf from '@/components/DiaLogPdf.vue';\nimport WarningDialog from '@/components/WarningDialog.vue';\nimport SmokeEquipment from '@/views/dataScreen/fileType/SmokeEquipment';\nimport RiskWarning from '@/components/comprehensive/RiskWarning.vue';\nimport RecentlyDanger from '@/components/comprehensive/RecentlyDanger.vue';\nimport AppropriationStatistics from '@/components/peopleDem/AppropriationStatistics.vue';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      smokeEqInfoFlag: false,\n      IntegratedTableList: [],\n      warmingTime: [],\n      xfList: [],\n      warningCount: 0,\n      tableData: [],\n      dialogType: {},\n      type: '智行充',\n      tabsArr: [\"站点\", \"设备\", \"插座\", \"预警\"],\n      tabsActive: 0,\n      IntegratedType: {\n        name: '一体化智能杆',\n        width: ' 50%',\n        height: 650,\n        data: [{\n          prop: 'addvnm1',\n          label: '水文站'\n        }, {\n          prop: 'voltage',\n          label: '电压'\n        }, {\n          prop: 'qa',\n          label: '总流量'\n        }, {\n          prop: 'q',\n          label: '瞬时流量'\n        }, {\n          prop: 'vj',\n          label: '瞬时流速'\n        }, {\n          prop: 'stnm',\n          label: '测站名称'\n        }, {\n          prop: 'signalinten',\n          label: '信号强度'\n        }, {\n          prop: 'tm',\n          label: '采集时间'\n        }, {\n          prop: 'z',\n          label: '水位'\n        }]\n      },\n      monitorCount: [],\n      //采集数据\n      dealDangerNum: '',\n      PreliminaryAndManage: '联动管理',\n      config: {\n        //工单\n        data: [['1.《天然气输送管道突发事件应急预案》'], ['2.《烟花爆竹事故应急预案》'], ['3.《食品安全突发事件应急预案》'], ['4.《火灾事故应急预案》'], ['5.《交通事故应急预案》'], ['6.《特种设备安全事故应急预案》'], ['7.《突发治安暴恐事件应急预案》']],\n        evenRowBGC: 'rgba(0,0,0,0)',\n        oddRowBGC: 'rgba(0,0,0,0)',\n        rowNum: 4\n      },\n      PdfUrl: '',\n      pdfDialogFlag: false,\n      dialogVisible: false,\n      eqCHartList: [{\n        name: \"视频设备\",\n        count: 27200\n      }, {\n        name: \"水压传感器\",\n        count: 553\n      }, {\n        name: \"流量仪\",\n        count: 22\n      }, {\n        name: \"烟感器\",\n        count: 781\n      }, {\n        name: \"智行充\",\n        count: 3393\n      }],\n      eqCHartIndex: null,\n      popFalg: false,\n      tableList: [],\n      // 筛选配置\n      filterConfig: {},\n      // 表头配置\n      tableHeader: {},\n      totalCount: 0,\n      tableType: 0,\n      orgIds: '',\n      headerList: [],\n      filterList: []\n    };\n  },\n  components: {\n    AppropriationStatistics,\n    RiskWarning,\n    RecentlyDanger,\n    Dialog,\n    SmokeEquipment,\n    DiaLogPdf,\n    WarningDialog\n  },\n  watch: {\n    type: {\n      handler(nv) {\n        document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = ''); //清空高亮\n        let data = typeConfig.filter(item => item.type == nv);\n        this.tabsArr = data[0].tabsArr;\n        this.tabsActive = 0;\n        this.headerList = data[0].headerList;\n        this.filterList = data[0].filterList;\n      },\n      immediate: true\n    }\n  },\n  computed: {},\n  mounted() {\n    for (let i = 166; i <= 279; i++) {\n      this.orgIds += i + ',';\n    }\n    this.getMonitorCountList();\n    this.getCountStatisticObj();\n    // 一体化智能干\n    IntegratedIntelligentRod().then(res => {\n      let list = res.data.data;\n      this.IntegratedTableList = list;\n    });\n    this.getAlarmListData({\n      pageNum: 1,\n      pageSize: 100\n    });\n    this.getFireAlarmList({\n      pageNum: 1,\n      pageSize: 100,\n      orgIds: this.orgIds\n    });\n  },\n  updated() {},\n  methods: {\n    //智行充设备总数\n    getCountStatisticObj() {\n      getCountStatistic().then(res => {\n        if (res.data.code == 200) {\n          this.eqCHartList[4].count = res.data.extra.deviceNum;\n        }\n      });\n    },\n    async getWarningList(params, type) {\n      if (type == 0) {\n        //站点\n        this.getQuerySiteListData(params);\n      } else if (type == 1) {\n        //设备\n        this.getQueryDeviceListData(params);\n      } else if (type == 2) {\n        //插座\n        this.getQueryPortListData(params);\n      } else if (type == 3) {\n        //预警\n        this.getAlarmListData(params);\n      } else if (type == '撒点') {\n        await this.warningPoi([params]);\n      }\n    },\n    //消防预警列表\n    getFireAlarmList(params) {\n      getFireAlarm(params).then(res => {\n        if (res.data.status == 200) {\n          this.xfList = res.data.data;\n        }\n        // if (params.pageSize == 1000) {\n        //     this.warmingTime = res.data.extra.records\n        // } else {\n        //     this.tableList = res.data.extra.records\n        //     this.totalCount = res.data.extra.total\n        // }\n      });\n    },\n    // 智行充预警列表\n    getAlarmListData(params) {\n      getAlarmList(params).then(res => {\n        if (params.pageSize == 100) {\n          this.warmingTime = res.data.extra.records;\n        } else {\n          this.tableList = res.data.extra.records;\n          this.totalCount = res.data.extra.total;\n        }\n      });\n    },\n    //插座列表\n    getQueryPortListData(params) {\n      getQueryPortList(params).then(res => {\n        if (res.data.code == 200) {\n          this.tableList = res.data.extra.records;\n          this.totalCount = res.data.extra.total;\n        }\n      });\n    },\n    //设备列表\n    getQueryDeviceListData(params) {\n      getQueryDeviceList(params).then(res => {\n        if (res.data.code == 200) {\n          this.tableList = res.data.extra.records;\n          this.totalCount = res.data.extra.total;\n        }\n      });\n    },\n    //站点列表\n    getQuerySiteListData(params) {\n      getQuerySiteList(params).then(res => {\n        if (res.data.code == 200) {\n          this.tableList = res.data.extra.records;\n          this.totalCount = res.data.extra.total;\n        }\n      });\n    },\n    showPop(name) {\n      if (name == 'zxc') {\n        // 智行充切换\n        this.tableType = this.tabsActive;\n        this.tableHeader = this.headerList[this.tabsActive];\n        this.filterConfig = this.filterList[this.tabsActive];\n      } else {\n        // 预警弹窗\n        this.tableHeader = this.headerList[3];\n        this.filterConfig = this.filterList[3];\n        this.tableType = 3;\n      }\n      this.popFalg = true;\n    },\n    // 关闭弹窗\n    closeDialog() {\n      this.popFalg = false;\n    },\n    tabsFn(index) {\n      this.tabsActive = index;\n      this.popFalg = false;\n    },\n    setTabsUrl(index) {\n      return require(`@/assets/${index == this.tabsActive ? 'type-active' : 'type'}.png`);\n    },\n    warningEvent() {\n      this.deleteFn();\n      getSiteList().then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra.splice(0, 200);\n          list.forEach(item => {\n            if (item.count >= 0 && item.count <= 50) {\n              item.IconType = 19;\n            } else if (item.count > 50 && item.count <= 100) {\n              item.IconType = 21;\n            } else if (item.count > 100) {\n              item.IconType = 20;\n            }\n            item.Name = item.count;\n            item.Height = \"100\";\n            item.TextX = \"0\";\n            item.TextY = \"-16\";\n            item.TextSize = \"10\";\n            item.ImageX = \"0.25\";\n            item.ImageY = \"0.25\";\n            item.channalCode = item.siteCode;\n          });\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": list\n          };\n          this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n          this.$eventBus.$emit('senTxtToUe', params); //撒点\n        }\n      });\n    },\n    xfWarningPoi(item, index) {\n      document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = '');\n      const imgPath = require('@/assets/images/ChongDianZhuang/warning-bg.png');\n      document.querySelectorAll('.others_item')[index].style.backgroundImage = `url(${imgPath})`;\n      this.deleteFn();\n      getLatLng({\n        orgId: item.organizationId\n      }).then(res => {\n        if (res.data.code == 200) {\n          let data = res.data.extra;\n          data.Name = data.orgName;\n          data.IconType = 14;\n          data.Height = \"100\";\n          data.TextX = \"0\";\n          data.TextY = \"-28\";\n          data.TextSize = \"10\";\n          data.ImageX = \"0.25\";\n          data.ImageY = \"0.25\";\n          data.Type = '实时警情';\n          data.channalCode = '320583040005';\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": [data]\n          };\n          this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n          this.$eventBus.$emit('senTxtToUe', params); //撒点\n        }\n      });\n    },\n    // 点击撒点\n    warningPoi(list, index) {\n      console.log(index, 'cexjhi');\n      if (typeof index == 'number') {\n        document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = '');\n        const imgPath = require('@/assets/images/ChongDianZhuang/warning-bg.png');\n        document.querySelectorAll('.others_item')[index].style.backgroundImage = `url(${imgPath})`;\n      }\n      this.deleteFn();\n      list.forEach(item => {\n        if (item.firstEvent == \"1\") {\n          item.IconType = 12;\n          item.Name = item.eventContent;\n          item.TextY = \"-28\";\n        } else if (item.firstEvent == \"2\") {\n          item.IconType = 14;\n          item.Name = item.eventContent;\n          item.TextY = \"-28\";\n        } else if (item.firstEvent == \"3\") {\n          item.IconType = 13;\n          item.Name = item.eventContent;\n          item.TextY = \"-28\";\n        } else {\n          // 站点 设备 插座图标\n          item.IconType = 4;\n          item.Name = `智行充${filterList[this.tabsActive].name}`;\n          item.TextY = \"-42\";\n        }\n        item.Height = \"100\";\n        item.TextX = \"0\";\n        item.TextSize = \"10\";\n        item.ImageX = \"0.25\";\n        item.ImageY = \"0.25\";\n        item.channalCode = item.siteCode;\n      });\n      let params = {\n        \"mode\": \"add\",\n        \"sources\": list\n      };\n      this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n      this.$eventBus.$emit('senTxtToUe', params); //撒点\n    },\n    // 字符串超出省略\n    splitFont(val, length) {\n      if (val) {\n        if (val.length > length - 1) {\n          return val.slice(0, length) + '...';\n        } else {\n          return val;\n        }\n      }\n    },\n    // 烟感设备\n    getSmokeFlagFun(val) {\n      this.smokeEqInfoFlag = val;\n    },\n    // 一体化智能杆列表\n    async showDataList(nv) {\n      if (nv == '雷达一体式流量计') {\n        this.tableData = [];\n        this.dialogType = this.IntegratedType;\n        this.dialogVisible = true;\n        this.tableData = this.IntegratedTableList;\n      } else if (nv == '烟感设备') {\n        this.smokeEqInfoFlag = true;\n      }\n    },\n    //删除点位\n    deleteFn() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    poiEvent(item, index) {\n      this.deleteFn();\n      if (this.eqCHartIndex == index) {\n        this.eqCHartIndex = null;\n      } else {\n        this.eqCHartIndex = index;\n        getkeyArea({\n          name: item.name\n        }).then(res => {\n          if (res.data.code == 200) {\n            let list = res.data.extra.data;\n            let params = {\n              \"mode\": \"add\",\n              \"sources\": JSON.parse(list[0].points)\n            };\n            this.$eventBus.$emit('senTxtToUe', params); //撒点\n            this.$eventBus.$emit('senTxtToUe', list[0].angle); //聚焦到当前位置\n          }\n        });\n      }\n    },\n    setUrl(index) {\n      return require(`@/assets/images/comprehensiveSituation/eqCHart-${index + 1}${this.eqCHartIndex == index ? '-active' : ''}.png`);\n    },\n    // 关闭pdf弹窗\n    pdfPopWindow(nv) {\n      this.pdfDialogFlag = nv;\n    },\n    // 采集数据\n    getMonitorCountList() {\n      getMonitorCount().then(res => {\n        this.monitorCount = res.data.data;\n        let nameList = ['雷达一体式流量计', '超声波多普勒', '烟感设备'];\n        for (var i of this.monitorCount) {\n          i.name = nameList[i.type - 1];\n          i.nameAll = i.name;\n          if (i.name.length > 4) {\n            i.name = i.name.substr(0, 4) + '...';\n          }\n        }\n      }).catch(e => {\n        console.log(e);\n      });\n    },\n    // 应急预案pdf\n    emergencyPlansPdf(row, ceil, rowIndex) {\n      this.pdfDialogFlag = true;\n      // window.open(`/pdf/emergencyPlan${row.rowIndex+1}.pdf`)\n      this.PdfUrl = `/pdf/emergencyPlan${row.rowIndex + 1}.pdf`;\n    }\n  }\n};", "map": {"version": 3, "names": ["typeConfig", "getMonitorCount", "IntegratedIntelligentRod", "getLatLng", "getAlarmList", "getSiteList", "getCountStatistic", "getQuerySiteList", "getQueryDeviceList", "getQueryPortList", "getFireAlarm", "getkeyArea", "Dialog", "DiaLogPdf", "WarningDialog", "SmokeEquipment", "RiskWarning", "RecentlyDanger", "AppropriationStatistics", "name", "data", "smokeEqInfoFlag", "IntegratedTableList", "warmingTime", "xfList", "warningCount", "tableData", "dialogType", "type", "tabsArr", "tabsActive", "IntegratedType", "width", "height", "prop", "label", "monitorCount", "dealDangerNum", "PreliminaryAndManage", "config", "evenRowBGC", "oddRowBGC", "row<PERSON>um", "PdfUrl", "pdfDialogFlag", "dialogVisible", "eqCHartList", "count", "eqCHartIndex", "popFalg", "tableList", "filterConfig", "tableHeader", "totalCount", "tableType", "orgIds", "headerList", "filterList", "components", "watch", "handler", "nv", "document", "querySelectorAll", "for<PERSON>ach", "item", "style", "backgroundImage", "filter", "immediate", "computed", "mounted", "i", "getMonitorCountList", "getCountStatisticObj", "then", "res", "list", "getAlarmListData", "pageNum", "pageSize", "getFireAlarmList", "updated", "methods", "code", "extra", "deviceNum", "getWarningList", "params", "getQuerySiteListData", "getQueryDeviceListData", "getQueryPortListData", "warningPoi", "status", "records", "total", "showPop", "closeDialog", "tabsFn", "index", "setTabsUrl", "require", "warningEvent", "deleteFn", "splice", "IconType", "Name", "Height", "TextX", "TextY", "TextSize", "ImageX", "ImageY", "channalCode", "siteCode", "$eventBus", "$emit", "xfWarningPoi", "imgPath", "orgId", "organizationId", "orgName", "Type", "console", "log", "firstEvent", "eventContent", "splitFont", "val", "length", "slice", "getSmokeFlagFun", "showDataList", "poiEvent", "JSON", "parse", "points", "angle", "setUrl", "pdfPopWindow", "nameList", "nameAll", "substr", "catch", "e", "emergencyPlansPdf", "row", "ceil", "rowIndex"], "sources": ["src/views/comprehensiveSituation/components/joint-command/left-view.vue"], "sourcesContent": ["<template>\n    <div class=\"leftView\">\n        <div class=\"mainLeft\">\n            <div class=\"areaTit\">\n                <div :class=\"PreliminaryAndManage == '联动管理' ? 'action' : ''\" @click=\"PreliminaryAndManage = '联动管理'\">\n                    联动管理\n                </div>\n                <!-- |\n                <div :class=\"PreliminaryAndManage == '联动分析' ? 'action' : ''\" @click=\"PreliminaryAndManage = '联动分析'\">\n                    联动分析\n                </div> -->\n            </div>\n            <div class=\"manageMain\" v-if=\"PreliminaryAndManage == '联动管理'\">\n                <div class=\"eqStatistics\">\n                    <div class=\"topItems\">\n                        应急预案\n                    </div>\n                    <dv-scroll-board :config=\"config\" @click=\"emergencyPlansPdf\" style=\"width:100%;height:200px\" />\n                    <div class=\"topItems\" style=\"margin-top: 51px;\">\n                        设备统计\n                    </div>\n\n                    <div class=\"eqCHart\">\n                        <p v-for=\"(item, index) in eqCHartList\" @click=\"poiEvent(item, index)\">\n                            <span>{{ item.count }}</span>\n                            <span>{{ item.name }}</span>\n                            <img ref=\"eqCHartImg\" :src=\"setUrl(index)\" alt=\"\">\n                        </p>\n                    </div>\n\n                </div>\n                <div class=\"riskWarning mt2\">\n                    <div class=\"topItems\">\n                        <span :class=\"{ topItemActive: type == '智行充' }\" @click=\"type = '智行充'\">\"智行充\"设备</span>|\n                        <span :class=\"{ topItemActive: type == '消防' }\" @click=\"type = '消防'\">智慧消防</span>\n                        <img @click.stop=\"showPop('zxc')\" src=\"@/assets/menu.png\" alt=\"\">\n                    </div>\n                    <div class=\"tabs\">\n                        <span v-for=\"(item, index) in tabsArr\" @click=\"tabsFn(index)\">\n                            {{ item }}\n                            <img :src=\"setTabsUrl(index)\" alt=\"\">\n                        </span>\n                    </div>\n                    <RiskWarning class=\"mt2\" :tabsActive=\"tabsActive\" :type=\"type\"></RiskWarning>\n                </div>\n                <div class=\"warningPush\" v-if=\"type == '智行充'\">\n                    <div class=\"topItems mt2\" @click=\"warningEvent\">\n                        设备预警\n                        <img @click.stop=\"showPop('yj')\" src=\"@/assets/menu.png\" alt=\"\">\n                    </div>\n                    <div class=\"waring_note_list\">\n                        <!-- <div class=\"picSwiper SwiperWaring swiper-container\"> -->\n                        <!-- <div class=\"swiper-wrapper gallery-top\"> -->\n                        <div class=\"others_item\" v-for=\"(item, index) of warmingTime\" @click=\"warningPoi([item], index)\"\n                            :key=\"index\">\n                            <div class=\"top disFlex justifyContentSpaceBetween alignItemsCenter fz32\">\n                                <div class=\"leftTop disFlex alignItemsCenter\">\n\n                                    <div class=\"icon\">\n                                        <img src=\"@/assets/images/ChongDianZhuang/Frame.png\" alt=\"\" width=\"40px\">\n                                    </div>\n                                    <div class=\"time fz37\">\n                                        {{ item?.eventTime }}\n                                    </div>\n                                    <div class=\"icon\">\n                                        <img src=\"@/assets/images/ChongDianZhuang/Group 1850.png\" alt=\"\" width=\"28px\">\n                                    </div>\n                                </div>\n\n                                <div class=\"result\">\n                                    {{ item.firstEvent == 1 ? \"蓝色预警\" : item.firstEvent == 2 ? \"红色预警\" : \"黄色预警\" }}\n                                </div>\n                            </div>\n                            <div class=\"address fz28\">\n                                {{ item?.eventContent }}\n                                <a :title=\"item?.processPropertyName\" class=\" fz32\"> {{\n                                    splitFont(item?.processPropertyName, 14) }}</a>\n                            </div>\n\n                            <!-- </div> -->\n\n                            <!-- </div> -->\n\n                        </div>\n                    </div>\n\n\n                </div>\n                <div class=\"warningPush\" v-else>\n                    <div class=\"topItems mt2\">\n                        实时警情\n                        <!-- <img @click.stop=\"showPop('yj')\" src=\"@/assets/menu.png\" alt=\"\"> -->\n                    </div>\n                    <div class=\"waring_note_list\">\n                        <div class=\"others_item\" v-for=\"(item, index) of xfList\" :key=\"index\"\n                            @click=\"xfWarningPoi(item, index)\">\n                            <div class=\"top disFlex justifyContentSpaceBetween alignItemsCenter fz32\">\n                                <div class=\"leftTop disFlex alignItemsCenter\">\n                                    <div class=\"icon\">\n                                        <img src=\"@/assets/images/ChongDianZhuang/Frame.png\" alt=\"\" width=\"40px\">\n                                    </div>\n                                    <div class=\"time fz37\">\n                                        {{ item?.sendTime }}\n                                    </div>\n                                    <div class=\"icon\">\n                                        <img src=\"@/assets/images/ChongDianZhuang/Group 1850.png\" alt=\"\" width=\"28px\">\n                                    </div>\n                                </div>\n\n                                <!-- <div class=\"result\">\n                                    {{ item.firstEvent == 1 ? \"蓝色预警\" : item.firstEvent == 2 ? \"红色预警\" : \"黄色预警\" }}\n                                </div> -->\n                            </div>\n                            <div class=\"address fz28\" :title=\"item?.fcFacilitiesName\">\n                                {{ splitFont(item?.fcFacilitiesName, 4) }}\n                                <a :title=\"item?.selfDesc\" class=\" fz32\"> {{\n                                    splitFont(item?.selfDesc, 14) }}</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"manageMain\" v-if=\"PreliminaryAndManage == '联动分析'\">\n                <div class=\"hiddenDanger\">\n                    <div class=\"topItems\" style=\"margin-top: 51px;\">\n                        隐患统计\n                    </div>\n                    <div class=\"dealNum\">\n                        <div class=\"dealTit\">\n                            处理累计数量:\n                        </div>\n                        <div class=\"sum\" v-for=\"(item, index) of dealDangerNum\" :key=\"index\">\n                            <div v-if=\"item != ','\" class=\"num\">{{ item }}</div>\n                            <div v-else>{{ item }}</div>\n                        </div>\n                    </div>\n\n                    <div class=\"recentlyDanger\">\n                        <RecentlyDanger></RecentlyDanger>\n\n                    </div>\n\n\n                </div>\n\n                <div class=\"dealDanger\">\n                    <div class=\"topItems\" style=\"margin-top: 51px;\">\n                        隐患处理\n                    </div>\n                    <div class=\"con_stat\">\n                        <div class=\"items\" v-for=\"(item, index) of monitorCount\" :key=\"index\"\n                            @click=\"showDataList(item.nameAll)\">\n                            <div class=\"num\">\n                                {{ item.count }}\n                            </div>\n                            <a class=\"txt\" :title=\"item.nameAll\">\n                                {{ item.name }}\n                            </a>\n                        </div>\n\n                    </div>\n\n                </div>\n                <div class=\"areaEqStatistics\">\n                    <div class=\"topItems\">\n                        垃圾分类图\n                    </div>\n\n                    <div class=\"areaChart\">\n                        <AppropriationStatistics :modelName=\"'垃圾分类'\" :disStyle=\"{ height: '450px' }\">\n                        </AppropriationStatistics>\n                    </div>\n                </div>\n            </div>\n\n        </div>\n        <!-- 一体化智能杆 / 多发事件预警详情-->\n        <div class=\"intelligentRodDialog\">\n            <Dialog ref=\"dialogBox\" :tableData=\"tableData\" :dialogType=\"dialogType\" :dialogVisible=\"dialogVisible\">\n            </Dialog>\n        </div>\n        <!-- 联动管理pdf -->\n        <DiaLogPdf v-if=\"pdfDialogFlag\" ref=\"diaLogPdf\" :url=\"PdfUrl\" :dialogVisible=\"pdfDialogFlag\"\n            @updateFun=\"pdfPopWindow\"></DiaLogPdf>\n        <!-- 烟感设备 -->\n        <SmokeEquipment :smokeEqInfoFlag=\"smokeEqInfoFlag\" @clickSmoke=\"getSmokeFlagFun\"></SmokeEquipment>\n        <!-- 设备预警弹窗 -->\n        <WarningDialog :popFalg=\"popFalg\" :totalCount=\"totalCount\" :tableType='tableType' :tableData=\"tableList\"\n            :filterConfig=\"filterConfig\" :tableHeader=\"tableHeader\" @closeDialog=\"closeDialog\"\n            @getList=\"getWarningList\" />\n    </div>\n</template>\n\n<script>\nimport { typeConfig } from './config'\nimport { getMonitorCount, IntegratedIntelligentRod } from '@/api/bigScreen.js'\nimport { getLatLng, getAlarmList, getSiteList, getCountStatistic, getQuerySiteList, getQueryDeviceList, getQueryPortList, getFireAlarm } from '@/api/chargingPiles.js'\n\nimport { getkeyArea } from '@/api/index.js'\nimport Dialog from '@/components/Dialog.vue'\nimport DiaLogPdf from '@/components/DiaLogPdf.vue'\nimport WarningDialog from '@/components/WarningDialog.vue'\nimport SmokeEquipment from '@/views/dataScreen/fileType/SmokeEquipment'\nimport RiskWarning from '@/components/comprehensive/RiskWarning.vue'\nimport RecentlyDanger from '@/components/comprehensive/RecentlyDanger.vue'\nimport AppropriationStatistics from '@/components/peopleDem/AppropriationStatistics.vue'\nexport default {\n    name: 'leftView',\n    data() {\n        return {\n            smokeEqInfoFlag: false,\n            IntegratedTableList: [],\n            warmingTime: [],\n            xfList: [],\n            warningCount: 0,\n            tableData: [],\n            dialogType: {},\n            type: '智行充',\n            tabsArr: [\"站点\", \"设备\", \"插座\", \"预警\"],\n            tabsActive: 0,\n            IntegratedType: {\n                name: '一体化智能杆',\n                width: ' 50%',\n                height: 650,\n                data: [\n                    {\n                        prop: 'addvnm1',\n                        label: '水文站'\n                    },\n                    {\n                        prop: 'voltage',\n                        label: '电压'\n                    },\n                    {\n                        prop: 'qa',\n                        label: '总流量'\n                    },\n                    {\n                        prop: 'q',\n                        label: '瞬时流量'\n                    }, {\n                        prop: 'vj',\n                        label: '瞬时流速'\n                    },\n                    {\n                        prop: 'stnm',\n                        label: '测站名称'\n                    },\n                    {\n                        prop: 'signalinten',\n                        label: '信号强度'\n                    },\n                    {\n                        prop: 'tm',\n                        label: '采集时间'\n                    },\n                    {\n                        prop: 'z',\n                        label: '水位'\n                    },\n                ]\n            },\n            monitorCount: [],//采集数据\n            dealDangerNum: '',\n            PreliminaryAndManage: '联动管理',\n            config: { //工单\n                data: [['1.《天然气输送管道突发事件应急预案》'], ['2.《烟花爆竹事故应急预案》'], ['3.《食品安全突发事件应急预案》'], ['4.《火灾事故应急预案》'], ['5.《交通事故应急预案》'], ['6.《特种设备安全事故应急预案》'], ['7.《突发治安暴恐事件应急预案》']],\n                evenRowBGC: 'rgba(0,0,0,0)',\n                oddRowBGC: 'rgba(0,0,0,0)',\n                rowNum: 4,\n            },\n            PdfUrl: '',\n            pdfDialogFlag: false,\n            dialogVisible: false,\n            eqCHartList: [\n                { name: \"视频设备\", count: 27200 },\n                { name: \"水压传感器\", count: 553 },\n                { name: \"流量仪\", count: 22 },\n                { name: \"烟感器\", count: 781 },\n                { name: \"智行充\", count: 3393 },\n            ],\n            eqCHartIndex: null,\n            popFalg: false,\n            tableList: [],\n            // 筛选配置\n            filterConfig: {},\n            // 表头配置\n            tableHeader: {},\n            totalCount: 0,\n            tableType: 0,\n            orgIds: '',\n            headerList: [],\n            filterList: [],\n        };\n    },\n    components: {\n        AppropriationStatistics,\n        RiskWarning,\n        RecentlyDanger,\n        Dialog,\n        SmokeEquipment,\n        DiaLogPdf,\n        WarningDialog\n    },\n    watch: {\n        type: {\n            handler(nv) {\n                document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = '')//清空高亮\n                let data = typeConfig.filter(item => item.type == nv);\n                this.tabsArr = data[0].tabsArr;\n                this.tabsActive = 0;\n                this.headerList = data[0].headerList;\n                this.filterList = data[0].filterList;\n            }, immediate: true,\n        },\n    },\n    computed: {\n    },\n\n    mounted() {\n        for (let i = 166; i <= 279; i++) {\n            this.orgIds += i + ',';\n        }\n        this.getMonitorCountList();\n        this.getCountStatisticObj();\n        // 一体化智能干\n        IntegratedIntelligentRod().then(res => {\n            let list = res.data.data\n            this.IntegratedTableList = list;\n        })\n        this.getAlarmListData({\n            pageNum: 1,\n            pageSize: 100\n        });\n        this.getFireAlarmList({\n            pageNum: 1,\n            pageSize: 100,\n            orgIds: this.orgIds,\n        });\n\n    },\n\n    updated() {\n\n    },\n\n    methods: {\n        //智行充设备总数\n        getCountStatisticObj() {\n            getCountStatistic().then(res => {\n                if (res.data.code == 200) {\n                    this.eqCHartList[4].count = res.data.extra.deviceNum;\n                }\n            })\n        },\n        async getWarningList(params, type) {\n            if (type == 0) {//站点\n                this.getQuerySiteListData(params);\n            } else if (type == 1) {//设备\n                this.getQueryDeviceListData(params);\n            } else if (type == 2) {//插座\n                this.getQueryPortListData(params);\n            } else if (type == 3) {//预警\n                this.getAlarmListData(params);\n            } else if (type == '撒点') {\n                await this.warningPoi([params]);\n            }\n        },\n        //消防预警列表\n        getFireAlarmList(params) {\n            getFireAlarm(params).then(res => {\n                if (res.data.status == 200) {\n                    this.xfList = res.data.data;\n                }\n                // if (params.pageSize == 1000) {\n                //     this.warmingTime = res.data.extra.records\n                // } else {\n                //     this.tableList = res.data.extra.records\n                //     this.totalCount = res.data.extra.total\n                // }\n            })\n        },\n        // 智行充预警列表\n        getAlarmListData(params) {\n            getAlarmList(params).then(res => {\n                if (params.pageSize == 100) {\n                    this.warmingTime = res.data.extra.records\n                } else {\n                    this.tableList = res.data.extra.records\n                    this.totalCount = res.data.extra.total\n                }\n            })\n        },\n        //插座列表\n        getQueryPortListData(params) {\n            getQueryPortList(params).then(res => {\n                if (res.data.code == 200) {\n                    this.tableList = res.data.extra.records\n                    this.totalCount = res.data.extra.total\n                }\n            })\n        },\n        //设备列表\n        getQueryDeviceListData(params) {\n            getQueryDeviceList(params).then(res => {\n                if (res.data.code == 200) {\n                    this.tableList = res.data.extra.records\n                    this.totalCount = res.data.extra.total\n                }\n            })\n        },\n        //站点列表\n        getQuerySiteListData(params) {\n            getQuerySiteList(params).then(res => {\n                if (res.data.code == 200) {\n                    this.tableList = res.data.extra.records\n                    this.totalCount = res.data.extra.total\n                }\n            })\n        },\n        showPop(name) {\n            if (name == 'zxc') {// 智行充切换\n                this.tableType = this.tabsActive;\n                this.tableHeader = this.headerList[this.tabsActive];\n                this.filterConfig = this.filterList[this.tabsActive];\n            } else {// 预警弹窗\n                this.tableHeader = this.headerList[3];\n                this.filterConfig = this.filterList[3];\n                this.tableType = 3;\n            }\n            this.popFalg = true;\n\n        },\n        // 关闭弹窗\n        closeDialog() {\n            this.popFalg = false;\n        },\n        tabsFn(index) {\n            this.tabsActive = index;\n            this.popFalg = false;\n        },\n        setTabsUrl(index) {\n            return require(`@/assets/${index == this.tabsActive ? 'type-active' : 'type'}.png`)\n        },\n        warningEvent() {\n            this.deleteFn();\n            getSiteList().then(res => {\n                if (res.data.code == 200) {\n                    let list = res.data.extra.splice(0, 200);\n                    list.forEach(item => {\n                        if (item.count >= 0 && item.count <= 50) {\n                            item.IconType = 19\n                        } else if (item.count > 50 && item.count <= 100) {\n                            item.IconType = 21\n                        } else if (item.count > 100) {\n                            item.IconType = 20\n                        }\n                        item.Name = item.count\n                        item.Height = \"100\"\n                        item.TextX = \"0\"\n                        item.TextY = \"-16\"\n                        item.TextSize = \"10\"\n                        item.ImageX = \"0.25\"\n                        item.ImageY = \"0.25\"\n                        item.channalCode = item.siteCode\n                    });\n                    let params = {\n                        \"mode\": \"add\",\n                        \"sources\": list\n                    }\n                    this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n                    this.$eventBus.$emit('senTxtToUe', params);//撒点\n                }\n            })\n\n        },\n        xfWarningPoi(item, index) {\n            document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = '')\n            const imgPath = require('@/assets/images/ChongDianZhuang/warning-bg.png');\n            document.querySelectorAll('.others_item')[index].style.backgroundImage = `url(${imgPath})`;\n            this.deleteFn();\n            getLatLng({ orgId: item.organizationId }).then(res => {\n                if (res.data.code == 200) {\n                    let data = res.data.extra;\n                    data.Name = data.orgName\n                    data.IconType = 14\n                    data.Height = \"100\"\n                    data.TextX = \"0\"\n                    data.TextY = \"-28\"\n                    data.TextSize = \"10\"\n                    data.ImageX = \"0.25\"\n                    data.ImageY = \"0.25\"\n                    data.Type = '实时警情'\n                    data.channalCode = '320583040005'\n                    let params = {\n                        \"mode\": \"add\",\n                        \"sources\": [data]\n                    }\n                    this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n                    this.$eventBus.$emit('senTxtToUe', params);//撒点\n                }\n            })\n        },\n        // 点击撒点\n        warningPoi(list, index) {\n            console.log(index, 'cexjhi')\n            if (typeof index == 'number') {\n                document.querySelectorAll('.others_item').forEach(item => item.style.backgroundImage = '')\n                const imgPath = require('@/assets/images/ChongDianZhuang/warning-bg.png');\n                document.querySelectorAll('.others_item')[index].style.backgroundImage = `url(${imgPath})`;\n            }\n            this.deleteFn();\n            list.forEach(item => {\n                if (item.firstEvent == \"1\") {\n                    item.IconType = 12\n                    item.Name = item.eventContent\n                    item.TextY = \"-28\"\n                } else if (item.firstEvent == \"2\") {\n                    item.IconType = 14\n                    item.Name = item.eventContent\n                    item.TextY = \"-28\"\n                } else if (item.firstEvent == \"3\") {\n                    item.IconType = 13\n                    item.Name = item.eventContent\n                    item.TextY = \"-28\"\n                } else {// 站点 设备 插座图标\n                    item.IconType = 4\n                    item.Name = `智行充${filterList[this.tabsActive].name}`\n                    item.TextY = \"-42\"\n                }\n                item.Height = \"100\"\n                item.TextX = \"0\"\n                item.TextSize = \"10\"\n                item.ImageX = \"0.25\"\n                item.ImageY = \"0.25\"\n                item.channalCode = item.siteCode\n            });\n            let params = {\n                \"mode\": \"add\",\n                \"sources\": list\n            }\n            this.$eventBus.$emit('senTxtToUe', \"全局视角\");\n            this.$eventBus.$emit('senTxtToUe', params);//撒点\n        },\n        // 字符串超出省略\n        splitFont(val, length) {\n            if (val) {\n                if (val.length > length - 1) {\n                    return val.slice(0, length) + '...';\n                } else {\n                    return val;\n                }\n\n            }\n        },\n        // 烟感设备\n        getSmokeFlagFun(val) {\n            this.smokeEqInfoFlag = val\n        },\n        // 一体化智能杆列表\n        async showDataList(nv) {\n            if (nv == '雷达一体式流量计') {\n                this.tableData = [];\n                this.dialogType = this.IntegratedType\n                this.dialogVisible = true\n                this.tableData = this.IntegratedTableList;\n            } else if (nv == '烟感设备') {\n                this.smokeEqInfoFlag = true\n            }\n\n        },\n        //删除点位\n        deleteFn() {\n            let params = {\n                \"mode\": \"delete\",\n            }\n            this.$eventBus.$emit('senTxtToUe', params);\n        },\n        poiEvent(item, index) {\n            this.deleteFn();\n            if (this.eqCHartIndex == index) {\n                this.eqCHartIndex = null;\n            } else {\n                this.eqCHartIndex = index;\n                getkeyArea({ name: item.name }).then(res => {\n                    if (res.data.code == 200) {\n                        let list = res.data.extra.data;\n                        let params = {\n                            \"mode\": \"add\",\n                            \"sources\": JSON.parse(list[0].points)\n                        }\n                        this.$eventBus.$emit('senTxtToUe', params);//撒点\n                        this.$eventBus.$emit('senTxtToUe', list[0].angle);//聚焦到当前位置\n                    }\n                })\n            }\n        },\n        setUrl(index) {\n            return require(`@/assets/images/comprehensiveSituation/eqCHart-${index + 1}${this.eqCHartIndex == index ? '-active' : ''}.png`);\n        },\n\n\n        // 关闭pdf弹窗\n        pdfPopWindow(nv) {\n            this.pdfDialogFlag = nv\n        },\n\n        // 采集数据\n        getMonitorCountList() {\n            getMonitorCount().then(res => {\n                this.monitorCount = res.data.data;\n                let nameList = ['雷达一体式流量计', '超声波多普勒', '烟感设备']\n                for (var i of this.monitorCount) {\n                    i.name = nameList[i.type - 1];\n                    i.nameAll = i.name\n                    if (i.name.length > 4) {\n                        i.name = i.name.substr(0, 4) + '...';\n                    }\n                }\n            }).catch(e => {\n                console.log(e)\n            });\n        },\n\n        // 应急预案pdf\n        emergencyPlansPdf(row, ceil, rowIndex) {\n            this.pdfDialogFlag = true\n            // window.open(`/pdf/emergencyPlan${row.rowIndex+1}.pdf`)\n            this.PdfUrl = `/pdf/emergencyPlan${row.rowIndex + 1}.pdf`\n        },\n\n\n\n    },\n};\n</script>\n\n<style scoped lang=\"less\">\n.intelligentRodDialog {\n    pointer-events: stroke;\n\n    /deep/ .el-dialog__header {\n        padding: 5rem;\n    }\n\n    /deep/.el-input__inner {\n        background-color: transparent !important;\n        border: 1px solid #1296db;\n        color: #fff;\n    }\n\n    /deep/ .el-table tr {\n        color: #fff;\n    }\n\n    /deep/.el-table,\n    /deep/ .el-table tr,\n    /deep/ .el-table td,\n    /deep/ .el-table th {\n        background-color: transparent !important;\n        border: 0;\n    }\n\n    /deep/ .el-table__cell.is-leaf {\n        border: 0;\n\n    }\n\n    /deep/ .el-table::before {\n        height: 0; // 将高度修改为0\n    }\n\n    ::v-deep .el-dialog {\n        /* background-color: transparent; */\n        background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\n        background-size: 100% 100%;\n\n        .el-dialog__header {\n            padding: 3rem 4rem 0;\n\n            .el-dialog__title {\n                color: #fff;\n                font-size: 1.4rem;\n\n            }\n\n            .el-dialog__close {\n                color: #fff;\n                padding: 1.6rem 1rem 0;\n            }\n        }\n    }\n\n    .GongDan_Content_Top {\n        .el-form {\n            display: flex;\n\n            /deep/.el-form-item__label {\n                color: #fff\n            }\n        }\n    }\n}\n\n.leftView {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n\n    .mainLeft {\n        .topItems {\n            width: 100%;\n            height: 46px;\n            line-height: 46px;\n            font-size: 36px;\n            display: flex;\n            align-items: center;\n            position: relative;\n\n            >span {\n                cursor: pointer;\n                margin-right: 10px;\n            }\n\n            >span:nth-child(2) {\n                margin-left: 10px;\n            }\n\n            .topItemActive {\n                color: #43FFF4;\n            }\n\n            >img {\n                width: 45px;\n                height: 45px;\n                position: absolute;\n                right: 25px;\n                cursor: pointer;\n            }\n        }\n\n        .topItems::before {\n            content: \" \";\n            display: inline-block;\n            width: 11px;\n            height: 46px;\n            background: #4C9FFF;\n            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\n            border-radius: 7px 7px 7px 7px;\n            margin-left: 28px;\n            margin-right: 15px;\n        }\n\n        .areaTit {\n            display: flex;\n            padding-left: 50px;\n            height: 67px;\n            background-size: 100% 100%;\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\n            font-family: Source Han Sans CN, Source Han Sans CN;\n            font-weight: 500;\n            font-size: 45px;\n            color: #FFFFFF;\n            line-height: 66px;\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\n            text-align: left;\n            font-style: normal;\n            text-transform: none;\n\n            >div {\n                margin: 0 10px;\n                cursor: pointer;\n\n            }\n\n            >div.action {\n                color: #43FFF4;\n\n            }\n        }\n\n        .eqCHart {\n            margin-top: 32px;\n            margin-left: 99px;\n            width: 554px;\n            height: 381px;\n            background-size: 100% 100%;\n            background-image: url(\"@/assets/images/comprehensiveSituation/eqCHart-bg.png\");\n            display: flex;\n            flex-wrap: wrap;\n\n            >p {\n                display: flex;\n                flex-direction: column;\n                width: 123px;\n                height: 139px;\n                position: relative;\n                margin-left: 46px;\n                margin-top: 18px;\n                align-items: center;\n                cursor: pointer;\n\n                >img {\n                    position: absolute;\n                    width: 123px;\n                    height: 139px;\n                    top: 0%;\n                    left: 0;\n                    z-index: -1;\n\n                }\n\n                >span {\n                    font-family: Source Han Sans CN, Source Han Sans CN;\n                    font-weight: 500;\n                    font-size: 32px;\n                    color: #FFFFFF;\n                    line-height: 20px;\n                    text-align: left;\n                    font-style: normal;\n                    text-transform: none;\n                }\n\n                >span:nth-child(1) {\n                    margin-top: 83px;\n                    margin-bottom: 32px;\n                }\n\n                >span:nth-child(2) {\n                    white-space: nowrap;\n                }\n            }\n\n            >p:nth-child(4) {\n                margin-left: 130px;\n            }\n\n            >p:nth-child(5) {\n                margin-left: 50px;\n            }\n        }\n\n        .eqStatistics {\n            ::v-deep .dv-scroll-board {\n                padding: 47px 0 0 69px;\n            }\n\n            ::v-deep .dv-scroll-board .header {\n                font-size: 19px;\n            }\n\n            ::v-deep .dv-scroll-board .rows .row-item {\n                font-size: 27px;\n                cursor: pointer;\n            }\n\n            ::v-deep .dv-scroll-board .rows .row-item:hover {\n                // padding-bottom: 10px;\n                color: rgb(70, 168, 253);\n\n            }\n\n        }\n\n        .areaEqStatistics {\n            margin-top: 5rem;\n\n            .areaChart {\n\n                // margin-top: 2rem;\n                .activeArea {\n                    background: #0B2A4E;\n                    box-shadow: inset 0 0 5px 5px rgba(20, 93, 164, 1);\n                }\n\n                .areaType {\n                    display: flex;\n                    width: 100%;\n                    box-shadow: inset 0 0 5px 5px rgba(20, 93, 164, .4);\n                    background: rgba(0, 0, 0, 0.3);\n                    border-radius: 0.25rem;\n\n                    >div {\n                        text-align: center;\n                        width: 20%;\n                        height: 3rem;\n                        line-height: 3rem;\n                        font-size: 1.5rem;\n                        cursor: pointer;\n\n                    }\n                }\n\n            }\n        }\n\n        .hiddenDanger {\n            .dealNum {\n                display: flex;\n                align-items: center;\n                margin-left: 1.75rem;\n                overflow: hidden;\n                margin-top: 2rem;\n\n                .dealTit {\n                    margin-bottom: 1rem;\n                    font-size: 1.5rem;\n                    font-weight: 500;\n                }\n\n                .sum {\n                    float: left;\n                    font-size: 2.28rem;\n                    font-weight: bold;\n\n                    >div {\n                        margin-left: 0.5rem;\n                    }\n\n                    .num {\n                        width: 3rem;\n                        height: 4rem;\n                        line-height: 4rem;\n                        text-align: center;\n                        margin-left: 1rem;\n                        background: url('@/assets/images/right_slices/bg_6.png') no-repeat center 0;\n                        background-size: 100%;\n                    }\n                }\n            }\n\n            .recentlyDanger {\n                margin-top: 1.63rem;\n                padding: 0 28px;\n            }\n\n        }\n\n        .dealDanger {\n            padding: 0 28px;\n\n            .incompleteArea {\n                margin-top: 2rem;\n            }\n\n            .con_stat {\n                margin-top: 1rem;\n                display: flex;\n                justify-content: center;\n                align-content: space-between;\n                flex-wrap: wrap;\n                width: 100%;\n                height: 400px;\n                background: url('@/assets/images/right_slices/bg_pic2.png') no-repeat;\n                background-size: 100%;\n\n                >div {\n\n                    font-size: 1.6rem;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: flex-end;\n                    text-align: center;\n                    width: 33.33%;\n                    height: 10rem;\n                    margin-top: 50px;\n                    padding-bottom: 5px;\n                    cursor: pointer;\n\n                    .num {\n                        height: 3rem;\n                        line-height: 3rem;\n                    }\n\n\n                }\n\n                >div:nth-child(1) {\n                    background: url('@/assets/images/right_slices/icon_pic1.png') no-repeat center 0;\n                    background-size: 85% 90%;\n                }\n\n                >div:nth-child(2) {\n                    background: url('@/assets/images/right_slices/icon_pic2.png') no-repeat center 0;\n                    background-size: 85% 90%;\n\n                }\n\n                >div:nth-child(3) {\n                    background: url('@/assets/images/right_slices/icon_pic3.png') no-repeat center 0;\n                    background-size: 85% 90%;\n\n                }\n\n                >div:nth-child(4) {\n                    background: url('@/assets/images/right_slices/icon_pic4.png') no-repeat center 0;\n                    background-size: 85% 90%;\n                    margin-top: 3rem;\n                    transform: translateX(-110px);\n                }\n\n                >div:nth-child(5) {\n                    background: url('@/assets/images/right_slices/icon_pic5.png') no-repeat center 0;\n                    background-size: 85% 90%;\n                    margin-top: 3rem;\n                }\n            }\n        }\n\n        .warningPush {\n\n\n            .swiper-container {\n                width: 100%;\n                height: 100%;\n            }\n\n            .waring_note_list {\n                width: 94%;\n                margin-top: 38px;\n                padding-left: 25px;\n                height: 502px;\n                overflow-y: scroll;\n\n                .others_item {\n                    height: 124px;\n                    background-size: 100% 100%;\n                    // background-image: url('@/assets/images/ChongDianZhuang/warning-bg.png');\n                    cursor: pointer;\n\n                    .icon {\n                        margin: 0 20px\n                    }\n\n                    .result {\n                        font-weight: bold;\n                        color: #1FC6FF;\n                    }\n\n                    .address {\n                        padding-left: 14px;\n                        height: 76px;\n                        box-sizing: border-box;\n                        line-height: 76px;\n                        background: url('@/assets/images/ChongDianZhuang/waring_bg_i22.png')no-repeat center;\n                        background-size: 100% 100%;\n                        text-shadow: 0px 0px 16px #FF0000;\n                        font-style: normal;\n                        text-transform: none;\n\n                        a {\n                            margin-left: 38px;\n                            color: #fff;\n                            text-shadow: 0px 0px 0px #fff;\n\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .riskWarning {\n        .tabs {\n            display: flex;\n            margin-top: 10px;\n            margin-left: 35px;\n\n            >span {\n                width: 165px;\n                height: 61px;\n                font-family: Source Han Sans CN, Source Han Sans CN;\n                font-weight: 500;\n                font-size: 32px;\n                color: #FFFFFF;\n                text-align: center;\n                line-height: 61px;\n                cursor: pointer;\n                margin-right: 7px;\n                position: relative;\n\n                >img {\n                    width: 165px;\n                    height: 61px;\n                    position: absolute;\n                    z-index: -1;\n                    left: 0;\n                    top: 0;\n                }\n            }\n        }\n    }\n\n}\n</style>"], "mappings": "AAkMA,SAAAA,UAAA;AACA,SAAAC,eAAA,EAAAC,wBAAA;AACA,SAAAC,SAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,YAAA;AAEA,SAAAC,UAAA;AACA,OAAAC,MAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,cAAA;AACA,OAAAC,WAAA;AACA,OAAAC,cAAA;AACA,OAAAC,uBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,eAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,MAAA;MACAC,YAAA;MACAC,SAAA;MACAC,UAAA;MACAC,IAAA;MACAC,OAAA;MACAC,UAAA;MACAC,cAAA;QACAZ,IAAA;QACAa,KAAA;QACAC,MAAA;QACAb,IAAA,GACA;UACAc,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA;MAEA;MACAC,YAAA;MAAA;MACAC,aAAA;MACAC,oBAAA;MACAC,MAAA;QAAA;QACAnB,IAAA;QACAoB,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACAC,MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,WAAA,GACA;QAAA3B,IAAA;QAAA4B,KAAA;MAAA,GACA;QAAA5B,IAAA;QAAA4B,KAAA;MAAA,GACA;QAAA5B,IAAA;QAAA4B,KAAA;MAAA,GACA;QAAA5B,IAAA;QAAA4B,KAAA;MAAA,GACA;QAAA5B,IAAA;QAAA4B,KAAA;MAAA,EACA;MACAC,YAAA;MACAC,OAAA;MACAC,SAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;MACAC,UAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,UAAA;IACAxC,uBAAA;IACAF,WAAA;IACAC,cAAA;IACAL,MAAA;IACAG,cAAA;IACAF,SAAA;IACAC;EACA;EACA6C,KAAA;IACA/B,IAAA;MACAgC,QAAAC,EAAA;QACAC,QAAA,CAAAC,gBAAA,iBAAAC,OAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,CAAAC,eAAA;QACA,IAAA/C,IAAA,GAAApB,UAAA,CAAAoE,MAAA,CAAAH,IAAA,IAAAA,IAAA,CAAArC,IAAA,IAAAiC,EAAA;QACA,KAAAhC,OAAA,GAAAT,IAAA,IAAAS,OAAA;QACA,KAAAC,UAAA;QACA,KAAA0B,UAAA,GAAApC,IAAA,IAAAoC,UAAA;QACA,KAAAC,UAAA,GAAArC,IAAA,IAAAqC,UAAA;MACA;MAAAY,SAAA;IACA;EACA;EACAC,QAAA,GACA;EAEAC,QAAA;IACA,SAAAC,CAAA,QAAAA,CAAA,SAAAA,CAAA;MACA,KAAAjB,MAAA,IAAAiB,CAAA;IACA;IACA,KAAAC,mBAAA;IACA,KAAAC,oBAAA;IACA;IACAxE,wBAAA,GAAAyE,IAAA,CAAAC,GAAA;MACA,IAAAC,IAAA,GAAAD,GAAA,CAAAxD,IAAA,CAAAA,IAAA;MACA,KAAAE,mBAAA,GAAAuD,IAAA;IACA;IACA,KAAAC,gBAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACA,KAAAC,gBAAA;MACAF,OAAA;MACAC,QAAA;MACAzB,MAAA,OAAAA;IACA;EAEA;EAEA2B,QAAA,GAEA;EAEAC,OAAA;IACA;IACAT,qBAAA;MACApE,iBAAA,GAAAqE,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,KAAAtC,WAAA,IAAAC,KAAA,GAAA6B,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAC,SAAA;QACA;MACA;IACA;IACA,MAAAC,eAAAC,MAAA,EAAA5D,IAAA;MACA,IAAAA,IAAA;QAAA;QACA,KAAA6D,oBAAA,CAAAD,MAAA;MACA,WAAA5D,IAAA;QAAA;QACA,KAAA8D,sBAAA,CAAAF,MAAA;MACA,WAAA5D,IAAA;QAAA;QACA,KAAA+D,oBAAA,CAAAH,MAAA;MACA,WAAA5D,IAAA;QAAA;QACA,KAAAkD,gBAAA,CAAAU,MAAA;MACA,WAAA5D,IAAA;QACA,WAAAgE,UAAA,EAAAJ,MAAA;MACA;IACA;IACA;IACAP,iBAAAO,MAAA;MACA9E,YAAA,CAAA8E,MAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAyE,MAAA;UACA,KAAArE,MAAA,GAAAoD,GAAA,CAAAxD,IAAA,CAAAA,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACA0D,iBAAAU,MAAA;MACApF,YAAA,CAAAoF,MAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAY,MAAA,CAAAR,QAAA;UACA,KAAAzD,WAAA,GAAAqD,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAS,OAAA;QACA;UACA,KAAA5C,SAAA,GAAA0B,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAS,OAAA;UACA,KAAAzC,UAAA,GAAAuB,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAU,KAAA;QACA;MACA;IACA;IACA;IACAJ,qBAAAH,MAAA;MACA/E,gBAAA,CAAA+E,MAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,KAAAlC,SAAA,GAAA0B,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAS,OAAA;UACA,KAAAzC,UAAA,GAAAuB,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAU,KAAA;QACA;MACA;IACA;IACA;IACAL,uBAAAF,MAAA;MACAhF,kBAAA,CAAAgF,MAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,KAAAlC,SAAA,GAAA0B,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAS,OAAA;UACA,KAAAzC,UAAA,GAAAuB,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAU,KAAA;QACA;MACA;IACA;IACA;IACAN,qBAAAD,MAAA;MACAjF,gBAAA,CAAAiF,MAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,KAAAlC,SAAA,GAAA0B,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAS,OAAA;UACA,KAAAzC,UAAA,GAAAuB,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAU,KAAA;QACA;MACA;IACA;IACAC,QAAA7E,IAAA;MACA,IAAAA,IAAA;QAAA;QACA,KAAAmC,SAAA,QAAAxB,UAAA;QACA,KAAAsB,WAAA,QAAAI,UAAA,MAAA1B,UAAA;QACA,KAAAqB,YAAA,QAAAM,UAAA,MAAA3B,UAAA;MACA;QAAA;QACA,KAAAsB,WAAA,QAAAI,UAAA;QACA,KAAAL,YAAA,QAAAM,UAAA;QACA,KAAAH,SAAA;MACA;MACA,KAAAL,OAAA;IAEA;IACA;IACAgD,YAAA;MACA,KAAAhD,OAAA;IACA;IACAiD,OAAAC,KAAA;MACA,KAAArE,UAAA,GAAAqE,KAAA;MACA,KAAAlD,OAAA;IACA;IACAmD,WAAAD,KAAA;MACA,OAAAE,OAAA,aAAAF,KAAA,SAAArE,UAAA;IACA;IACAwE,aAAA;MACA,KAAAC,QAAA;MACAlG,WAAA,GAAAsE,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,IAAAP,IAAA,GAAAD,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAmB,MAAA;UACA3B,IAAA,CAAAb,OAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAAlB,KAAA,SAAAkB,IAAA,CAAAlB,KAAA;cACAkB,IAAA,CAAAwC,QAAA;YACA,WAAAxC,IAAA,CAAAlB,KAAA,SAAAkB,IAAA,CAAAlB,KAAA;cACAkB,IAAA,CAAAwC,QAAA;YACA,WAAAxC,IAAA,CAAAlB,KAAA;cACAkB,IAAA,CAAAwC,QAAA;YACA;YACAxC,IAAA,CAAAyC,IAAA,GAAAzC,IAAA,CAAAlB,KAAA;YACAkB,IAAA,CAAA0C,MAAA;YACA1C,IAAA,CAAA2C,KAAA;YACA3C,IAAA,CAAA4C,KAAA;YACA5C,IAAA,CAAA6C,QAAA;YACA7C,IAAA,CAAA8C,MAAA;YACA9C,IAAA,CAAA+C,MAAA;YACA/C,IAAA,CAAAgD,WAAA,GAAAhD,IAAA,CAAAiD,QAAA;UACA;UACA,IAAA1B,MAAA;YACA;YACA,WAAAX;UACA;UACA,KAAAsC,SAAA,CAAAC,KAAA;UACA,KAAAD,SAAA,CAAAC,KAAA,eAAA5B,MAAA;QACA;MACA;IAEA;IACA6B,aAAApD,IAAA,EAAAkC,KAAA;MACArC,QAAA,CAAAC,gBAAA,iBAAAC,OAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,CAAAC,eAAA;MACA,MAAAmD,OAAA,GAAAjB,OAAA;MACAvC,QAAA,CAAAC,gBAAA,iBAAAoC,KAAA,EAAAjC,KAAA,CAAAC,eAAA,UAAAmD,OAAA;MACA,KAAAf,QAAA;MACApG,SAAA;QAAAoH,KAAA,EAAAtD,IAAA,CAAAuD;MAAA,GAAA7C,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;UACA,IAAAhE,IAAA,GAAAwD,GAAA,CAAAxD,IAAA,CAAAiE,KAAA;UACAjE,IAAA,CAAAsF,IAAA,GAAAtF,IAAA,CAAAqG,OAAA;UACArG,IAAA,CAAAqF,QAAA;UACArF,IAAA,CAAAuF,MAAA;UACAvF,IAAA,CAAAwF,KAAA;UACAxF,IAAA,CAAAyF,KAAA;UACAzF,IAAA,CAAA0F,QAAA;UACA1F,IAAA,CAAA2F,MAAA;UACA3F,IAAA,CAAA4F,MAAA;UACA5F,IAAA,CAAAsG,IAAA;UACAtG,IAAA,CAAA6F,WAAA;UACA,IAAAzB,MAAA;YACA;YACA,YAAApE,IAAA;UACA;UACA,KAAA+F,SAAA,CAAAC,KAAA;UACA,KAAAD,SAAA,CAAAC,KAAA,eAAA5B,MAAA;QACA;MACA;IACA;IACA;IACAI,WAAAf,IAAA,EAAAsB,KAAA;MACAwB,OAAA,CAAAC,GAAA,CAAAzB,KAAA;MACA,WAAAA,KAAA;QACArC,QAAA,CAAAC,gBAAA,iBAAAC,OAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,CAAAC,eAAA;QACA,MAAAmD,OAAA,GAAAjB,OAAA;QACAvC,QAAA,CAAAC,gBAAA,iBAAAoC,KAAA,EAAAjC,KAAA,CAAAC,eAAA,UAAAmD,OAAA;MACA;MACA,KAAAf,QAAA;MACA1B,IAAA,CAAAb,OAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAA4D,UAAA;UACA5D,IAAA,CAAAwC,QAAA;UACAxC,IAAA,CAAAyC,IAAA,GAAAzC,IAAA,CAAA6D,YAAA;UACA7D,IAAA,CAAA4C,KAAA;QACA,WAAA5C,IAAA,CAAA4D,UAAA;UACA5D,IAAA,CAAAwC,QAAA;UACAxC,IAAA,CAAAyC,IAAA,GAAAzC,IAAA,CAAA6D,YAAA;UACA7D,IAAA,CAAA4C,KAAA;QACA,WAAA5C,IAAA,CAAA4D,UAAA;UACA5D,IAAA,CAAAwC,QAAA;UACAxC,IAAA,CAAAyC,IAAA,GAAAzC,IAAA,CAAA6D,YAAA;UACA7D,IAAA,CAAA4C,KAAA;QACA;UAAA;UACA5C,IAAA,CAAAwC,QAAA;UACAxC,IAAA,CAAAyC,IAAA,SAAAjD,UAAA,MAAA3B,UAAA,EAAAX,IAAA;UACA8C,IAAA,CAAA4C,KAAA;QACA;QACA5C,IAAA,CAAA0C,MAAA;QACA1C,IAAA,CAAA2C,KAAA;QACA3C,IAAA,CAAA6C,QAAA;QACA7C,IAAA,CAAA8C,MAAA;QACA9C,IAAA,CAAA+C,MAAA;QACA/C,IAAA,CAAAgD,WAAA,GAAAhD,IAAA,CAAAiD,QAAA;MACA;MACA,IAAA1B,MAAA;QACA;QACA,WAAAX;MACA;MACA,KAAAsC,SAAA,CAAAC,KAAA;MACA,KAAAD,SAAA,CAAAC,KAAA,eAAA5B,MAAA;IACA;IACA;IACAuC,UAAAC,GAAA,EAAAC,MAAA;MACA,IAAAD,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA,GAAAA,MAAA;UACA,OAAAD,GAAA,CAAAE,KAAA,IAAAD,MAAA;QACA;UACA,OAAAD,GAAA;QACA;MAEA;IACA;IACA;IACAG,gBAAAH,GAAA;MACA,KAAA3G,eAAA,GAAA2G,GAAA;IACA;IACA;IACA,MAAAI,aAAAvE,EAAA;MACA,IAAAA,EAAA;QACA,KAAAnC,SAAA;QACA,KAAAC,UAAA,QAAAI,cAAA;QACA,KAAAc,aAAA;QACA,KAAAnB,SAAA,QAAAJ,mBAAA;MACA,WAAAuC,EAAA;QACA,KAAAxC,eAAA;MACA;IAEA;IACA;IACAkF,SAAA;MACA,IAAAf,MAAA;QACA;MACA;MACA,KAAA2B,SAAA,CAAAC,KAAA,eAAA5B,MAAA;IACA;IACA6C,SAAApE,IAAA,EAAAkC,KAAA;MACA,KAAAI,QAAA;MACA,SAAAvD,YAAA,IAAAmD,KAAA;QACA,KAAAnD,YAAA;MACA;QACA,KAAAA,YAAA,GAAAmD,KAAA;QACAxF,UAAA;UAAAQ,IAAA,EAAA8C,IAAA,CAAA9C;QAAA,GAAAwD,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;YACA,IAAAP,IAAA,GAAAD,GAAA,CAAAxD,IAAA,CAAAiE,KAAA,CAAAjE,IAAA;YACA,IAAAoE,MAAA;cACA;cACA,WAAA8C,IAAA,CAAAC,KAAA,CAAA1D,IAAA,IAAA2D,MAAA;YACA;YACA,KAAArB,SAAA,CAAAC,KAAA,eAAA5B,MAAA;YACA,KAAA2B,SAAA,CAAAC,KAAA,eAAAvC,IAAA,IAAA4D,KAAA;UACA;QACA;MACA;IACA;IACAC,OAAAvC,KAAA;MACA,OAAAE,OAAA,mDAAAF,KAAA,YAAAnD,YAAA,IAAAmD,KAAA;IACA;IAGA;IACAwC,aAAA9E,EAAA;MACA,KAAAjB,aAAA,GAAAiB,EAAA;IACA;IAEA;IACAY,oBAAA;MACAxE,eAAA,GAAA0E,IAAA,CAAAC,GAAA;QACA,KAAAxC,YAAA,GAAAwC,GAAA,CAAAxD,IAAA,CAAAA,IAAA;QACA,IAAAwH,QAAA;QACA,SAAApE,CAAA,SAAApC,YAAA;UACAoC,CAAA,CAAArD,IAAA,GAAAyH,QAAA,CAAApE,CAAA,CAAA5C,IAAA;UACA4C,CAAA,CAAAqE,OAAA,GAAArE,CAAA,CAAArD,IAAA;UACA,IAAAqD,CAAA,CAAArD,IAAA,CAAA8G,MAAA;YACAzD,CAAA,CAAArD,IAAA,GAAAqD,CAAA,CAAArD,IAAA,CAAA2H,MAAA;UACA;QACA;MACA,GAAAC,KAAA,CAAAC,CAAA;QACArB,OAAA,CAAAC,GAAA,CAAAoB,CAAA;MACA;IACA;IAEA;IACAC,kBAAAC,GAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAxG,aAAA;MACA;MACA,KAAAD,MAAA,wBAAAuG,GAAA,CAAAE,QAAA;IACA;EAIA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}