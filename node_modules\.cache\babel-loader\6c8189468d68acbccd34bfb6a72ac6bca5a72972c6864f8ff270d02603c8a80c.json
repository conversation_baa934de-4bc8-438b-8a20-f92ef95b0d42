{"ast": null, "code": "import * as echarts from 'echarts';\nimport { refuseClassIfication } from '@/api/index.js';\nexport default {\n  name: 'AppropriationStatistics',\n  props: {\n    disStyle: Object,\n    modelName: String\n  },\n  data() {\n    return {\n      statistics: null,\n      data: [{\n        value: 8,\n        name: '支沟建设'\n      }, {\n        value: 10,\n        name: '变压器增容'\n      }, {\n        value: 12,\n        name: '环境增绿'\n      }, {\n        value: 34,\n        name: '自来水管网建设'\n      }, {\n        value: 36,\n        name: '村级公路硬化'\n      }],\n      uid: null\n    };\n  },\n  beforeDestroy() {\n    if (this.statistics) {\n      this.statistics.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.uid = Math.floor(Math.random() * 100) + 0.3;\n      setTimeout(() => {\n        this.myChart();\n      }, 200);\n    });\n  },\n  methods: {\n    myChart() {\n      // 1. 实例化对象\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.statistics != null && this.statistics != \"\" && this.statistics != undefined) {\n          this.statistics.dispose();\n        }\n        this.statistics = echarts.init(document.getElementById(this.uid));\n        this.statistics.clear();\n        if (this.modelName == '垃圾分类') {\n          let params = {\n            year: '2022'\n          };\n          refuseClassIfication(params).then(res => {\n            let dataList = res.data.extra;\n            let nameData = ['厨余垃圾', '有害垃圾', '可回收垃圾', '绿化垃圾', '建筑垃圾', '大件垃圾', '其他垃圾'];\n            let name = '';\n            this.data = [];\n            for (var i of dataList) {\n              name = nameData[i.refuseType - 1];\n              this.data.push({\n                value: i.weightCount,\n                name: name\n              });\n            }\n            this.data.sort((a, b) => {\n              return a.value - b.value;\n            });\n            this.setOption();\n          });\n        } else {\n          this.setOption();\n        }\n      });\n    },\n    setOption() {\n      let option = {\n        color: ['rgba(0, 100, 255,1)', 'rgba(0, 135, 255,1)', 'rgba(0, 164, 255,1)', 'rgba(0, 201, 255,1)', 'rgba(55, 232, 252,1)'],\n        series: [{\n          type: 'pie',\n          radius: ['15%', '80%'],\n          center: ['50%', '50%'],\n          height: '80%',\n          top: \"15%\",\n          left: 'center',\n          width: '90%',\n          roseType: 'area',\n          itemStyle: {\n            borderWidth: 1\n          },\n          label: {\n            alignTo: 'edge',\n            formatter: v => {\n              if (this.modelName == '垃圾分类') {\n                return '{a|' + v.name + '}\\n\\n\\t\\t' + '{b|' + v.value + '吨}';\n              } else {\n                return '{a|' + v.name + '}\\n\\n\\t\\t' + '{b|' + v.value + '小时}';\n              }\n            },\n            // minMargin: 5,\n            edgeDistance: 10,\n            lineHeight: 15,\n            rich: {\n              a: {\n                fontSize: 22,\n                color: '#fff'\n              },\n              b: {\n                fontSize: 22,\n                color: '#fff',\n                fontWeight: 'bold'\n              }\n            }\n          },\n          labelLine: {\n            length: 15,\n            length2: 0,\n            maxSurfaceAngle: 80,\n            lineStyle: {\n              width: 2\n            }\n          },\n          data: this.data\n        }]\n      };\n      if (option && typeof option === 'object') {\n        this.statistics.setOption(option);\n      }\n      window.onresize = this.statistics.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "refuseClassIfication", "name", "props", "disStyle", "Object", "modelName", "String", "data", "statistics", "value", "uid", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "Math", "floor", "random", "setTimeout", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "document", "getElementById", "params", "year", "res", "dataList", "extra", "nameData", "i", "refuseType", "push", "weightCount", "sort", "a", "b", "setOption", "option", "color", "series", "type", "radius", "center", "height", "top", "left", "width", "roseType", "itemStyle", "borderWidth", "label", "alignTo", "formatter", "v", "edgeDistance", "lineHeight", "rich", "fontSize", "fontWeight", "labelLine", "length", "length2", "maxSurfaceAngle", "lineStyle", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/AppropriationStatistics.vue"], "sourcesContent": ["<template>\r\n    <div :id=\"uid\" class=\"box\" ref=\"statistics\" :style=\"disStyle\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { refuseClassIfication } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'AppropriationStatistics',\r\n    props: {\r\n        disStyle: Object,\r\n        modelName: String,\r\n    },\r\n    data() {\r\n        return {\r\n            statistics: null,\r\n            data: [\r\n                { value: 8, name: '支沟建设' },\r\n                { value: 10, name: '变压器增容' },\r\n                { value: 12, name: '环境增绿' },\r\n                { value: 34, name: '自来水管网建设' },\r\n                { value: 36, name: '村级公路硬化' },\r\n            ],\r\n            uid: null,\r\n\r\n        };\r\n    },\r\n    beforeDestroy(){\r\n        if(this.statistics){\r\n            this.statistics.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.uid = Math.floor(Math.random() * 100)+0.3;\r\n            setTimeout(()=>{\r\n                this.myChart()\r\n                \r\n            },200)\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            // 1. 实例化对象\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.statistics != null && this.statistics != \"\" && this.statistics != undefined) {\r\n                    this.statistics.dispose();\r\n                }\r\n                this.statistics = echarts.init(document.getElementById(this.uid));\r\n                this.statistics.clear();\r\n                if (this.modelName == '垃圾分类') {\r\n                    let params = {\r\n                        year: '2022'\r\n                    }\r\n                    refuseClassIfication(params).then(res => {\r\n                        let dataList = res.data.extra;\r\n                        let nameData = ['厨余垃圾', '有害垃圾', '可回收垃圾', '绿化垃圾', '建筑垃圾', '大件垃圾', '其他垃圾'];\r\n                        let name = '';\r\n                        this.data=[]\r\n                        for (var i of dataList) {\r\n                            name = nameData[i.refuseType - 1];\r\n                            this.data.push({\r\n                                value: i.weightCount,\r\n                                name: name\r\n                            })\r\n\r\n                        }\r\n                        this.data.sort((a,b)=>{\r\n                            return a.value-b.value\r\n                        })\r\n                        this.setOption();\r\n\r\n                    })\r\n                }else{\r\n                    this.setOption();\r\n                }\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            let option = {\r\n                color: ['rgba(0, 100, 255,1)', 'rgba(0, 135, 255,1)', 'rgba(0, 164, 255,1)', 'rgba(0, 201, 255,1)', 'rgba(55, 232, 252,1)',],\r\n\r\n                series:\r\n                    [{\r\n                        type: 'pie',\r\n                        radius: ['15%', '80%'],\r\n                        center: ['50%', '50%'],\r\n                        height: '80%',\r\n                        top: \"15%\",\r\n                        left: 'center',\r\n                        width: '90%',\r\n                        roseType: 'area',\r\n\r\n                        itemStyle: {\r\n                            borderWidth: 1\r\n                        },\r\n                        label: {\r\n                            alignTo: 'edge',\r\n                            formatter: (v)=>{\r\n                                if(this.modelName == '垃圾分类'){\r\n                                    return '{a|'+v.name+'}\\n\\n\\t\\t'+'{b|'+v.value+'吨}'\r\n                                }else{\r\n                                    return '{a|'+v.name+'}\\n\\n\\t\\t'+'{b|'+v.value+'小时}'\r\n                                   \r\n                                }\r\n                            },\r\n                            // minMargin: 5,\r\n                            edgeDistance: 10,\r\n                            lineHeight: 15,\r\n                            rich: {\r\n                                a: {\r\n                                    fontSize: 22,\r\n                                    color: '#fff',\r\n\r\n                                },\r\n                                b: {\r\n                                    fontSize: 22,\r\n                                    color: '#fff',\r\n                                    fontWeight: 'bold',\r\n                                }\r\n                            },\r\n                        },\r\n                        labelLine: {\r\n                            length: 15,\r\n                            length2: 0,\r\n                            maxSurfaceAngle: 80,\r\n                            lineStyle: {\r\n                                width: 2,\r\n                            }\r\n                        },\r\n\r\n                        data: this.data\r\n                    }]\r\n\r\n            };\r\n\r\n            if (option && typeof option === 'object') {\r\n                this.statistics.setOption(option);\r\n            }\r\n            window.onresize = this.statistics.resize;\r\n        },\r\n\r\n\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n \r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,QAAA,EAAAC,MAAA;IACAC,SAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAC,UAAA;MACAD,IAAA,GACA;QAAAE,KAAA;QAAAR,IAAA;MAAA,GACA;QAAAQ,KAAA;QAAAR,IAAA;MAAA,GACA;QAAAQ,KAAA;QAAAR,IAAA;MAAA,GACA;QAAAQ,KAAA;QAAAR,IAAA;MAAA,GACA;QAAAQ,KAAA;QAAAR,IAAA;MAAA,EACA;MACAS,GAAA;IAEA;EACA;EACAC,cAAA;IACA,SAAAH,UAAA;MACA,KAAAA,UAAA,CAAAI,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAJ,GAAA,GAAAK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACAC,UAAA;QACA,KAAAC,OAAA;MAEA;IAEA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAf,UAAA,iBAAAA,UAAA,eAAAA,UAAA,IAAAgB,SAAA;UACA,KAAAhB,UAAA,CAAAiB,OAAA;QACA;QACA,KAAAjB,UAAA,GAAAT,OAAA,CAAA2B,IAAA,CAAAC,QAAA,CAAAC,cAAA,MAAAlB,GAAA;QACA,KAAAF,UAAA,CAAAI,KAAA;QACA,SAAAP,SAAA;UACA,IAAAwB,MAAA;YACAC,IAAA;UACA;UACA9B,oBAAA,CAAA6B,MAAA,EAAAN,IAAA,CAAAQ,GAAA;YACA,IAAAC,QAAA,GAAAD,GAAA,CAAAxB,IAAA,CAAA0B,KAAA;YACA,IAAAC,QAAA;YACA,IAAAjC,IAAA;YACA,KAAAM,IAAA;YACA,SAAA4B,CAAA,IAAAH,QAAA;cACA/B,IAAA,GAAAiC,QAAA,CAAAC,CAAA,CAAAC,UAAA;cACA,KAAA7B,IAAA,CAAA8B,IAAA;gBACA5B,KAAA,EAAA0B,CAAA,CAAAG,WAAA;gBACArC,IAAA,EAAAA;cACA;YAEA;YACA,KAAAM,IAAA,CAAAgC,IAAA,EAAAC,CAAA,EAAAC,CAAA;cACA,OAAAD,CAAA,CAAA/B,KAAA,GAAAgC,CAAA,CAAAhC,KAAA;YACA;YACA,KAAAiC,SAAA;UAEA;QACA;UACA,KAAAA,SAAA;QACA;MAEA;IACA;IACAA,UAAA;MACA,IAAAC,MAAA;QACAC,KAAA;QAEAC,MAAA,EACA;UACAC,IAAA;UACAC,MAAA;UACAC,MAAA;UACAC,MAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,QAAA;UAEAC,SAAA;YACAC,WAAA;UACA;UACAC,KAAA;YACAC,OAAA;YACAC,SAAA,EAAAC,CAAA;cACA,SAAAtD,SAAA;gBACA,eAAAsD,CAAA,CAAA1D,IAAA,yBAAA0D,CAAA,CAAAlD,KAAA;cACA;gBACA,eAAAkD,CAAA,CAAA1D,IAAA,yBAAA0D,CAAA,CAAAlD,KAAA;cAEA;YACA;YACA;YACAmD,YAAA;YACAC,UAAA;YACAC,IAAA;cACAtB,CAAA;gBACAuB,QAAA;gBACAnB,KAAA;cAEA;cACAH,CAAA;gBACAsB,QAAA;gBACAnB,KAAA;gBACAoB,UAAA;cACA;YACA;UACA;UACAC,SAAA;YACAC,MAAA;YACAC,OAAA;YACAC,eAAA;YACAC,SAAA;cACAjB,KAAA;YACA;UACA;UAEA7C,IAAA,OAAAA;QACA;MAEA;MAEA,IAAAoC,MAAA,WAAAA,MAAA;QACA,KAAAnC,UAAA,CAAAkC,SAAA,CAAAC,MAAA;MACA;MACA2B,MAAA,CAAAC,QAAA,QAAA/D,UAAA,CAAAgE,MAAA;IACA;EAIA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}