{"ast": null, "code": "import { getAllToken, myUserinfo } from '@/api/userMenu.js';\nexport default {\n  state: {\n    token: '',\n    userInfo: {}\n  },\n  getters: {},\n  mutations: {\n    setUser(state, token) {\n      state.token = token;\n      localStorage.setItem('token', JSON.stringify(token));\n    },\n    setUserInfo(state, userInfo) {\n      state.userInfo = userInfo;\n      localStorage.setItem('userId', JSON.stringify(userInfo.userId));\n    }\n  },\n  actions: {\n    getUser(context, params) {\n      let token = location.href.split('token=')[1].split('#')[0];\n      context.commit('setUser', token);\n    },\n    async getUserInfo(context, params) {\n      await myUserinfo().then(res => {\n        context.commit('setUserInfo', res.data.extra);\n      }).catch(e => {\n        console.log(e);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["getAllToken", "myUserinfo", "state", "token", "userInfo", "getters", "mutations", "setUser", "localStorage", "setItem", "JSON", "stringify", "setUserInfo", "userId", "actions", "getUser", "context", "params", "location", "href", "split", "commit", "getUserInfo", "then", "res", "data", "extra", "catch", "e", "console", "log"], "sources": ["D:/Project/HuaQiaoSanQi/src/store/user.js"], "sourcesContent": ["import { getAllToken, myUserinfo } from '@/api/userMenu.js'\r\n\r\nexport default {\r\n    state: {\r\n        token: '',\r\n        userInfo: {}\r\n\r\n    },\r\n    getters: {\r\n\r\n    },\r\n    mutations: {\r\n        setUser(state, token) {\r\n            state.token = token;\r\n            localStorage.setItem('token', JSON.stringify(token))\r\n        },\r\n        setUserInfo(state, userInfo) {\r\n            state.userInfo = userInfo;\r\n            localStorage.setItem('userId', JSON.stringify(userInfo.userId))\r\n        }\r\n\r\n    },\r\n    actions: {\r\n        getUser(context, params) {\r\n\r\n            let token = location.href.split('token=')[1].split('#')[0];\r\n            context.commit('setUser', token)\r\n        },\r\n        async getUserInfo(context, params) {\r\n            await myUserinfo().then(res => {\r\n                context.commit('setUserInfo', res.data.extra)\r\n            }).catch(e => {\r\n                console.log(e);\r\n            })\r\n        },\r\n    }\r\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,UAAU,QAAQ,mBAAmB;AAE3D,eAAe;EACXC,KAAK,EAAE;IACHC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,CAAC;EAEf,CAAC;EACDC,OAAO,EAAE,CAET,CAAC;EACDC,SAAS,EAAE;IACPC,OAAOA,CAACL,KAAK,EAAEC,KAAK,EAAE;MAClBD,KAAK,CAACC,KAAK,GAAGA,KAAK;MACnBK,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAACR,KAAK,CAAC,CAAC;IACxD,CAAC;IACDS,WAAWA,CAACV,KAAK,EAAEE,QAAQ,EAAE;MACzBF,KAAK,CAACE,QAAQ,GAAGA,QAAQ;MACzBI,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACS,MAAM,CAAC,CAAC;IACnE;EAEJ,CAAC;EACDC,OAAO,EAAE;IACLC,OAAOA,CAACC,OAAO,EAAEC,MAAM,EAAE;MAErB,IAAId,KAAK,GAAGe,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1DJ,OAAO,CAACK,MAAM,CAAC,SAAS,EAAElB,KAAK,CAAC;IACpC,CAAC;IACD,MAAMmB,WAAWA,CAACN,OAAO,EAAEC,MAAM,EAAE;MAC/B,MAAMhB,UAAU,CAAC,CAAC,CAACsB,IAAI,CAACC,GAAG,IAAI;QAC3BR,OAAO,CAACK,MAAM,CAAC,aAAa,EAAEG,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC;MACjD,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAI;QACVC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MAClB,CAAC,CAAC;IACN;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}