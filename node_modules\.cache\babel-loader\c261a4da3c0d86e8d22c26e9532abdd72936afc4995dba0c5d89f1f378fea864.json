{"ast": null, "code": "import * as echarts from 'echarts';\nimport { EconomicDevelopment } from '@/api/index.js';\nexport default {\n  name: 'Trend<PERSON><PERSON>',\n  props: {\n    fontSize: {\n      type: Number,\n      default: 20\n    },\n    height: {\n      type: String,\n      default: '19rem'\n    }\n  },\n  data() {\n    return {\n      trend: '',\n      dataX: [],\n      data: []\n    };\n  },\n  beforeDestroy() {\n    if (this.trend) {\n      this.trend.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.myChart();\n      }, 200);\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.trend != null && this.trend != \"\" && this.trend != undefined) {\n          this.trend.dispose();\n        }\n        this.trend = echarts.init(this.$refs.trend); // 要引用的页面的元素或id值为\n        this.trend.clear();\n        // 生产值实际情况\n        EconomicDevelopment().then(res => {\n          let dataList = res.data.extra;\n          for (var i of dataList) {\n            this.dataX.push(i.edName);\n            this.data.push(i.gdp);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        grid: {\n          top: \"10%\",\n          right: '5%',\n          left: '10%',\n          bottom: '10%'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          },\n          backgroundColor: \"rgba(34, 154, 255, .4)\",\n          borderWidth: \"1\",\n          //边框宽度设置1\n          borderColor: \"rgba(33, 242, 196, 1)\",\n          //设置边框颜色\n          textStyle: {\n            color: \"#fff\",\n            //设置文字颜色\n            fontSize: this.fontSize\n          }\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: this.dataX,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          axisLabel: {\n            color: \"#fff\",\n            padding: [5, 0, 0, 0],\n            fontSize: this.fontSize\n          }\n        },\n        yAxis: {\n          type: 'value',\n          // interval: 1000000,\n          // boundaryGap: [0, '100%'],\n          axisTick: {\n            show: false\n          },\n          // axisLine: {\n          //     show: false,\n          // },\n          splitLine: {\n            //多条横线\n            show: false\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: this.fontSize\n          }\n        },\n        series: [{\n          name: '亿元',\n          type: 'line',\n          smooth: true,\n          symbol: 'none',\n          sampling: 'lttb',\n          itemStyle: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: 'rgba(29, 214, 130, .5)'\n            }, {\n              offset: 1,\n              color: 'rgba(0, 252, 255, .5)'\n            }])\n          },\n          areaStyle: {\n            color: \"rgba(0, 176, 255, .5)\"\n          },\n          data: this.data\n        }]\n      };\n      this.trend.setOption(option, true);\n      window.onresize = this.trend.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "EconomicDevelopment", "name", "props", "fontSize", "type", "Number", "default", "height", "String", "data", "trend", "dataX", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "setTimeout", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "extra", "i", "push", "ed<PERSON>ame", "gdp", "setOption", "option", "grid", "top", "right", "left", "bottom", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "backgroundColor", "borderWidth", "borderColor", "textStyle", "xAxis", "boundaryGap", "axisTick", "show", "axisLine", "axisLabel", "padding", "yAxis", "splitLine", "series", "smooth", "symbol", "sampling", "itemStyle", "graphic", "LinearGradient", "offset", "areaStyle", "window", "onresize", "resize"], "sources": ["src/components/comprehensive/GDPTrend.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div id=\"trend\" ref=\"trend\" :style=\"{ 'height': this.height }\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { EconomicDevelopment } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'TrendChart',\r\n    props: {\r\n        fontSize: {\r\n            type: Number,\r\n            default: 20\r\n        },\r\n        height: {\r\n            type: String,\r\n            default: '19rem'\r\n        }\r\n    },\r\n\r\n    data() {\r\n        return {\r\n            trend: '',\r\n            dataX: [],\r\n            data: []\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.trend) {\r\n            this.trend.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            setTimeout(() => {\r\n                this.myChart()\r\n\r\n            }, 200)\r\n\r\n\r\n        })\r\n\r\n    },\r\n\r\n    methods: {\r\n\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.trend != null && this.trend != \"\" && this.trend != undefined) {\r\n                    this.trend.dispose();\r\n                }\r\n                this.trend = echarts.init(this.$refs.trend); // 要引用的页面的元素或id值为\r\n                this.trend.clear()\r\n                // 生产值实际情况\r\n                EconomicDevelopment().then(res => {\r\n                    let dataList = res.data.extra\r\n                    for (var i of dataList) {\r\n                        this.dataX.push(i.edName);\r\n                        this.data.push(i.gdp)\r\n                    }\r\n                    this.setOption()\r\n                })\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n\r\n            let option = {\r\n                grid: {\r\n                    top: \"10%\",\r\n                    right: '5%',\r\n                    left: '10%',\r\n                    bottom: '10%',\r\n\r\n                },\r\n                tooltip: {\r\n                    trigger: 'axis',\r\n                    axisPointer: {\r\n                        type: 'cross',\r\n                        crossStyle: {\r\n                            color: '#999'\r\n                        }\r\n                    },\r\n                    backgroundColor: \"rgba(34, 154, 255, .4)\",\r\n                    borderWidth: \"1\", //边框宽度设置1\r\n                    borderColor: \"rgba(33, 242, 196, 1)\", //设置边框颜色\r\n                    textStyle: {\r\n                        color: \"#fff\", //设置文字颜色\r\n                        fontSize: this.fontSize\r\n                    },\r\n                },\r\n\r\n                xAxis: {\r\n                    type: 'category',\r\n                    boundaryGap: false,\r\n                    data: this.dataX,\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n                    },\r\n                    axisLabel: {\r\n                        color: \"#fff\",\r\n                        padding: [5, 0, 0, 0],\r\n                        fontSize: this.fontSize,\r\n                    }\r\n                },\r\n                yAxis: {\r\n                    type: 'value',\r\n                    // interval: 1000000,\r\n                    // boundaryGap: [0, '100%'],\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    // axisLine: {\r\n                    //     show: false,\r\n                    // },\r\n                    splitLine: { //多条横线\r\n                        show: false\r\n                    },\r\n\r\n                    axisLabel: {\r\n                        color: \"#fff\",\r\n                        fontSize: this.fontSize,\r\n                    },\r\n\r\n                },\r\n\r\n                series: [\r\n                    {\r\n                        name: '亿元',\r\n                        type: 'line',\r\n                        smooth: true,\r\n                        symbol: 'none',\r\n                        sampling: 'lttb',\r\n                        itemStyle: {\r\n                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                {\r\n                                    offset: 0,\r\n                                    color: 'rgba(29, 214, 130, .5)'\r\n                                },\r\n                                {\r\n                                    offset: 1,\r\n                                    color: 'rgba(0, 252, 255, .5)'\r\n                                }\r\n                            ])\r\n                        },\r\n                        areaStyle: {\r\n\r\n                            color: \"rgba(0, 176, 255, .5)\",\r\n                        },\r\n                        data: this.data\r\n                    }\r\n                ]\r\n            };\r\n            this.trend.setOption(option, true);\r\n            window.onresize = this.trend.resize;\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#trend {\r\n    width: 94%;\r\n    height: 240px;\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EAEAG,KAAA;IACA;MACAC,KAAA;MACAC,KAAA;MACAF,IAAA;IACA;EACA;EACAG,cAAA;IACA,SAAAF,KAAA;MACA,KAAAA,KAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACAC,UAAA;QACA,KAAAC,OAAA;MAEA;IAGA;EAEA;EAEAC,OAAA;IAEAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAX,KAAA,iBAAAA,KAAA,eAAAA,KAAA,IAAAY,SAAA;UACA,KAAAZ,KAAA,CAAAa,OAAA;QACA;QACA,KAAAb,KAAA,GAAAX,OAAA,CAAAyB,IAAA,MAAAC,KAAA,CAAAf,KAAA;QACA,KAAAA,KAAA,CAAAG,KAAA;QACA;QACAb,mBAAA,GAAAqB,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAjB,IAAA,CAAAmB,KAAA;UACA,SAAAC,CAAA,IAAAF,QAAA;YACA,KAAAhB,KAAA,CAAAmB,IAAA,CAAAD,CAAA,CAAAE,MAAA;YACA,KAAAtB,IAAA,CAAAqB,IAAA,CAAAD,CAAA,CAAAG,GAAA;UACA;UACA,KAAAC,SAAA;QACA;MAEA;IACA;IACAA,UAAA;MAEA,IAAAC,MAAA;QACAC,IAAA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,MAAA;QAEA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAtC,IAAA;YACAuC,UAAA;cACAC,KAAA;YACA;UACA;UACAC,eAAA;UACAC,WAAA;UAAA;UACAC,WAAA;UAAA;UACAC,SAAA;YACAJ,KAAA;YAAA;YACAzC,QAAA,OAAAA;UACA;QACA;QAEA8C,KAAA;UACA7C,IAAA;UACA8C,WAAA;UACAzC,IAAA,OAAAE,KAAA;UACAwC,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;UACAE,SAAA;YACAV,KAAA;YACAW,OAAA;YACApD,QAAA,OAAAA;UACA;QACA;QACAqD,KAAA;UACApD,IAAA;UACA;UACA;UACA+C,QAAA;YACAC,IAAA;UACA;UACA;UACA;UACA;UACAK,SAAA;YAAA;YACAL,IAAA;UACA;UAEAE,SAAA;YACAV,KAAA;YACAzC,QAAA,OAAAA;UACA;QAEA;QAEAuD,MAAA,GACA;UACAzD,IAAA;UACAG,IAAA;UACAuD,MAAA;UACAC,MAAA;UACAC,QAAA;UACAC,SAAA;YACAlB,KAAA,MAAA7C,OAAA,CAAAgE,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACArB,KAAA;YACA,GACA;cACAqB,MAAA;cACArB,KAAA;YACA,EACA;UACA;UACAsB,SAAA;YAEAtB,KAAA;UACA;UACAnC,IAAA,OAAAA;QACA;MAEA;MACA,KAAAC,KAAA,CAAAuB,SAAA,CAAAC,MAAA;MACAiC,MAAA,CAAAC,QAAA,QAAA1D,KAAA,CAAA2D,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}