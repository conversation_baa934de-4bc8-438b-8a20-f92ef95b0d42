{"ast": null, "code": "import { getAllType, getOverdueStatistic } from '@/api/userMenu.js';\nimport AreaEqStatistics from '@/components/comprehensive/AreaEqStatistics.vue';\nimport HotAppealType from '@/components/peopleDem/HotAppealType.vue';\nimport ProjectProgress from '@/components/peopleDem/ProjectProgress.vue';\nexport default {\n  name: 'rightView',\n  data() {\n    return {\n      rankTxt: '诉求排行',\n      typeDataList: {},\n      areaType: 'A区',\n      sumProject: 0,\n      overList: [{\n        is_overdue: '按期结办',\n        count: 32923\n      }, {\n        is_overdue: '超期结办',\n        count: 4613\n      }, {\n        is_overdue: '未超期未办结',\n        count: 93701\n      }, {\n        is_overdue: '超期未办结',\n        count: 3001\n      }]\n    };\n  },\n  components: {\n    HotAppealType,\n    ProjectProgress,\n    AreaEqStatistics\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.initData();\n  },\n  updated() {},\n  methods: {\n    toggleDatePanel() {\n      this.rankTxt == '诉求排行' ? this.rankTxt = \"诉求类型\" : this.rankTxt = \"诉求排行\";\n    },\n    initData() {\n      getAllType().then(res => {\n        if (res.status == 200) {\n          this.typeDataList = res.data.data;\n          this.areaType = Object.keys(this.typeDataList)[0];\n        }\n      });\n      getOverdueStatistic().then(res => {\n        if (res.status == 200) {\n          this.overList = res.data.extra;\n        }\n      });\n    },\n    clickAreaType(key, item) {\n      this.areaType = key;\n    }\n  }\n};", "map": {"version": 3, "names": ["getAllType", "getOverdueStatistic", "AreaEqStatistics", "HotAppealType", "ProjectProgress", "name", "data", "rankTxt", "typeDataList", "areaType", "sumProject", "overList", "is_overdue", "count", "components", "watch", "computed", "mounted", "initData", "updated", "methods", "toggleDatePanel", "then", "res", "status", "Object", "keys", "extra", "clickAreaType", "key", "item"], "sources": ["src/views/peopleLivelihood/components/people-data/left-datapanel.vue"], "sourcesContent": ["<template>\r\n    <div class=\"rightView\">\r\n        <div class=\"title-box\">\r\n            <span>数据分析</span>\r\n        </div>\r\n        <div class=\"two-title\">热点诉求类型</div>\r\n        <div class=\"rank-bg\" @click=\"toggleDatePanel\"><span>{{ rankTxt }}</span></div>\r\n        <HotAppealType v-if=\"rankTxt == '诉求排行'\"></HotAppealType>\r\n        <div class=\"statisticsChart\" v-else>\r\n            <div class=\"areaChart\">\r\n\r\n                <div class=\"areaType\">\r\n                    <div v-for=\"(item, key) in typeDataList\" :class=\"areaType == key ? 'activeArea' : ''\"\r\n                        @click=\"clickAreaType(key, item)\">\r\n                        {{ key }}\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n            <div class=\"areaChartsShow\">\r\n                <AreaEqStatistics :areaType=\"areaType\" :typeDataList=\"typeDataList\"></AreaEqStatistics>\r\n            </div>\r\n        </div>\r\n        <div class=\"two-title\">近半年投诉事件趋势</div>\r\n        <div class=\"projectNum\">\r\n            <div class=\"sumTotal\">\r\n                近半年投诉量<span>{{ sumProject }}</span>\r\n            </div>\r\n        </div>\r\n        <div class=\"ProjectProgressChart\">\r\n            <ProjectProgress></ProjectProgress>\r\n        </div>\r\n\r\n        <div class=\"two-title\">部门事件办理详情</div>\r\n        <div class=\"satStatistics\">\r\n            <div class=\"satisfaction oneIcon\">\r\n                <div class=\"tit\">{{ overList[0].is_overdue }}</div>\r\n                <div class=\"num\">\r\n                    <div class=\"serial\">\r\n                        {{ overList[0].count }}\r\n                    </div>\r\n                    <div class=\"per\">\r\n                        {{\r\n                            Math.floor(overList[0].count * 100 / (overList[0].count + overList[1].count\r\n                                + overList[2].count + overList[3].count)) }}%\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"satisfaction twoIcon\">\r\n                <div class=\"tit\">{{ overList[1].is_overdue }}</div>\r\n                <div class=\"num\">\r\n                    <div class=\"serial\">\r\n                        {{ overList[1].count }}\r\n                    </div>\r\n                    <div class=\"per\">\r\n                        {{\r\n                            Math.floor(overList[1].count * 100 / (overList[0].count + overList[1].count\r\n                                + overList[2].count + overList[3].count)) }}%\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"satisfaction threeIcon\">\r\n                <div class=\"tit\">{{ overList[2].is_overdue }}</div>\r\n                <div class=\"num\">\r\n                    <div class=\"serial\">\r\n                        {{ overList[2].count }}\r\n                    </div>\r\n                    <div class=\"per\">\r\n                        {{\r\n                            Math.floor(overList[2].count * 100 / (overList[0].count + overList[1].count\r\n                                + overList[2].count + overList[3].count)) }}%\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"satisfaction fourIcon\">\r\n                <div class=\"tit\">{{ overList[3].is_overdue }}</div>\r\n                <div class=\"num\">\r\n                    <div class=\"serial\">\r\n                        {{ overList[3].count }}\r\n                    </div>\r\n                    <div class=\"per\">\r\n                        {{\r\n                            Math.floor(overList[3].count * 100 / (overList[0].count + overList[1].count\r\n                                + overList[2].count + overList[3].count)) }}%\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getAllType, getOverdueStatistic } from '@/api/userMenu.js'\r\nimport AreaEqStatistics from '@/components/comprehensive/AreaEqStatistics.vue'\r\nimport HotAppealType from '@/components/peopleDem/HotAppealType.vue'\r\nimport ProjectProgress from '@/components/peopleDem/ProjectProgress.vue'\r\nexport default {\r\n    name: 'rightView',\r\n    data() {\r\n        return {\r\n            rankTxt: '诉求排行',\r\n            typeDataList: {},\r\n            areaType: 'A区',\r\n            sumProject: 0,\r\n            overList: [\r\n                {\r\n                    is_overdue: '按期结办',\r\n                    count: 32923\r\n                },\r\n                {\r\n                    is_overdue: '超期结办',\r\n                    count: 4613\r\n                },\r\n                {\r\n                    is_overdue: '未超期未办结',\r\n                    count: 93701\r\n                },\r\n                {\r\n                    is_overdue: '超期未办结',\r\n                    count: 3001\r\n                }\r\n            ]\r\n        };\r\n    },\r\n\r\n    components: {\r\n        HotAppealType,\r\n        ProjectProgress,\r\n        AreaEqStatistics\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.initData()\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        toggleDatePanel() {\r\n            this.rankTxt == '诉求排行' ? this.rankTxt = \"诉求类型\" : this.rankTxt = \"诉求排行\";\r\n        },\r\n        initData() {\r\n            getAllType().then(res => {\r\n                if (res.status == 200) {\r\n                    this.typeDataList = res.data.data;\r\n                    this.areaType = Object.keys(this.typeDataList)[0]\r\n\r\n                }\r\n            })\r\n            getOverdueStatistic().then(res => {\r\n                if (res.status == 200) {\r\n                    this.overList = res.data.extra\r\n                }\r\n            })\r\n        },\r\n        clickAreaType(key, item) {\r\n            this.areaType = key\r\n\r\n        },\r\n\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    width: 700px;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    margin-top: 20px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n\r\n.rightView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n\r\n\r\n    .rank-bg {\r\n        width: 180px;\r\n        height: 45px;\r\n        background: url('@/assets/images/right_slices/people/suqiu.png') no-repeat center;\r\n        // background-size: 61% 94%;\r\n        background-size: cover;\r\n        position: relative;\r\n        left: 490px;\r\n        top: -46px;\r\n        cursor: pointer;\r\n\r\n        span {\r\n            width: 128px;\r\n            height: 57px;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 32px;\r\n            color: #FFFFFF;\r\n            line-height: 56px;\r\n            text-shadow: 0px 5px 7px rgba(0, 0, 0, 0.72), 0px 5px 13px #9F7EFF;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            white-space: nowrap;\r\n            position: absolute;\r\n            margin-left: 24px;\r\n            top: -8px;\r\n        }\r\n    }\r\n\r\n    .statisticsChart {\r\n        width: 100%;\r\n        display: flex;\r\n        // justify-content: space-between;\r\n\r\n        .areaChartsShow {\r\n            width: 70%;\r\n        }\r\n\r\n        .areaChart {\r\n\r\n            margin-top: 1rem;\r\n\r\n            .activeArea {\r\n                background: #0B2A4E;\r\n                box-shadow: inset 0 0 5px 5px rgba(20, 93, 164, 1);\r\n            }\r\n\r\n            .areaType {\r\n                // display: flex;\r\n                width: fit-content;\r\n                box-shadow: inset 0 0 5px 5px rgba(20, 93, 164, .4);\r\n                background: rgba(0, 0, 0, 0.3);\r\n                border-radius: 0.25rem;\r\n\r\n                >div {\r\n                    text-align: center;\r\n                    // width: 20%;\r\n                    padding: 0 20px;\r\n                    height: 3rem;\r\n                    line-height: 3rem;\r\n                    font-size: 1.5rem;\r\n                    cursor: pointer;\r\n\r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n\r\n    .projectNum {\r\n        margin: 0 auto;\r\n        position: relative;\r\n        top: 1.5rem;\r\n        // width: 85%;\r\n        // height: 19rem;\r\n        left: -2.5rem;\r\n        width: 676px;\r\n        height: 196px;\r\n        background: url('@/assets/images/left_slices/people/bg_project.png') no-repeat center;\r\n        // background-color: #9F7EFF;\r\n        background-size: auto;\r\n\r\n\r\n\r\n        .sumTotal {\r\n            display: flex;\r\n            align-items: center;\r\n            line-height: 8rem;\r\n            padding-left: 13rem;\r\n            font-size: 32px;\r\n            margin-top: 20px;\r\n\r\n            // font-size: 1.5rem;\r\n\r\n            >span {\r\n                margin-left: 8.5rem;\r\n                font-size: 2.5rem;\r\n                font-weight: bold;\r\n                background: linear-gradient(0deg, rgba(41, 221, 254, 1) 55%, #FFFFFF 45%);\r\n                -webkit-background-clip: text;\r\n                -webkit-text-fill-color: transparent;\r\n            }\r\n        }\r\n    }\r\n\r\n    .satStatistics {\r\n        margin-top: 3.5rem;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        justify-content: space-around;\r\n        height: 10rem;\r\n        gap: 30px;\r\n        position: relative;\r\n        left: -25px;\r\n\r\n        >div {\r\n            position: relative;\r\n            height: 5.4rem;\r\n            width: 40%;\r\n\r\n            .num {\r\n                font-weight: bold;\r\n                padding: 0 1.9rem 0;\r\n                width: 100%;\r\n                position: absolute;\r\n                bottom: -.2rem;\r\n                display: flex;\r\n                justify-content: space-between;\r\n            }\r\n\r\n            .tit {\r\n                text-align: center;\r\n                line-height: 3.2rem;\r\n            }\r\n        }\r\n\r\n        .satisfaction {\r\n            margin-bottom: 3rem;\r\n        }\r\n\r\n        .oneIcon {\r\n            width: 300px;\r\n            height: 111px;\r\n            background: url('@/assets/images/right_slices/people/icon-one.png')no-repeat center;\r\n\r\n            background-size: cover;\r\n            // background-size: 90%;\r\n\r\n            .tit {\r\n                width: auto;\r\n                height: 28px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 30px;\r\n                color: #9BF4FF;\r\n                line-height: 50px;\r\n                text-align: center;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                position: relative;\r\n            }\r\n\r\n            .num {\r\n\r\n                .serial {\r\n                    position: relative;\r\n                    margin-left: 10px;\r\n                }\r\n\r\n                .per {\r\n                    position: relative;\r\n                    margin-right: 80px;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .twoIcon {\r\n            background: url('@/assets/images/right_slices/people/icon-two.png')no-repeat center;\r\n            width: 300px;\r\n            height: 111px;\r\n\r\n            .tit {\r\n                width: auto;\r\n                height: 28px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 30px;\r\n                color: #E3E8A2;\r\n                line-height: 50px;\r\n                text-align: center;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                position: relative;\r\n            }\r\n\r\n            .num {\r\n\r\n                .serial {\r\n                    position: relative;\r\n                    margin-left: 10px;\r\n                }\r\n\r\n                .per {\r\n                    position: relative;\r\n                    margin-right: 80px;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .threeIcon {\r\n            background: url('@/assets/images/right_slices/people/icon-three.png')no-repeat center;\r\n            width: 300px;\r\n            height: 111px;\r\n\r\n            .tit {\r\n\r\n                width: auto;\r\n                height: 28px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 30px;\r\n                color: #9BFFCD;\r\n                line-height: 58px;\r\n                text-align: center;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                position: relative;\r\n            }\r\n\r\n            .num {\r\n\r\n                .serial {\r\n                    position: relative;\r\n                    margin-left: 10px;\r\n                }\r\n\r\n                .per {\r\n                    position: relative;\r\n                    margin-right: 80px;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .fourIcon {\r\n            background: url('@/assets/images/right_slices/people/icon-four.png')no-repeat center;\r\n            width: 300px;\r\n            height: 111px;\r\n\r\n            .tit {\r\n                width: auto;\r\n                height: 28px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 30px;\r\n                color: #FFAF9B;\r\n                line-height: 58px;\r\n                text-align: center;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                white-space: nowrap;\r\n\r\n            }\r\n\r\n            .num {\r\n\r\n                .serial {\r\n                    position: relative;\r\n                    margin-left: 10px;\r\n                }\r\n\r\n                .per {\r\n                    position: relative;\r\n                    margin-right: 80px;\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        // .noIcon {\r\n        //     background: url('@/assets/images/right_slices/people/noBg_1.png')no-repeat center;\r\n        //     background-size: 90%;\r\n        // }\r\n    }\r\n\r\n\r\n\r\n\r\n\r\n}\r\n</style>"], "mappings": "AAiGA,SAAAA,UAAA,EAAAC,mBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,eAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MACAC,QAAA;MACAC,UAAA;MACAC,QAAA,GACA;QACAC,UAAA;QACAC,KAAA;MACA,GACA;QACAD,UAAA;QACAC,KAAA;MACA,GACA;QACAD,UAAA;QACAC,KAAA;MACA,GACA;QACAD,UAAA;QACAC,KAAA;MACA;IAEA;EACA;EAEAC,UAAA;IACAX,aAAA;IACAC,eAAA;IACAF;EACA;EACAa,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,QAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,gBAAA;MACA,KAAAd,OAAA,kBAAAA,OAAA,iBAAAA,OAAA;IACA;IACAW,SAAA;MACAlB,UAAA,GAAAsB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACA,KAAAhB,YAAA,GAAAe,GAAA,CAAAjB,IAAA,CAAAA,IAAA;UACA,KAAAG,QAAA,GAAAgB,MAAA,CAAAC,IAAA,MAAAlB,YAAA;QAEA;MACA;MACAP,mBAAA,GAAAqB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACA,KAAAb,QAAA,GAAAY,GAAA,CAAAjB,IAAA,CAAAqB,KAAA;QACA;MACA;IACA;IACAC,cAAAC,GAAA,EAAAC,IAAA;MACA,KAAArB,QAAA,GAAAoB,GAAA;IAEA;EAGA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}