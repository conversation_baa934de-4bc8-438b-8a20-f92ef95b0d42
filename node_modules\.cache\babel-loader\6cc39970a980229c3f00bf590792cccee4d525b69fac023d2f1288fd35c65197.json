{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"voice_page\",\n    staticClass: \"voiceChat\"\n  }, [_c(\"img\", {\n    staticClass: \"voiceIcon\",\n    attrs: {\n      src: require(\"@/assets/images/voice_chat/voice_icon.png\"),\n      alt: \"\"\n    },\n    on: {\n      click: _vm.isShowVoiceChat\n    }\n  }), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.isShow,\n      expression: \"isShow\"\n    }],\n    staticClass: \"voiceFrame\"\n  }, [_c(\"div\", {\n    ref: \"scrollContainer\",\n    staticClass: \"text-main\"\n  }, _vm._l(_vm.voiceInfo, function (item) {\n    return _c(\"div\", {\n      staticClass: \"text-box\"\n    }, [item.type == 1 ? _c(\"p\", {\n      staticClass: \"aitext\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/avatar2.png\"),\n        alt: \"\"\n      }\n    }), _c(\"span\", [_vm._v(_vm._s(item.text))])]) : _vm._e(), item.type == 2 ? _c(\"p\", {\n      staticClass: \"myText\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/avatar1.png\"),\n        alt: \"\"\n      }\n    }), _c(\"span\", [_vm._v(_vm._s(item.text))])]) : _vm._e(), item.type == 3 ? _c(\"p\", [_c(\"img\", {\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/avatar2.png\"),\n        alt: \"\"\n      }\n    }), _c(\"span\", {\n      staticClass: \"voiceBar\"\n    }, [item.flag ? _c(\"img\", {\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/voice-bg2.gif\"),\n        alt: \"\"\n      }\n    }) : _c(\"img\", {\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/voice-bg1.png\"),\n        alt: \"\"\n      }\n    })]), _c(\"audio\", {\n      attrs: {\n        id: \"myAudio\",\n        src: item.answerUrl\n      },\n      on: {\n        ended: _vm.onAudioEnded\n      }\n    })]) : _vm._e()]);\n  }), 0), _c(\"div\", {\n    staticClass: \"sendInfo\"\n  }, [_vm._m(0), _c(\"p\", {\n    on: {\n      mousedown: _vm.startRecording,\n      mouseup: _vm.stopRecording\n    }\n  }, [_vm.recordingFlag ? _c(\"span\", {\n    staticClass: \"up\"\n  }, [_vm._v(\"识别中...\")]) : _c(\"span\", {\n    staticClass: \"down\"\n  }, [_vm._v(\"按住说话\")])]), _c(\"p\", [_vm._v(\"发送\")])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"p\", [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/voice_chat/voice_icon1.png\")\n    }\n  })]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "attrs", "src", "require", "alt", "on", "click", "isShowVoiceChat", "directives", "name", "rawName", "value", "isShow", "expression", "_l", "voiceInfo", "item", "type", "_v", "_s", "text", "_e", "flag", "id", "answerUrl", "ended", "onAudioEnded", "_m", "mousedown", "startRecording", "mouseup", "stopRecording", "recordingFlag", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/VoiceChat/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"voice_page\", staticClass: \"voiceChat\" }, [\n    _c(\"img\", {\n      staticClass: \"voiceIcon\",\n      attrs: {\n        src: require(\"@/assets/images/voice_chat/voice_icon.png\"),\n        alt: \"\",\n      },\n      on: { click: _vm.isShowVoiceChat },\n    }),\n    _c(\n      \"div\",\n      {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.isShow,\n            expression: \"isShow\",\n          },\n        ],\n        staticClass: \"voiceFrame\",\n      },\n      [\n        _c(\n          \"div\",\n          { ref: \"scrollContainer\", staticClass: \"text-main\" },\n          _vm._l(_vm.voiceInfo, function (item) {\n            return _c(\"div\", { staticClass: \"text-box\" }, [\n              item.type == 1\n                ? _c(\"p\", { staticClass: \"aitext\" }, [\n                    _c(\"img\", {\n                      attrs: {\n                        src: require(\"@/assets/images/voice_chat/avatar2.png\"),\n                        alt: \"\",\n                      },\n                    }),\n                    _c(\"span\", [_vm._v(_vm._s(item.text))]),\n                  ])\n                : _vm._e(),\n              item.type == 2\n                ? _c(\"p\", { staticClass: \"myText\" }, [\n                    _c(\"img\", {\n                      attrs: {\n                        src: require(\"@/assets/images/voice_chat/avatar1.png\"),\n                        alt: \"\",\n                      },\n                    }),\n                    _c(\"span\", [_vm._v(_vm._s(item.text))]),\n                  ])\n                : _vm._e(),\n              item.type == 3\n                ? _c(\"p\", [\n                    _c(\"img\", {\n                      attrs: {\n                        src: require(\"@/assets/images/voice_chat/avatar2.png\"),\n                        alt: \"\",\n                      },\n                    }),\n                    _c(\"span\", { staticClass: \"voiceBar\" }, [\n                      item.flag\n                        ? _c(\"img\", {\n                            attrs: {\n                              src: require(\"@/assets/images/voice_chat/voice-bg2.gif\"),\n                              alt: \"\",\n                            },\n                          })\n                        : _c(\"img\", {\n                            attrs: {\n                              src: require(\"@/assets/images/voice_chat/voice-bg1.png\"),\n                              alt: \"\",\n                            },\n                          }),\n                    ]),\n                    _c(\"audio\", {\n                      attrs: { id: \"myAudio\", src: item.answerUrl },\n                      on: { ended: _vm.onAudioEnded },\n                    }),\n                  ])\n                : _vm._e(),\n            ])\n          }),\n          0\n        ),\n        _c(\"div\", { staticClass: \"sendInfo\" }, [\n          _vm._m(0),\n          _c(\n            \"p\",\n            {\n              on: { mousedown: _vm.startRecording, mouseup: _vm.stopRecording },\n            },\n            [\n              _vm.recordingFlag\n                ? _c(\"span\", { staticClass: \"up\" }, [_vm._v(\"识别中...\")])\n                : _c(\"span\", { staticClass: \"down\" }, [_vm._v(\"按住说话\")]),\n            ]\n          ),\n          _c(\"p\", [_vm._v(\"发送\")]),\n        ]),\n      ]\n    ),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"p\", [\n      _c(\"img\", {\n        attrs: { src: require(\"@/assets/images/voice_chat/voice_icon1.png\") },\n      }),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,YAAY;IAAEC,WAAW,EAAE;EAAY,CAAC,EAAE,CAChEH,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,2CAA2C,CAAC;MACzDC,GAAG,EAAE;IACP,CAAC;IACDC,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACW;IAAgB;EACnC,CAAC,CAAC,EACFV,EAAE,CACA,KAAK,EACL;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEf,GAAG,CAACgB,MAAM;MACjBC,UAAU,EAAE;IACd,CAAC,CACF;IACDb,WAAW,EAAE;EACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;IAAEE,GAAG,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAAY,CAAC,EACpDJ,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,SAAS,EAAE,UAAUC,IAAI,EAAE;IACpC,OAAOnB,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAW,CAAC,EAAE,CAC5CgB,IAAI,CAACC,IAAI,IAAI,CAAC,GACVpB,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAAS,CAAC,EAAE,CACjCH,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,wCAAwC,CAAC;QACtDC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,CACxC,CAAC,GACFxB,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZL,IAAI,CAACC,IAAI,IAAI,CAAC,GACVpB,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAAS,CAAC,EAAE,CACjCH,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,wCAAwC,CAAC;QACtDC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAACH,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,CACxC,CAAC,GACFxB,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZL,IAAI,CAACC,IAAI,IAAI,CAAC,GACVpB,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,wCAAwC,CAAC;QACtDC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAW,CAAC,EAAE,CACtCgB,IAAI,CAACM,IAAI,GACLzB,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,0CAA0C,CAAC;QACxDC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,GACFP,EAAE,CAAC,KAAK,EAAE;MACRI,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,0CAA0C,CAAC;QACxDC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,CACP,CAAC,EACFP,EAAE,CAAC,OAAO,EAAE;MACVI,KAAK,EAAE;QAAEsB,EAAE,EAAE,SAAS;QAAErB,GAAG,EAAEc,IAAI,CAACQ;MAAU,CAAC;MAC7CnB,EAAE,EAAE;QAAEoB,KAAK,EAAE7B,GAAG,CAAC8B;MAAa;IAChC,CAAC,CAAC,CACH,CAAC,GACF9B,GAAG,CAACyB,EAAE,CAAC,CAAC,CACb,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACDxB,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCJ,GAAG,CAAC+B,EAAE,CAAC,CAAC,CAAC,EACT9B,EAAE,CACA,GAAG,EACH;IACEQ,EAAE,EAAE;MAAEuB,SAAS,EAAEhC,GAAG,CAACiC,cAAc;MAAEC,OAAO,EAAElC,GAAG,CAACmC;IAAc;EAClE,CAAC,EACD,CACEnC,GAAG,CAACoC,aAAa,GACbnC,EAAE,CAAC,MAAM,EAAE;IAAEG,WAAW,EAAE;EAAK,CAAC,EAAE,CAACJ,GAAG,CAACsB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GACrDrB,EAAE,CAAC,MAAM,EAAE;IAAEG,WAAW,EAAE;EAAO,CAAC,EAAE,CAACJ,GAAG,CAACsB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAE7D,CAAC,EACDrB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACsB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACxB,CAAC,CAEN,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIe,eAAe,GAAG,CACpB,YAAY;EACV,IAAIrC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,GAAG,EAAE,CACbA,EAAE,CAAC,KAAK,EAAE;IACRI,KAAK,EAAE;MAAEC,GAAG,EAAEC,OAAO,CAAC,4CAA4C;IAAE;EACtE,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDR,MAAM,CAACuC,aAAa,GAAG,IAAI;AAE3B,SAASvC,MAAM,EAAEsC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}