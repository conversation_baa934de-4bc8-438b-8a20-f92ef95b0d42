{"ast": null, "code": "import addressSearch from '@/components/addressSearch.vue';\nimport VoiceChat from '@/components/VoiceChat/index.vue';\nimport ScreenView from '@/components/ScreenView.vue';\nimport Header from '@/components/Header.vue';\nimport FootBtn from '@/components/FootBtn.vue';\nimport playUeShow from '@/components/play.vue';\nimport pointPop from './fileType/pointPop.vue';\nimport waterWarningType from '@/views/dataScreen/fileType/waterWarningType/warningIndex.vue';\nimport { BuildingSummary, getHouseSummary } from '@/api/userMenu.js';\nimport { mapState } from 'vuex';\nimport WaterQualityDetection from '@/components/smartWater/WaterQualityDetection.vue';\nexport default {\n  name: 'HomePage',\n  components: {\n    ScreenView,\n    Header,\n    FootBtn,\n    playUeShow,\n    pointPop,\n    waterWarningType,\n    WaterQualityDetection,\n    addressSearch,\n    VoiceChat\n  },\n  data() {\n    return {\n      w: 3840,\n      // 设计图尺寸宽ue\n      h: 2160,\n      // 设计图尺寸高\n      //   w: 4840,// 设计图尺寸宽\n      //   h: 2490,// 设计图尺寸高\n\n      isShow: true,\n      peopleHouseInfoPop: false,\n      peopleOneHouseInfoPop: false,\n      peopleInfoList: {},\n      houseTitle: '',\n      peopleOneHouseInfoPopList: [],\n      showPopName: \"\"\n    };\n  },\n  watch: {\n    isScreenShow(nv) {\n      if (nv) {\n        // this.$eventBus.$emit(\"senTxtToUe\", \"清除\")\n        // this.$eventBus.$emit(\"senTxtToUe\", \"首页\")\n        this.$store.commit(\"action/screenClearAll\", false);\n      }\n    },\n    dataChannelText(nv) {\n      // console.log('main',nv)\n      if (nv?.typeName == '人房信息' && this.showPopName == '城市管理') {\n        this.houseTitle = nv.name;\n        this.getBuildingSummary(nv.name);\n      }\n    }\n  },\n  computed: {\n    ...mapState(['dataChannelText']),\n    ...mapState('action', [\"isScreenShow\"]),\n    ...mapState('action', [\"btnIndex\"]),\n    ...mapState('action', [\"changeScreenValue\"]),\n    ...mapState('action', [\"childrenBtn\"]),\n    ...mapState('warningType', ['currentIndex'])\n  },\n  created() {},\n  mounted() {\n    // this.changeScreenValue = this.$route.meta.name;\n    // window.onhashchange = () => {\n    //     this.changeScreenValue = this.$route.meta.name\n    // };\n  },\n  beforeDestroy() {\n    this.isShow = false;\n    this.$store.commit(\"action/screenClearAll\", false);\n  },\n  destroyed() {},\n  methods: {\n    async oneHouseInfo_show(index, row) {\n      this.peopleHouseInfoPop = false;\n      let {\n        qfwm\n      } = row;\n      await getHouseSummary(qfwm).then(res => {\n        this.peopleOneHouseInfoPopList = res.data.extra;\n      });\n      this.peopleOneHouseInfoPop = true;\n    },\n    closeOneHouseInfoPop() {\n      this.peopleOneHouseInfoPop = false;\n      this.peopleHouseInfoPop = true;\n    },\n    // 楼栋信息\n    async getBuildingSummary(val) {\n      let str = val;\n      str = val.replace(/栋/g, '幢');\n      // 截取市后面的字符串\n      str = str.split('市')[1];\n      await BuildingSummary(str).then(res => {\n        this.peopleInfoList = res.data.extra;\n        if (!this.peopleInfoList.houses.length) {\n          this.peopleHouseInfoPop = false;\n        } else {\n          this.peopleOneHouseInfoPop = false;\n          this.peopleHouseInfoPop = true;\n        }\n      });\n    },\n    tabsFn(item, index) {\n      localStorage.removeItem('selectedRowId');\n      this.$store.commit('action/getBufferShow', {\n        flag: false,\n        bufferGeometry: {}\n      });\n      this.showPopName = item;\n      this.$store.commit('action/getIsShowSearch', false);\n      this.peopleOneHouseInfoPop = false;\n      this.peopleHouseInfoPop = false;\n      this.$store.commit('action/getBtnIndex', index); //二级菜单高亮\n      this.$eventBus.$emit(\"senTxtToUe\", this.changeScreenValue);\n      this.$store.commit('setFooterTbsItem', {\n        item,\n        index\n      });\n    },\n    //大屏页面切换\n    changeScreenBtn(value) {\n      localStorage.removeItem('selectedRowId');\n      this.$store.commit('action/getIsShowSearch', false);\n      this.$store.commit('action/getBufferShow', {\n        flag: false,\n        bufferGeometry: {}\n      });\n      this.peopleOneHouseInfoPop = false;\n      this.peopleHouseInfoPop = false;\n      this.$store.commit('action/getBtnIndex', 0); //二级菜单高亮\n      this.$store.commit('action/getChangeScreenValue', value); //一级菜单高亮\n      this.$store.commit('warningType/getCurrentIndex', -1);\n      this.$eventBus.$emit(\"senTxtToUe\", value);\n      this.$store.commit('setFooterTbsItem', {\n        item: value\n      });\n    },\n    getDataLayer(val) {\n      this.$emit('senTxtToUe', val);\n    }\n  }\n};", "map": {"version": 3, "names": ["addressSearch", "VoiceChat", "ScreenView", "Header", "FootBtn", "playUeShow", "pointPop", "waterWarningType", "BuildingSummary", "getHouseSummary", "mapState", "WaterQualityDetection", "name", "components", "data", "w", "h", "isShow", "peopleHouseInfoPop", "peopleOneHouseInfoPop", "peopleInfoList", "houseTitle", "peopleOneHouseInfoPopList", "showPopName", "watch", "isScreenShow", "nv", "$store", "commit", "dataChannelText", "typeName", "getBuildingSummary", "computed", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "methods", "oneHouseInfo_show", "index", "row", "qfwm", "then", "res", "extra", "closeOneHouseInfoPop", "val", "str", "replace", "split", "houses", "length", "tabsFn", "item", "localStorage", "removeItem", "flag", "bufferGeometry", "$eventBus", "$emit", "changeScreenValue", "changeScreenBtn", "value", "get<PERSON>ata<PERSON><PERSON><PERSON>"], "sources": ["src/views/dataScreen/HomePage.vue"], "sourcesContent": ["<template>\r\n    <div class=\"homeBigBox\">\r\n\r\n        <div class=\"screenShow\">\r\n            <v-scale-screen width=\"3840\" height=\"2160\">\r\n                <div class=\"hq-container\">\r\n                    <Header></Header>\r\n                    <div class=\"mainMask\" v-show=\"isScreenShow\">\r\n                        <router-view></router-view>\r\n                    </div>\r\n                    <div class=\"footer\" v-show=\"isScreenShow\">\r\n                        <div class=\"footerBtn\">\r\n                            <router-link to=\"/home/<USER>\">\r\n                                <div :class=\"changeScreenValue == '智慧水务' ? 'activeBTn' : ''\"\r\n                                    @click=\"changeScreenBtn('智慧水务')\" style=\"margin-top: 50px;\">\r\n                                    智慧水务\r\n                                </div>\r\n                            </router-link>\r\n                            <router-link to=\"/home/<USER>\">\r\n                                <div :class=\"changeScreenValue == '综合态势' ? 'activeCenterBTn' : 'centerBtn'\"\r\n                                    @click=\"changeScreenBtn('综合态势')\" style=\"margin: 0 55px; margin-bottom: 20px;\">\r\n                                    综合态势\r\n                                </div>\r\n                            </router-link>\r\n                            <router-link to=\"/home/<USER>\">\r\n                                <div :class=\"changeScreenValue == '民生诉求' ? 'activeBTn' : ''\"\r\n                                    @click=\"changeScreenBtn('民生诉求')\" style=\"margin-top: 50px;\">\r\n                                    民生诉求\r\n                                </div>\r\n                            </router-link>\r\n                        </div>\r\n                        <div class=\"footer-children\">\r\n                            <span :class=\"btnIndex == index ? 'btnActive' : ''\"\r\n                                v-for=\"(item, index) in childrenBtn[changeScreenValue]\" @click=\"tabsFn(item, index)\">{{\r\n                                    item\r\n                                }}</span>\r\n                        </div>\r\n                        <!-- <FootBtn :screenName=changeScreenValue :changeScreenValue=\"changeScreenValue\" :sendScreenBtn=\"changeScreenBtn\"></FootBtn> -->\r\n                    </div>\r\n\r\n                </div>\r\n                <!-- 事件告警 -->\r\n                <!-- <pointPop></pointPop> -->\r\n                <!-- 水务所有预警详情 -->\r\n                <waterWarningType></waterWarningType>\r\n                <!-- 地址搜索 -->\r\n                <addressSearch></addressSearch>\r\n\r\n            </v-scale-screen>\r\n            <!-- 人房信息弹窗展示 -->\r\n            <div class=\"peopleHouseInfo\">\r\n                <el-card class=\"box-card\" v-show=\"peopleHouseInfoPop\">\r\n                    <div slot=\"header\" class=\"clearfix\" style=\"transform: translateY(30px); \">\r\n                        <span> {{ houseTitle.substring(houseTitle.indexOf('号') + 1) }}</span>\r\n                        <el-button icon=\"el-icon-close\" style=\"float: right; padding: 3px 10px; font-size: 20px;\"\r\n                            type=\"text\" @click=\"peopleHouseInfoPop = false\"></el-button>\r\n                    </div>\r\n                    <div class=\"peoplePerShow\">\r\n                        <div class=\"totalHouses\">\r\n                            住户: {{ peopleInfoList.houses?.length }} 户\r\n                        </div>\r\n                        <WaterQualityDetection :width=\"'300px'\" :height=\"'112px'\" :fontSize=\"12\"\r\n                            :peopleCount=\"peopleInfoList\">\r\n                        </WaterQualityDetection>\r\n                    </div>\r\n                    <el-table :data=\"peopleInfoList.houses\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"270\"\r\n                        :header-cell-style=\"{\r\n                            color: '#000',\r\n                            fontWeight: '700',\r\n                            backgroundColor: 'rgba(18, 76, 111, .45)',\r\n\r\n                        }\" class=\"houseTable\">\r\n                        <el-table-column align=\"center\" type=\"index\" label=\"序号\" width=\"50\"> </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"hz.bzaddress\" label=\"门牌号\" :show-overflow-tooltip=\"true\">\r\n                            <template slot-scope=\"scope\">\r\n\r\n                                <span>\r\n                                    {{ scope.row?.qfwm.substring(scope.row.qfwm.indexOf('幢') + 1) }}\r\n                                </span>\r\n                            </template>\r\n                        </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"hz.name\" label=\"户主\" :show-overflow-tooltip=\"true\">\r\n                            <template slot-scope=\"scope\">\r\n                                <span>\r\n                                    {{ scope.row?.hz.name.substring(0, 1) + '*' + scope.row?.hz.name.substring(2) }}\r\n                                </span>\r\n                            </template>\r\n                        </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"hz.phone\" label=\"联系方式\" :show-overflow-tooltip=\"true\">\r\n                        </el-table-column>\r\n                        <el-table-column fixed=\"right\" label=\"操作\" width=\"100\">\r\n                            <template slot-scope=\"scope\">\r\n\r\n                                <el-button @click.native.prevent=\"oneHouseInfo_show(scope.$index, scope.row)\"\r\n                                    type=\"text\" size=\"small\">\r\n                                    详情\r\n                                </el-button>\r\n                            </template>\r\n                        </el-table-column>\r\n\r\n                    </el-table>\r\n                </el-card>\r\n\r\n                <el-card class=\"box-card\" v-show=\"peopleOneHouseInfoPop\">\r\n                    <div slot=\"header\" class=\"clearfix\" style=\"transform: translateY(30px); \">\r\n                        <span>{{ houseTitle.substring(houseTitle.indexOf('号') + 1) }}</span>\r\n                        <el-button icon=\"el-icon-close\" style=\"float: right; padding: 3px 10px; font-size: 20px;\"\r\n                            type=\"text\" @click=\"closeOneHouseInfoPop\"></el-button>\r\n                    </div>\r\n                    <el-table :data=\"peopleOneHouseInfoPopList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"350\"\r\n                        :header-cell-style=\"{\r\n                            color: '#000',\r\n                            fontWeight: '700',\r\n                            backgroundColor: 'rgba(18, 76, 111, .7)',\r\n\r\n                        }\" class=\"houseTable\">\r\n                        <el-table-column align=\"center\" type=\"index\" label=\"序号\" width=\"50\"> </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"qdzShort\" label=\"门牌号\" :show-overflow-tooltip=\"true\">\r\n                            <template slot-scope=\"scope\">\r\n\r\n                                <span>\r\n                                    {{ scope.row?.qdzShort.substring(scope.row.qdzShort.indexOf('幢') + 1) }}\r\n                                </span>\r\n                            </template>\r\n                        </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"name\" label=\"户主\" :show-overflow-tooltip=\"true\">\r\n                            <template slot-scope=\"scope\">\r\n                                <span>\r\n                                    {{ scope.row?.name.substring(0, 1) + '*' + scope.row?.name.substring(2) }}\r\n                                </span>\r\n                            </template>\r\n                        </el-table-column>\r\n                        <el-table-column align=\"center\" prop=\"phone\" label=\"联系方式\" :show-overflow-tooltip=\"true\">\r\n                        </el-table-column>\r\n\r\n                    </el-table>\r\n                </el-card>\r\n\r\n            </div>\r\n            <VoiceChat />\r\n        </div>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport addressSearch from '@/components/addressSearch.vue'\r\nimport VoiceChat from '@/components/VoiceChat/index.vue'\r\nimport ScreenView from '@/components/ScreenView.vue'\r\nimport Header from '@/components/Header.vue'\r\n\r\nimport FootBtn from '@/components/FootBtn.vue'\r\nimport playUeShow from '@/components/play.vue'\r\nimport pointPop from './fileType/pointPop.vue'\r\nimport waterWarningType from '@/views/dataScreen/fileType/waterWarningType/warningIndex.vue'\r\nimport { BuildingSummary, getHouseSummary } from '@/api/userMenu.js'\r\nimport { mapState } from 'vuex'\r\nimport WaterQualityDetection from '@/components/smartWater/WaterQualityDetection.vue'\r\nexport default {\r\n    name: 'HomePage',\r\n    components: {\r\n        ScreenView,\r\n        Header,\r\n        FootBtn,\r\n        playUeShow,\r\n        pointPop,\r\n        waterWarningType,\r\n        WaterQualityDetection,\r\n        addressSearch,\r\n        VoiceChat,\r\n    },\r\n    data() {\r\n        return {\r\n            w: 3840,// 设计图尺寸宽ue\r\n            h: 2160,// 设计图尺寸高\r\n            //   w: 4840,// 设计图尺寸宽\r\n            //   h: 2490,// 设计图尺寸高\r\n\r\n\r\n            isShow: true,\r\n\r\n            peopleHouseInfoPop: false,\r\n            peopleOneHouseInfoPop: false,\r\n            peopleInfoList: {},\r\n            houseTitle: '',\r\n            peopleOneHouseInfoPopList: [],\r\n            showPopName: \"\"\r\n        }\r\n    },\r\n    watch: {\r\n        isScreenShow(nv) {\r\n            if (nv) {\r\n                // this.$eventBus.$emit(\"senTxtToUe\", \"清除\")\r\n                // this.$eventBus.$emit(\"senTxtToUe\", \"首页\")\r\n                this.$store.commit(\"action/screenClearAll\", false)\r\n\r\n            }\r\n        },\r\n        dataChannelText(nv) {\r\n            // console.log('main',nv)\r\n            if (nv?.typeName == '人房信息' && this.showPopName == '城市管理') {\r\n                this.houseTitle = nv.name\r\n                this.getBuildingSummary(nv.name)\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['dataChannelText']),\r\n        ...mapState('action', [\"isScreenShow\"]),\r\n        ...mapState('action', [\"btnIndex\"]),\r\n        ...mapState('action', [\"changeScreenValue\"]),\r\n        ...mapState('action', [\"childrenBtn\"]),\r\n        ...mapState('warningType', ['currentIndex'])\r\n    },\r\n    created() {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        // this.changeScreenValue = this.$route.meta.name;\r\n        // window.onhashchange = () => {\r\n        //     this.changeScreenValue = this.$route.meta.name\r\n        // };\r\n    },\r\n    beforeDestroy() {\r\n        this.isShow = false\r\n        this.$store.commit(\"action/screenClearAll\", false)\r\n    },\r\n    destroyed() {\r\n\r\n    },\r\n\r\n    methods: {\r\n\r\n        async oneHouseInfo_show(index, row) {\r\n            this.peopleHouseInfoPop = false\r\n            let { qfwm } = row;\r\n            await getHouseSummary(qfwm).then(res => {\r\n                this.peopleOneHouseInfoPopList = res.data.extra\r\n            })\r\n\r\n            this.peopleOneHouseInfoPop = true\r\n        },\r\n        closeOneHouseInfoPop() {\r\n            this.peopleOneHouseInfoPop = false\r\n            this.peopleHouseInfoPop = true\r\n        },\r\n        // 楼栋信息\r\n        async getBuildingSummary(val) {\r\n\r\n            let str = val;\r\n            str = val.replace(/栋/g, '幢');\r\n            // 截取市后面的字符串\r\n            str = str.split('市')[1];\r\n            await BuildingSummary(str).then(res => {\r\n                this.peopleInfoList = res.data.extra;\r\n                if (!this.peopleInfoList.houses.length) {\r\n                    this.peopleHouseInfoPop = false;\r\n                } else {\r\n                    this.peopleOneHouseInfoPop = false\r\n                    this.peopleHouseInfoPop = true;\r\n                }\r\n\r\n            })\r\n        },\r\n        tabsFn(item, index) {\r\n            localStorage.removeItem('selectedRowId');\r\n            this.$store.commit('action/getBufferShow', { flag: false, bufferGeometry: {} })\r\n            this.showPopName = item;\r\n            this.$store.commit('action/getIsShowSearch', false)\r\n            this.peopleOneHouseInfoPop = false\r\n            this.peopleHouseInfoPop = false;\r\n            this.$store.commit('action/getBtnIndex', index)//二级菜单高亮\r\n            this.$eventBus.$emit(\"senTxtToUe\", this.changeScreenValue)\r\n            this.$store.commit('setFooterTbsItem', { item, index })\r\n        },\r\n        //大屏页面切换\r\n        changeScreenBtn(value) {\r\n            localStorage.removeItem('selectedRowId');\r\n            this.$store.commit('action/getIsShowSearch', false)\r\n            this.$store.commit('action/getBufferShow', { flag: false, bufferGeometry: {} })\r\n            this.peopleOneHouseInfoPop = false\r\n            this.peopleHouseInfoPop = false;\r\n            this.$store.commit('action/getBtnIndex', 0)//二级菜单高亮\r\n            this.$store.commit('action/getChangeScreenValue', value)//一级菜单高亮\r\n            this.$store.commit('warningType/getCurrentIndex', -1)\r\n            this.$eventBus.$emit(\"senTxtToUe\", value)\r\n            this.$store.commit('setFooterTbsItem', { item: value })\r\n        },\r\n\r\n        getDataLayer(val) {\r\n            this.$emit('senTxtToUe', val)\r\n\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.peopleHouseInfo {\r\n    position: absolute;\r\n    top: 500px;\r\n    left: 80%;\r\n    transform: translate(-50%, -50%);\r\n\r\n    :deep(.el-card) {\r\n        background: url('@/assets/images/mainPics/popBg.png') no-repeat center;\r\n        background-size: 100% 100%;\r\n        border: none;\r\n        color: #fff;\r\n\r\n\r\n    }\r\n\r\n    /deep/ .el-card__header {\r\n        border: none;\r\n        // padding: 30px 40px 15px;\r\n    }\r\n\r\n    /deep/ .el-table,\r\n    /deep/ .el-table tr,\r\n    /deep/ .el-table td,\r\n    /deep/ .el-table th {\r\n        color: #fff !important;\r\n        background-color: transparent !important;\r\n        border: 0;\r\n    }\r\n\r\n    /deep/ .el-table__cell.is-leaf {\r\n        border: 0;\r\n\r\n    }\r\n\r\n    /deep/ .el-table::before {\r\n        height: 0; // 将高度修改为0\r\n    }\r\n\r\n    .text {\r\n        font-size: 12px;\r\n\r\n    }\r\n\r\n    .item {\r\n        margin-bottom: 14px;\r\n    }\r\n\r\n    .clearfix:before,\r\n    .clearfix:after {\r\n        display: table;\r\n        content: \"\";\r\n    }\r\n\r\n    .clearfix:after {\r\n        clear: both\r\n    }\r\n}\r\n\r\n.screenShow {\r\n    .screen-box {\r\n        pointer-events: none;\r\n    }\r\n}\r\n\r\n.returnPage {\r\n    pointer-events: stroke;\r\n    position: absolute;\r\n    left: 28.2%;\r\n    top: 30%;\r\n    width: fit-content;\r\n    height: 40px;\r\n    line-height: 40px;\r\n    background: rgba(16, 107, 171, 0.5);\r\n    cursor: pointer;\r\n    padding: 0 5px;\r\n    user-select: none;\r\n    cursor: pointer;\r\n    color: #fff;\r\n}\r\n\r\n.returnPage.go {\r\n    left: 0;\r\n}\r\n\r\n.returnPage:active {\r\n    background: rgba(16, 107, 171, 1);\r\n\r\n}\r\n\r\n.uiContainer {\r\n    position: fixed;\r\n    top: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.hq-container {\r\n    pointer-events: none;\r\n    position: relative;\r\n    width: 100%;\r\n    min-height: 100%;\r\n    font-size: 1.38rem;\r\n    color: white;\r\n\r\n    .waterPointShow {\r\n        position: absolute;\r\n        bottom: 0;\r\n    }\r\n\r\n    .footer {\r\n        pointer-events: stroke;\r\n        position: absolute;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 12.94rem;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    .footer-children {\r\n        position: absolute;\r\n        display: flex;\r\n        // align-items: center;\r\n        justify-content: center;\r\n        top: -60px;\r\n        // left: 20px;\r\n        margin-left: 50px;\r\n\r\n        >span {\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 40px;\r\n            color: #72E5FF;\r\n            line-height: 55px;\r\n            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.69);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            cursor: pointer;\r\n            margin-right: 75px;\r\n        }\r\n\r\n        >span:nth-child(4),\r\n        >span:nth-last-child(4) {\r\n            margin-top: 40px;\r\n        }\r\n\r\n        >span:nth-child(2),\r\n        >span:nth-child(3) {\r\n            margin-bottom: 20px;\r\n        }\r\n\r\n        .btnActive {\r\n            color: #FFFFFF;\r\n            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.69), 0px 0px 9px #FFC52A;\r\n        }\r\n    }\r\n\r\n    .footerBtn {\r\n        width: 1300px;\r\n        height: 238px;\r\n        position: absolute;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        // margin-bottom: 3rem;\r\n        font-size: 2rem;\r\n        font-weight: 700;\r\n        line-height: 5.53rem;\r\n        background-image: url(\"@/assets/footer-bg.png\");\r\n        background-size: 100% 100%;\r\n\r\n        a {\r\n            text-decoration: none;\r\n            color: #fff;\r\n        }\r\n\r\n        div {\r\n            cursor: pointer;\r\n            width: 291px;\r\n            height: 80px;\r\n            text-align: center;\r\n            background: url('@/assets/images/slices/box4.png') no-repeat center;\r\n            background-size: 100%;\r\n            color: #fff;\r\n            font-size: 43px;\r\n\r\n\r\n        }\r\n\r\n        .activeBTn {\r\n            width: 291px;\r\n            height: 80px;\r\n            line-height: 80px;\r\n            background: url('@/assets/images/slices/box1.png') no-repeat center;\r\n            background-size: 100%;\r\n        }\r\n\r\n        .activeCenterBTn {\r\n            width: 291px;\r\n            height: 80px;\r\n            line-height: 80px;\r\n            font-size: 45px;\r\n            text-align: center;\r\n            background: url('@/assets/images/slices/box2.png') no-repeat center;\r\n            background-size: 100%;\r\n        }\r\n\r\n\r\n\r\n        .centerBtn {\r\n            width: 291px;\r\n            height: 80px;\r\n            line-height: 80px;\r\n            font-size: 43px;\r\n            text-align: center;\r\n            background: url('@/assets/images/slices/box3.png') no-repeat center;\r\n            background-size: 100%;\r\n        }\r\n\r\n\r\n\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAmJA,OAAAA,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,MAAA;AAEA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,QAAA;AACA,OAAAC,gBAAA;AACA,SAAAC,eAAA,EAAAC,eAAA;AACA,SAAAC,QAAA;AACA,OAAAC,qBAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAX,UAAA;IACAC,MAAA;IACAC,OAAA;IACAC,UAAA;IACAC,QAAA;IACAC,gBAAA;IACAI,qBAAA;IACAX,aAAA;IACAC;EACA;EACAa,KAAA;IACA;MACAC,CAAA;MAAA;MACAC,CAAA;MAAA;MACA;MACA;;MAGAC,MAAA;MAEAC,kBAAA;MACAC,qBAAA;MACAC,cAAA;MACAC,UAAA;MACAC,yBAAA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACAC,aAAAC,EAAA;MACA,IAAAA,EAAA;QACA;QACA;QACA,KAAAC,MAAA,CAAAC,MAAA;MAEA;IACA;IACAC,gBAAAH,EAAA;MACA;MACA,IAAAA,EAAA,EAAAI,QAAA,mBAAAP,WAAA;QACA,KAAAF,UAAA,GAAAK,EAAA,CAAAd,IAAA;QACA,KAAAmB,kBAAA,CAAAL,EAAA,CAAAd,IAAA;MACA;IACA;EACA;EACAoB,QAAA;IACA,GAAAtB,QAAA;IACA,GAAAA,QAAA;IACA,GAAAA,QAAA;IACA,GAAAA,QAAA;IACA,GAAAA,QAAA;IACA,GAAAA,QAAA;EACA;EACAuB,QAAA,GAEA;EAEAC,QAAA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC,cAAA;IACA,KAAAlB,MAAA;IACA,KAAAU,MAAA,CAAAC,MAAA;EACA;EACAQ,UAAA,GAEA;EAEAC,OAAA;IAEA,MAAAC,kBAAAC,KAAA,EAAAC,GAAA;MACA,KAAAtB,kBAAA;MACA;QAAAuB;MAAA,IAAAD,GAAA;MACA,MAAA/B,eAAA,CAAAgC,IAAA,EAAAC,IAAA,CAAAC,GAAA;QACA,KAAArB,yBAAA,GAAAqB,GAAA,CAAA7B,IAAA,CAAA8B,KAAA;MACA;MAEA,KAAAzB,qBAAA;IACA;IACA0B,qBAAA;MACA,KAAA1B,qBAAA;MACA,KAAAD,kBAAA;IACA;IACA;IACA,MAAAa,mBAAAe,GAAA;MAEA,IAAAC,GAAA,GAAAD,GAAA;MACAC,GAAA,GAAAD,GAAA,CAAAE,OAAA;MACA;MACAD,GAAA,GAAAA,GAAA,CAAAE,KAAA;MACA,MAAAzC,eAAA,CAAAuC,GAAA,EAAAL,IAAA,CAAAC,GAAA;QACA,KAAAvB,cAAA,GAAAuB,GAAA,CAAA7B,IAAA,CAAA8B,KAAA;QACA,UAAAxB,cAAA,CAAA8B,MAAA,CAAAC,MAAA;UACA,KAAAjC,kBAAA;QACA;UACA,KAAAC,qBAAA;UACA,KAAAD,kBAAA;QACA;MAEA;IACA;IACAkC,OAAAC,IAAA,EAAAd,KAAA;MACAe,YAAA,CAAAC,UAAA;MACA,KAAA5B,MAAA,CAAAC,MAAA;QAAA4B,IAAA;QAAAC,cAAA;MAAA;MACA,KAAAlC,WAAA,GAAA8B,IAAA;MACA,KAAA1B,MAAA,CAAAC,MAAA;MACA,KAAAT,qBAAA;MACA,KAAAD,kBAAA;MACA,KAAAS,MAAA,CAAAC,MAAA,uBAAAW,KAAA;MACA,KAAAmB,SAAA,CAAAC,KAAA,oBAAAC,iBAAA;MACA,KAAAjC,MAAA,CAAAC,MAAA;QAAAyB,IAAA;QAAAd;MAAA;IACA;IACA;IACAsB,gBAAAC,KAAA;MACAR,YAAA,CAAAC,UAAA;MACA,KAAA5B,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;QAAA4B,IAAA;QAAAC,cAAA;MAAA;MACA,KAAAtC,qBAAA;MACA,KAAAD,kBAAA;MACA,KAAAS,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA,gCAAAkC,KAAA;MACA,KAAAnC,MAAA,CAAAC,MAAA;MACA,KAAA8B,SAAA,CAAAC,KAAA,eAAAG,KAAA;MACA,KAAAnC,MAAA,CAAAC,MAAA;QAAAyB,IAAA,EAAAS;MAAA;IACA;IAEAC,aAAAjB,GAAA;MACA,KAAAa,KAAA,eAAAb,GAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}