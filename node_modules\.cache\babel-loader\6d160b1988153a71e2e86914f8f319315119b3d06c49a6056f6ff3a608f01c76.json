{"ast": null, "code": "import typeOption from '@/assets/js/typeOption';\nexport default {\n  name: 'videoTypeOption',\n  props: {},\n  components: {},\n  data() {\n    return {\n      typeOptions: [],\n      valueType: []\n    };\n  },\n  methods: {\n    changeValue(val) {\n      this.$emit('sendKeyWords', val);\n    }\n  },\n  mounted() {\n    this.typeOptions = typeOption;\n  }\n};", "map": {"version": 3, "names": ["typeOption", "name", "props", "components", "data", "typeOptions", "valueType", "methods", "changeValue", "val", "$emit", "mounted"], "sources": ["src/components/videoTypeOption/videoTypeOption.vue"], "sourcesContent": ["<template>\r\n    <div class=\"\">\r\n        <el-select v-model=\"valueType\" clearable filterable placeholder=\"请选择\"  @change=\"changeValue\">\r\n            <el-option v-for=\"item in typeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                :value=\"item.label\">\r\n            </el-option>\r\n        </el-select>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport typeOption from '@/assets/js/typeOption'\r\nexport default {\r\n    name: 'videoTypeOption',\r\n    props: {},\r\n    components: {},\r\n    data() {\r\n        return {\r\n            typeOptions:[],\r\n            valueType: [],\r\n        }\r\n    },\r\n    methods: {\r\n        changeValue(val){\r\n            this.$emit('sendKeyWords',val)\r\n        }\r\n    },\r\n    mounted() {\r\n        this.typeOptions=typeOption\r\n    },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n\r\n</style>"], "mappings": "AAWA,OAAAA,UAAA;AACA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAAC,GAAA;MACA,KAAAC,KAAA,iBAAAD,GAAA;IACA;EACA;EACAE,QAAA;IACA,KAAAN,WAAA,GAAAL,UAAA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}