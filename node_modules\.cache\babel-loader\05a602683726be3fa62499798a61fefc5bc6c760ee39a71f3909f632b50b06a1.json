{"ast": null, "code": "import * as echarts from 'echarts';\nexport default {\n  name: 'AreaSchoolMembersChart',\n  props: {\n    fontSize: {\n      type: Number,\n      default: 24\n    },\n    height: {\n      //高度\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      membersNum: null,\n      yData: ['昆山花桥国际商务城中等专业学校', '昆山花桥高级中学', '昆山市花桥中学', '昆山市花桥集善中学', '昆山市花桥徐公桥中学 ', '昆山市花桥中心小学校', '昆山花桥国际商务城花溪小学', '昆山市花桥徐公桥小学', '昆山花桥经济开发区天福小学', '昆山市花桥鑫苑小学', '昆山市花桥集善小学', '昆山市花桥集善小学(北校区)', '昆山市花桥金城小学', '昆山市花桥中心幼儿园', '昆山市花桥天福幼儿园', '昆山花桥经济开发区金中幼儿园', '昆山花桥经济开发区聚福幼儿园', '昆山花桥经济开发区花溪幼儿园', '昆山花桥经济开发区鑫苑幼儿园', '昆山市花桥枫浜幼儿园', '昆山市花桥徐公桥幼儿园', '昆山花桥曹安幼儿园', '昆山花桥经济开发区启航幼儿园', '昆山花桥开发区横漕幼儿园', '昆山市花桥黄墅江幼儿园'],\n      studentData: [2122, 2501, 1629, 1862, 1142, 1912, 1447, 2374, 427, 1963, 3000, 1144, 3055, 497, 305, 209, 837, 473, 435, 601, 599, 500, 250, 235, 790],\n      teacherData: [173, 207, 118, 145, 65, 103, 88, 129, 22, 108, 158, 55, 159, 38, 21, 14, 54, 29, 26, 39, 40, 28, 20, 16, 45],\n      num: 0\n    };\n  },\n  mounted() {\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.myChart();\n      }, 200);\n    });\n  },\n  beforeDestroy() {\n    if (this.membersNum) {\n      this.membersNum.clear();\n    }\n  },\n  methods: {\n    myChart() {\n      // 1. 实例化对象\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.membersNum != null && this.membersNum != \"\" && this.membersNum != undefined) {\n          this.membersNum.dispose();\n        }\n        this.membersNum = echarts.init(this.$refs.membersNum);\n        this.membersNum.clear();\n        let option = {\n          color: ['rgba(61, 252, 205, 1)', 'rgba(35, 185, 255, 1)'],\n          title: {\n            text: '各校师生人数',\n            textStyle: {\n              color: \"#fff\",\n              fontWeight: 'bold',\n              fontSize: this.fontSize + 2 * 1\n            }\n          },\n          dataZoom: [{\n            yAxisIndex: 0,\n            show: false,\n            type: 'slider',\n            realtime: true,\n            startValue: 0,\n            endValue: 2\n          }],\n          tooltip: {\n            show: false,\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          legend: {\n            x: 'right',\n            itemHeight: 11,\n            itemWidth: 24,\n            textStyle: {\n              fontSize: this.fontSize,\n              color: \"#fff\"\n            }\n          },\n          grid: {\n            top: '30%',\n            left: '6%',\n            right: '10%',\n            bottom: '0%'\n            // containLabel: true\n          },\n          xAxis: {\n            show: false,\n            type: 'value',\n            boundaryGap: [0, 0.01],\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            }\n          },\n          yAxis: {\n            type: 'category',\n            data: this.yData,\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            inverse: true,\n            axisLabel: {\n              color: \"#fff\",\n              verticalAlign: \"bottom\",\n              align: \"left\",\n              padding: [200, 0, 20, 0],\n              fontSize: this.fontSize\n            }\n          },\n          series: [{\n            name: '学生',\n            type: 'bar',\n            barWidth: '30%',\n            data: this.studentData,\n            barMaxWidth: 12,\n            label: {\n              show: true,\n              position: \"right\",\n              color: '#fff',\n              fontWeight: 'bold',\n              fontSize: this.fontSize - 2\n              // padding:[0,10]\n            }\n          }, {\n            name: '教师工',\n            type: 'bar',\n            barWidth: '30%',\n            barGap: '50%',\n            barMaxWidth: 12,\n            data: this.teacherData,\n            label: {\n              show: true,\n              position: \"right\",\n              offset: [0, 5],\n              color: '#fff',\n              fontWeight: 'bold',\n              fontSize: this.fontSize - 2\n            }\n          }]\n        };\n        if (this.num + 1 == this.yData.length) {\n          this.num = 0;\n        } else {\n          this.num++;\n        }\n        option.dataZoom[0].startValue = this.num;\n        option.dataZoom[0].endValue = this.num + 2;\n        if (option && typeof option === 'object') {\n          this.membersNum.setOption(option);\n        }\n        window.onresize = this.membersNum.resize;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "name", "props", "fontSize", "type", "Number", "default", "height", "String", "data", "membersNum", "yData", "studentData", "teacher<PERSON><PERSON>", "num", "mounted", "$nextTick", "setTimeout", "myChart", "<PERSON><PERSON><PERSON><PERSON>", "clear", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "option", "color", "title", "text", "textStyle", "fontWeight", "dataZoom", "yAxisIndex", "show", "realtime", "startValue", "endValue", "tooltip", "trigger", "axisPointer", "legend", "x", "itemHeight", "itemWidth", "grid", "top", "left", "right", "bottom", "xAxis", "boundaryGap", "axisTick", "axisLine", "splitLine", "yAxis", "inverse", "axisLabel", "verticalAlign", "align", "padding", "series", "<PERSON><PERSON><PERSON><PERSON>", "barMaxWidth", "label", "position", "barGap", "offset", "length", "setOption", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/AreaSchoolMembersChart.vue"], "sourcesContent": ["<template>\r\n    <div id=\"membersNum\" ref=\"membersNum\" :style=\"{ 'height': height }\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nexport default {\r\n    name: 'AreaSchoolMembersChart',\r\n    props: {\r\n        fontSize: {\r\n            type: Number,\r\n            default: 24\r\n        },\r\n        height: {//高度\r\n            type: String,\r\n            default: '300px'\r\n        },\r\n    },\r\n    data() {\r\n        return {\r\n            membersNum: null,\r\n            yData: ['昆山花桥国际商务城中等专业学校', '昆山花桥高级中学', '昆山市花桥中学', '昆山市花桥集善中学', '昆山市花桥徐公桥中学 ', '昆山市花桥中心小学校', '昆山花桥国际商务城花溪小学', '昆山市花桥徐公桥小学', '昆山花桥经济开发区天福小学', '昆山市花桥鑫苑小学', '昆山市花桥集善小学', '昆山市花桥集善小学(北校区)', '昆山市花桥金城小学', '昆山市花桥中心幼儿园', '昆山市花桥天福幼儿园', '昆山花桥经济开发区金中幼儿园', '昆山花桥经济开发区聚福幼儿园', '昆山花桥经济开发区花溪幼儿园', '昆山花桥经济开发区鑫苑幼儿园', '昆山市花桥枫浜幼儿园', '昆山市花桥徐公桥幼儿园', '昆山花桥曹安幼儿园', '昆山花桥经济开发区启航幼儿园', '昆山花桥开发区横漕幼儿园', '昆山市花桥黄墅江幼儿园'],\r\n            studentData: [2122, 2501, 1629, 1862, 1142, 1912, 1447, 2374, 427, 1963, 3000, 1144, 3055, 497, 305, 209, 837, 473, 435, 601, 599, 500, 250, 235, 790],\r\n            teacherData: [173, 207, 118, 145, 65, 103, 88, 129, 22, 108, 158, 55, 159, 38, 21, 14, 54, 29, 26, 39, 40, 28, 20, 16, 45],\r\n            num: 0\r\n        };\r\n    },\r\n\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            setTimeout(() => {\r\n                this.myChart()\r\n\r\n            }, 200)\r\n\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n        if (this.membersNum) {\r\n            this.membersNum.clear()\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            // 1. 实例化对象\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.membersNum != null && this.membersNum != \"\" && this.membersNum != undefined) {\r\n                    this.membersNum.dispose();\r\n                }\r\n                this.membersNum = echarts.init(this.$refs.membersNum);\r\n                this.membersNum.clear();\r\n                let option = {\r\n                    color: ['rgba(61, 252, 205, 1)', 'rgba(35, 185, 255, 1)'],\r\n                    title: {\r\n                        text: '各校师生人数',\r\n                        textStyle: {\r\n                            color: \"#fff\",\r\n                            fontWeight: 'bold',\r\n                            fontSize: this.fontSize + 2 * 1,\r\n                        }\r\n                    },\r\n                    dataZoom: [\r\n                        {\r\n                            yAxisIndex: 0,\r\n                            show: false,\r\n                            type: 'slider',\r\n                            realtime: true,\r\n                            startValue: 0,\r\n                            endValue: 2,\r\n                        }\r\n                    ],\r\n                    tooltip: {\r\n                        show: false,\r\n                        trigger: 'axis',\r\n                        axisPointer: {\r\n                            type: 'shadow'\r\n                        }\r\n                    },\r\n                    legend: {\r\n                        x: 'right',\r\n                        itemHeight: 11,\r\n                        itemWidth: 24,\r\n                        textStyle: {\r\n                            fontSize: this.fontSize,\r\n                            color: \"#fff\",\r\n                        }\r\n                    },\r\n                    grid: {\r\n                        top: '30%',\r\n                        left: '6%',\r\n                        right: '10%',\r\n                        bottom: '0%',\r\n                        // containLabel: true\r\n                    },\r\n                    xAxis: {\r\n                        show: false,\r\n                        type: 'value',\r\n                        boundaryGap: [0, 0.01],\r\n\r\n                        axisTick: {\r\n                            show: false\r\n                        },\r\n                        axisLine: {\r\n                            show: false\r\n                        },\r\n                        splitLine: {\r\n                            show: false\r\n                        },\r\n\r\n                    },\r\n                    yAxis: {\r\n                        type: 'category',\r\n                        data: this.yData,\r\n                        axisTick: {\r\n                            show: false\r\n                        },\r\n                        axisLine: {\r\n                            show: false\r\n                        },\r\n                        splitLine: {\r\n                            show: false\r\n                        },\r\n                        inverse: true,\r\n                        axisLabel: {\r\n                            color: \"#fff\",\r\n                            verticalAlign: \"bottom\",\r\n                            align: \"left\",\r\n                            padding: [200, 0, 20, 0],\r\n                            fontSize: this.fontSize\r\n                        },\r\n\r\n                    },\r\n                    series: [\r\n                        {\r\n                            name: '学生',\r\n                            type: 'bar',\r\n                            barWidth: '30%',\r\n                            data: this.studentData,\r\n                            barMaxWidth: 12,\r\n\r\n                            label: {\r\n                                show: true,\r\n                                position: \"right\",\r\n                                color: '#fff',\r\n                                fontWeight: 'bold',\r\n                                fontSize: this.fontSize - 2,\r\n                                // padding:[0,10]\r\n                            }\r\n                        },\r\n                        {\r\n                            name: '教师工',\r\n                            type: 'bar',\r\n                            barWidth: '30%',\r\n                            barGap: '50%',\r\n                            barMaxWidth: 12,\r\n\r\n                            data: this.teacherData,\r\n                            label: {\r\n                                show: true,\r\n                                position: \"right\",\r\n                                offset: [0, 5],\r\n                                color: '#fff',\r\n                                fontWeight: 'bold',\r\n                                fontSize: this.fontSize - 2\r\n                            }\r\n                        }\r\n                    ]\r\n                };\r\n                if (this.num + 1 == this.yData.length) {\r\n                    this.num = 0\r\n\r\n                } else {\r\n                    this.num++\r\n\r\n                }\r\n\r\n                option.dataZoom[0].startValue = this.num\r\n                option.dataZoom[0].endValue = this.num + 2\r\n\r\n                if (option && typeof option === 'object') {\r\n                    this.membersNum.setOption(option);\r\n\r\n                }\r\n\r\n                window.onresize = this.membersNum.resize;\r\n            })\r\n        },\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#membersNum {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,WAAA;MACAC,GAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,SAAA;MACAC,UAAA;QACA,KAAAC,OAAA;MAEA;IAEA;EACA;EACAC,cAAA;IACA,SAAAT,UAAA;MACA,KAAAA,UAAA,CAAAU,KAAA;IACA;EACA;EAEAC,OAAA;IACAH,QAAA;MACA;MACA,IAAAI,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAd,UAAA,iBAAAA,UAAA,eAAAA,UAAA,IAAAe,SAAA;UACA,KAAAf,UAAA,CAAAgB,OAAA;QACA;QACA,KAAAhB,UAAA,GAAAV,OAAA,CAAA2B,IAAA,MAAAC,KAAA,CAAAlB,UAAA;QACA,KAAAA,UAAA,CAAAU,KAAA;QACA,IAAAS,MAAA;UACAC,KAAA;UACAC,KAAA;YACAC,IAAA;YACAC,SAAA;cACAH,KAAA;cACAI,UAAA;cACA/B,QAAA,OAAAA,QAAA;YACA;UACA;UACAgC,QAAA,GACA;YACAC,UAAA;YACAC,IAAA;YACAjC,IAAA;YACAkC,QAAA;YACAC,UAAA;YACAC,QAAA;UACA,EACA;UACAC,OAAA;YACAJ,IAAA;YACAK,OAAA;YACAC,WAAA;cACAvC,IAAA;YACA;UACA;UACAwC,MAAA;YACAC,CAAA;YACAC,UAAA;YACAC,SAAA;YACAd,SAAA;cACA9B,QAAA,OAAAA,QAAA;cACA2B,KAAA;YACA;UACA;UACAkB,IAAA;YACAC,GAAA;YACAC,IAAA;YACAC,KAAA;YACAC,MAAA;YACA;UACA;UACAC,KAAA;YACAhB,IAAA;YACAjC,IAAA;YACAkD,WAAA;YAEAC,QAAA;cACAlB,IAAA;YACA;YACAmB,QAAA;cACAnB,IAAA;YACA;YACAoB,SAAA;cACApB,IAAA;YACA;UAEA;UACAqB,KAAA;YACAtD,IAAA;YACAK,IAAA,OAAAE,KAAA;YACA4C,QAAA;cACAlB,IAAA;YACA;YACAmB,QAAA;cACAnB,IAAA;YACA;YACAoB,SAAA;cACApB,IAAA;YACA;YACAsB,OAAA;YACAC,SAAA;cACA9B,KAAA;cACA+B,aAAA;cACAC,KAAA;cACAC,OAAA;cACA5D,QAAA,OAAAA;YACA;UAEA;UACA6D,MAAA,GACA;YACA/D,IAAA;YACAG,IAAA;YACA6D,QAAA;YACAxD,IAAA,OAAAG,WAAA;YACAsD,WAAA;YAEAC,KAAA;cACA9B,IAAA;cACA+B,QAAA;cACAtC,KAAA;cACAI,UAAA;cACA/B,QAAA,OAAAA,QAAA;cACA;YACA;UACA,GACA;YACAF,IAAA;YACAG,IAAA;YACA6D,QAAA;YACAI,MAAA;YACAH,WAAA;YAEAzD,IAAA,OAAAI,WAAA;YACAsD,KAAA;cACA9B,IAAA;cACA+B,QAAA;cACAE,MAAA;cACAxC,KAAA;cACAI,UAAA;cACA/B,QAAA,OAAAA,QAAA;YACA;UACA;QAEA;QACA,SAAAW,GAAA,aAAAH,KAAA,CAAA4D,MAAA;UACA,KAAAzD,GAAA;QAEA;UACA,KAAAA,GAAA;QAEA;QAEAe,MAAA,CAAAM,QAAA,IAAAI,UAAA,QAAAzB,GAAA;QACAe,MAAA,CAAAM,QAAA,IAAAK,QAAA,QAAA1B,GAAA;QAEA,IAAAe,MAAA,WAAAA,MAAA;UACA,KAAAnB,UAAA,CAAA8D,SAAA,CAAA3C,MAAA;QAEA;QAEA4C,MAAA,CAAAC,QAAA,QAAAhE,UAAA,CAAAiE,MAAA;MACA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}