{"ast": null, "code": "import { interfaceApiAdd, interfaceApiEdit } from '@/api/userMenu';\nexport default {\n  name: \"interfaceDialog\",\n  components: {},\n  data() {\n    return {\n      title: \"\",\n      open: false,\n      form: {},\n      rules: {\n        name: [{\n          required: true,\n          message: \"请输入接口名称\",\n          trigger: \"blur\"\n        }],\n        description: [{\n          required: true,\n          message: \"请输入接口描述\",\n          trigger: \"blur\"\n        }],\n        subject: [{\n          required: true,\n          message: \"请输入接口类型\",\n          trigger: \"blur\"\n        }],\n        createTime: [{\n          required: true,\n          message: \"请输入上线时间\",\n          trigger: \"blur\"\n        }],\n        requestType: [{\n          required: true,\n          message: \"请输入请求方式\",\n          trigger: \"blur\"\n        }],\n        protocol: [{\n          required: true,\n          message: \"请输入接口协议\",\n          trigger: \"blur\"\n        }],\n        url: [{\n          required: true,\n          message: \"请输入接口地址\",\n          trigger: \"blur\"\n        }],\n        paramList: [{\n          required: true,\n          message: \"请输入body参数\",\n          trigger: \"blur\"\n        }]\n      }\n    };\n  },\n  methods: {\n    openAciton(type, info) {\n      this.open = true;\n      this.form = info;\n      this.title = type;\n    },\n    //新增\n    interfaceApiAddList() {\n      interfaceApiAdd(this.form).then(res => {\n        if (res.data.code == 200) {\n          this.open = false;\n          this.$emit('resetList');\n        }\n        this.$message({\n          showClose: true,\n          message: res.data.msg,\n          type: res.data.code == 200 ? \"success\" : \"error\"\n        });\n      });\n    },\n    // 编辑\n    interfaceApiEditList() {\n      interfaceApiEdit(this.form).then(res => {\n        if (res.data.code == 200) {\n          this.open = false;\n          this.$emit('resetList');\n        }\n        this.$message({\n          showClose: true,\n          message: res.data.msg,\n          type: res.data.code == 200 ? \"success\" : \"error\"\n        });\n      });\n    },\n    submitCheck() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.title == 'add') {\n            this.interfaceApiAddList();\n          } else {\n            this.interfaceApiEditList();\n          }\n        } else {\n          return false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["interfaceApiAdd", "interfaceApiEdit", "name", "components", "data", "title", "open", "form", "rules", "required", "message", "trigger", "description", "subject", "createTime", "requestType", "protocol", "url", "paramList", "methods", "openAciton", "type", "info", "interfaceApiAddList", "then", "res", "code", "$emit", "$message", "showClose", "msg", "interfaceApiEditList", "submit<PERSON>heck", "$refs", "validate", "valid"], "sources": ["src/views/mainPage/components/resource-center/components/interfaceDialog.vue"], "sourcesContent": ["<template>\r\n    <el-dialog :title=\"title == 'add' ? '新增接口' : '编辑接口'\" :visible.sync=\"open\" width=\"900px\" top=\"10vh\" :modal=\"false\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\">\r\n            <el-form-item label=\"接口名称:\" prop=\"name\">\r\n                <el-input v-model.trim=\"form.name\" placeholder=\"请输入接口名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"接口描述:\" prop=\"description\">\r\n                <el-input v-model.trim=\"form.description\" placeholder=\"请输入接口描述\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"接口类型:\" prop=\"subject\">\r\n                <el-input v-model.trim=\"form.subject\" placeholder=\"请输入接口类型\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"上线时间:\" prop=\"createTime\">\r\n                <el-input v-model.trim=\"form.createTime\" placeholder=\"请输入上线时间\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"请求方式:\" prop=\"requestType\">\r\n                <el-input v-model.trim=\"form.requestType\" placeholder=\"请输入请求方式\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"接口协议:\" prop=\"protocol\">\r\n                <el-input v-model.trim=\"form.protocol\" placeholder=\"请输入接口协议\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"接口地址:\" prop=\"url\">\r\n                <el-input v-model.trim=\"form.url\" placeholder=\"请输入接口地址\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"body参数:\" prop=\"paramList\" class=\"last-item\">\r\n                <el-input v-model.trim=\"form.paramList\" type=\"textarea\" placeholder=\"请输入body参数\" />\r\n            </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"submitCheck()\" type=\"primary\">确 定</el-button>\r\n            <el-button @click=\"open = false\">取 消</el-button>\r\n        </div>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { interfaceApiAdd, interfaceApiEdit } from '@/api/userMenu'\r\nexport default {\r\n    name: \"interfaceDialog\",\r\n    components: {},\r\n    data() {\r\n        return {\r\n            title: \"\",\r\n            open: false,\r\n            form: {\r\n            },\r\n            rules: {\r\n                name: [{ required: true, message: \"请输入接口名称\", trigger: \"blur\" }],\r\n                description: [\r\n                    { required: true, message: \"请输入接口描述\", trigger: \"blur\" },\r\n                ],\r\n                subject: [\r\n                    { required: true, message: \"请输入接口类型\", trigger: \"blur\" },\r\n                ],\r\n                createTime: [\r\n                    { required: true, message: \"请输入上线时间\", trigger: \"blur\" },\r\n                ],\r\n                requestType: [\r\n                    { required: true, message: \"请输入请求方式\", trigger: \"blur\" },\r\n                ],\r\n                protocol: [\r\n                    { required: true, message: \"请输入接口协议\", trigger: \"blur\" },\r\n                ],\r\n                url: [\r\n                    { required: true, message: \"请输入接口地址\", trigger: \"blur\" },\r\n                ],\r\n                paramList: [\r\n                    { required: true, message: \"请输入body参数\", trigger: \"blur\" },\r\n                ],\r\n            }\r\n        };\r\n    },\r\n    methods: {\r\n        openAciton(type, info) {\r\n            this.open = true;\r\n            this.form = info;\r\n            this.title = type;\r\n        },\r\n        //新增\r\n        interfaceApiAddList() {\r\n            interfaceApiAdd(this.form).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.open = false;\r\n                    this.$emit('resetList');\r\n                }\r\n                this.$message({ showClose: true, message: res.data.msg, type: res.data.code == 200 ? \"success\" : \"error\" });\r\n            })\r\n        },\r\n        // 编辑\r\n        interfaceApiEditList() {\r\n            interfaceApiEdit(this.form).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.open = false;\r\n                    this.$emit('resetList');\r\n                }\r\n                this.$message({ showClose: true, message: res.data.msg, type: res.data.code == 200 ? \"success\" : \"error\" });\r\n            })\r\n        },\r\n        submitCheck() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.title == 'add') {\r\n                        this.interfaceApiAddList();\r\n                    } else {\r\n                        this.interfaceApiEditList();\r\n                    }\r\n\r\n                } else {\r\n                    return false;\r\n                }\r\n            });\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n::v-deep .el-dialog {\r\n    background: #124C6F;\r\n\r\n    .el-dialog__title {\r\n        color: #fff;\r\n    }\r\n\r\n    .el-form-item {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .el-form-item__label {\r\n            width: 120px;\r\n        }\r\n\r\n        .el-form-item__content {\r\n\r\n            margin-left: 40px;\r\n\r\n            .el-input,\r\n            .el-input__inner {\r\n                width: 600px !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .el-textarea {\r\n        height: 35px;\r\n        caret-color: #fff;\r\n\r\n        .el-textarea__inner {\r\n            background: #103452;\r\n            border-radius: 5px;\r\n            border: 1px solid #6A8096;\r\n            color: #fff;\r\n            height: 35px;\r\n            width: 600px !important;\r\n            height: 200px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.uploadInfo {\r\n    display: inline-block;\r\n    margin-top: 10px;\r\n    margin-left: 20px;\r\n}\r\n\r\n::v-deep .el-scrollbar__wrap {\r\n    max-height: 500px;\r\n}\r\n\r\n::v-deep .el-dialog__body {\r\n    padding: 24px;\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n    max-height: 500px;\r\n}\r\n\r\n::v-deep .dialog-footer {\r\n    margin-top: 10px;\r\n}\r\n\r\n::v-deep .el-form {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.maintain-radio {\r\n    ::v-deep .el-form-item__content {\r\n        display: flex;\r\n    }\r\n\r\n    ::v-deep .el-radio--medium.is-bordered {\r\n        margin-right: 18px;\r\n    }\r\n\r\n    ::v-deep .inner-input-radio.el-radio--medium.is-bordered {\r\n        padding: 3px 10px 0 10px;\r\n    }\r\n\r\n    .radio-input {\r\n        ::v-deep .el-input__inner {\r\n            width: 145px;\r\n            margin-left: 15px;\r\n            // padding: 0 10px;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAoCA,SAAAA,eAAA,EAAAC,gBAAA;AACA;EACAC,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA,GACA;MACAC,KAAA;QACAN,IAAA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,OAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,UAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,WAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,QAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,GAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,SAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAQ,OAAA;IACAC,WAAAC,IAAA,EAAAC,IAAA;MACA,KAAAhB,IAAA;MACA,KAAAC,IAAA,GAAAe,IAAA;MACA,KAAAjB,KAAA,GAAAgB,IAAA;IACA;IACA;IACAE,oBAAA;MACAvB,eAAA,MAAAO,IAAA,EAAAiB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArB,IAAA,CAAAsB,IAAA;UACA,KAAApB,IAAA;UACA,KAAAqB,KAAA;QACA;QACA,KAAAC,QAAA;UAAAC,SAAA;UAAAnB,OAAA,EAAAe,GAAA,CAAArB,IAAA,CAAA0B,GAAA;UAAAT,IAAA,EAAAI,GAAA,CAAArB,IAAA,CAAAsB,IAAA;QAAA;MACA;IACA;IACA;IACAK,qBAAA;MACA9B,gBAAA,MAAAM,IAAA,EAAAiB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArB,IAAA,CAAAsB,IAAA;UACA,KAAApB,IAAA;UACA,KAAAqB,KAAA;QACA;QACA,KAAAC,QAAA;UAAAC,SAAA;UAAAnB,OAAA,EAAAe,GAAA,CAAArB,IAAA,CAAA0B,GAAA;UAAAT,IAAA,EAAAI,GAAA,CAAArB,IAAA,CAAAsB,IAAA;QAAA;MACA;IACA;IACAM,YAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,SAAA9B,KAAA;YACA,KAAAkB,mBAAA;UACA;YACA,KAAAQ,oBAAA;UACA;QAEA;UACA;QACA;MACA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}