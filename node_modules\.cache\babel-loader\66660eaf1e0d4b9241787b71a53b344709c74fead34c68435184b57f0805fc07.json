{"ast": null, "code": "import Vue from 'vue';\nimport Vuex from 'vuex';\nimport user from './user.js';\nimport warningType from './warningType.js';\nimport action from './action.js';\nimport eventProcessFlow from './eventProcessFlow.js';\nimport { getTokenAuthentication } from '@/api/hzVideo.js';\nVue.use(Vuex);\nexport default new Vuex.Store({\n  state: {\n    dataChannelText: null,\n    userVideoToken: null,\n    larksr: null,\n    isShowPlay: \"3d\",\n    aspectRatio: 16 / 9,\n    footerTbsItem: null,\n    nodeIndex: 0\n  },\n  mutations: {\n    setNodeIndex(state, data) {\n      state.nodeIndex = data;\n    },\n    // ue场景\n    saveUeLarksr(state, data) {\n      state.larksr = data;\n    },\n    //footertabs切换数据\n    setFooterTbsItem(state, data) {\n      state.footerTbsItem = data;\n    },\n    // 接受ue发来的信息\n    getLarksrInfo(state, data) {\n      // console.log(data,'vuex------------')\n      state.dataChannelText = data;\n    },\n    getAspectRatio(state, data) {\n      state.aspectRatio = data;\n    },\n    getUserVideoTokenData(state, data) {\n      if (data.status == 200) {\n        state.userVideoToken = data.data;\n      } else {\n        state.userVideoToken = null;\n      }\n    },\n    getIsShowPlay(state, data) {\n      state.isShowPlay = data;\n    }\n  },\n  actions: {\n    getUeLarksr({\n      commit\n    }, data) {\n      commit('saveUeLarksr', data);\n    },\n    // 华智区域监控的用户token\n\n    getTokenAuthentication(context) {\n      getTokenAuthentication().then(res => {\n        context.commit('getUserVideoTokenData', res);\n      }).catch(err => {\n        console.log('用户认证失败=============', err);\n      });\n    }\n  },\n  modules: {\n    user,\n    warningType,\n    action,\n    eventProcessFlow\n  }\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "user", "warningType", "action", "eventProcessFlow", "getTokenAuthentication", "use", "Store", "state", "dataChannelText", "userVideoToken", "larksr", "isShowPlay", "aspectRatio", "footerTbsItem", "nodeIndex", "mutations", "setNodeIndex", "data", "saveUeLarksr", "setFooterTbsItem", "getLarksrInfo", "getAspectRatio", "getUserVideoTokenData", "status", "getIsShowPlay", "actions", "getUeLarksr", "commit", "context", "then", "res", "catch", "err", "console", "log", "modules"], "sources": ["D:/Project/HuaQiaoSanQi/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport user from './user.js'\nimport warningType from './warningType.js'\nimport action from './action.js'\nimport eventProcessFlow from './eventProcessFlow.js'\nimport { getTokenAuthentication } from '@/api/hzVideo.js'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    dataChannelText: null,\n    userVideoToken: null,\n    larksr: null,\n    isShowPlay: \"3d\",\n    aspectRatio: 16 / 9,\n    footerTbsItem: null,\n    nodeIndex: 0,\n  },\n  mutations: {\n    setNodeIndex(state, data) {\n      state.nodeIndex = data;\n    },\n    // ue场景\n    saveUeLarksr(state, data) {\n      state.larksr = data;\n\n    },\n    //footertabs切换数据\n    setFooterTbsItem(state, data) {\n      state.footerTbsItem = data;\n    },\n    // 接受ue发来的信息\n    getLarksrInfo(state, data) {\n      // console.log(data,'vuex------------')\n      state.dataChannelText = data;\n    },\n    getAspectRatio(state, data) {\n      state.aspectRatio = data;\n    },\n\n    getUserVideoTokenData(state, data) {\n      if (data.status == 200) {\n        state.userVideoToken = data.data;\n      } else {\n        state.userVideoToken = null;\n      }\n    },\n    getIsShowPlay(state, data) {\n      state.isShowPlay = data;\n    }\n  },\n  actions: {\n    getUeLarksr({ commit }, data) {\n      commit('saveUeLarksr', data)\n    },\n    // 华智区域监控的用户token\n\n    getTokenAuthentication(context) {\n\n      getTokenAuthentication().then(res => {\n        context.commit('getUserVideoTokenData', res);\n      }).catch(err => {\n        console.log('用户认证失败=============', err);\n      })\n    }\n  },\n  modules: {\n    user,\n    warningType,\n    action,\n    eventProcessFlow\n  }\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,sBAAsB,QAAQ,kBAAkB;AAEzDN,GAAG,CAACO,GAAG,CAACN,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACO,KAAK,CAAC;EAC5BC,KAAK,EAAE;IACLC,eAAe,EAAE,IAAI;IACrBC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,EAAE,GAAG,CAAC;IACnBC,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACTC,YAAYA,CAACT,KAAK,EAAEU,IAAI,EAAE;MACxBV,KAAK,CAACO,SAAS,GAAGG,IAAI;IACxB,CAAC;IACD;IACAC,YAAYA,CAACX,KAAK,EAAEU,IAAI,EAAE;MACxBV,KAAK,CAACG,MAAM,GAAGO,IAAI;IAErB,CAAC;IACD;IACAE,gBAAgBA,CAACZ,KAAK,EAAEU,IAAI,EAAE;MAC5BV,KAAK,CAACM,aAAa,GAAGI,IAAI;IAC5B,CAAC;IACD;IACAG,aAAaA,CAACb,KAAK,EAAEU,IAAI,EAAE;MACzB;MACAV,KAAK,CAACC,eAAe,GAAGS,IAAI;IAC9B,CAAC;IACDI,cAAcA,CAACd,KAAK,EAAEU,IAAI,EAAE;MAC1BV,KAAK,CAACK,WAAW,GAAGK,IAAI;IAC1B,CAAC;IAEDK,qBAAqBA,CAACf,KAAK,EAAEU,IAAI,EAAE;MACjC,IAAIA,IAAI,CAACM,MAAM,IAAI,GAAG,EAAE;QACtBhB,KAAK,CAACE,cAAc,GAAGQ,IAAI,CAACA,IAAI;MAClC,CAAC,MAAM;QACLV,KAAK,CAACE,cAAc,GAAG,IAAI;MAC7B;IACF,CAAC;IACDe,aAAaA,CAACjB,KAAK,EAAEU,IAAI,EAAE;MACzBV,KAAK,CAACI,UAAU,GAAGM,IAAI;IACzB;EACF,CAAC;EACDQ,OAAO,EAAE;IACPC,WAAWA,CAAC;MAAEC;IAAO,CAAC,EAAEV,IAAI,EAAE;MAC5BU,MAAM,CAAC,cAAc,EAAEV,IAAI,CAAC;IAC9B,CAAC;IACD;;IAEAb,sBAAsBA,CAACwB,OAAO,EAAE;MAE9BxB,sBAAsB,CAAC,CAAC,CAACyB,IAAI,CAACC,GAAG,IAAI;QACnCF,OAAO,CAACD,MAAM,CAAC,uBAAuB,EAAEG,GAAG,CAAC;MAC9C,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,GAAG,CAAC;MACzC,CAAC,CAAC;IACJ;EACF,CAAC;EACDG,OAAO,EAAE;IACPnC,IAAI;IACJC,WAAW;IACXC,MAAM;IACNC;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}