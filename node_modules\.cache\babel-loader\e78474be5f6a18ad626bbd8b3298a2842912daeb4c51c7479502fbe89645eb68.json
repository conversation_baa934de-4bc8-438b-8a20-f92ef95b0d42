{"ast": null, "code": "import RslPlayer from \"@/components/rslPlayer/rslPlayer_v1.0.all.js\";\nimport { getVideoUrlByCode,\n//查询视频流\nkeepLive //保活\n// closeVideo,\n// batchPollingQueryCutoffList,\n// getWaterMarkSet\n} from '@/api/video';\n// import { mapGetters } from \"vuex\";\nimport { openFullscreen } from '@/utils/fullScreen';\nimport moment from 'moment';\nimport loading from \"@/assets/images/video/player/player-load-ing.png\";\nimport load_fail from '@/assets/images/video/player/player-load-fail.png';\nexport default {\n  name: \"canvas-player-item\",\n  data() {\n    return {\n      stream: '',\n      videoStatusTimer: null,\n      markValue1: 100,\n      markValue2: 14,\n      markOptions: [],\n      player: null,\n      sessionId: '',\n      // 会话id\n      cameraCode: '',\n      // 国标编码\n      keepLiveTimer: null,\n      // 心跳保活定时器;\n      cameraName: '',\n      aliasName: '',\n      installAddr: '',\n      httpUri: '',\n      loading: false,\n      message: '',\n      // 鼠标是否已经移入到播放器\n      enterCanvas: false,\n      // 播放器状态组(无成功状态,加载成功直接展示播放器开始播放)\n      loadingTypeMap: {\n        // 视频加载中\n        \"0\": {\n          img: loading,\n          text: '正在加载中...'\n        },\n        // 视频加载失败\n        \"1\": {\n          img: load_fail,\n          text: '视频加载失败'\n        },\n        \"2\": {\n          img: load_fail,\n          text: '视频资源暂时无法加载，请稍后再试'\n        },\n        \"3\": {\n          img: load_fail,\n          text: ''\n        }\n      },\n      loadingType: \"0\",\n      playerType: false,\n      item: null,\n      show: false,\n      loadingHoverBottom: false,\n      isPlaying: true,\n      isClose: true,\n      // 是否关闭流\n      renderTimer: null,\n      clockTime: '',\n      // ntp服务时间更新\n      isFullScreen: true // 当前的播放器包裹组件v-dialog 是否是全屏状态;\n    };\n  },\n  computed: {\n    // ...mapGetters(['userinfo']),\n    components() {\n      if (this.bottomList && this.bottomList.length > 0) {\n        return this.bottomList.map(bottom => {\n          return {\n            show: false,\n            ...bottom\n          };\n        });\n      }\n      return [];\n    },\n    // 处理宽高变化\n    widthOrHeight() {\n      return {\n        width: this.width,\n        height: this.height\n      };\n    },\n    //获取新高\n    getNewHeight() {\n      return this.height;\n    }\n  },\n  props: {\n    nameFieldDisplay: {\n      type: String,\n      default: '0'\n    },\n    // 是否隐藏底部功能按钮 \n    hideAllFunc: {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    },\n    // 播放器宽度\n    width: {\n      type: Number,\n      default() {\n        return 300;\n      }\n    },\n    // 播放器高度\n    height: {\n      type: Number,\n      default() {\n        return 150;\n      }\n    },\n    // 播放器id 不重复\n    id: {\n      type: String,\n      default() {\n        return 'canvas-player';\n      }\n    },\n    // 是否显示全屏图标\n    showFull: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    // 是否显示关闭图标\n    showClose: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    // 是否显示播放暂停的按钮;\n    showStopAndPlay: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    // hover时头部的颜色\n    bottomHoverColor: {\n      type: String,\n      default() {\n        return 'transparent';\n      }\n    },\n    topHoverColor: {\n      type: String,\n      default() {\n        return 'transparent';\n      }\n    },\n    // 底部的按钮组\n    bottomList: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    prefix: {\n      type: String,\n      default() {\n        return 'canvasPlayer-slot';\n      }\n    },\n    // 是否是视频融合在调用播放器\n    isFuse: {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    },\n    // 当前调用组件的自定义标志符\n    current: {\n      type: String,\n      default() {\n        return '';\n      }\n    },\n    // 当前的包裹组件是否在全屏状态\n    isFull: {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    }\n  },\n  mounted() {\n    this.initPlayer();\n    // this.getWatermarkOptions()\n    // try {\n    //   this.clockTime = this.$moment(Date.now() + this.$store.state.clock.offset).format('MM月DD日 HH:mm:ss') // 初始化默认值\n    //   // 监听ntf服务器时间变化并处理\n    //   eventBus.$on(EVENT_NAMES.SECOND, (now) => {\n    //     // 无需根据差值计算，直接使用（`now`是计算后的当前时间时间戳）\n    //     // 该Date对象的时间纠差后的标准时间\n    //     // const date = new Date(now);\n    //     // 亦可直接用于moment中进行格式化\n    //     this.clockTime = this.$moment(now).format('MM月DD日 HH:mm:ss');\n    //   });\n    // } catch(e) {\n    //   console.log(e)\n    //   this.clockTime = '' // 遇到错误重置ntp服务时间为空字符\n    // }\n  },\n  methods: {\n    // async getWatermarkOptions() {\n    //   await getWaterMarkSet().then((res) => {\n    //     this.markOptions = res.data\n    //     this.markOptions.forEach(item => {\n    //       if (item.dictLabel == 'font_transparence') {\n    //         this.markValue1 = Number(item.dictValue)\n    //       } else if (item.dictLabel == 'font_size') {\n    //         this.markValue2 = Number(item.dictValue)\n    //       }\n    //     })\n    //   });\n    // },\n    resetWatermark(item) {\n      // this.getWatermarkOptions()\n      // let { cameraIp  } = item;\n      // let { nickName } = this.userinfo;\n      // this.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n    },\n    // 初始化播放器;\n    initPlayer() {\n      let _that = this;\n      this.player = new RslPlayer({\n        canvasId: _that.id,\n        callback: _that.playerCallback\n      });\n    },\n    // 画布响应鼠标移入移出事件\n    hoverHandler(enterCanvas) {\n      if (!this.loading) {\n        // this.$set(this, 'enterCanvas', enterCanvas)\n        this.enterCanvas = enterCanvas;\n      }\n    },\n    handleBtnClick(btn, index) {\n      // this.$set(this, 'enterCanvas', false);\n      this.enterCanvas = false;\n      this.$nextTick(() => {\n        // this.$set(this, 'show', true);\n        this.show = true;\n        const componentRef = `bottomBtn${index}`;\n        if (this.$refs[componentRef] && this.$refs[componentRef][0] && this.$refs[componentRef][0].init) {\n          this.$refs[componentRef][0].init(this.item, index, this.current);\n        }\n        this.$emit('handleBtnClick', btn, index);\n      });\n    },\n    // 开始播放;\n    play(item) {\n      let _that = this;\n      let {\n        cameraCode,\n        cameraName,\n        aliasName,\n        cameraIp,\n        installAddr\n      } = item;\n\n      // let { nickName } = this.userinfo.sysUser;\n      const nickName = \"南星渎商业三期\";\n      // console.log(this.userinfo, '---', nickName)\n      this.isClose = false;\n      // this.$set(this, 'loading', true);\n      // this.$set(this, 'loadingType', '0');\n      // this.$set(this, 'cameraCode', cameraCode);\n      // this.$set(this, 'cameraName', cameraName);\n      // this.$set(this, 'aliasName', aliasName);\n      // this.$set(this, 'installAddr', installAddr);\n      // this.$set(this, 'item', item);\n      this.loading = true;\n      this.loadingType = '0';\n      this.cameraCode = cameraCode;\n      this.cameraName = cameraName;\n      this.aliasName = aliasName;\n      this.installAddr = installAddr;\n      this.item = item;\n      if (cameraCode) {\n        getVideoUrlByCode(cameraCode).then(res => {\n          if (res && !this.isClose) {\n            // let { http_uri, http_port } = res || {};\n            // let sessionId=1938\n            // let httpUri='https://172.16.0.64:28182/flvgd?app=vcloud&stream=112\"'\n            // httpUri = `/flvUrl?url=${httpUri}`\n            // let arr = http_port.split(\"&\");\n            // // console.log(arr)\n            // const httpUri = http_uri\n            // const sessionId = http_port\n            const {\n              httpUri,\n              sessionId\n            } = res.data.data || {};\n            let item = httpUri.split('&')[1].split('=')[1];\n            // console.log(item);\n            this.stream = item;\n            this.$set(this, 'httpUri', httpUri);\n            this.$set(this, 'sessionId', sessionId);\n            this.sessionId = sessionId;\n            if (this.httpUri && this.sessionId) {\n              _that.player.setUrl(this.httpUri);\n              // 绘制水印的方法;\n              // try {\n              //   if (_that.renderTimer) {\n              //     clearInterval(_that.renderTimer);\n              //     _that.renderTimer = null;\n              //   }\n              //   _that.renderTimer = setInterval(() => {\n              //     _that.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n              //   }, 1000);\n              // } catch (e) {\n              //   console.log(e)\n              // }\n              _that.$nextTick(() => {\n                _that.player.play();\n\n                // 打开开始播放的标志位;\n                // _that.$set(_that, 'isPlaying', true);\n                _that.isPlaying = true;\n                if (_that.videoStatusTimer) {\n                  clearTimeout(_that.videoStatusTimer);\n                  _that.videoStatusTimer = null;\n                }\n                this.videoStatusTimer = setTimeout(() => {\n                  if (_that.loading) {\n                    // _that.$set(_that, 'loadingType', '2')\n                    _that.loadingType = '2';\n                  }\n                }, 20000);\n              });\n              _that.$emit('playType', _that.cameraCode, true);\n              _that.keepLiveHeart();\n            } else {\n              // 展示暂未获取到视频链接;\n              // _that.$set(_that, 'loading', true);\n              // _that.$set(_that, 'enterCanvas', false);\n              _that.loading = true;\n              _that.enterCanvas = false;\n              if (msg) {\n                // _that.$set(_that, 'loadingType', '3')\n                _that.loadingType = '3';\n                _that.loadingTypeMap['3'].text = msg;\n              } else {\n                // _that.$set(_that, 'loadingType', '2');\n                _that.loadingType = '2';\n              }\n              _that.$emit('playType', _that.cameraCode, false);\n            }\n          }\n        }).catch(err => {\n          // _that.$set(_that, 'loadingType', '2');\n          _that.loadingType = '2';\n        });\n\n        // 根据国标编码获取视频流地址;\n        // getVideoUrlByCode(cameraCode).then(res => {\n        //   if (res && res.code == 0 && !this.isClose) {\n        //     // let { httpUri, sessionId, msg } = res.data || {};\n        //     let sessionId=1938\n        //     let httpUri='https://172.16.0.64:28182/flvgd?app=vcloud&stream=112\"'\n        //     this.$set(this, 'httpUri', httpUri);\n        //     this.$set(this, 'sessionId', sessionId);\n        //     if (this.httpUri && this.sessionId) {\n        //       _that.player.setUrl(httpUri);\n        //       // 绘制水印的方法;\n        //       try {\n        //         if (_that.renderTimer) {\n        //           clearInterval(_that.renderTimer);\n        //           _that.renderTimer = null;\n        //         }\n        //         _that.renderTimer = setInterval(() => {\n        //           _that.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n        //         }, 1000);\n        //       } catch (e) {\n        //         console.log(e)\n        //       }\n        //       _that.$nextTick(() => {\n        //         _that.player.play();\n        //         // 打开开始播放的标志位;\n        //         _that.$set(_that, 'isPlaying', true);\n        //         if (_that.videoStatusTimer) {\n        //           console.log(this.videoStatusTimer,'videoStatusTimer');\n        //           clearTimeout(_that.videoStatusTimer)\n        //           _that.videoStatusTimer = null\n        //         }\n        //         this.videoStatusTimer = setTimeout(() => {\n        //           if (_that.loading) {\n        //             _that.$set(_that, 'loadingType', '2')\n        //           }\n        //         }, 20000);\n        //       });\n        //       _that.$emit('playType', _that.cameraCode, true);\n        //       _that.keepLiveHeart();\n        //     } else {\n        //       // 展示暂未获取到视频链接;\n        //       _that.$set(_that, 'loading', true);\n        //       _that.$set(_that, 'enterCanvas', false);\n        //       if (msg) {\n        //         _that.$set(_that, 'loadingType', '3')\n        //         _that.loadingTypeMap['3'].text = msg\n        //       } else {\n        //         _that.$set(_that, 'loadingType', '2');\n        //       }\n        //       _that.$emit('playType', _that.cameraCode, false);\n        //     }\n        //   }\n        // }).catch(err => {\n        //   _that.$set(_that, 'loadingType', '2');\n        // })\n      } else {\n        // this.$set(this, 'loading', false);\n        this.loading = false;\n      }\n      console.timeEnd(\"时间\");\n    },\n    handleChangePlayType() {\n      if (this.playerType) {\n        this.player.play();\n      } else {\n        this.player.stop();\n      }\n      this.playerType = !this.playerType;\n    },\n    // 插件的响应消息\n    playerCallback(code, message) {\n      if (code == 1) {\n        // this.$set(this, 'loading', false);\n        this.loading = false;\n      } else {\n        // this.$set(_that, 'loadingType', '0');\n        // this.$set(this, 'loading', true);\n        // this.$set(this, 'enterCanvas', false)\n        _that.loadingType = '0';\n        this.enterCanvas = false;\n        this.enterCanvas = false;\n      }\n    },\n    // 重连\n    reconnection() {\n      if (this.player && this.player.cancel) {\n        this.player.cancel();\n        this.player = new RslPlayer({\n          canvasId: this.id,\n          callback: this.playerCallback\n        });\n      }\n      if (this.keepLiveTimer) {\n        clearTimeout(this.keepLiveTimer);\n        this.keepLiveTimer = null;\n      }\n      clearTimeout(this.videoStatusTimer);\n      this.videoStatusTimer = null;\n      this.play({\n        cameraCode: this.cameraCode\n      });\n    },\n    // 心跳保活\n    keepLiveHeart() {\n      let _that = this;\n      if (this.httpUri && this.cameraCode && this.sessionId) {\n        keepLive(this.cameraCode, this.stream).then(res => {\n          // _that.getCameraCutoff(this.cameraCode)\n          if (res && res.status == 200) {\n            if (this.keepLiveTimer) {\n              clearTimeout(_that.keepLiveTimer);\n              this.keepLiveTimer = null;\n            }\n            _that.keepLiveTimer = setTimeout(() => {\n              _that.keepLiveHeart();\n            }, 60000);\n          }\n        }).catch(err => {\n          // this.reconnection();\n        });\n      }\n    },\n    // 轮询查询点位是否已断流, 如果已断流，则销毁播放器\n    getCameraCutoff(cameraCode) {\n      console.log('测试');\n      // batchPollingQueryCutoffList([cameraCode]).then(res => {\n      //   console.log('1111', res)\n      //   if (res && res.code == 0 && res.data.length > 0) {\n      //     const obj = res.data[0]\n      //     this.destory()\n      //     // this.$set(this, 'loadingType', '3')\n      //     this.loadingType = '3'\n      //     this.loadingTypeMap['3'].text = obj.answerRemake\n      //     // this.$set(this, 'loading', true)\n      //     // this.$set(this, 'enterCanvas', false)\n      //     this.loading = true\n      //     this.enterCanvas = false\n      //     this.cameraCode = cameraCode\n      //   }\n      // })\n    },\n    //销毁视频播放插件\n    destory() {\n      let _that = this;\n      if (this.player && this.player.cancel) {\n        this.player.cancel();\n        this.player = new RslPlayer({\n          canvasId: _that.id,\n          callback: _that.playerCallback\n        });\n      }\n      if (this.keepLiveTimer) {\n        clearTimeout(_that.keepLiveTimer);\n        this.keepLiveTimer = null;\n      }\n      if (this.renderTimer) {\n        clearInterval(this.renderTimer);\n        this.renderTimer = null;\n      }\n      clearTimeout(this.videoStatusTimer);\n      this.videoStatusTimer = null;\n      // this.$set(this, 'show', false);\n      this.show = false;\n      this.isClose = true;\n      this.httpUri = \"\";\n      this.cameraCode = \"\";\n      this.sessionId = \"\";\n      this.message = \"\";\n      this.cameraName = \"\";\n      this.aliasName = \"\";\n      this.installAddr = \"\";\n      this.loading = false;\n      // this.$set(this, 'isPlaying', false);\n      this.isPlaying = false;\n      this.$emit('close', this.item);\n\n      // this.$set(this, 'item', null);\n      this.item = null;\n    },\n    // 关闭播放器\n    close() {\n      // this.$set(this, 'isPlaying', false);\n      // this.$set(this, 'isClose', true)\n      this.isPlaying = false;\n      this.isClose = true;\n      return new Promise((resolve, reject) => {\n        this.destory();\n        resolve();\n        // if (this.httpUri && this.cameraCode && this.sessionId) {\n        //   // closeVideo(this.cameraCode, this.stream).then(res => {\n        //   //   this.destory();\n        //   //   resolve()\n        //   // }).catch((error) => {\n        //   //   this.destory();\n        //   //   resolve();\n        //   // })\n        // } else {\n\n        //   this.destory();\n        //   resolve();\n        // }\n      });\n    },\n    stop() {\n      return new Promise((resolve, reject) => {\n        this.player.stop();\n      });\n    },\n    /*\n      播放器全屏;\n      (注意区分播放器全屏和UI设计稿全屏的区别)\n      播放器全屏可以调用此方法;\n      UI设计稿全屏不能调用此方法;\n    */\n    fullScreen() {\n      if (this.player) {\n        openFullscreen(`${this.id}`);\n      }\n    },\n    /**\n     * 截取当前播放信息画面\n     * @return  {[type]}  [return description]\n     */\n    screenshot() {\n      const url = this.player._canvas.toDataURL(\"image/png\");\n      this.$refs.annotation.open({\n        url,\n        cameraCode: this.cameraCode,\n        cameraName: this.cameraName,\n        aliasName: this.aliasName,\n        installAddr: this.installAddr\n      });\n    },\n    /*\n      当前的播放器是否在播放;\n    */\n    isPlay() {\n      return this.isPlaying;\n    },\n    // 动态修改单个播放器的大小;number\n    resize() {\n      this.$nextTick(() => {\n        if (this.isPlay()) {\n          this.player.resize();\n        } else {}\n      });\n    },\n    // 嵌套组件的点击事件\n    handleComponentClick(type, index) {\n      if (type === \"hideSelf\") {\n        // this.$set(this, 'show', false);\n        this.show = false;\n      }\n    },\n    // 绘制水印\n    renderWaterMarkText(text) {\n      let waterCanvas = document.getElementById(`water${this.id}`);\n      if (!waterCanvas) {\n        return false;\n      }\n      let ctx = waterCanvas.getContext('2d');\n      // 清除画布\n      let width = waterCanvas.width;\n      let height = waterCanvas.height;\n      if (ctx) {\n        ctx.clearRect(0, 0, width, height);\n      }\n      let time = this.clockTime || moment().format(\"YYYY-MM-DD HH:mm:ss\");\n      let textArray = text.split('###');\n      let name = textArray[0] ? textArray[0] : '';\n      let ip = textArray[1] ? textArray[1] : '';\n      let fontSize = 14;\n      let points = [{\n        x: width / 4,\n        y: height / 4\n      }, {\n        x: 3 * width / 4,\n        y: height / 4\n      }, {\n        x: width / 4,\n        y: 3 * height / 4\n      }, {\n        x: 3 * width / 4,\n        y: 3 * height / 4\n      }, {\n        // 中心点\n        x: width / 2,\n        y: height / 2\n      }];\n      for (let i = 0; i < points.length; i++) {\n        let {\n          x,\n          y\n        } = points[i];\n        this._renderCanvasText(waterCanvas, name, x, y, 'fix');\n        this._renderCanvasText(waterCanvas, time, x, y);\n        this._renderCanvasText(waterCanvas, ip, x, y);\n      }\n    },\n    // 绘制水印\n    _renderCanvasText: function (waterCanvas, text, x, y, fix) {\n      let _ctx = waterCanvas.getContext('2d');\n      if (_ctx) {\n        let metrics = _ctx.measureText(text);\n        let strWidth = metrics.width;\n        let actualHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;\n        _ctx.save();\n        _ctx.font = `${this.markValue2}px 微软雅黑`;\n        _ctx.fillStyle = `rgba(255,255,255,${(this.markValue1 / 100).toFixed(2)})`;\n        let yHeight = fix ? y + strWidth / 2 - actualHeight : y + strWidth / 2;\n        _ctx.rotate(-15 * Math.PI / 180);\n        _ctx.fillText(text, x - strWidth / 2 - strWidth / 2, yHeight);\n        _ctx.restore();\n      }\n    }\n  },\n  watch: {\n    // 监听宽高变化\n    widthOrHeight(oldval, newval) {\n      this.resize();\n    }\n    // width(oldVal,newVal){\n    //   this.resize();\n    // },\n    // height(oldVal,newVal){\n    //   this.resize();\n    // }\n  },\n  // 组件销毁前主动关闭连接\n  beforeDestroy() {\n    if (this.isPlay()) {\n      this.close();\n      this.player = null;\n    }\n  }\n};", "map": {"version": 3, "names": ["RslPlayer", "getVideoUrlByCode", "keepLive", "openFullscreen", "moment", "loading", "load_fail", "name", "data", "stream", "videoStatusTimer", "markValue1", "markValue2", "markOptions", "player", "sessionId", "cameraCode", "keepLiveTimer", "cameraName", "<PERSON><PERSON><PERSON>", "installAddr", "httpUri", "message", "enterCanvas", "loadingTypeMap", "img", "text", "loadingType", "playerType", "item", "show", "loadingHoverBottom", "isPlaying", "isClose", "renderTimer", "clockTime", "isFullScreen", "computed", "components", "bottomList", "length", "map", "bottom", "widthOrHeight", "width", "height", "getNewHeight", "props", "nameFieldDisplay", "type", "String", "default", "hideAllFunc", "Boolean", "Number", "id", "showFull", "showClose", "showStopAndPlay", "bottomHoverColor", "topHoverColor", "Array", "prefix", "isFuse", "current", "isFull", "mounted", "initPlayer", "methods", "resetWatermark", "_that", "canvasId", "callback", "<PERSON><PERSON><PERSON><PERSON>", "hoverHandler", "handleBtnClick", "btn", "index", "$nextTick", "componentRef", "$refs", "init", "$emit", "play", "cameraIp", "nick<PERSON><PERSON>", "then", "res", "split", "$set", "setUrl", "clearTimeout", "setTimeout", "keepLiveHeart", "msg", "catch", "err", "console", "timeEnd", "handleChangePlayType", "stop", "code", "reconnection", "cancel", "status", "getC<PERSON>ra<PERSON><PERSON><PERSON>", "log", "destory", "clearInterval", "close", "Promise", "resolve", "reject", "fullScreen", "screenshot", "url", "_canvas", "toDataURL", "annotation", "open", "isPlay", "resize", "handleComponentClick", "renderWaterMarkText", "waterCanvas", "document", "getElementById", "ctx", "getContext", "clearRect", "time", "format", "textArray", "ip", "fontSize", "points", "x", "y", "i", "_renderCanvasText", "fix", "_ctx", "metrics", "measureText", "str<PERSON>idth", "actualHeight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "save", "font", "fillStyle", "toFixed", "yHeight", "rotate", "Math", "PI", "fillText", "restore", "watch", "oldval", "newval", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/rslPlayer/components/canvasPlayerItem.vue"], "sourcesContent": ["<template>\n  <div class=\"player-container\" :id=\"id + '-container'\">\n    <!-- 当存在cameraCode(国标编码) 和 无错误消息(message)时才展示播放器,错误消息来源于解码器  v-show=\"cameraCode!='' && !message\"-->\n    <!--加载遮罩层(只有加载中,播放过程中的一些异常,或者加载失败才显示这个遮罩)-->\n    <div class=\"player-loading mask\" v-if=\"loading\" @mouseover=\"loadingHoverBottom = true\"\n      @mouseout=\"loadingHoverBottom = false\">\n      <div class=\"hover-top no-play-top\" :style=\"{ 'backgroundColor': topHoverColor }\"\n        v-show=\"loadingHoverBottom && isFuse\">\n        <div class=\"hover-top-title\">{{ nameFieldDisplay === '0' ? cameraName || aliasName : aliasName || cameraName }}\n        </div>\n      </div>\n      <div class=\"player-loading-icon\">\n        <div class=\"loading-icon\">\n          <img :src=\"loadingTypeMap[loadingType]['img']\" />\n        </div>\n        <div class=\"loading-text\" v-html=\"loadingTypeMap[loadingType]['text']\"></div>\n      </div>\n      <!--加载失败也要显示设备详情  -->\n      <!-- <div class=\"loading-hover-bottom\" v-show=\"loadingHoverBottom\">\n        <div class=\"hover-bottom-inner\" v-if=\"bottomList && bottomList.length > 0 && !hideAllFunc\">\n          <el-button :type=\"btn.type ? btn.type : 'primary'\" :size=\"btn.size ? btn.size : 'mini'\"\n            v-for=\"(btn, index) in bottomList\" :key=\"index\" @click=\"handleBtnClick(btn, index)\">{{ btn.name }}</el-button>\n        </div>\n      </div> -->\n    </div>\n    <!--显示暂停,播放及一些其他的自定义功能按钮-->\n    <div class=\"player-mask mask\" v-show=\"enterCanvas\" @mouseover=\"hoverHandler(true)\" @mouseout=\"hoverHandler(false)\">\n      <div class=\"hover-top\" :style=\"{ 'backgroundColor': topHoverColor }\">\n        <div class=\"hover-top-title\">{{ nameFieldDisplay === '0' ? cameraName || aliasName : aliasName || cameraName }}\n        </div>\n        <!--播放器全屏-->\n        <div class=\"fullscreen btn\" @click=\"fullScreen\" v-if=\"showFull\"></div>\n        <!--关闭当前的播放器-->\n        <div class=\"close btn\" @click=\"close\" v-if=\"showClose\"></div>\n      </div>\n      <div class=\"hover-center\" v-show=\"showStopAndPlay\">\n        <div :class=\"['player-btn', playerType ? 'play' : 'stop']\" @click=\"handleChangePlayType\"></div>\n      </div>\n      <div class=\"hover-bottom\" :style=\"{ 'backgroundColor': bottomHoverColor }\">\n        <div class=\"hover-bottom-inner\" v-if=\"cameraCode && bottomList && bottomList.length > 0 && !hideAllFunc\">\n          <!-- <el-button :type=\"btn.type ? btn.type : 'primary'\" :size=\"btn.size ? btn.size : 'mini'\"\n            v-for=\"(btn, index) in bottomList\" :key=\"index\" @click=\"handleBtnClick(btn, index)\">{{ btn.name }}</el-button> -->\n        </div>\n      </div>\n    </div>\n    <!--水印遮罩-->\n    <canvas :id=\"'water' + id\" class=\"waterText\" :width=width :height=getNewHeight @mouseover=\"hoverHandler(true)\"\n      @mouseout=\"hoverHandler(false)\"></canvas>\n    <!--播放器-->\n    <canvas :id=\"id\" :width=width :height=getNewHeight></canvas>\n    <!--未增加视频时的占位图片-->\n    <!-- <div v-show=\"cameraCode === ''\" class=\"placeholder_image\"></div> -->\n    <!--叠加到播放器上的一层,适合各个业务组件添加自己的基于播放器的功能-->\n    <div class=\"canvasPlayer-slot\" v-show=\"components && components.length > 0 && show && !hideAllFunc\">\n      <component @handleClick=\"handleComponentClick\" :isFull=\"isFull\"\n        :is=\"component.components ? component.components : null\" :ref=\"'bottomBtn' + index\"\n        v-for=\"(component, index) in components\" :key=\"index\" />\n    </div>\n  </div>\n</template>\n<script>\nimport RslPlayer from \"@/components/rslPlayer/rslPlayer_v1.0.all.js\";\nimport {\n  getVideoUrlByCode,//查询视频流\n  keepLive,//保活\n  // closeVideo,\n  // batchPollingQueryCutoffList,\n  // getWaterMarkSet\n} from '@/api/video';\n// import { mapGetters } from \"vuex\";\nimport { openFullscreen } from '@/utils/fullScreen';\nimport moment from 'moment';\nimport loading from \"@/assets/images/video/player/player-load-ing.png\";\nimport load_fail from '@/assets/images/video/player/player-load-fail.png';\nexport default {\n  name: \"canvas-player-item\",\n  data() {\n    return {\n      stream: '',\n      videoStatusTimer: null,\n      markValue1: 100,\n      markValue2: 14,\n      markOptions: [],\n      player: null,\n      sessionId: '',// 会话id\n      cameraCode: '',// 国标编码\n      keepLiveTimer: null, // 心跳保活定时器;\n      cameraName: '',\n      aliasName: '',\n      installAddr: '',\n      httpUri: '',\n      loading: false,\n      message: '',\n      // 鼠标是否已经移入到播放器\n      enterCanvas: false,\n      // 播放器状态组(无成功状态,加载成功直接展示播放器开始播放)\n      loadingTypeMap: {\n        // 视频加载中\n        \"0\": {\n          img: loading,\n          text: '正在加载中...'\n        },\n        // 视频加载失败\n        \"1\": {\n          img: load_fail,\n          text: '视频加载失败'\n        },\n        \"2\": {\n          img: load_fail,\n          text: '视频资源暂时无法加载，请稍后再试'\n        },\n        \"3\": {\n          img: load_fail,\n          text: ''\n        }\n      },\n      loadingType: \"0\",\n      playerType: false,\n      item: null,\n      show: false,\n      loadingHoverBottom: false,\n      isPlaying: true,\n      isClose: true, // 是否关闭流\n      renderTimer: null,\n      clockTime: '', // ntp服务时间更新\n      isFullScreen: true // 当前的播放器包裹组件v-dialog 是否是全屏状态;\n    };\n  },\n  computed: {\n    // ...mapGetters(['userinfo']),\n    components() {\n      if (this.bottomList && this.bottomList.length > 0) {\n        return this.bottomList.map(bottom => {\n          return {\n            show: false,\n            ...bottom\n          }\n        })\n      }\n      return [];\n    },\n    // 处理宽高变化\n    widthOrHeight() {\n      return { width: this.width, height: this.height }\n    },\n    //获取新高\n    getNewHeight() {\n      return this.height\n    },\n  },\n  props: {\n    nameFieldDisplay: {\n      type: String,\n      default: '0'\n    },\n    // 是否隐藏底部功能按钮 \n    hideAllFunc: {\n      type: Boolean,\n      default() {\n        return false\n      }\n    },\n    // 播放器宽度\n    width: {\n      type: Number,\n      default() {\n        return 300\n      }\n    },\n    // 播放器高度\n    height: {\n      type: Number,\n      default() {\n        return 150\n      }\n    },\n    // 播放器id 不重复\n    id: {\n      type: String,\n      default() {\n        return 'canvas-player'\n      }\n    },\n    // 是否显示全屏图标\n    showFull: {\n      type: Boolean,\n      default() {\n        return true\n      }\n    },\n    // 是否显示关闭图标\n    showClose: {\n      type: Boolean,\n      default() {\n        return true;\n      }\n    },\n    // 是否显示播放暂停的按钮;\n    showStopAndPlay: {\n      type: Boolean,\n      default() {\n        return true\n      }\n    },\n    // hover时头部的颜色\n    bottomHoverColor: {\n      type: String,\n      default() {\n        return 'transparent'\n      }\n    },\n    topHoverColor: {\n      type: String,\n      default() {\n        return 'transparent'\n      }\n    },\n    // 底部的按钮组\n    bottomList: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    prefix: {\n      type: String,\n      default() {\n        return 'canvasPlayer-slot'\n      }\n    },\n    // 是否是视频融合在调用播放器\n    isFuse: {\n      type: Boolean,\n      default() {\n        return false\n      }\n    },\n    // 当前调用组件的自定义标志符\n    current: {\n      type: String,\n      default() {\n        return ''\n      }\n    },\n    // 当前的包裹组件是否在全屏状态\n    isFull: {\n      type: Boolean,\n      default() {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.initPlayer();\n    // this.getWatermarkOptions()\n    // try {\n    //   this.clockTime = this.$moment(Date.now() + this.$store.state.clock.offset).format('MM月DD日 HH:mm:ss') // 初始化默认值\n    //   // 监听ntf服务器时间变化并处理\n    //   eventBus.$on(EVENT_NAMES.SECOND, (now) => {\n    //     // 无需根据差值计算，直接使用（`now`是计算后的当前时间时间戳）\n    //     // 该Date对象的时间纠差后的标准时间\n    //     // const date = new Date(now);\n    //     // 亦可直接用于moment中进行格式化\n    //     this.clockTime = this.$moment(now).format('MM月DD日 HH:mm:ss');\n    //   });\n    // } catch(e) {\n    //   console.log(e)\n    //   this.clockTime = '' // 遇到错误重置ntp服务时间为空字符\n    // }\n  },\n  methods: {\n    // async getWatermarkOptions() {\n    //   await getWaterMarkSet().then((res) => {\n    //     this.markOptions = res.data\n    //     this.markOptions.forEach(item => {\n    //       if (item.dictLabel == 'font_transparence') {\n    //         this.markValue1 = Number(item.dictValue)\n    //       } else if (item.dictLabel == 'font_size') {\n    //         this.markValue2 = Number(item.dictValue)\n    //       }\n    //     })\n    //   });\n    // },\n    resetWatermark(item) {\n      // this.getWatermarkOptions()\n      // let { cameraIp  } = item;\n      // let { nickName } = this.userinfo;\n      // this.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n    },\n    // 初始化播放器;\n    initPlayer() {\n      let _that = this;\n      this.player = new RslPlayer({\n        canvasId: _that.id,\n        callback: _that.playerCallback\n      });\n    },\n    // 画布响应鼠标移入移出事件\n    hoverHandler(enterCanvas) {\n      if (!this.loading) {\n        // this.$set(this, 'enterCanvas', enterCanvas)\n        this.enterCanvas = enterCanvas\n      }\n    },\n    handleBtnClick(btn, index) {\n      // this.$set(this, 'enterCanvas', false);\n      this.enterCanvas = false\n      this.$nextTick(() => {\n        // this.$set(this, 'show', true);\n        this.show = true\n        const componentRef = `bottomBtn${index}`;\n        if (this.$refs[componentRef] && this.$refs[componentRef][0] && this.$refs[componentRef][0].init) {\n          this.$refs[componentRef][0].init(this.item, index, this.current);\n        }\n        this.$emit('handleBtnClick', btn, index);\n      })\n    },\n    // 开始播放;\n    play(item) {\n      let _that = this;\n      let { cameraCode, cameraName, aliasName, cameraIp, installAddr } = item;\n\n      // let { nickName } = this.userinfo.sysUser;\n      const nickName = \"南星渎商业三期\"\n      // console.log(this.userinfo, '---', nickName)\n      this.isClose = false\n      // this.$set(this, 'loading', true);\n      // this.$set(this, 'loadingType', '0');\n      // this.$set(this, 'cameraCode', cameraCode);\n      // this.$set(this, 'cameraName', cameraName);\n      // this.$set(this, 'aliasName', aliasName);\n      // this.$set(this, 'installAddr', installAddr);\n      // this.$set(this, 'item', item);\n      this.loading = true\n      this.loadingType = '0'\n      this.cameraCode = cameraCode\n      this.cameraName = cameraName\n      this.aliasName = aliasName\n      this.installAddr = installAddr\n      this.item = item\n      if (cameraCode) {\n        getVideoUrlByCode(cameraCode).then(res => {\n          if (res && !this.isClose) {\n            // let { http_uri, http_port } = res || {};\n            // let sessionId=1938\n            // let httpUri='https://172.16.0.64:28182/flvgd?app=vcloud&stream=112\"'\n            // httpUri = `/flvUrl?url=${httpUri}`\n            // let arr = http_port.split(\"&\");\n            // // console.log(arr)\n            // const httpUri = http_uri\n            // const sessionId = http_port\n            const { httpUri, sessionId } = res.data.data || {}\n            let item = httpUri.split('&')[1].split('=')[1]\n            // console.log(item);\n            this.stream = item\n            this.$set(this, 'httpUri', httpUri);\n            this.$set(this, 'sessionId', sessionId);\n            this.sessionId = sessionId\n            if (this.httpUri && this.sessionId) {\n              _that.player.setUrl(this.httpUri);\n              // 绘制水印的方法;\n              // try {\n              //   if (_that.renderTimer) {\n              //     clearInterval(_that.renderTimer);\n              //     _that.renderTimer = null;\n              //   }\n              //   _that.renderTimer = setInterval(() => {\n              //     _that.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n              //   }, 1000);\n              // } catch (e) {\n              //   console.log(e)\n              // }\n              _that.$nextTick(() => {\n                _that.player.play();\n\n                // 打开开始播放的标志位;\n                // _that.$set(_that, 'isPlaying', true);\n                _that.isPlaying = true\n                if (_that.videoStatusTimer) {\n                  clearTimeout(_that.videoStatusTimer)\n                  _that.videoStatusTimer = null\n                }\n                this.videoStatusTimer = setTimeout(() => {\n                  if (_that.loading) {\n                    // _that.$set(_that, 'loadingType', '2')\n                    _that.loadingType = '2'\n                  }\n                }, 20000);\n              });\n              _that.$emit('playType', _that.cameraCode, true);\n              _that.keepLiveHeart();\n            } else {\n              // 展示暂未获取到视频链接;\n              // _that.$set(_that, 'loading', true);\n              // _that.$set(_that, 'enterCanvas', false);\n              _that.loading = true\n              _that.enterCanvas = false\n              if (msg) {\n                // _that.$set(_that, 'loadingType', '3')\n                _that.loadingType = '3'\n                _that.loadingTypeMap['3'].text = msg\n              } else {\n                // _that.$set(_that, 'loadingType', '2');\n                _that.loadingType = '2'\n              }\n              _that.$emit('playType', _that.cameraCode, false);\n            }\n          }\n        }).catch(err => {\n          // _that.$set(_that, 'loadingType', '2');\n          _that.loadingType = '2'\n        })\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n        // 根据国标编码获取视频流地址;\n        // getVideoUrlByCode(cameraCode).then(res => {\n        //   if (res && res.code == 0 && !this.isClose) {\n        //     // let { httpUri, sessionId, msg } = res.data || {};\n        //     let sessionId=1938\n        //     let httpUri='https://172.16.0.64:28182/flvgd?app=vcloud&stream=112\"'\n        //     this.$set(this, 'httpUri', httpUri);\n        //     this.$set(this, 'sessionId', sessionId);\n        //     if (this.httpUri && this.sessionId) {\n        //       _that.player.setUrl(httpUri);\n        //       // 绘制水印的方法;\n        //       try {\n        //         if (_that.renderTimer) {\n        //           clearInterval(_that.renderTimer);\n        //           _that.renderTimer = null;\n        //         }\n        //         _that.renderTimer = setInterval(() => {\n        //           _that.renderWaterMarkText(`${nickName ? nickName : ''}###${cameraIp ? cameraIp : ''}`);\n        //         }, 1000);\n        //       } catch (e) {\n        //         console.log(e)\n        //       }\n        //       _that.$nextTick(() => {\n        //         _that.player.play();\n        //         // 打开开始播放的标志位;\n        //         _that.$set(_that, 'isPlaying', true);\n        //         if (_that.videoStatusTimer) {\n        //           console.log(this.videoStatusTimer,'videoStatusTimer');\n        //           clearTimeout(_that.videoStatusTimer)\n        //           _that.videoStatusTimer = null\n        //         }\n        //         this.videoStatusTimer = setTimeout(() => {\n        //           if (_that.loading) {\n        //             _that.$set(_that, 'loadingType', '2')\n        //           }\n        //         }, 20000);\n        //       });\n        //       _that.$emit('playType', _that.cameraCode, true);\n        //       _that.keepLiveHeart();\n        //     } else {\n        //       // 展示暂未获取到视频链接;\n        //       _that.$set(_that, 'loading', true);\n        //       _that.$set(_that, 'enterCanvas', false);\n        //       if (msg) {\n        //         _that.$set(_that, 'loadingType', '3')\n        //         _that.loadingTypeMap['3'].text = msg\n        //       } else {\n        //         _that.$set(_that, 'loadingType', '2');\n        //       }\n        //       _that.$emit('playType', _that.cameraCode, false);\n        //     }\n        //   }\n        // }).catch(err => {\n        //   _that.$set(_that, 'loadingType', '2');\n        // })\n      } else {\n        // this.$set(this, 'loading', false);\n        this.loading = false\n      }\n      console.timeEnd(\"时间\");\n    },\n    handleChangePlayType() {\n      if (this.playerType) {\n        this.player.play();\n      } else {\n        this.player.stop();\n      }\n      this.playerType = !this.playerType;\n    },\n    // 插件的响应消息\n    playerCallback(code, message) {\n      if (code == 1) {\n        // this.$set(this, 'loading', false);\n        this.loading = false\n      } else {\n        // this.$set(_that, 'loadingType', '0');\n        // this.$set(this, 'loading', true);\n        // this.$set(this, 'enterCanvas', false)\n        _that.loadingType = '0'\n        this.enterCanvas = false\n        this.enterCanvas = false\n      }\n    },\n    // 重连\n    reconnection() {\n      if (this.player && this.player.cancel) {\n        this.player.cancel();\n        this.player = new RslPlayer({\n          canvasId: this.id,\n          callback: this.playerCallback\n        });\n      }\n      if (this.keepLiveTimer) {\n        clearTimeout(this.keepLiveTimer);\n        this.keepLiveTimer = null;\n      }\n      clearTimeout(this.videoStatusTimer)\n      this.videoStatusTimer = null\n      this.play({\n        cameraCode: this.cameraCode\n      });\n    },\n    // 心跳保活\n    keepLiveHeart() {\n      let _that = this;\n      if (this.httpUri && this.cameraCode && this.sessionId) {\n        keepLive(this.cameraCode, this.stream).then(res => {\n          // _that.getCameraCutoff(this.cameraCode)\n          if (res && res.status == 200) {\n            if (this.keepLiveTimer) {\n              clearTimeout(_that.keepLiveTimer);\n              this.keepLiveTimer = null;\n            }\n            _that.keepLiveTimer = setTimeout(() => {\n              _that.keepLiveHeart();\n\n            }, 60000);\n          }\n\n        }).catch(err => {\n          // this.reconnection();\n        })\n      }\n    },\n    // 轮询查询点位是否已断流, 如果已断流，则销毁播放器\n    getCameraCutoff(cameraCode) {\n      console.log('测试')\n      // batchPollingQueryCutoffList([cameraCode]).then(res => {\n      //   console.log('1111', res)\n      //   if (res && res.code == 0 && res.data.length > 0) {\n      //     const obj = res.data[0]\n      //     this.destory()\n      //     // this.$set(this, 'loadingType', '3')\n      //     this.loadingType = '3'\n      //     this.loadingTypeMap['3'].text = obj.answerRemake\n      //     // this.$set(this, 'loading', true)\n      //     // this.$set(this, 'enterCanvas', false)\n      //     this.loading = true\n      //     this.enterCanvas = false\n      //     this.cameraCode = cameraCode\n      //   }\n      // })\n    },\n    //销毁视频播放插件\n    destory() {\n      let _that = this;\n      if (this.player && this.player.cancel) {\n        this.player.cancel();\n        this.player = new RslPlayer({\n          canvasId: _that.id,\n          callback: _that.playerCallback\n        });\n      }\n      if (this.keepLiveTimer) {\n        clearTimeout(_that.keepLiveTimer);\n        this.keepLiveTimer = null;\n      }\n      if (this.renderTimer) {\n        clearInterval(this.renderTimer);\n        this.renderTimer = null;\n      }\n      clearTimeout(this.videoStatusTimer)\n      this.videoStatusTimer = null\n      // this.$set(this, 'show', false);\n      this.show = false\n      this.isClose = true\n      this.httpUri = \"\";\n      this.cameraCode = \"\";\n      this.sessionId = \"\";\n      this.message = \"\";\n      this.cameraName = \"\";\n      this.aliasName = \"\";\n      this.installAddr = \"\";\n      this.loading = false;\n      // this.$set(this, 'isPlaying', false);\n      this.isPlaying = false\n      this.$emit('close', this.item);\n\n      // this.$set(this, 'item', null);\n      this.item = null\n    },\n    // 关闭播放器\n    close() {\n      // this.$set(this, 'isPlaying', false);\n      // this.$set(this, 'isClose', true)\n      this.isPlaying = false\n      this.isClose = true\n\n      return new Promise((resolve, reject) => {\n        this.destory();\n        resolve();\n        // if (this.httpUri && this.cameraCode && this.sessionId) {\n        //   // closeVideo(this.cameraCode, this.stream).then(res => {\n        //   //   this.destory();\n        //   //   resolve()\n        //   // }).catch((error) => {\n        //   //   this.destory();\n        //   //   resolve();\n        //   // })\n        // } else {\n\n        //   this.destory();\n        //   resolve();\n        // }\n      })\n    },\n    stop() {\n      return new Promise((resolve, reject) => {\n        this.player.stop();\n      });\n    },\n    /*\n      播放器全屏;\n      (注意区分播放器全屏和UI设计稿全屏的区别)\n      播放器全屏可以调用此方法;\n      UI设计稿全屏不能调用此方法;\n    */\n    fullScreen() {\n      if (this.player) {\n        openFullscreen(`${this.id}`);\n      }\n    },\n    /**\n     * 截取当前播放信息画面\n     * @return  {[type]}  [return description]\n     */\n    screenshot() {\n      const url = this.player._canvas.toDataURL(\"image/png\")\n      this.$refs.annotation.open({ url, cameraCode: this.cameraCode, cameraName: this.cameraName, aliasName: this.aliasName, installAddr: this.installAddr })\n    },\n    /*\n      当前的播放器是否在播放;\n    */\n    isPlay() {\n      return this.isPlaying;\n    },\n    // 动态修改单个播放器的大小;number\n    resize() {\n      this.$nextTick(() => {\n        if (this.isPlay()) {\n          this.player.resize()\n        } else {\n        }\n      })\n    },\n    // 嵌套组件的点击事件\n    handleComponentClick(type, index) {\n      if (type === \"hideSelf\") {\n        // this.$set(this, 'show', false);\n        this.show = false\n      }\n    },\n    // 绘制水印\n    renderWaterMarkText(text) {\n      let waterCanvas = document.getElementById(`water${this.id}`);\n      if (!waterCanvas) {\n        return false\n      }\n      let ctx = waterCanvas.getContext('2d');\n      // 清除画布\n      let width = waterCanvas.width;\n      let height = waterCanvas.height;\n      if (ctx) {\n        ctx.clearRect(0, 0, width, height);\n      }\n      let time = this.clockTime || moment().format(\"YYYY-MM-DD HH:mm:ss\");\n      let textArray = text.split('###');\n      let name = textArray[0] ? textArray[0] : '';\n      let ip = textArray[1] ? textArray[1] : '';\n      let fontSize = 14;\n      let points = [\n        {\n          x: width / 4,\n          y: height / 4\n        },\n        {\n          x: 3 * width / 4,\n          y: height / 4\n        },\n        {\n          x: width / 4,\n          y: 3 * height / 4\n        },\n        {\n          x: 3 * width / 4,\n          y: 3 * height / 4\n        },\n        {// 中心点\n          x: width / 2,\n          y: height / 2\n        }\n      ];\n      for (let i = 0; i < points.length; i++) {\n        let { x, y } = points[i];\n        this._renderCanvasText(waterCanvas, name, x, y, 'fix');\n        this._renderCanvasText(waterCanvas, time, x, y);\n        this._renderCanvasText(waterCanvas, ip, x, y);\n      }\n    },\n    // 绘制水印\n    _renderCanvasText: function (waterCanvas, text, x, y, fix) {\n      let _ctx = waterCanvas.getContext('2d');\n      if (_ctx) {\n        let metrics = _ctx.measureText(text);\n        let strWidth = metrics.width;\n        let actualHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;\n        _ctx.save();\n        _ctx.font = `${this.markValue2}px 微软雅黑`;\n        _ctx.fillStyle = `rgba(255,255,255,${(this.markValue1 / 100).toFixed(2)})`;\n        let yHeight = fix ? (y + strWidth / 2 - actualHeight) : (y + strWidth / 2);\n        _ctx.rotate((-15 * Math.PI / 180));\n        _ctx.fillText(text, x - strWidth / 2 - strWidth / 2, yHeight);\n        _ctx.restore();\n      }\n    },\n  },\n  watch: {\n    // 监听宽高变化\n    widthOrHeight(oldval, newval) {\n      this.resize()\n    }\n    // width(oldVal,newVal){\n    //   this.resize();\n    // },\n    // height(oldVal,newVal){\n    //   this.resize();\n    // }\n  },\n  // 组件销毁前主动关闭连接\n  beforeDestroy() {\n    if (this.isPlay()) {\n      this.close();\n      this.player = null;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.player-container {\n  width: 100%;\n  height: 100%;\n  position: relative;\n\n  .canvasPlayer-slot {\n    width: 100%;\n    height: 100%;\n    top: 0px;\n    left: 0px;\n    position: absolute;\n    z-index: 10;\n  }\n\n  .mask {\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, .1);\n    position: absolute;\n    top: 0px;\n    left: 0px;\n    z-index: 10;\n\n    .loading-hover-bottom {\n      position: absolute;\n      bottom: 0px;\n      width: 100%;\n      height: 32px;\n      z-index: 10;\n      display: flex;\n      flex-flow: row nowrap;\n      justify-content: center;\n      align-items: center;\n\n      .hover-bottom-inner {\n        width: calc(100% - 20px);\n        padding: 0 10px;\n        display: flex;\n        flex-flow: row nowrap;\n        justify-content: flex-end;\n        align-items: center;\n      }\n    }\n\n    .no-play-top {\n      position: absolute;\n      top: 0px;\n    }\n  }\n\n  .player-loading-icon {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    flex-flow: column;\n    justify-content: center;\n    align-items: center;\n\n    .loading-text {\n      font-size: 16px;\n      font-family: PingFang SC;\n      font-weight: 400;\n      color: #FFFFFF;\n      padding-top: 5px;\n    }\n  }\n\n  .player-mask {\n    z-index: 100;\n    display: flex;\n    flex-flow: column;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  // 暂停和播放的按钮\n  .player-btn {\n    width: 48px;\n    height: 48px;\n    background-repeat: no-repeat;\n    background-size: 48px 48px;\n    cursor: pointer;\n\n    &.stop {\n      background-image: url('@/assets/images/video/player/stop.png')\n    }\n\n    &.play {\n      background-image: url('@/assets/images/video/player/play.png')\n    }\n  }\n\n  .hover-top {\n    height: 32px;\n    width: 100%;\n    display: flex;\n    flex-flow: row nowrap;\n    justify-content: flex-end;\n    align-items: center;\n    padding-right: 15px;\n\n    .btn {\n      width: 20px;\n      height: 20px;\n      background-repeat: no-repeat;\n      background-size: 20px 20px;\n      background-position: center;\n    }\n\n    .close {\n      background-image: url('@/assets/images/video/player/player-close.png');\n    }\n\n    .fullscreen {\n      background-image: url('@/assets/images/video/player/player-fullscreen.png');\n    }\n\n    .hover-top-title {\n      flex: 1;\n      font-size: 16px;\n      color: #fff;\n      font-weight: bold;\n      text-indent: 10px;\n      display: flex;\n      justify-content: space-between;\n    }\n  }\n\n  .hover-bottom {\n    width: 100%;\n    height: 32px;\n    display: flex;\n    flex-flow: row nowrap;\n    justify-content: center;\n    align-items: center;\n\n    .hover-bottom-inner {\n      width: calc(100% - 20px);\n      padding: 0 10px;\n      display: flex;\n      flex-flow: row nowrap;\n      justify-content: flex-end;\n      align-items: center;\n    }\n  }\n\n  .placeholder_image {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 70%;\n    height: 100%;\n    // background-image: url('~@/assets/images/video/equiment/video_placeholder.png');\n    background-size: 100% 100%;\n    background-position: center;\n    background-repeat: no-repeat;\n    z-index: 9;\n  }\n\n  .waterText {\n    position: absolute;\n    top: 0px;\n    left: 0px;\n  }\n}\n</style>\n"], "mappings": "AA6DA,OAAAA,SAAA;AACA,SACAC,iBAAA;AAAA;AACAC,QAAA;AACA;AACA;AACA;AAAA,OACA;AACA;AACA,SAAAC,cAAA;AACA,OAAAC,MAAA;AACA,OAAAC,OAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,WAAA;MACAC,OAAA;MACAhB,OAAA;MACAiB,OAAA;MACA;MACAC,WAAA;MACA;MACAC,cAAA;QACA;QACA;UACAC,GAAA,EAAApB,OAAA;UACAqB,IAAA;QACA;QACA;QACA;UACAD,GAAA,EAAAnB,SAAA;UACAoB,IAAA;QACA;QACA;UACAD,GAAA,EAAAnB,SAAA;UACAoB,IAAA;QACA;QACA;UACAD,GAAA,EAAAnB,SAAA;UACAoB,IAAA;QACA;MACA;MACAC,WAAA;MACAC,UAAA;MACAC,IAAA;MACAC,IAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,OAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,WAAA;MACA,SAAAC,UAAA,SAAAA,UAAA,CAAAC,MAAA;QACA,YAAAD,UAAA,CAAAE,GAAA,CAAAC,MAAA;UACA;YACAZ,IAAA;YACA,GAAAY;UACA;QACA;MACA;MACA;IACA;IACA;IACAC,cAAA;MACA;QAAAC,KAAA,OAAAA,KAAA;QAAAC,MAAA,OAAAA;MAAA;IACA;IACA;IACAC,aAAA;MACA,YAAAD,MAAA;IACA;EACA;EACAE,KAAA;IACAC,gBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAP,KAAA;MACAK,IAAA,EAAAK,MAAA;MACAH,QAAA;QACA;MACA;IACA;IACA;IACAN,MAAA;MACAI,IAAA,EAAAK,MAAA;MACAH,QAAA;QACA;MACA;IACA;IACA;IACAI,EAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAM,SAAA;MACAR,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAO,eAAA;MACAT,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAQ,gBAAA;MACAV,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACAS,aAAA;MACAX,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACAZ,UAAA;MACAU,IAAA,EAAAY,KAAA;MACAV,QAAA;QACA;MACA;IACA;IACAW,MAAA;MACAb,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACAY,MAAA;MACAd,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAa,OAAA;MACAf,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACAc,MAAA;MACAhB,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;EACA;EACAe,QAAA;IACA,KAAAC,UAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,eAAAxC,IAAA;MACA;MACA;MACA;MACA;IAAA,CACA;IACA;IACAsC,WAAA;MACA,IAAAG,KAAA;MACA,KAAAxD,MAAA,OAAAd,SAAA;QACAuE,QAAA,EAAAD,KAAA,CAAAf,EAAA;QACAiB,QAAA,EAAAF,KAAA,CAAAG;MACA;IACA;IACA;IACAC,aAAAnD,WAAA;MACA,UAAAlB,OAAA;QACA;QACA,KAAAkB,WAAA,GAAAA,WAAA;MACA;IACA;IACAoD,eAAAC,GAAA,EAAAC,KAAA;MACA;MACA,KAAAtD,WAAA;MACA,KAAAuD,SAAA;QACA;QACA,KAAAhD,IAAA;QACA,MAAAiD,YAAA,eAAAF,KAAA;QACA,SAAAG,KAAA,CAAAD,YAAA,UAAAC,KAAA,CAAAD,YAAA,aAAAC,KAAA,CAAAD,YAAA,KAAAE,IAAA;UACA,KAAAD,KAAA,CAAAD,YAAA,KAAAE,IAAA,MAAApD,IAAA,EAAAgD,KAAA,OAAAb,OAAA;QACA;QACA,KAAAkB,KAAA,mBAAAN,GAAA,EAAAC,KAAA;MACA;IACA;IACA;IACAM,KAAAtD,IAAA;MACA,IAAAyC,KAAA;MACA;QAAAtD,UAAA;QAAAE,UAAA;QAAAC,SAAA;QAAAiE,QAAA;QAAAhE;MAAA,IAAAS,IAAA;;MAEA;MACA,MAAAwD,QAAA;MACA;MACA,KAAApD,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA5B,OAAA;MACA,KAAAsB,WAAA;MACA,KAAAX,UAAA,GAAAA,UAAA;MACA,KAAAE,UAAA,GAAAA,UAAA;MACA,KAAAC,SAAA,GAAAA,SAAA;MACA,KAAAC,WAAA,GAAAA,WAAA;MACA,KAAAS,IAAA,GAAAA,IAAA;MACA,IAAAb,UAAA;QACAf,iBAAA,CAAAe,UAAA,EAAAsE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,UAAAtD,OAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;cAAAZ,OAAA;cAAAN;YAAA,IAAAwE,GAAA,CAAA/E,IAAA,CAAAA,IAAA;YACA,IAAAqB,IAAA,GAAAR,OAAA,CAAAmE,KAAA,SAAAA,KAAA;YACA;YACA,KAAA/E,MAAA,GAAAoB,IAAA;YACA,KAAA4D,IAAA,kBAAApE,OAAA;YACA,KAAAoE,IAAA,oBAAA1E,SAAA;YACA,KAAAA,SAAA,GAAAA,SAAA;YACA,SAAAM,OAAA,SAAAN,SAAA;cACAuD,KAAA,CAAAxD,MAAA,CAAA4E,MAAA,MAAArE,OAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAiD,KAAA,CAAAQ,SAAA;gBACAR,KAAA,CAAAxD,MAAA,CAAAqE,IAAA;;gBAEA;gBACA;gBACAb,KAAA,CAAAtC,SAAA;gBACA,IAAAsC,KAAA,CAAA5D,gBAAA;kBACAiF,YAAA,CAAArB,KAAA,CAAA5D,gBAAA;kBACA4D,KAAA,CAAA5D,gBAAA;gBACA;gBACA,KAAAA,gBAAA,GAAAkF,UAAA;kBACA,IAAAtB,KAAA,CAAAjE,OAAA;oBACA;oBACAiE,KAAA,CAAA3C,WAAA;kBACA;gBACA;cACA;cACA2C,KAAA,CAAAY,KAAA,aAAAZ,KAAA,CAAAtD,UAAA;cACAsD,KAAA,CAAAuB,aAAA;YACA;cACA;cACA;cACA;cACAvB,KAAA,CAAAjE,OAAA;cACAiE,KAAA,CAAA/C,WAAA;cACA,IAAAuE,GAAA;gBACA;gBACAxB,KAAA,CAAA3C,WAAA;gBACA2C,KAAA,CAAA9C,cAAA,MAAAE,IAAA,GAAAoE,GAAA;cACA;gBACA;gBACAxB,KAAA,CAAA3C,WAAA;cACA;cACA2C,KAAA,CAAAY,KAAA,aAAAZ,KAAA,CAAAtD,UAAA;YACA;UACA;QACA,GAAA+E,KAAA,CAAAC,GAAA;UACA;UACA1B,KAAA,CAAA3C,WAAA;QACA;;QA8BA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA,KAAAtB,OAAA;MACA;MACA4F,OAAA,CAAAC,OAAA;IACA;IACAC,qBAAA;MACA,SAAAvE,UAAA;QACA,KAAAd,MAAA,CAAAqE,IAAA;MACA;QACA,KAAArE,MAAA,CAAAsF,IAAA;MACA;MACA,KAAAxE,UAAA,SAAAA,UAAA;IACA;IACA;IACA6C,eAAA4B,IAAA,EAAA/E,OAAA;MACA,IAAA+E,IAAA;QACA;QACA,KAAAhG,OAAA;MACA;QACA;QACA;QACA;QACAiE,KAAA,CAAA3C,WAAA;QACA,KAAAJ,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IACA;IACA+E,aAAA;MACA,SAAAxF,MAAA,SAAAA,MAAA,CAAAyF,MAAA;QACA,KAAAzF,MAAA,CAAAyF,MAAA;QACA,KAAAzF,MAAA,OAAAd,SAAA;UACAuE,QAAA,OAAAhB,EAAA;UACAiB,QAAA,OAAAC;QACA;MACA;MACA,SAAAxD,aAAA;QACA0E,YAAA,MAAA1E,aAAA;QACA,KAAAA,aAAA;MACA;MACA0E,YAAA,MAAAjF,gBAAA;MACA,KAAAA,gBAAA;MACA,KAAAyE,IAAA;QACAnE,UAAA,OAAAA;MACA;IACA;IACA;IACA6E,cAAA;MACA,IAAAvB,KAAA;MACA,SAAAjD,OAAA,SAAAL,UAAA,SAAAD,SAAA;QACAb,QAAA,MAAAc,UAAA,OAAAP,MAAA,EAAA6E,IAAA,CAAAC,GAAA;UACA;UACA,IAAAA,GAAA,IAAAA,GAAA,CAAAiB,MAAA;YACA,SAAAvF,aAAA;cACA0E,YAAA,CAAArB,KAAA,CAAArD,aAAA;cACA,KAAAA,aAAA;YACA;YACAqD,KAAA,CAAArD,aAAA,GAAA2E,UAAA;cACAtB,KAAA,CAAAuB,aAAA;YAEA;UACA;QAEA,GAAAE,KAAA,CAAAC,GAAA;UACA;QAAA,CACA;MACA;IACA;IACA;IACAS,gBAAAzF,UAAA;MACAiF,OAAA,CAAAS,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC,QAAA;MACA,IAAArC,KAAA;MACA,SAAAxD,MAAA,SAAAA,MAAA,CAAAyF,MAAA;QACA,KAAAzF,MAAA,CAAAyF,MAAA;QACA,KAAAzF,MAAA,OAAAd,SAAA;UACAuE,QAAA,EAAAD,KAAA,CAAAf,EAAA;UACAiB,QAAA,EAAAF,KAAA,CAAAG;QACA;MACA;MACA,SAAAxD,aAAA;QACA0E,YAAA,CAAArB,KAAA,CAAArD,aAAA;QACA,KAAAA,aAAA;MACA;MACA,SAAAiB,WAAA;QACA0E,aAAA,MAAA1E,WAAA;QACA,KAAAA,WAAA;MACA;MACAyD,YAAA,MAAAjF,gBAAA;MACA,KAAAA,gBAAA;MACA;MACA,KAAAoB,IAAA;MACA,KAAAG,OAAA;MACA,KAAAZ,OAAA;MACA,KAAAL,UAAA;MACA,KAAAD,SAAA;MACA,KAAAO,OAAA;MACA,KAAAJ,UAAA;MACA,KAAAC,SAAA;MACA,KAAAC,WAAA;MACA,KAAAf,OAAA;MACA;MACA,KAAA2B,SAAA;MACA,KAAAkD,KAAA,eAAArD,IAAA;;MAEA;MACA,KAAAA,IAAA;IACA;IACA;IACAgF,MAAA;MACA;MACA;MACA,KAAA7E,SAAA;MACA,KAAAC,OAAA;MAEA,WAAA6E,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA,KAAAL,OAAA;QACAI,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;MACA;IACA;IACAX,KAAA;MACA,WAAAU,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA,KAAAlG,MAAA,CAAAsF,IAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAa,WAAA;MACA,SAAAnG,MAAA;QACAX,cAAA,SAAAoD,EAAA;MACA;IACA;IACA;AACA;AACA;AACA;IACA2D,WAAA;MACA,MAAAC,GAAA,QAAArG,MAAA,CAAAsG,OAAA,CAAAC,SAAA;MACA,KAAArC,KAAA,CAAAsC,UAAA,CAAAC,IAAA;QAAAJ,GAAA;QAAAnG,UAAA,OAAAA,UAAA;QAAAE,UAAA,OAAAA,UAAA;QAAAC,SAAA,OAAAA,SAAA;QAAAC,WAAA,OAAAA;MAAA;IACA;IACA;AACA;AACA;IACAoG,OAAA;MACA,YAAAxF,SAAA;IACA;IACA;IACAyF,OAAA;MACA,KAAA3C,SAAA;QACA,SAAA0C,MAAA;UACA,KAAA1G,MAAA,CAAA2G,MAAA;QACA,QACA;MACA;IACA;IACA;IACAC,qBAAAzE,IAAA,EAAA4B,KAAA;MACA,IAAA5B,IAAA;QACA;QACA,KAAAnB,IAAA;MACA;IACA;IACA;IACA6F,oBAAAjG,IAAA;MACA,IAAAkG,WAAA,GAAAC,QAAA,CAAAC,cAAA,cAAAvE,EAAA;MACA,KAAAqE,WAAA;QACA;MACA;MACA,IAAAG,GAAA,GAAAH,WAAA,CAAAI,UAAA;MACA;MACA,IAAApF,KAAA,GAAAgF,WAAA,CAAAhF,KAAA;MACA,IAAAC,MAAA,GAAA+E,WAAA,CAAA/E,MAAA;MACA,IAAAkF,GAAA;QACAA,GAAA,CAAAE,SAAA,OAAArF,KAAA,EAAAC,MAAA;MACA;MACA,IAAAqF,IAAA,QAAA/F,SAAA,IAAA/B,MAAA,GAAA+H,MAAA;MACA,IAAAC,SAAA,GAAA1G,IAAA,CAAA8D,KAAA;MACA,IAAAjF,IAAA,GAAA6H,SAAA,MAAAA,SAAA;MACA,IAAAC,EAAA,GAAAD,SAAA,MAAAA,SAAA;MACA,IAAAE,QAAA;MACA,IAAAC,MAAA,IACA;QACAC,CAAA,EAAA5F,KAAA;QACA6F,CAAA,EAAA5F,MAAA;MACA,GACA;QACA2F,CAAA,MAAA5F,KAAA;QACA6F,CAAA,EAAA5F,MAAA;MACA,GACA;QACA2F,CAAA,EAAA5F,KAAA;QACA6F,CAAA,MAAA5F,MAAA;MACA,GACA;QACA2F,CAAA,MAAA5F,KAAA;QACA6F,CAAA,MAAA5F,MAAA;MACA,GACA;QAAA;QACA2F,CAAA,EAAA5F,KAAA;QACA6F,CAAA,EAAA5F,MAAA;MACA,EACA;MACA,SAAA6F,CAAA,MAAAA,CAAA,GAAAH,MAAA,CAAA/F,MAAA,EAAAkG,CAAA;QACA;UAAAF,CAAA;UAAAC;QAAA,IAAAF,MAAA,CAAAG,CAAA;QACA,KAAAC,iBAAA,CAAAf,WAAA,EAAArH,IAAA,EAAAiI,CAAA,EAAAC,CAAA;QACA,KAAAE,iBAAA,CAAAf,WAAA,EAAAM,IAAA,EAAAM,CAAA,EAAAC,CAAA;QACA,KAAAE,iBAAA,CAAAf,WAAA,EAAAS,EAAA,EAAAG,CAAA,EAAAC,CAAA;MACA;IACA;IACA;IACAE,iBAAA,WAAAA,CAAAf,WAAA,EAAAlG,IAAA,EAAA8G,CAAA,EAAAC,CAAA,EAAAG,GAAA;MACA,IAAAC,IAAA,GAAAjB,WAAA,CAAAI,UAAA;MACA,IAAAa,IAAA;QACA,IAAAC,OAAA,GAAAD,IAAA,CAAAE,WAAA,CAAArH,IAAA;QACA,IAAAsH,QAAA,GAAAF,OAAA,CAAAlG,KAAA;QACA,IAAAqG,YAAA,GAAAH,OAAA,CAAAI,uBAAA,GAAAJ,OAAA,CAAAK,wBAAA;QACAN,IAAA,CAAAO,IAAA;QACAP,IAAA,CAAAQ,IAAA,WAAAzI,UAAA;QACAiI,IAAA,CAAAS,SAAA,6BAAA3I,UAAA,QAAA4I,OAAA;QACA,IAAAC,OAAA,GAAAZ,GAAA,GAAAH,CAAA,GAAAO,QAAA,OAAAC,YAAA,GAAAR,CAAA,GAAAO,QAAA;QACAH,IAAA,CAAAY,MAAA,OAAAC,IAAA,CAAAC,EAAA;QACAd,IAAA,CAAAe,QAAA,CAAAlI,IAAA,EAAA8G,CAAA,GAAAQ,QAAA,OAAAA,QAAA,MAAAQ,OAAA;QACAX,IAAA,CAAAgB,OAAA;MACA;IACA;EACA;EACAC,KAAA;IACA;IACAnH,cAAAoH,MAAA,EAAAC,MAAA;MACA,KAAAvC,MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;EACAwC,cAAA;IACA,SAAAzC,MAAA;MACA,KAAAX,KAAA;MACA,KAAA/F,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}