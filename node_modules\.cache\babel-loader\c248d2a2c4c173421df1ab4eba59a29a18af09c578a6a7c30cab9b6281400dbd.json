{"ast": null, "code": "import { getHistoryAccumulation, getHistoryRiverQuality, getHistoryTunnelLevel, getHistoryRiverLevelMonitoring } from '@/api/index.js';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'warningInfo',\n  data() {\n    return {\n      eventType: {},\n      dataText: {}\n    };\n  },\n  watch: {\n    selectEventInfoOne: {\n      handler(val) {\n        this.eventType = val;\n      },\n      immediate: true\n    },\n    dataChannelText: {\n      handler(val) {}\n    }\n  },\n  computed: {\n    ...mapState({\n      selectEventInfoOne: state => state.warningType.selectEventInfoOne,\n      historyEventList: state => state.warningType.historyEventList,\n      dataChannelText: state => state.index.dataChannelText\n    })\n  },\n  methods: {\n    WaterHistory() {\n      this.searchHistoryData(this.selectEventInfoOne);\n      this.$store.commit('warningType/getIsShowHistoryEventList', true);\n    },\n    // 对应预警类型历史数据\n    searchHistoryData(val) {\n      switch (val.typename) {\n        case '积水预警事件':\n          let params = {\n            pageSize: 10,\n            pageNum: 1,\n            response_person_name: val.responsePersonName\n          };\n          getHistoryAccumulation(params).then(res => {\n            // console.log('积水---', res)\n            this.$store.commit('warningType/getCurrentEvent', val.typename);\n            this.$store.commit('warningType/getHistoryEventList', res.data.extra.data);\n          });\n          break;\n        case '河道液位预警':\n          let params2 = {\n            pageSize: 10,\n            pageNum: 1,\n            number: val.number\n          };\n          getHistoryRiverQuality(params2).then(res => {\n            // console.log('河道---', res)\n            this.$store.commit('warningType/getCurrentEvent', val.typename);\n            this.$store.commit('warningType/getHistoryEventList', res.data.extra.data);\n          });\n          break;\n        case '剖面仪预警':\n          let params3 = {\n            pageSize: 10,\n            pageNum: 1,\n            device_code: val.deviceCode\n          };\n          getHistoryRiverLevelMonitoring(params3).then(res => {\n            // console.log('剖面仪---', res)\n            this.$store.commit('warningType/getCurrentEvent', val.typename);\n            this.$store.commit('warningType/getHistoryEventList', res.data.extra.data);\n          });\n          break;\n        case '一体化智能杆':\n          let params4 = {\n            pageSize: 10,\n            pageNum: 1,\n            uid: val.uid\n          };\n          getHistoryTunnelLevel(params4).then(res => {\n            // console.log('隧道---', res)\n          });\n          break;\n        default:\n          break;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getHistoryAccumulation", "getHistoryRiverQuality", "getHistoryTunnelLevel", "getHistoryRiverLevelMonitoring", "mapState", "name", "data", "eventType", "dataText", "watch", "selectEventInfoOne", "handler", "val", "immediate", "dataChannelText", "computed", "state", "warningType", "historyEventList", "index", "methods", "WaterHistory", "searchHistoryData", "$store", "commit", "typename", "params", "pageSize", "pageNum", "response_person_name", "responsePersonName", "then", "res", "extra", "params2", "number", "params3", "device_code", "deviceCode", "params4", "uid"], "sources": ["src/views/dataScreen/fileType/waterWarningType/warningInfo.vue"], "sourcesContent": ["<template>\r\n    <div class=\"warningInfo\">\r\n        <div class=\"event_Type_info\">\r\n            <ul>\r\n                <li>\r\n                    <span class=\"leftTit\">名称</span>\r\n                    <span class=\"rightTxt\">{{ eventType.Name }}</span>\r\n                </li>\r\n\r\n                <li>\r\n                    <span class=\"leftTit\">时间</span>\r\n                    <span class=\"rightTxt\">{{ eventType.eventTime }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.isStatus\">\r\n                    <span class=\"leftTit\">设备状态</span>\r\n                    <span class=\"rightTxt\">{{ eventType.isStatus }}</span>\r\n                </li>\r\n                <!-- 积水 -->\r\n                <li v-if=\"eventType.nameLocation\">\r\n                    <span class=\"leftTit\">地址</span>\r\n                    <span class=\"rightTxt\">{{ eventType.nameLocation }}</span>\r\n                </li>\r\n                <!-- 河道水质 -->\r\n                <li v-if=\"eventType.dissolvedOxygen\">\r\n                    <span class=\"leftTit\">溶解氧</span>\r\n                    <span class=\"rightTxt\">{{ eventType.dissolvedOxygen }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.nhn\">\r\n                    <span class=\"leftTit\">氨氢</span>\r\n                    <span class=\"rightTxt\">{{ eventType.nhn }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.permanganate\">\r\n                    <span class=\"leftTit\">总磷</span>\r\n                    <span class=\"rightTxt\">{{ eventType.permanganate }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.totalPhosphorus\">\r\n                    <span class=\"leftTit\">高锰酸盐</span>\r\n                    <span class=\"rightTxt\">{{ eventType.totalPhosphorus }}</span>\r\n                </li>\r\n\r\n                <!-- 剖面仪 -->\r\n                <li v-if=\"eventType.statusVx\">\r\n                    <span class=\"leftTit\">实时水位(m)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.statusVx }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.waterLevelNow\">\r\n                    <span class=\"leftTit\">累计流量(m3)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.flowTotal }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.waterLevelNow\">\r\n                    <span class=\"leftTit\">平均流速(m/s)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.flowVelocityAvg }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.flowVa\">\r\n                    <span class=\"leftTit\">过水面积(m2)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.flowVa }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.flowVx\">\r\n                    <span class=\"leftTit\">分层流速(m/s)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.flowVx }}</span>\r\n                </li>\r\n\r\n                <li v-if=\"eventType.statusV\">\r\n                    <span class=\"leftTit\">水位状态</span>\r\n                    <span class=\"rightTxt\">{{ eventType.statusV }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.pqa\">\r\n                    <span class=\"leftTit\">正总流量</span>\r\n                    <span class=\"rightTxt\">{{ eventType.pqa }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.nqa\">\r\n                    <span class=\"leftTit\">负总流量</span>\r\n                    <span class=\"rightTxt\">{{ eventType.nqa }}</span>\r\n                </li>\r\n                <!-- 隧道 -->\r\n                <li v-if=\"eventType.statusVn\">\r\n                    <span class=\"leftTit\">状态</span>\r\n                    <span class=\"rightTxt\">{{ eventType.statusVn }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.responsiblePersonName\">\r\n                    <span class=\"leftTit\">责任人</span>\r\n                    <span class=\"rightTxt\">{{ eventType.responsiblePersonName }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.value\">\r\n                    <span class=\"leftTit\">水值(cm)</span>\r\n                    <span class=\"rightTxt\">{{ eventType.value }}</span>\r\n                </li>\r\n                <li v-if=\"eventType.telephone\">\r\n                    <span class=\"leftTit\">电话</span>\r\n                    <span class=\"rightTxt\">{{ eventType.telephone }}</span>\r\n                </li>\r\n\r\n                <li>\r\n                    <span class=\"leftTit\" @click=\"WaterHistory\"> 历史数据</span>\r\n                </li>\r\n            </ul>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { getHistoryAccumulation, getHistoryRiverQuality, getHistoryTunnelLevel, getHistoryRiverLevelMonitoring} from '@/api/index.js'\r\n\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'warningInfo',\r\n    data() {\r\n        return {\r\n            eventType: {},\r\n            dataText:{}\r\n        }\r\n    },\r\n    watch: {\r\n        selectEventInfoOne: {\r\n            handler(val) {\r\n                this.eventType = val\r\n            },\r\n            immediate: true\r\n        },\r\n        dataChannelText:{\r\n            handler(val){\r\n\r\n            }\r\n        }\r\n        \r\n       \r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            selectEventInfoOne: state => state.warningType.selectEventInfoOne,\r\n            historyEventList:state=>state.warningType.historyEventList,\r\n            dataChannelText:state=>state.index.dataChannelText\r\n        })\r\n    },\r\n    methods: {\r\n        WaterHistory() {\r\n         this.searchHistoryData(this.selectEventInfoOne)\r\n            this.$store.commit('warningType/getIsShowHistoryEventList', true)\r\n        },\r\n        // 对应预警类型历史数据\r\n        searchHistoryData(val) {\r\n            switch (val.typename) {\r\n                case '积水预警事件':\r\n                    let params = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        response_person_name: val.responsePersonName\r\n                    }\r\n                    getHistoryAccumulation(params).then(res => {\r\n                        // console.log('积水---', res)\r\n                        this.$store.commit('warningType/getCurrentEvent', val.typename)\r\n                        this.$store.commit('warningType/getHistoryEventList', res.data.extra.data)\r\n                    })\r\n                    break;\r\n                case '河道液位预警':\r\n                    let params2 = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        number: val.number\r\n                    };\r\n                    getHistoryRiverQuality(params2).then(res => {\r\n                        // console.log('河道---', res)\r\n                        this.$store.commit('warningType/getCurrentEvent', val.typename)\r\n                        this.$store.commit('warningType/getHistoryEventList', res.data.extra.data)\r\n                    })\r\n                    break;\r\n                case '剖面仪预警':\r\n                    let params3 = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        device_code: val.deviceCode\r\n                    };\r\n                    getHistoryRiverLevelMonitoring(params3).then(res => {\r\n                        // console.log('剖面仪---', res)\r\n                        this.$store.commit('warningType/getCurrentEvent', val.typename)\r\n                        this.$store.commit('warningType/getHistoryEventList', res.data.extra.data)\r\n                    })\r\n                    break;\r\n                case '一体化智能杆':\r\n                    let params4 = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        uid: val.uid\r\n                    };\r\n                    getHistoryTunnelLevel(params4).then(res => {\r\n                        // console.log('隧道---', res)\r\n                    })\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.warningInfo {\r\n    position: absolute;\r\n    left: calc(3rem);\r\n    top:100px;\r\n    font-size: 1.3rem;\r\n    pointer-events: stroke;\r\n\r\n    .event_Type_info {\r\n        width: calc(45rem);\r\n        height: fit-content;\r\n        background: url(\"@/assets/images/event/bgImage.png\") no-repeat center 100%;\r\n        background-size: 100% 100%;\r\n        padding: 1.1rem 3rem;\r\n        font-size: 1.7rem;\r\n        box-sizing: border-box;\r\n\r\n        ul {\r\n\r\n            li {\r\n                display: flex;\r\n                height: fit-content;\r\n                margin: 1rem 0;\r\n                line-height: 2.5rem;\r\n\r\n                .leftTit {\r\n                    display: inline-block;\r\n                    width: 200px;\r\n                    display: inline-block;\r\n                    box-sizing: border-box;\r\n\r\n                }\r\n\r\n                .rightTxt {\r\n                    box-sizing: border-box;\r\n                    width: calc(23rem);\r\n                    display: inline-block;\r\n                    text-wrap: wrap;\r\n\r\n                }\r\n\r\n                &:last-child {\r\n                    text-align: right;\r\n                    color: red;\r\n                    cursor: pointer;\r\n                    font-weight: bold;\r\n                    font-size: 2rem;\r\n                    user-select: none;\r\n\r\n                    &:active {\r\n                        color: rgba(255, 0, 0, .5)\r\n                    }\r\n                }\r\n            }\r\n\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAoGA,SAAAA,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,8BAAA;AAEA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAC,kBAAA;MACAC,QAAAC,GAAA;QACA,KAAAL,SAAA,GAAAK,GAAA;MACA;MACAC,SAAA;IACA;IACAC,eAAA;MACAH,QAAAC,GAAA,GAEA;IACA;EAGA;EACAG,QAAA;IACA,GAAAX,QAAA;MACAM,kBAAA,EAAAM,KAAA,IAAAA,KAAA,CAAAC,WAAA,CAAAP,kBAAA;MACAQ,gBAAA,EAAAF,KAAA,IAAAA,KAAA,CAAAC,WAAA,CAAAC,gBAAA;MACAJ,eAAA,EAAAE,KAAA,IAAAA,KAAA,CAAAG,KAAA,CAAAL;IACA;EACA;EACAM,OAAA;IACAC,aAAA;MACA,KAAAC,iBAAA,MAAAZ,kBAAA;MACA,KAAAa,MAAA,CAAAC,MAAA;IACA;IACA;IACAF,kBAAAV,GAAA;MACA,QAAAA,GAAA,CAAAa,QAAA;QACA;UACA,IAAAC,MAAA;YACAC,QAAA;YACAC,OAAA;YACAC,oBAAA,EAAAjB,GAAA,CAAAkB;UACA;UACA9B,sBAAA,CAAA0B,MAAA,EAAAK,IAAA,CAAAC,GAAA;YACA;YACA,KAAAT,MAAA,CAAAC,MAAA,gCAAAZ,GAAA,CAAAa,QAAA;YACA,KAAAF,MAAA,CAAAC,MAAA,oCAAAQ,GAAA,CAAA1B,IAAA,CAAA2B,KAAA,CAAA3B,IAAA;UACA;UACA;QACA;UACA,IAAA4B,OAAA;YACAP,QAAA;YACAC,OAAA;YACAO,MAAA,EAAAvB,GAAA,CAAAuB;UACA;UACAlC,sBAAA,CAAAiC,OAAA,EAAAH,IAAA,CAAAC,GAAA;YACA;YACA,KAAAT,MAAA,CAAAC,MAAA,gCAAAZ,GAAA,CAAAa,QAAA;YACA,KAAAF,MAAA,CAAAC,MAAA,oCAAAQ,GAAA,CAAA1B,IAAA,CAAA2B,KAAA,CAAA3B,IAAA;UACA;UACA;QACA;UACA,IAAA8B,OAAA;YACAT,QAAA;YACAC,OAAA;YACAS,WAAA,EAAAzB,GAAA,CAAA0B;UACA;UACAnC,8BAAA,CAAAiC,OAAA,EAAAL,IAAA,CAAAC,GAAA;YACA;YACA,KAAAT,MAAA,CAAAC,MAAA,gCAAAZ,GAAA,CAAAa,QAAA;YACA,KAAAF,MAAA,CAAAC,MAAA,oCAAAQ,GAAA,CAAA1B,IAAA,CAAA2B,KAAA,CAAA3B,IAAA;UACA;UACA;QACA;UACA,IAAAiC,OAAA;YACAZ,QAAA;YACAC,OAAA;YACAY,GAAA,EAAA5B,GAAA,CAAA4B;UACA;UACAtC,qBAAA,CAAAqC,OAAA,EAAAR,IAAA,CAAAC,GAAA;YACA;UAAA,CACA;UACA;QACA;UACA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}