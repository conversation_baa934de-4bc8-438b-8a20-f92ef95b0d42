/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"./Analysis.js";import{equals as r}from"../core/arrayUtils.js";import o from"../core/Logger.js";import{isNone as s,isSome as i}from"../core/maybe.js";import{measurementAreaUnits as n}from"../core/unitUtils.js";import{property as l}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as a}from"../core/accessorSupport/decorators/subclass.js";import p from"../geometry/Polygon.js";const m=o.getLogger("esri.analysis.AreaMeasurementAnalysis");let y=class extends t{constructor(e){super(e),this.type="area-measurement",this.extent=null,this.unit=null,this.nonEditableMessage="Assign a closed polygon geometry containing more than two points to the analysis to allow editing."}set geometry(e){if(s(e))return this._set("geometry",null),void this._set("extent",null);e.rings.length>1&&m.warn("Measuring polygons with multiple rings is not supported."),this._set("geometry",e.clone()),this._set("extent",e.extent)}get requiredPropertiesForEditing(){if(i(this.geometry)&&1===this.geometry.rings.length){const e=this.geometry.rings[0];if(e.length<=2||!r(e[0],e[e.length-1]))return[null]}return[this.geometry]}};e([l({type:["area-measurement"]})],y.prototype,"type",void 0),e([l({value:null,type:p})],y.prototype,"geometry",null),e([l({readOnly:!0})],y.prototype,"extent",void 0),e([l({type:n,value:null})],y.prototype,"unit",void 0),e([l({readOnly:!0})],y.prototype,"requiredPropertiesForEditing",null),e([l({readOnly:!0})],y.prototype,"nonEditableMessage",void 0),y=e([a("esri.analysis.AreaMeasurementAnalysis")],y);const g=y;export{g as default};
