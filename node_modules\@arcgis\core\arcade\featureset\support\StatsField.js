/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{FeatureServiceDatabaseType as e}from"./shared.js";import{toWhereClauseFromTree as r}from"./sqlUtils.js";import{WhereClause as t}from"../../../core/sql/WhereClause.js";function a(a){if("function"===a.parseTree.type){if(0===a.parseTree.args.value.length)return{name:a.parseTree.name,expr:null};if(a.parseTree.args.value.length>1)throw new Error("Statistic does not have 1 or 0 Parameters");const n=t.create(r(a.parseTree.args.value[0],e.Standardised,a.parameters),a.fieldsIndex);return{name:a.parseTree.name,expr:n}}return null}class n{clone(){const e=new n;return e.field=this.field,e.tofieldname=this.tofieldname,e.typeofstat=this.typeofstat,e.workingexpr=this.workingexpr,e}static parseStatField(e,r,i){const s=new n;s.field=e;const o=t.create(r,i),l=a(o);if(null===l)throw new Error("Invalid Statistic Function");const p=l.name.toUpperCase().trim();if("MIN"===p){if(s.typeofstat="MIN",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else if("MAX"===p){if(s.typeofstat="MAX",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else if("COUNT"===p)s.typeofstat="COUNT",s.workingexpr=l.expr;else if("STDEV"===p){if(s.typeofstat="STDDEV",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else if("SUM"===p){if(s.typeofstat="SUM",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else if("MEAN"===p){if(s.typeofstat="AVG",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else if("AVG"===p){if(s.typeofstat="AVG",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}else{if("VAR"!==p)throw new Error("Invalid Statistic Function");if(s.typeofstat="VAR",s.workingexpr=l.expr,null===o)throw new Error("Invalid Statistic Function Parameters")}return s}toStatisticsName(){switch(this.typeofstat.toUpperCase()){case"MIN":return"min";case"MAX":return"max";case"SUM":return"sum";case"COUNT":default:return"count";case"VAR":return"var";case"STDDEV":return"stddev";case"AVG":return"avg"}}}export{n as default};
