{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"box\"\n  }, [_c(\"span\", {\n    class: [\"fullOrSmall\", _vm.isFull ? \"isFull\" : \"\"],\n    on: {\n      click: function ($event) {\n        $event.stopPropagation();\n        return _vm.fullScreen.apply(null, arguments);\n      }\n    }\n  }), _c(\"canvas-player-list\", {\n    ref: \"videoRsl\",\n    attrs: {\n      \"player-list-style\": _vm.playerSingleStyle,\n      \"player-array\": _vm.playerArray2,\n      \"init-num\": _vm.currentWin,\n      height: _vm.canvasPlayListStyle.height + \"px\",\n      width: _vm.canvasPlayListStyle.width + \"px\",\n      \"bottom-list\": _vm.bottomList\n    },\n    on: {\n      playType: _vm.handlePlayType,\n      handleBtnClick: _vm.handleBtnClick,\n      closeItem: _vm.handleCloseItem,\n      handleDblclick: _vm.handleDblclick\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "isFull", "on", "click", "$event", "stopPropagation", "fullScreen", "apply", "arguments", "ref", "attrs", "player<PERSON><PERSON><PERSON><PERSON><PERSON>le", "playerArray2", "currentWin", "height", "canvasPlayListStyle", "width", "bottomList", "playType", "handlePlayType", "handleBtnClick", "closeItem", "handleCloseItem", "handleDblclick", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/rslPlayer/components/canvasPlayerSingle.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"box\" },\n    [\n      _c(\"span\", {\n        class: [\"fullOrSmall\", _vm.isFull ? \"isFull\" : \"\"],\n        on: {\n          click: function ($event) {\n            $event.stopPropagation()\n            return _vm.fullScreen.apply(null, arguments)\n          },\n        },\n      }),\n      _c(\"canvas-player-list\", {\n        ref: \"videoRsl\",\n        attrs: {\n          \"player-list-style\": _vm.playerSingleStyle,\n          \"player-array\": _vm.playerArray2,\n          \"init-num\": _vm.currentWin,\n          height: _vm.canvasPlayListStyle.height + \"px\",\n          width: _vm.canvasPlayListStyle.width + \"px\",\n          \"bottom-list\": _vm.bottomList,\n        },\n        on: {\n          playType: _vm.handlePlayType,\n          handleBtnClick: _vm.handleBtnClick,\n          closeItem: _vm.handleCloseItem,\n          handleDblclick: _vm.handleDblclick,\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAM,CAAC,EACtB,CACEF,EAAE,CAAC,MAAM,EAAE;IACTG,KAAK,EAAE,CAAC,aAAa,EAAEJ,GAAG,CAACK,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC;IAClDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBA,MAAM,CAACC,eAAe,CAAC,CAAC;QACxB,OAAOT,GAAG,CAACU,UAAU,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C;IACF;EACF,CAAC,CAAC,EACFX,EAAE,CAAC,oBAAoB,EAAE;IACvBY,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;MACL,mBAAmB,EAAEd,GAAG,CAACe,iBAAiB;MAC1C,cAAc,EAAEf,GAAG,CAACgB,YAAY;MAChC,UAAU,EAAEhB,GAAG,CAACiB,UAAU;MAC1BC,MAAM,EAAElB,GAAG,CAACmB,mBAAmB,CAACD,MAAM,GAAG,IAAI;MAC7CE,KAAK,EAAEpB,GAAG,CAACmB,mBAAmB,CAACC,KAAK,GAAG,IAAI;MAC3C,aAAa,EAAEpB,GAAG,CAACqB;IACrB,CAAC;IACDf,EAAE,EAAE;MACFgB,QAAQ,EAAEtB,GAAG,CAACuB,cAAc;MAC5BC,cAAc,EAAExB,GAAG,CAACwB,cAAc;MAClCC,SAAS,EAAEzB,GAAG,CAAC0B,eAAe;MAC9BC,cAAc,EAAE3B,GAAG,CAAC2B;IACtB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB7B,MAAM,CAAC8B,aAAa,GAAG,IAAI;AAE3B,SAAS9B,MAAM,EAAE6B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}