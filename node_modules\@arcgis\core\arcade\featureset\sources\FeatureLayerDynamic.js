/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import t from"../../../request.js";import r from"../../Attachment.js";import i from"../support/FeatureSet.js";import s from"../support/IdSet.js";import{defaultMaxRecords as a,stableStringify as n,FeatureServiceDatabaseType as l,IdState as o,extractServiceUrl as u}from"../support/shared.js";import{toWhereClause as d,isSingleField as h}from"../support/sqlUtils.js";import{decodeStatType as p}from"../support/stats.js";import{createMD5Hash as c,outputTypes as y}from"../../../core/MD5.js";import{id as f}from"../../../kernel.js";import{create as _,resolve as m,reject as g}from"../../../core/promiseUtils.js";import{fromJSON as F}from"../../../geometry/support/jsonUtils.js";import S from"../../../layers/FeatureLayer.js";import{convertToFeatureSet as b}from"../../../layers/graphics/featureConversionUtils.js";import"../../../core/urlUtils.js";import"../../../core/unitUtils.js";import"../../../rest/query/support/AttachmentInfo.js";import"../../../rest/support/AttachmentQuery.js";import{executeForCount as R}from"../../../rest/query/executeForCount.js";import"../../../geometry.js";import{executeQueryPBF as D}from"../../../rest/query/operations/query.js";import x from"../../../rest/support/Query.js";import{executeForIds as w}from"../../../rest/query/executeForIds.js";import{executeQueryJSON as I}from"../../../rest/query/executeQueryJSON.js";import"../../../core/has.js";import"../../../geometry/support/spatialReferenceUtils.js";import q from"../../../rest/support/FeatureSet.js";import"../../../rest/support/RelationshipQuery.js";import"../../../geometry/support/normalizeUtils.js";import"../../../rest/support/TopFeaturesQuery.js";import{OptimizedFeatureSetParserContext as P}from"../../../rest/query/operations/pbfOptimizedFeatureSet.js";import v from"../../../rest/support/StatisticDefinition.js";import C from"../../Dictionary.js";class j extends i{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerDynamic",this._removeGeometry=!1,this._overrideFields=null,this.formulaCredential=null,this._pageJustIds=!1,this._requestStandardised=!1,this._useDefinitionExpression=!0,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return a}end(){return this._layer}optimisePagingFeatureQueries(e){this._pageJustIds=e}convertQueryToLruCacheKey(e){const t=n(e.toJSON());return c(t,y.String)}load(){return null===this._loadPromise&&(this._loadPromise=_(((e,t)=>{try{if(!0===this._layer.loaded)return this._initialiseFeatureSet(),void e(this);this._layer.when().then((()=>{try{this._initialiseFeatureSet(),e(this)}catch(r){t(r)}}),t),this._layer.load()}catch(r){t(r)}}))),this._loadPromise}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields)if(1===this._layer.outFields.length&&"*"===this._layer.outFields[0]);else{const e=[];for(const t of this.fields)if("oid"===t.type)e.push(t);else for(const r of this._layer.outFields)if(r.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}else;if(null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const i of this._overrideFields)if(i.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}if(this._layer.source&&this._layer.source.sourceJSON){const e=this._layer.source.sourceJSON.currentVersion;!0===this._layer.source.sourceJSON.useStandardizedQueries?(this._databaseType=l.StandardisedNoInterval,null!=e&&e>=10.61&&(this._databaseType=l.Standardised)):null!=e&&(e>=10.5&&(this._databaseType=l.StandardisedNoInterval,this._requestStandardised=!0),e>=10.61&&(this._databaseType=l.Standardised))}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this.typeIdField=this._layer.typeIdField,this.types=this._layer.types}_isInFeatureSet(){return o.InFeatureSet}_refineSetBlock(e){return m(e)}_candidateIdTransform(e){return e}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._getFilteredSet("",null,null,null,e))).then((e=>(this._wset=e,e))):m(this._wset)}_runDatabaseProbe(e){return _(((t,r)=>{try{this._ensureLoaded().then((()=>{try{const i=new x;i.where=e.replace("OBJECTID",this._layer.objectIdField),this._layer.queryObjectIds(i).then((()=>{t(!0)}),(()=>{try{t(!1)}catch(e){r(e)}}))}catch(i){r(i)}}))}catch(i){r(i)}}))}_canUsePagination(){return!(!this._layer.capabilities||!this._layer.capabilities.query||!0!==this._layer.capabilities.query.supportsPagination)}_cacheableFeatureSetSourceKey(){return this._layer.url}pbfSupportedForQuery(e){return!e.outStatistics&&this._layer&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsFormatPBF&&!0===this._layer.capabilities.query.supportsQuantizationEditMode}queryPBF(e){return e.quantizationParameters={mode:"edit"},D(this._layer.parsedUrl,e,new P({})).then((e=>q.fromJSON(b(e.data)).unquantize()))}get gdbVersion(){return this._layer&&this._layer.capabilities&&this._layer.capabilities.data&&this._layer.capabilities.data.isVersioned?this._layer.gdbVersion?this._layer.gdbVersion:"SDE.DEFAULT":""}nativeCapabilities(){return{title:this._layer.title,source:this,canQueryRelated:!0,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:this._requestStandardised}}executeQuery(e,t){const r="execute"===t?I:"executeForCount"===t?R:w,i="execute"===t&&this.pbfSupportedForQuery(e);let s=null;if(this.recentlyUsedQueries){const t=this.convertQueryToLruCacheKey(e);s=this.recentlyUsedQueries.getFromCache(t),null===s&&(s=!0!==i?r(this._layer.parsedUrl.path,e):this.queryPBF(e),this.recentlyUsedQueries.addToCache(t,s),s=s.catch((e=>{throw this.recentlyUsedQueries.removeFromCache(t),e})))}return this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:e,method:t}),null===s&&(s=!0!==i?r(this._layer.parsedUrl.path,e):this.queryPBF(e)),s}_getFilteredSet(e,t,r,i,a){return this.databaseType().then((n=>{if(this.isTable()&&t&&null!==e&&""!==e){return new s([],[],!0,null)}if(this._canUsePagination())return this._getFilteredSetUsingPaging(e,t,r,i,a);let l="",o=!1;null!==i&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(l=i.constructClause(),o=!0);const u=new x;return u.where=null===r?null===t?"1=1":"":d(r,n),this._requestStandardised&&(u.sqlFormat="standard"),u.spatialRelationship=this._makeRelationshipEnum(e),u.outSpatialReference=this.spatialReference,u.orderByFields=""!==l?l.split(","):null,u.geometry=null===t?null:t,u.relationParameter=this._makeRelationshipParam(e),this.executeQuery(u,"executeForIds").then((e=>{null===e&&(e=[]),this._checkCancelled(a);return new s([],e,o,null)}))}))}_expandPagedSet(e,t,r,i,s){return this._expandPagedSetFeatureSet(e,t,r,i,s)}_getFilteredSetUsingPaging(e,t,r,i,a){try{let n="",l=!1;return null!==i&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(n=i.constructClause(),l=!0),this.databaseType().then((i=>{let o=null===r?null===t?"1=1":"":d(r,i);this._layer.definitionExpression&&this._useDefinitionExpression&&(o=""!==o?"(("+this._layer.definitionExpression+") AND ("+o+"))":this._layer.definitionExpression);let u=this._maxQueryRate();const h=this._layer.capabilities.query.maxRecordCount;void 0!==h&&h<u&&(u=h);let p=null;if(!0===this._pageJustIds)p=new s([],["GETPAGES"],l,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:this._layer.objectIdField,resultRecordCount:u,resultOffset:0,geometry:null===t?null:t,where:o,orderByFields:n,returnGeometry:!1,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}});else{let r=!0;!0===this._removeGeometry&&(r=!1);const i=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]);p=new s([],["GETPAGES"],l,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:i.join(","),resultRecordCount:u,resultOffset:0,geometry:null===t?null:t,where:o,orderByFields:n,returnGeometry:r,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}return this._expandPagedSet(p,u,0,1,a).then((()=>p))}))}catch(n){return g(n)}}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}_getPhysicalPage(e,t,r){try{const t=e.pagesDefinition.internal.lastRetrieved,i=t,s=e.pagesDefinition.internal.lastPage,a=new x;return this._requestStandardised&&(a.sqlFormat="standard"),a.spatialRelationship=e.pagesDefinition.spatialRel,a.relationParameter=e.pagesDefinition.relationParam,a.outFields=e.pagesDefinition.outFields.split(","),a.num=e.pagesDefinition.resultRecordCount,a.start=e.pagesDefinition.internal.lastPage,a.geometry=e.pagesDefinition.geometry,a.where=e.pagesDefinition.where,a.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,a.returnGeometry=e.pagesDefinition.returnGeometry,a.outSpatialReference=this.spatialReference,this.executeQuery(a,"execute").then((a=>{if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==s)return"done";for(let t=0;t<a.features.length;t++)e.pagesDefinition.internal.set[i+t]=a.features[t].attributes[this._layer.objectIdField];if(!1===this._pageJustIds)for(let e=0;e<a.features.length;e++)this._featureCache[a.features[e].attributes[this._layer.objectIdField]]=a.features[e];return(void 0===a.exceededTransferLimit&&a.features.length!==e.pagesDefinition.resultRecordCount||!1===a.exceededTransferLimit)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=t+a.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,"done"}))}catch(i){return g(i)}}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice(0);if(t.indexOf("*")>-1)return t;let r=!1;for(const i of t)if(i.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}_getFeatures(e,t,r,i){const s=[];try{if(-1!==t&&void 0===this._featureCache[t]&&s.push(t),!0===this._checkIfNeedToExpandKnownPage(e,this._maxProcessingRate()))return this._expandPagedSet(e,this._maxProcessingRate(),0,0,i).then((()=>this._getFeatures(e,t,r,i)));let n=0;for(let i=e._lastFetchedIndex;i<e._known.length;i++){if(e._lastFetchedIndex+=1,n++,void 0===this._featureCache[e._known[i]]){let r=!1;if(null!==this._layer._mode&&void 0!==this._layer._mode){const t=this._layer._mode;if(void 0!==t._featureMap[e._known[i]]){const s=t._featureMap[e._known[i]];null!==s&&(r=!0,this._featureCache[e._known[i]]=s)}}if(!1===r&&(e._known[i]!==t&&s.push(e._known[i]),s.length>=this._maxProcessingRate()-1))break}if(n>=r&&0===s.length)break}if(0===s.length)return m("success");try{const e=new x;return this._requestStandardised&&(e.sqlFormat="standard"),e.objectIds=s,e.outFields=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]),e.returnGeometry=!0,!0===this._removeGeometry&&(e.returnGeometry=!1),e.outSpatialReference=this.spatialReference,this.executeQuery(e,"execute").then((e=>{if(this._checkCancelled(i),void 0!==e.error)return g(new Error(e.error));for(let t=0;t<e.features.length;t++)this._featureCache[e.features[t].attributes[this._layer.objectIdField]]=e.features[t];return"success"}))}catch(a){return g(a)}}catch(a){return g(a)}}_getDistinctPages(e,t,r,i,s,a,n,l,o){return this._ensureLoaded().then((()=>this.databaseType())).then((u=>{let h=r.parseTree.column;for(let e=0;e<this._layer.fields.length;e++)if(this._layer.fields[e].name.toLowerCase()===h.toLowerCase()){h=this._layer.fields[e].name;break}const p=new x;this._requestStandardised&&(p.sqlFormat="standard");let c=null===a?null===s?"1=1":"":d(a,u);return this._layer.definitionExpression&&this._useDefinitionExpression&&(c=""!==c?"(("+this._layer.definitionExpression+") AND ("+c+"))":this._layer.definitionExpression),p.where=c,p.spatialRelationship=this._makeRelationshipEnum(i),p.relationParameter=this._makeRelationshipParam(i),p.geometry=null===s?null:s,p.returnDistinctValues=!0,p.returnGeometry=!1,p.outFields=[h],this.executeQuery(p,"execute").then((u=>{if(this._checkCancelled(o),!u.hasOwnProperty("features"))return g(new Error("Unnexected Result querying statistics from layer"));let d=!1;for(let e=0;e<this._layer.fields.length;e++)if(this._layer.fields[e].name===h){"date"===this._layer.fields[e].type&&(d=!0);break}for(let e=0;e<u.features.length;e++){if(d){const t=u.features[e].attributes[h];null!==t?l.push(new Date(t)):l.push(t)}else l.push(u.features[e].attributes[h]);if(l.length>=n)break}return 0===u.features.length?l:u.features.length===this._layer.capabilities.query.maxRecordCount&&l.length<n?this._getDistinctPages(e+u.features.length,t,r,i,s,a,n,l,o).then((e=>({calculated:!0,result:e}))):l}))}))}_distinctStat(e,t,r,i,s,a,n){return this._getDistinctPages(0,e,t,r,i,s,a,[],n).then((e=>({calculated:!0,result:e})))}isTable(){return this._layer.isTable||null===this._layer.geometryType||"table"===this._layer.type||""===this._layer.geometryType}_countstat(e,t,r,i){return this.databaseType().then((e=>{const s=new x;if(this._requestStandardised&&(s.sqlFormat="standard"),this.isTable()&&r&&null!==t&&""!==t)return{calculated:!0,result:0};let a=null===i?null===r?"1=1":"":d(i,e);return this._layer.definitionExpression&&this._useDefinitionExpression&&(a=""!==a?"(("+this._layer.definitionExpression+") AND ("+a+"))":this._layer.definitionExpression),s.where=a,s.where=a,s.spatialRelationship=this._makeRelationshipEnum(t),s.relationParameter=this._makeRelationshipParam(t),s.geometry=null===r?null:r,s.returnGeometry=!1,this.executeQuery(s,"executeForCount").then((e=>({calculated:!0,result:e})))}))}_stats(e,t,r,i,s,a,n){return this._ensureLoaded().then((()=>{const l=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsSqlExpression,o=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsStatistics,u=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsDistinct;return"count"===e?u?this._countstat(e,r,i,s):{calculated:!1}:!1===o||!1===h(t)&&!1===l||!1===t.isStandardized?""!==r||null!==s?{calculated:!1}:this._manualStat(e,t,a,n):"distinct"===e?!1===u?""!==r||null!==s?{calculated:!1}:this._manualStat(e,t,a,n):this._distinctStat(e,t,r,i,s,a,n):this.databaseType().then((a=>{if(this.isTable()&&i&&null!==r&&""!==r)return{calculated:!0,result:null};const n=new x;this._requestStandardised&&(n.sqlFormat="standard");let l=null===s?null===i?"1=1":"":d(s,a);this._layer.definitionExpression&&this._useDefinitionExpression&&(l=""!==l?"(("+this._layer.definitionExpression+") AND ("+l+"))":this._layer.definitionExpression),n.where=l,n.spatialRelationship=this._makeRelationshipEnum(r),n.relationParameter=this._makeRelationshipParam(r),n.geometry=null===i?null:i;const o=new v;o.statisticType=p(e),o.onStatisticField=d(t,a),o.outStatisticFieldName="ARCADE_STAT_RESULT",n.returnGeometry=!1;let u="ARCADE_STAT_RESULT";return n.outStatistics=[o],this.executeQuery(n,"execute").then((e=>{if(!e.hasOwnProperty("features")||0===e.features.length)return g(new Error("Unnexected Result querying statistics from layer"));let t=!1;for(let r=0;r<e.fields.length;r++)if("ARCADE_STAT_RESULT"===e.fields[r].name.toUpperCase()){u=e.fields[r].name,"date"===e.fields[r].type&&(t=!0);break}if(t){let t=e.features[0].attributes[u];return null!==t&&(t=new Date(e.features[0].attributes[u])),{calculated:!0,result:t}}return{calculated:!0,result:e.features[0].attributes[u]}}))}))}))}_stat(e,t,r,i,s,a,n){return this._stats(e,t,r,i,s,a,n)}_canDoAggregates(e,t){return this._ensureLoaded().then((()=>{let e=!1;const r=this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsSqlExpression;if(void 0!==this._layer.capabilities&&null!==this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsStatistics&&!0===this._layer.capabilities.query.supportsOrderBy&&(e=!0),e)for(let i=0;i<t.length-1;i++)null!==t[i].workingexpr&&(!1===t[i].workingexpr.isStandardized||!1===h(t[i].workingexpr)&&!1===r)&&(e=!1);return!1!==e}))}_makeRelationshipEnum(e){if(e.indexOf("esriSpatialRelRelation")>=0)return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.indexOf("esriSpatialRelRelation")>=0?e.split(":")[1]:""}_getAggregatePagesDataSourceDefinition(e,t,r,i,a,n,l){return this._ensureLoaded().then((()=>this.databaseType())).then((o=>{let u="",h=!1,p=!1;null!==n&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(u=n.constructClause(),p=!0),this._layer.capabilities&&this._layer.capabilities.query&&!1===this._layer.capabilities.query.supportsPagination&&(p=!1,h=!0,u=this._layer.objectIdField);const c=[];for(let e=0;e<t.length;e++){const r=new v;r.onStatisticField=null!==t[e].workingexpr?d(t[e].workingexpr,o):"",r.outStatisticFieldName=t[e].field,r.statisticType=t[e].toStatisticsName(),c.push(r)}""===u&&(u=e.join(","));let y=this._maxQueryRate();const f=this._layer.capabilities.query.maxRecordCount;void 0!==f&&f<y&&(y=f);let _=null===a?null===i?"1=1":"":d(a,o);this._layer.definitionExpression&&this._useDefinitionExpression&&(_=""!==_?"(("+this._layer.definitionExpression+") AND ("+_+"))":this._layer.definitionExpression);return new s([],["GETPAGES"],p,{groupbypage:!0,spatialRel:this._makeRelationshipEnum(r),relationParam:this._makeRelationshipParam(r),outFields:["*"],useOIDpagination:h,generatedOid:l,resultRecordCount:y,resultOffset:0,groupByFieldsForStatistics:e,outStatistics:c,geometry:null===i?null:i,where:_,orderByFields:u,returnGeometry:!1,returnIdsOnly:!1,internal:{lastMaxId:-1,set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}))}_getAgregagtePhysicalPage(t,r,i){try{let r=t.pagesDefinition.where;!0===t.pagesDefinition.useOIDpagination&&(r=""!==r?"("+r+") AND ("+t.pagesDefinition.generatedOid+">"+t.pagesDefinition.internal.lastMaxId.toString()+")":t.pagesDefinition.generatedOid+">"+t.pagesDefinition.internal.lastMaxId.toString());const s=t.pagesDefinition.internal.lastRetrieved,a=s,n=t.pagesDefinition.internal.lastPage,l=new x;return this._requestStandardised&&(l.sqlFormat="standard"),l.where=r,l.spatialRelationship=t.pagesDefinition.spatialRel,l.relationParameter=t.pagesDefinition.relationParam,l.outFields=t.pagesDefinition.outFields,l.outStatistics=t.pagesDefinition.outStatistics,l.geometry=t.pagesDefinition.geometry,l.groupByFieldsForStatistics=t.pagesDefinition.groupByFieldsForStatistics,l.num=t.pagesDefinition.resultRecordCount,l.start=t.pagesDefinition.internal.lastPage,l.returnGeometry=t.pagesDefinition.returnGeometry,l.orderByFields=""!==t.pagesDefinition.orderByFields?t.pagesDefinition.orderByFields.split(","):null,this.isTable()&&l.geometry&&l.spatialRelationship?m([]):this.executeQuery(l,"execute").then((r=>{if(this._checkCancelled(i),!r.hasOwnProperty("features"))return g(new Error("Unnexected Result querying aggregates from layer"));const l=[];if(t.pagesDefinition.internal.lastPage!==n)return[];for(let e=0;e<r.features.length;e++)t.pagesDefinition.internal.set[a+e]=r.features[e].attributes[t.pagesDefinition.generatedOid];for(let t=0;t<r.features.length;t++)l.push(new e({attributes:r.features[t].attributes,geometry:null}));return!0===t.pagesDefinition.useOIDpagination?0===r.features.length?t.pagesDefinition.internal.fullyResolved=!0:t.pagesDefinition.internal.lastMaxId=r.features[r.features.length-1].attributes[t.pagesDefinition.generatedOid]:(void 0===r.exceededTransferLimit&&r.features.length!==t.pagesDefinition.resultRecordCount||!1===r.exceededTransferLimit)&&(t.pagesDefinition.internal.fullyResolved=!0),t.pagesDefinition.internal.lastRetrieved=s+r.features.length,t.pagesDefinition.internal.lastPage+=t.pagesDefinition.resultRecordCount,l}))}catch(s){return g(s)}}static create(e,t,r,i,s){const a=new S({url:e,outFields:null===t?["*"]:t});return new j({layer:a,spatialReference:r,lrucache:i,interceptor:s})}relationshipMetaData(){return this._layer&&this._layer.source&&this._layer.source.sourceJSON&&this._layer.source.sourceJSON.relationships?this._layer.source.sourceJSON.relationships:[]}serviceUrl(){return u(this._layer.parsedUrl.path)}queryAttachments(e,t,i,s,a){if(this._layer.capabilities.data.supportsAttachment&&this._layer.capabilities.operations.supportsQueryAttachments){const n={objectIds:[e],returnMetadata:a};return(t&&t>0||i&&i>0)&&(n.size=[t&&t>0?t:0,i&&i>0?i:t+1]),s&&s.length>0&&(n.attachmentTypes=s),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:n,method:"attachments"}),this._layer.queryAttachments(n).then((t=>{const i=[];return t&&t[e]&&t[e].forEach((t=>{const s=this._layer.parsedUrl.path+"/"+e.toString()+"/attachments/"+t.id.toString();let n=null;a&&t.exifInfo&&(n=C.convertJsonToArcade(t.exifInfo,!0)),i.push(new r(t.id,t.name,t.contentType,t.size,s,n))})),i}))}return m([])}queryRelatedFeatures(r){const i={f:"json",relationshipId:r.relationshipId.toString(),definitionExpression:r.where,outFields:r.outFields.join(","),returnGeometry:r.returnGeometry.toString()};return void 0!==r.resultOffset&&null!==r.resultOffset&&(i.resultOffset=r.resultOffset.toString()),void 0!==r.resultRecordCount&&null!==r.resultRecordCount&&(i.resultRecordCount=r.resultRecordCount.toString()),r.orderByFields&&(i.orderByFields=r.orderByFields.join(",")),r.objectIds.length>0&&(i.objectIds=r.objectIds.join(",")),r.outSpatialReference&&(i.outSR=JSON.stringify(r.outSpatialReference.toJSON())),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preRequestCallback({layer:this._layer,queryPayload:i,method:"relatedrecords",url:this._layer.parsedUrl.path+"/queryRelatedRecords"}),t(this._layer.parsedUrl.path+"/queryRelatedRecords",{responseType:"json",query:i}).then((t=>{if(t.data){const r={},i=t.data;if(i&&i.relatedRecordGroups){const t=i.spatialReference;for(const s of i.relatedRecordGroups){const a=s.objectId,n=[];for(const r of s.relatedRecords){r.geometry&&(r.geometry.spatialReference=t);const i=new e({geometry:r.geometry?F(r.geometry):null,attributes:r.attributes});n.push(i)}r[a]={features:n,exceededTransferLimit:!0===i.exceededTransferLimit}}}return r}return g("Invalid Request")}))}getFeatureByObjectId(e,t){const r=new x;return r.outFields=t,r.returnGeometry=!1,r.outSpatialReference=this.spatialReference,r.where=this.objectIdField+"="+e.toString(),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:r,method:"execute"}),I(this._layer.parsedUrl.path,r).then((e=>1===e.features.length?e.features[0]:null))}getIdentityUser(){return this.load().then((()=>{var e;const t=null==(e=f)?void 0:e.findCredential(this._layer.url);return t?t.userId:null}))}getOwningSystemUrl(){return this.load().then((()=>{var e;const r=null==(e=f)?void 0:e.findServerInfo(this._layer.url);if(r)return m(r.owningSystemUrl);let i=this._layer.url;const s=i.toLowerCase().indexOf("/rest/services");return i=s>-1?i.substring(0,s):i,i?(i+="/rest/info",_(((e,r)=>{t(i,{query:{f:"json"}}).then((t=>{let r="";t.data&&t.data.owningSystemUrl&&(r=t.data.owningSystemUrl),e(r)}),(t=>{e("")}))}))):m("")}))}getDataSourceFeatureSet(){const e=new j({layer:this._layer,spatialReference:this.spatialReference,outFields:this._overrideFields,includeGeometry:!this._removeGeometry,lrucache:this.recentlyUsedQueries,interceptor:this.featureSetQueryInterceptor});return e._useDefinitionExpression=!1,e}}export{j as default};
