{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"videoBoxCom\"\n  }, [_vm.playerList.videoListTree ? _c(\"videoPlayerTree\") : _vm._e(), _vm.playerList.videoPlayerBox ? _c(\"singleHlsVideo\", {\n    attrs: {\n      allStyle: _vm.allStyle,\n      minWidth: _vm.minWidth\n    },\n    on: {\n      updateStyle: _vm.handleStyle\n    }\n  }) : _vm._e(), _vm.playerList.informationBox ? _c(\"informationType\") : _vm._e(), _vm.playerList.videoInfoFlag ? _c(\"getVideoInfo\") : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "playerList", "videoListTree", "_e", "videoPlayerBox", "attrs", "allStyle", "min<PERSON><PERSON><PERSON>", "on", "updateStyle", "handleStyle", "informationBox", "videoInfoFlag", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/views/mainPage/components/videoPlayerSteam.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"videoBoxCom\" },\n    [\n      _vm.playerList.videoListTree ? _c(\"videoPlayerTree\") : _vm._e(),\n      _vm.playerList.videoPlayerBox\n        ? _c(\"singleHlsVideo\", {\n            attrs: { allStyle: _vm.allStyle, minWidth: _vm.minWidth },\n            on: { updateStyle: _vm.handleStyle },\n          })\n        : _vm._e(),\n      _vm.playerList.informationBox ? _c(\"informationType\") : _vm._e(),\n      _vm.playerList.videoInfoFlag ? _c(\"getVideoInfo\") : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEH,GAAG,CAACI,UAAU,CAACC,aAAa,GAAGJ,EAAE,CAAC,iBAAiB,CAAC,GAAGD,GAAG,CAACM,EAAE,CAAC,CAAC,EAC/DN,GAAG,CAACI,UAAU,CAACG,cAAc,GACzBN,EAAE,CAAC,gBAAgB,EAAE;IACnBO,KAAK,EAAE;MAAEC,QAAQ,EAAET,GAAG,CAACS,QAAQ;MAAEC,QAAQ,EAAEV,GAAG,CAACU;IAAS,CAAC;IACzDC,EAAE,EAAE;MAAEC,WAAW,EAAEZ,GAAG,CAACa;IAAY;EACrC,CAAC,CAAC,GACFb,GAAG,CAACM,EAAE,CAAC,CAAC,EACZN,GAAG,CAACI,UAAU,CAACU,cAAc,GAAGb,EAAE,CAAC,iBAAiB,CAAC,GAAGD,GAAG,CAACM,EAAE,CAAC,CAAC,EAChEN,GAAG,CAACI,UAAU,CAACW,aAAa,GAAGd,EAAE,CAAC,cAAc,CAAC,GAAGD,GAAG,CAACM,EAAE,CAAC,CAAC,CAC7D,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIU,eAAe,GAAG,EAAE;AACxBjB,MAAM,CAACkB,aAAa,GAAG,IAAI;AAE3B,SAASlB,MAAM,EAAEiB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}