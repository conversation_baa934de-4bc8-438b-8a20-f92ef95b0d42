{"ast": null, "code": "import { mapState } from 'vuex';\nexport default {\n  name: \"informationType\",\n  data() {\n    return {\n      checkedTypes: [],\n      types: [],\n      maxWinPlayer: 1,\n      areaTitInfo: null,\n      areaTitInfoList: [],\n      typeEquipmentAll: [],\n      isIndeterminate: true,\n      checkAll: false,\n      pageInfo: {\n        page_num: 1,\n        page_size: 10,\n        total_num: 10\n      }\n    };\n  },\n  props: {\n    allStyle: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    },\n    sizeStyle: {\n      type: Object,\n      default: () => {\n        return {\n          transform: ' scale(1)',\n          left: \"calc(25%)\"\n        };\n      }\n    }\n  },\n  computed: {\n    ...mapState(\"action\", [\"playerList\", \"TypeVideoAll\", \"eventSubBoxFlag\"]),\n    // TypeVideoAll() {\n    //     return this.$store.state.action.TypeVideoAll\n    // },\n    winPlayerNum() {\n      return this.$store.state.action.winPlayerNum;\n    }\n  },\n  watch: {\n    playerList: {\n      handler(newVal) {\n        if (!newVal.videoPlayerBox) {\n          // this.handleCheckAllChange(false)\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    TypeVideoAll: {\n      handler(nv) {\n        if (nv) {\n          this.checkedTypes = [];\n          this.types = [];\n          if (nv?.paging) {\n            this.types = nv.data;\n            this.pageInfo = nv.paging;\n          } else {\n            this.types = nv;\n            // if (nv.length) {\n            //     // 给ue发送撒点\n            //     let params = {\n            //         name: '区域监控',\n            //         data: nv,\n            //         flag: true\n            //     }\n            //     this.$eventBus.$emit('senTxtToUe', params)\n            // }\n          }\n        }\n      },\n      immediate: true\n    },\n    winPlayerNum: {\n      handler(nv) {\n        if (nv > this.maxWinPlayer) {\n          this.maxWinPlayer = nv;\n        }\n      },\n      immediate: true\n    }\n    // checkedTypes(nv) {\n    //     // if (nv.length > this.winPlayerNum) {\n    //     if (nv.length > 1) {\n    //         //删除 this.checkedTypes 第一个数组\n    //         this.checkedTypes.shift()\n    //     }\n    // },\n    // playerList:{\n    //     handler(nv){\n    //         this.checkedTypes=[]\n    //     },\n    //     deep:true,\n    // }\n  },\n  methods: {\n    handleCheckAllChange(val) {\n      this.checkedTypes = val ? this.types : [];\n      this.isIndeterminate = false;\n      this.sendUePoint(this.checkedTypes, val);\n      this.$store.commit(\"action/getVideoPlayerBox\", false);\n    },\n    sendUePoint(list, flag) {\n      let params = {\n        name: '区域监控',\n        data: list,\n        flag: flag,\n        number: 4\n      };\n      console.log('params', params);\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    changeVal(val) {\n      let checkedCount = val.length;\n      this.checkAll = checkedCount === this.types.length;\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.types.length;\n      // this.openPlayer(val[checkedCount - 1])\n    },\n    changeBox(flag, val) {\n      this.sendUePoint([val.target._value], flag);\n      if (flag) {\n        this.openPlayer(val.target._value);\n      } else {\n        if (val.target._value.channalCode == this.areaTitInfo.channalCode) {\n          this.$store.commit(\"action/getVideoPlayerBox\", false);\n        }\n      }\n    },\n    // 播放视频流\n    openPlayer(val) {\n      this.areaTitInfo = val;\n      this.openVideo();\n      this.$store.commit(\"action/getVideoPlayerList\", this.areaTitInfo);\n    },\n    // 关闭弹窗\n    closeInfo() {\n      this.$store.commit(\"action/getInformationBox\", false);\n      this.$store.commit('action/getEventSubBoxFlag', true);\n      this.$store.commit(\"action/getEventType\", true);\n      this.$store.commit(\"action/getIsShowNumInitChange\", false);\n      this.$store.commit(\"action/getVideoPlayerBox\", false);\n    },\n    // 打开监控弹窗\n    openVideo() {\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n      this.$store.commit(\"action/getIsShowNumInitChange\", true);\n    },\n    // 页数变化\n    handleCurrentChange(val) {\n      let params = {\n        page_num: val,\n        page_size: 10,\n        org_index: this.TypeVideoAll.data[0].org_index\n      };\n      this.$store.dispatch(\"action/getTypeVideoAll\", params);\n    }\n  },\n  mounted() {\n    // this.getInfo()\n  }\n};", "map": {"version": 3, "names": ["mapState", "name", "data", "checkedTypes", "types", "maxWinPlayer", "areaTitInfo", "areaTitInfoList", "typeEquipmentAll", "isIndeterminate", "checkAll", "pageInfo", "page_num", "page_size", "total_num", "props", "allStyle", "type", "Object", "default", "sizeStyle", "transform", "left", "computed", "winPlayerNum", "$store", "state", "action", "watch", "playerList", "handler", "newVal", "videoPlayerBox", "immediate", "deep", "TypeVideoAll", "nv", "paging", "methods", "handleCheckAllChange", "val", "sendUePoint", "commit", "list", "flag", "params", "number", "console", "log", "$eventBus", "$emit", "changeVal", "checkedCount", "length", "changeBox", "target", "_value", "openPlayer", "channalCode", "openVideo", "closeInfo", "handleCurrentChange", "org_index", "dispatch", "mounted"], "sources": ["src/components/videoTypeOption/informationType.vue"], "sourcesContent": ["<template>\r\n    <div class=\"inform boxBgStyle\" :style=\"sizeStyle\">\r\n        <div class=\"topMain\">\r\n            <div class=\"title\">\r\n                <div class=\"txt\">监控点位</div>\r\n            </div>\r\n            <div class=\"delete\" @click=\"closeInfo\">\r\n                <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"mainCon\">\r\n            <div class=\"allBox\">\r\n                <el-checkbox :indeterminate=\"isIndeterminate\" v-model=\"checkAll\"\r\n                    @change=\"handleCheckAllChange\">全选</el-checkbox>\r\n            </div>\r\n            <!-- <el-checkbox-group @change=\"changeVal\" v-model=\"checkedTypes\" :min=\"0\" :max=\"maxWinPlayer / 1 + 1 / 1\"> -->\r\n            <el-checkbox-group @change=\"changeVal\" v-model=\"checkedTypes\">\r\n                <el-checkbox v-for=\"item in types\" :label=\"item\" :key=\"item.id\" @change=\"changeBox\">{{ item.name ?\r\n                    item.name : item.deviceName }}</el-checkbox>\r\n            </el-checkbox-group>\r\n            <div class=\"pageSelect\" v-if=\"TypeVideoAll?.paging\">\r\n                <el-pagination layout=\"prev, pager, next\" :current-page.sync=\"pageInfo.page_num\"\r\n                    :page-size=\"pageInfo.page_size\" :total=\"pageInfo.total_num\" :pager-count=\"5\" small\r\n                    @current-change=\"handleCurrentChange\">\r\n                </el-pagination>\r\n\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: \"informationType\",\r\n    data() {\r\n        return {\r\n            checkedTypes: [],\r\n            types: [],\r\n            maxWinPlayer: 1,\r\n            areaTitInfo: null,\r\n            areaTitInfoList: [],\r\n            typeEquipmentAll: [],\r\n            isIndeterminate: true,\r\n            checkAll: false,\r\n            pageInfo: {\r\n                page_num: 1,\r\n                page_size: 10,\r\n                total_num: 10\r\n            },\r\n        }\r\n    },\r\n    props: {\r\n        allStyle: {\r\n            type: Object,\r\n            default: () => {\r\n                return {\r\n\r\n                }\r\n            }\r\n        },\r\n        sizeStyle: {\r\n            type: Object,\r\n            default: () => {\r\n                return {\r\n                    transform: ' scale(1)',\r\n                    left: \"calc(25%)\"\r\n                }\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"playerList\",\r\n            \"TypeVideoAll\",\r\n            \"eventSubBoxFlag\",\r\n        ]),\r\n        // TypeVideoAll() {\r\n        //     return this.$store.state.action.TypeVideoAll\r\n        // },\r\n        winPlayerNum() {\r\n            return this.$store.state.action.winPlayerNum\r\n        }\r\n    },\r\n    watch: {\r\n        playerList:{\r\n            handler(newVal){\r\n                if(!newVal.videoPlayerBox ){\r\n                    // this.handleCheckAllChange(false)\r\n                }\r\n            },\r\n            immediate:true,\r\n            deep: true,\r\n        },\r\n        TypeVideoAll: {\r\n            handler(nv) {\r\n                if (nv) {\r\n                    this.checkedTypes = [];\r\n                    this.types = [];\r\n                    if (nv?.paging) {\r\n                        this.types = nv.data;\r\n                        this.pageInfo = nv.paging\r\n\r\n                    } else {\r\n                        this.types = nv;\r\n                        // if (nv.length) {\r\n                        //     // 给ue发送撒点\r\n                        //     let params = {\r\n                        //         name: '区域监控',\r\n                        //         data: nv,\r\n                        //         flag: true\r\n                        //     }\r\n                        //     this.$eventBus.$emit('senTxtToUe', params)\r\n                        // }\r\n                    }\r\n\r\n\r\n                }\r\n            },\r\n            immediate: true,\r\n        },\r\n        winPlayerNum: {\r\n            handler(nv) {\r\n                if (nv > this.maxWinPlayer) {\r\n                    this.maxWinPlayer = nv\r\n\r\n                }\r\n            },\r\n            immediate: true\r\n        },\r\n        // checkedTypes(nv) {\r\n        //     // if (nv.length > this.winPlayerNum) {\r\n        //     if (nv.length > 1) {\r\n        //         //删除 this.checkedTypes 第一个数组\r\n        //         this.checkedTypes.shift()\r\n        //     }\r\n        // },\r\n        // playerList:{\r\n        //     handler(nv){\r\n        //         this.checkedTypes=[]\r\n        //     },\r\n        //     deep:true,\r\n        // }\r\n\r\n    },\r\n\r\n    methods: {\r\n        handleCheckAllChange(val) {\r\n            this.checkedTypes = val ? this.types : [];\r\n            this.isIndeterminate = false;\r\n            this.sendUePoint(this.checkedTypes, val)\r\n            this.$store.commit(\"action/getVideoPlayerBox\", false)\r\n        },\r\n        sendUePoint(list, flag) {\r\n            let params = {\r\n                name: '区域监控',\r\n                data: list,\r\n                flag: flag,\r\n                number:4\r\n            }\r\n            console.log('params', params)\r\n            this.$eventBus.$emit('senTxtToUe', params)\r\n        },\r\n        changeVal(val) {\r\n            let checkedCount = val.length;\r\n            this.checkAll = checkedCount === this.types.length;\r\n            this.isIndeterminate = checkedCount > 0 && checkedCount < this.types.length;\r\n            // this.openPlayer(val[checkedCount - 1])\r\n        },\r\n        changeBox(flag, val) {\r\n            this.sendUePoint([val.target._value], flag)\r\n            if (flag) {\r\n                this.openPlayer(val.target._value)\r\n            } else {\r\n                if (val.target._value.channalCode == this.areaTitInfo.channalCode) {\r\n                    this.$store.commit(\"action/getVideoPlayerBox\", false)\r\n                }\r\n            }\r\n\r\n        },\r\n        // 播放视频流\r\n        openPlayer(val) {\r\n            this.areaTitInfo = val\r\n            this.openVideo()\r\n            this.$store.commit(\"action/getVideoPlayerList\", this.areaTitInfo)\r\n        },\r\n        // 关闭弹窗\r\n        closeInfo() {\r\n            this.$store.commit(\"action/getInformationBox\", false);\r\n            this.$store.commit('action/getEventSubBoxFlag', true)\r\n            this.$store.commit(\"action/getEventType\", true)\r\n            this.$store.commit(\"action/getIsShowNumInitChange\", false)\r\n            this.$store.commit(\"action/getVideoPlayerBox\", false)\r\n\r\n        },\r\n        // 打开监控弹窗\r\n        openVideo() {\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n            this.$store.commit(\"action/getIsShowNumInitChange\", true)\r\n        },\r\n        // 页数变化\r\n        handleCurrentChange(val) {\r\n            let params = {\r\n                page_num: val,\r\n                page_size: 10,\r\n                org_index: this.TypeVideoAll.data[0].org_index\r\n            }\r\n            this.$store.dispatch(\"action/getTypeVideoAll\", params)\r\n        }\r\n\r\n    },\r\n    mounted() {\r\n        // this.getInfo()\r\n    }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.inform {\r\n    pointer-events: stroke;\r\n    z-index: 22;\r\n    position: absolute;\r\n    top: calc(50%);\r\n    left: calc(43%) !important;\r\n    min-width: 300px;\r\n    padding-bottom: 20px;\r\n    height: 365px;\r\n\r\n    .mainCon {\r\n        padding-left: 50px;\r\n        max-height: 300px;\r\n        overflow-y: auto;\r\n        ;\r\n\r\n        .mainCon_item {\r\n            display: flex;\r\n            padding: 10px;\r\n\r\n            .item_title {\r\n                width: 15%;\r\n                color: #00C2FF;\r\n                text-align: right;\r\n            }\r\n\r\n            .item_con {\r\n                width: 80%;\r\n            }\r\n        }\r\n    }\r\n\r\n    ::v-deep .allBox {\r\n        .el-checkbox {\r\n            display: block;\r\n        }\r\n\r\n        .el-checkbox__label {\r\n            color: #fff;\r\n            line-height: 50px;\r\n            font-size: 20px;\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n            width: 25px;\r\n            height: 25px;\r\n        }\r\n\r\n        /* 对勾样式 */\r\n        .el-checkbox__inner::after {\r\n            left: 10px;\r\n            top: 5px;\r\n        }\r\n\r\n        .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n            transform: rotate(50deg) scaleY(1.8);\r\n        }\r\n    }\r\n\r\n    .pageSelect {\r\n        position: absolute;\r\n        right: 10px;\r\n        bottom: 0;\r\n        color: #fff;\r\n\r\n        ::v-deep .el-pagination__total {\r\n            color: #fff;\r\n        }\r\n\r\n        ::v-deep .el-pager li {\r\n            background: transparent !important;\r\n        }\r\n\r\n        ::v-deep .el-pagination {\r\n            color: #fff;\r\n        }\r\n\r\n        ::v-deep .el-pagination .btn-next,\r\n        ::v-deep .el-pagination .btn-prev {\r\n            background: transparent !important;\r\n            color: #fff;\r\n        }\r\n\r\n        ::v-deep .el-pagination--small .more::before,\r\n        ::v-deep .el-pagination--small li.more::before {\r\n            color: #fff !important;\r\n        }\r\n    }\r\n\r\n    ::v-deep .el-checkbox-group {\r\n\r\n        .el-checkbox__input.is-checked .el-checkbox__inner,\r\n        .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n            background-color: transparent;\r\n        }\r\n\r\n        .el-checkbox {\r\n            display: block;\r\n            margin-bottom: 10px;\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: transparent;\r\n        }\r\n\r\n        .el-checkbox__label {\r\n            font-size: 16px;\r\n\r\n            color: #fff;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAgCA,SAAAA,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,KAAA;MACAC,YAAA;MACAC,WAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,QAAA;MACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA,QAEA;MACA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;UACAE,SAAA;UACAC,IAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAvB,QAAA,YACA,cACA,gBACA,kBACA;IACA;IACA;IACA;IACAwB,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAH,YAAA;IACA;EACA;EACAI,KAAA;IACAC,UAAA;MACAC,QAAAC,MAAA;QACA,KAAAA,MAAA,CAAAC,cAAA;UACA;QAAA;MAEA;MACAC,SAAA;MACAC,IAAA;IACA;IACAC,YAAA;MACAL,QAAAM,EAAA;QACA,IAAAA,EAAA;UACA,KAAAjC,YAAA;UACA,KAAAC,KAAA;UACA,IAAAgC,EAAA,EAAAC,MAAA;YACA,KAAAjC,KAAA,GAAAgC,EAAA,CAAAlC,IAAA;YACA,KAAAS,QAAA,GAAAyB,EAAA,CAAAC,MAAA;UAEA;YACA,KAAAjC,KAAA,GAAAgC,EAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;QAGA;MACA;MACAH,SAAA;IACA;IACAT,YAAA;MACAM,QAAAM,EAAA;QACA,IAAAA,EAAA,QAAA/B,YAAA;UACA,KAAAA,YAAA,GAAA+B,EAAA;QAEA;MACA;MACAH,SAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAEA;EAEAK,OAAA;IACAC,qBAAAC,GAAA;MACA,KAAArC,YAAA,GAAAqC,GAAA,QAAApC,KAAA;MACA,KAAAK,eAAA;MACA,KAAAgC,WAAA,MAAAtC,YAAA,EAAAqC,GAAA;MACA,KAAAf,MAAA,CAAAiB,MAAA;IACA;IACAD,YAAAE,IAAA,EAAAC,IAAA;MACA,IAAAC,MAAA;QACA5C,IAAA;QACAC,IAAA,EAAAyC,IAAA;QACAC,IAAA,EAAAA,IAAA;QACAE,MAAA;MACA;MACAC,OAAA,CAAAC,GAAA,WAAAH,MAAA;MACA,KAAAI,SAAA,CAAAC,KAAA,eAAAL,MAAA;IACA;IACAM,UAAAX,GAAA;MACA,IAAAY,YAAA,GAAAZ,GAAA,CAAAa,MAAA;MACA,KAAA3C,QAAA,GAAA0C,YAAA,UAAAhD,KAAA,CAAAiD,MAAA;MACA,KAAA5C,eAAA,GAAA2C,YAAA,QAAAA,YAAA,QAAAhD,KAAA,CAAAiD,MAAA;MACA;IACA;IACAC,UAAAV,IAAA,EAAAJ,GAAA;MACA,KAAAC,WAAA,EAAAD,GAAA,CAAAe,MAAA,CAAAC,MAAA,GAAAZ,IAAA;MACA,IAAAA,IAAA;QACA,KAAAa,UAAA,CAAAjB,GAAA,CAAAe,MAAA,CAAAC,MAAA;MACA;QACA,IAAAhB,GAAA,CAAAe,MAAA,CAAAC,MAAA,CAAAE,WAAA,SAAApD,WAAA,CAAAoD,WAAA;UACA,KAAAjC,MAAA,CAAAiB,MAAA;QACA;MACA;IAEA;IACA;IACAe,WAAAjB,GAAA;MACA,KAAAlC,WAAA,GAAAkC,GAAA;MACA,KAAAmB,SAAA;MACA,KAAAlC,MAAA,CAAAiB,MAAA,mCAAApC,WAAA;IACA;IACA;IACAsD,UAAA;MACA,KAAAnC,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;IAEA;IACA;IACAiB,UAAA;MACA,KAAAlC,MAAA,CAAAiB,MAAA;MACA,KAAAjB,MAAA,CAAAiB,MAAA;IACA;IACA;IACAmB,oBAAArB,GAAA;MACA,IAAAK,MAAA;QACAjC,QAAA,EAAA4B,GAAA;QACA3B,SAAA;QACAiD,SAAA,OAAA3B,YAAA,CAAAjC,IAAA,IAAA4D;MACA;MACA,KAAArC,MAAA,CAAAsC,QAAA,2BAAAlB,MAAA;IACA;EAEA;EACAmB,QAAA;IACA;EAAA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}