{"ast": null, "code": "export default {\n  name: \"RotationData\",\n  data() {\n    return {\n      config: {\n        header: [],\n        //表头数据\n        data: [],\n        index: false,\n        //显示行号\n        columnWidth: [210, 210, 230, 270, 290, 200],\n        //列宽度 \n        evenRowBGC: \" rgba(46,101,167,0)\",\n        //偶数行背景色\n        rowNum: 5,\n        //表行数\n        fontSize: 25,\n        //表行数\n        align: [\"center\", \"center\", \"center\", \"center\", \"center\", \"center\"],\n        //列对齐方式\n        waitTime: 2000\n      },\n      changeRow: null,\n      timer: null\n    };\n  },\n  props: {\n    data: {\n      type: Array,\n      default: () => {\n        return null;\n      }\n    },\n    header: {\n      type: Array,\n      default: () => {\n        return null;\n      }\n    },\n    styleAll: {\n      type: Object,\n      default: () => {\n        return {\n          width: \"100%\",\n          height: \"100%\"\n        };\n      }\n    },\n    SelectList: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    },\n    headerBGC: {\n      //表头背景色\n      type: String,\n      default: \"rgba(46,101,167,0.5)\"\n    },\n    oddRowBGC: {\n      //奇数行背景色\n      type: String,\n      default: \" rgba(46,101,167,0)\"\n    }\n  },\n  watch: {\n    data: {\n      handler(val) {\n        this.$set(this.config, \"data\", val);\n        this.config = {\n          ...this.config,\n          headerBGC: this.headerBGC,\n          oddRowBGC: this.oddRowBGC\n        };\n        this.changeChildOne({\n          id: 0\n        });\n      },\n      immediate: true\n    },\n    SelectList(nv) {\n      if (nv?.id) {\n        this.changeChildOne(nv);\n      }\n    },\n    header: {\n      handler(val) {\n        this.$set(this.config, \"header\", val);\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {});\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n      this.timer = null;\n    }\n  },\n  methods: {\n    clickHandler(row) {\n      this.$emit(\"sendInfo_Option\", row);\n\n      // 获取当前数据的序号\n      this.changeRow = row;\n      // this.changeChildOne()\n    },\n    // 点击表格行数据， 改变背景颜色\n    changeChildOne(row) {\n      let ele = document.querySelector(\".board>.rows\");\n      if (!ele) {\n        return;\n      } else {\n        let child = ele?.children;\n        for (let i = 0; i < child.length; i++) {\n          // 获取字符串中所有数字\n          let str = child[i].innerText.match(/\\d+/g);\n          if (str.length) {\n            let id = str[0];\n            if (id == row.id) {\n              child[i].style.background = \"rgba(31, 55, 90, 0.45)\";\n              this.loopData();\n            } else {\n              child[i].style.background = \"rgba(31, 55, 90, 0)\";\n              if (this.timer) {\n                clearInterval(this.timer);\n                this.timer = null;\n              }\n            }\n          }\n        }\n      }\n    },\n    loopData() {\n      let length = this.data.length;\n      let time = length / 2 * this.config.waitTime;\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n      this.timer = setInterval(() => {\n        this.changeChildOne(this.SelectList);\n      }, time);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "config", "header", "index", "columnWidth", "evenRowBGC", "row<PERSON>um", "fontSize", "align", "waitTime", "changeRow", "timer", "props", "type", "Array", "default", "styleAll", "Object", "width", "height", "SelectList", "headerBGC", "String", "oddRowBGC", "watch", "handler", "val", "$set", "changeChildOne", "id", "immediate", "nv", "mounted", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "clickHandler", "row", "$emit", "ele", "document", "querySelector", "child", "children", "i", "length", "str", "innerText", "match", "style", "background", "loopData", "time", "setInterval"], "sources": ["src/components/RotationData/RotationData.vue"], "sourcesContent": ["<template>\r\n    <div class=\"RotationData\" :style=\"styleAll\">\r\n        <dv-scroll-board class=\"board\" :config=\"config\" @click=\"clickHandler\" />\r\n    </div>\r\n</template>\r\n<script>\r\nexport default {\r\n    name: \"RotationData\",\r\n\r\n    data() {\r\n        return {\r\n            config: {\r\n                header: [], //表头数据\r\n                data: [],\r\n                index: false, //显示行号\r\n                columnWidth: [210, 210, 230, 270, 290, 200], //列宽度 \r\n                evenRowBGC: \" rgba(46,101,167,0)\", //偶数行背景色\r\n                rowNum: 5, //表行数\r\n                fontSize: 25, //表行数\r\n                align: [\"center\", \"center\", \"center\", \"center\", \"center\", \"center\"], //列对齐方式\r\n                waitTime: 2000,\r\n\r\n            },\r\n            changeRow: null,\r\n            timer: null,\r\n        };\r\n    },\r\n    props: {\r\n        data: {\r\n            type: Array,\r\n            default: () => {\r\n                return null;\r\n            }\r\n        },\r\n        header: {\r\n            type: Array,\r\n            default: () => {\r\n                return null\r\n            }\r\n        },\r\n        styleAll: {\r\n            type: Object,\r\n            default: () => {\r\n                return {\r\n                    width: \"100%\",\r\n                    height: \"100%\"\r\n                }\r\n            }\r\n        },\r\n        SelectList: {\r\n            type: Object,\r\n            default: () => {\r\n                return {}\r\n            }\r\n        },\r\n        headerBGC: {//表头背景色\r\n            type: String,\r\n            default: \"rgba(46,101,167,0.5)\"\r\n        },\r\n        oddRowBGC: { //奇数行背景色\r\n            type: String,\r\n            default: \" rgba(46,101,167,0)\",\r\n        }\r\n\r\n    },\r\n    watch: {\r\n        data: {\r\n            handler(val) {\r\n                this.$set(this.config, \"data\", val);\r\n                this.config = { ...this.config, headerBGC: this.headerBGC, oddRowBGC: this.oddRowBGC }\r\n                this.changeChildOne({ id: 0 })\r\n            },\r\n            immediate: true,\r\n        },\r\n        SelectList(nv) {\r\n            if (nv?.id) {\r\n                this.changeChildOne(nv)\r\n            }\r\n        },\r\n        header: {\r\n            handler(val) {\r\n                this.$set(this.config, \"header\", val);\r\n\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n        });\r\n    },\r\n    beforeDestroy() {\r\n        if (this.timer) {\r\n            clearInterval(this.timer)\r\n            this.timer = null\r\n        }\r\n    },\r\n\r\n    methods: {\r\n       \r\n        clickHandler(row) {\r\n            this.$emit(\"sendInfo_Option\", row)\r\n\r\n            // 获取当前数据的序号\r\n            this.changeRow = row\r\n            // this.changeChildOne()\r\n\r\n        },\r\n        // 点击表格行数据， 改变背景颜色\r\n        changeChildOne(row) {\r\n            let ele = document.querySelector(\".board>.rows\");\r\n            if (!ele) {\r\n                return;\r\n            } else {\r\n                let child = ele?.children;\r\n                for (let i = 0; i < child.length; i++) {\r\n                    // 获取字符串中所有数字\r\n                    let str = child[i].innerText.match(/\\d+/g);\r\n                    if (str.length) {\r\n                        let id = str[0];\r\n                        if (id == row.id) {\r\n                            child[i].style.background = \"rgba(31, 55, 90, 0.45)\";\r\n                            this.loopData()\r\n\r\n                        } else {\r\n                            child[i].style.background = \"rgba(31, 55, 90, 0)\";\r\n                            if (this.timer) {\r\n                                clearInterval(this.timer)\r\n                                this.timer = null\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                }\r\n            }\r\n\r\n\r\n        },\r\n        loopData() {\r\n\r\n            let length = this.data.length\r\n            let time = length / 2 * this.config.waitTime;\r\n            if (this.timer) {\r\n                clearInterval(this.timer)\r\n                this.timer = null\r\n            }\r\n\r\n            this.timer = setInterval(() => {\r\n                this.changeChildOne(this.SelectList)\r\n            }, time);\r\n        }\r\n\r\n    },\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n::v-deep .header {\r\n    color: #fff;\r\n}\r\n\r\n::v-deep .row-item {\r\n    color: #eee;\r\n}\r\n\r\n::v-deep .dv-scroll-board .rows .index {\r\n    cursor: pointer;\r\n    background-color: transparent !important;\r\n}\r\n\r\n// 表头元素设置\r\n::v-deep .header-item {\r\n    padding: 0.6rem 0;\r\n    font-size: 1.5rem !important;\r\n\r\n}\r\n\r\n::v-deep .dv-scroll-board .header {\r\n    padding: 10px 0;\r\n}\r\n\r\n::v-deep .dv-scroll-board .rows .row-item {\r\n    // padding: 0.6rem 0;\r\n    font-size: 24px !important;\r\n    cursor: pointer;\r\n\r\n}\r\n</style>"], "mappings": "AAMA;EACAA,IAAA;EAEAC,KAAA;IACA;MACAC,MAAA;QACAC,MAAA;QAAA;QACAF,IAAA;QACAG,KAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;QAAA;QACAC,MAAA;QAAA;QACAC,QAAA;QAAA;QACAC,KAAA;QAAA;QACAC,QAAA;MAEA;MACAC,SAAA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAZ,IAAA;MACAa,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;QACA;MACA;IACA;IACAb,MAAA;MACAW,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;QACA;MACA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;QACA;UACAG,KAAA;UACAC,MAAA;QACA;MACA;IACA;IACAC,UAAA;MACAP,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;QACA;MACA;IACA;IACAM,SAAA;MAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;IACAQ,SAAA;MAAA;MACAV,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;EAEA;EACAS,KAAA;IACAxB,IAAA;MACAyB,QAAAC,GAAA;QACA,KAAAC,IAAA,MAAA1B,MAAA,UAAAyB,GAAA;QACA,KAAAzB,MAAA;UAAA,QAAAA,MAAA;UAAAoB,SAAA,OAAAA,SAAA;UAAAE,SAAA,OAAAA;QAAA;QACA,KAAAK,cAAA;UAAAC,EAAA;QAAA;MACA;MACAC,SAAA;IACA;IACAV,WAAAW,EAAA;MACA,IAAAA,EAAA,EAAAF,EAAA;QACA,KAAAD,cAAA,CAAAG,EAAA;MACA;IACA;IACA7B,MAAA;MACAuB,QAAAC,GAAA;QACA,KAAAC,IAAA,MAAA1B,MAAA,YAAAyB,GAAA;MAEA;MACAI,SAAA;IACA;EACA;EACAE,QAAA;IACA,KAAAC,SAAA,QACA;EACA;EACAC,cAAA;IACA,SAAAvB,KAAA;MACAwB,aAAA,MAAAxB,KAAA;MACA,KAAAA,KAAA;IACA;EACA;EAEAyB,OAAA;IAEAC,aAAAC,GAAA;MACA,KAAAC,KAAA,oBAAAD,GAAA;;MAEA;MACA,KAAA5B,SAAA,GAAA4B,GAAA;MACA;IAEA;IACA;IACAV,eAAAU,GAAA;MACA,IAAAE,GAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,KAAAF,GAAA;QACA;MACA;QACA,IAAAG,KAAA,GAAAH,GAAA,EAAAI,QAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,KAAA,CAAAG,MAAA,EAAAD,CAAA;UACA;UACA,IAAAE,GAAA,GAAAJ,KAAA,CAAAE,CAAA,EAAAG,SAAA,CAAAC,KAAA;UACA,IAAAF,GAAA,CAAAD,MAAA;YACA,IAAAjB,EAAA,GAAAkB,GAAA;YACA,IAAAlB,EAAA,IAAAS,GAAA,CAAAT,EAAA;cACAc,KAAA,CAAAE,CAAA,EAAAK,KAAA,CAAAC,UAAA;cACA,KAAAC,QAAA;YAEA;cACAT,KAAA,CAAAE,CAAA,EAAAK,KAAA,CAAAC,UAAA;cACA,SAAAxC,KAAA;gBACAwB,aAAA,MAAAxB,KAAA;gBACA,KAAAA,KAAA;cACA;YACA;UACA;QAEA;MACA;IAGA;IACAyC,SAAA;MAEA,IAAAN,MAAA,QAAA9C,IAAA,CAAA8C,MAAA;MACA,IAAAO,IAAA,GAAAP,MAAA,YAAA7C,MAAA,CAAAQ,QAAA;MACA,SAAAE,KAAA;QACAwB,aAAA,MAAAxB,KAAA;QACA,KAAAA,KAAA;MACA;MAEA,KAAAA,KAAA,GAAA2C,WAAA;QACA,KAAA1B,cAAA,MAAAR,UAAA;MACA,GAAAiC,IAAA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}