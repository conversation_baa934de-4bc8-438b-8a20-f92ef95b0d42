{"ast": null, "code": "import { mapState } from 'vuex';\nimport { getImgUrl } from '@/api/hzVideo.js';\nimport { number } from 'echarts';\nexport default {\n  data() {\n    return {\n      eventType: {},\n      imgUrl: '',\n      rightpx: '50px'\n    };\n  },\n  watch: {\n    processFlowInfo: {\n      handler(nv) {\n        // console.log('11',nv)\n        // 预警推送图片地址  \n        if (nv?.url.length) {\n          if (nv.url.substring(0, 9) != '/file/mg/') {\n            getImgUrl([nv.url]).then(res => {\n              if (res.status == 200) {\n                this.imgUrl = res.data.data[0];\n              }\n            }).catch(e => {\n              // console.log(e, '=====')\n            });\n          } else {\n            this.imgUrl = 'http://2.36.239.130:11125/' + nv.url;\n          }\n        }\n        this.eventType = nv;\n      },\n      immediate: true\n    },\n    eventRight: {\n      handler(nv) {\n        this.rightpx = nv;\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState(\"action\", [\"processFlowInfo\"]),\n    ...mapState(\"action\", [\"eventRight\"])\n  },\n  mounted() {},\n  methods: {\n    close() {\n      this.$store.commit('action/getEventType', false);\n      // this.$store.commit('action/getIsScreenShow', true)\n    },\n    nearResource() {\n      let params = {\n        latitude: this.eventType.lat84,\n        longitude: this.eventType.lng84\n      };\n      this.$store.dispatch('action/getNeighbourVideoList', params);\n      this.$store.commit('action/getEventSubBoxFlag', false);\n      this.$store.commit('action/getInformationBox', true);\n      this.$store.commit(\"action/getEventType\", true);\n    },\n    people() {\n      if (this.eventType?.formType == '人员聚集') {\n        let params = {\n          name: '人群聚集',\n          jd: this.eventType.lng84,\n          wd: this.eventType.lat84,\n          number: 4\n        };\n        this.$eventBus.$emit('senTxtToUe', params);\n        console.log(params);\n      }\n    },\n    person() {\n      if (this.eventType?.formType == '区域入侵') {\n        let params = {\n          name: '人员闯入',\n          jd: this.eventType.lng84,\n          wd: this.eventType.lat84,\n          number: 4\n        };\n        this.$eventBus.$emit('senTxtToUe', params);\n        console.log(params);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "getImgUrl", "number", "data", "eventType", "imgUrl", "rightpx", "watch", "processFlowInfo", "handler", "nv", "url", "length", "substring", "then", "res", "status", "catch", "e", "immediate", "eventRight", "computed", "mounted", "methods", "close", "$store", "commit", "nearResource", "params", "latitude", "lat84", "longitude", "lng84", "dispatch", "people", "formType", "name", "jd", "wd", "$eventBus", "$emit", "console", "log", "person"], "sources": ["src/views/dataScreen/fileType/eventListProcessFlow/eventType.vue"], "sourcesContent": ["<template>\r\n    <div class=\"eventType\" :style=\"{ right: rightpx }\">\r\n        <div class=\"delete\" @click=\"close\">\r\n            <i class=\"el-icon-close\" :style=\"{ fontSize: '40px' }\"></i>\r\n        </div>\r\n        <div class=\"event_Type_info\">\r\n            <ul>\r\n                <li>\r\n                    <span class=\"leftTit\">事件名称</span>\r\n                    <span class=\"rightTxt\">{{ eventType.firstName }}</span>\r\n                </li>\r\n                <li>\r\n                    <span class=\"leftTit\">事件来源</span>\r\n                    <span class=\"rightTxt\">{{ eventType.orderSource }}</span>\r\n                </li>\r\n                <li>\r\n                    <span class=\"leftTit\">发生时间</span>\r\n                    <span class=\"rightTxt\">{{ eventType.waitingTime }}</span>\r\n                </li>\r\n                <li>\r\n                    <span class=\"leftTit\" @click=\"nearResource\">周边资源</span>\r\n                </li>\r\n            </ul>\r\n        </div>\r\n        <div class=\"event_deal_process_flow\">\r\n            <div class=\"title\">\r\n                <div class=\"txt\">事情处置</div>\r\n            </div>\r\n\r\n            <div class=\"process fz1\">\r\n                <el-timeline>\r\n                    <el-timeline-item v-if=\"eventType?.title.length\">\r\n                        <div class=\"titleTimeLine\">\r\n                            事件标题\r\n                        </div>\r\n                        <div class=\"icon bigIcon\">\r\n                            <img src=\"@/assets/images/event/stratCircle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n                        <!-- <div class=\"topTime\">\r\n                            <div class=\"left disFlex alignItemsCenter\"> \r\n                                <div class=\"timer\">{{eventType.title}}</div>\r\n                            </div>\r\n                            \r\n                        </div> -->\r\n                        <div class=\"card fz1\">\r\n                            <p>{{ eventType.title }}</p>\r\n\r\n                        </div>\r\n\r\n                    </el-timeline-item>\r\n                    <el-timeline-item>\r\n                        <div class=\"titleTimeLine\">\r\n                            事件描述\r\n                        </div>\r\n                        <div class=\"icon context\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card\">\r\n                            <div class=\"dirInfo \" style=\"display: flex;\">\r\n                                <div class=\"time info\">{{ eventType.contentText }}</div>\r\n                                <button v-if=\"eventType.contentText == '人员聚集'\" @click=\"people\">人员聚集展示</button>\r\n                                <button v-if=\"eventType.contentText == '区域入侵'\" @click=\"person\">人员闯入展示</button>\r\n                            </div>\r\n                        </div>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item v-if=\"eventType?.finalDept.length\">\r\n                        <div class=\"titleTimeLine\">\r\n                            处理部门\r\n                        </div>\r\n                        <div class=\"icon\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card\">\r\n\r\n                            <div class=\"dirInfo\">\r\n                                <div class=\"info fz1\">{{ eventType.finalDept }}</div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item>\r\n                        <div class=\"titleTimeLine\">\r\n                            事件地址\r\n                        </div>\r\n                        <div class=\"icon\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card\">\r\n\r\n                            <p class=\"time\">{{ eventType.appealAddress }}</p>\r\n\r\n                        </div>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item>\r\n                        <div class=\"titleTimeLine\">\r\n                            所属社区\r\n                        </div>\r\n                        <div class=\"icon\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card\" v-if=\"eventType.url\">\r\n                            <!-- <img :src=\"imgUrl\" alt=\"\" width=\"100%\"> -->\r\n                            <el-image style=\"width: 100%\" :src=\"imgUrl\" :preview-src-list=\"[imgUrl]\">\r\n                            </el-image>\r\n                        </div>\r\n                        <div class=\"card\" v-else>\r\n                            <div class=\"dirInfo disFlex\">\r\n                                <div class=\"name\">所属社区：</div>\r\n                                <div class=\"context\">{{ eventType.appealCommunityCity }}</div>\r\n                            </div>\r\n                            <div class=\"dirInfo disFlex\">\r\n                                <div class=\"name\">所属小区：</div>\r\n                                <div class=\"context\">{{ eventType.appealRegionCity }}</div>\r\n                            </div>\r\n                        </div>\r\n                    </el-timeline-item>\r\n                    <el-timeline-item v-if=\"eventType?.isOverdue.length\">\r\n                        <div class=\"titleTimeLine\">\r\n                            当前流程\r\n                        </div>\r\n                        <div class=\"icon\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card\">\r\n\r\n                            <div class=\"dirInfo disFlex\">\r\n                                <div class=\"info fz1\">{{ eventType.isOverdue }}</div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </el-timeline-item>\r\n\r\n                    <el-timeline-item v-if=\"eventType?.orderStatus.length\">\r\n                        <div class=\"titleTimeLine\">\r\n                            处理结果\r\n                        </div>\r\n                        <div class=\"icon\">\r\n                            <img src=\"@/assets/images/event/circle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card fz1 colorRed\">\r\n\r\n                            <p>{{ eventType.orderStatus }}</p>\r\n                        </div>\r\n                    </el-timeline-item>\r\n\r\n                    <el-timeline-item>\r\n                        <div class=\"titleTimeLine\">\r\n                            当前状态\r\n                        </div>\r\n                        <div class=\"icon bigIcon\">\r\n                            <img src=\"@/assets/images/event/bigCircle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n\r\n                        <div class=\"card fz1 colorRed\">\r\n\r\n                            <p>事件关闭</p>\r\n                        </div>\r\n                    </el-timeline-item>\r\n\r\n                    <!-- <el-timeline-item>\r\n                        <div class=\"icon bigIcon\">\r\n                            <img src=\"@/assets/images/event/bigCircle.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n                        <div class=\"topTime\">\r\n                            <div class=\"left disFlex\">\r\n\r\n                                <div class=\"timer colorFFF fz1\"> 时间关闭</div>\r\n                            </div>\r\n\r\n                        </div>\r\n\r\n                    </el-timeline-item> -->\r\n\r\n                </el-timeline>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { getImgUrl } from '@/api/hzVideo.js'\r\nimport { number } from 'echarts';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            eventType: {\r\n            },\r\n            imgUrl: '',\r\n            rightpx: '50px'\r\n        }\r\n    },\r\n    watch: {\r\n        processFlowInfo: {\r\n            handler(nv) {\r\n                // console.log('11',nv)\r\n                // 预警推送图片地址  \r\n                if (nv?.url.length) {\r\n                    if (nv.url.substring(0, 9) != '/file/mg/') {\r\n                        getImgUrl([nv.url]).then(res => {\r\n                            if (res.status == 200) {\r\n                                this.imgUrl = res.data.data[0]\r\n                            }\r\n                        }).catch(e => {\r\n                            // console.log(e, '=====')\r\n                        })\r\n                    } else {\r\n                        this.imgUrl = 'http://2.36.239.130:11125/' + nv.url\r\n                    }\r\n                }\r\n                this.eventType = nv\r\n\r\n            },\r\n            immediate: true\r\n        },\r\n        eventRight: {\r\n            handler(nv) {\r\n                this.rightpx = nv;\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n\r\n    computed: {\r\n        ...mapState(\"action\", [\"processFlowInfo\"]),\r\n        ...mapState(\"action\", [\"eventRight\"])\r\n    },\r\n\r\n    mounted() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        close() {\r\n\r\n            this.$store.commit('action/getEventType', false)\r\n            // this.$store.commit('action/getIsScreenShow', true)\r\n        },\r\n        nearResource() {\r\n            let params = {\r\n                latitude: this.eventType.lat84,\r\n                longitude: this.eventType.lng84\r\n            }\r\n            this.$store.dispatch('action/getNeighbourVideoList', params)\r\n            this.$store.commit('action/getEventSubBoxFlag', false)\r\n            this.$store.commit('action/getInformationBox', true)\r\n            this.$store.commit(\"action/getEventType\", true)\r\n        },\r\n        people() {\r\n            if (this.eventType?.formType == '人员聚集') {\r\n                let params = {\r\n                    name: '人群聚集',\r\n                    jd: this.eventType.lng84,\r\n                    wd: this.eventType.lat84,\r\n                    number: 4\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params)\r\n                console.log(params)\r\n            }\r\n        },\r\n        person() {\r\n            if (this.eventType?.formType == '区域入侵') {\r\n                let params = {\r\n                    name: '人员闯入',\r\n                    jd: this.eventType.lng84,\r\n                    wd: this.eventType.lat84,\r\n                    number: 4\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params)\r\n                console.log(params)\r\n            }\r\n        },\r\n    },\r\n\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.fz1 {\r\n    font-size: 1.7rem;\r\n}\r\n\r\n.colorRed {\r\n    font-weight: bold;\r\n}\r\n\r\n.eventType {\r\n    pointer-events: stroke;\r\n    z-index: 1;\r\n    color: #fff;\r\n    position: absolute;\r\n\r\n    top: calc(10rem);\r\n    font-size: 1.3rem;\r\n\r\n    .delete {\r\n        position: absolute;\r\n        right: 40px;\r\n        top: 40px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n        color: #fff;\r\n\r\n    }\r\n\r\n    .event_Type_info {\r\n        width: calc(45rem);\r\n        height: fit-content;\r\n        background: url(\"@/assets/images/event/bgImage.png\") no-repeat center 100%;\r\n        background-size: 100% 100%;\r\n        padding: 1.1rem 3rem;\r\n        font-size: 1.7rem;\r\n        box-sizing: border-box;\r\n\r\n        ul {\r\n\r\n            li {\r\n                display: flex;\r\n                height: fit-content;\r\n                margin: 1rem 0;\r\n                line-height: 2.5rem;\r\n\r\n                .leftTit {\r\n                    width: calc(8rem);\r\n                    display: inline-block;\r\n                    box-sizing: border-box;\r\n\r\n                }\r\n\r\n                .rightTxt {\r\n                    box-sizing: border-box;\r\n                    width: calc(23rem);\r\n                    display: inline-block;\r\n                    text-wrap: wrap;\r\n\r\n                }\r\n\r\n                &:last-child {\r\n                    text-align: right;\r\n                    color: red;\r\n                    cursor: pointer;\r\n                    font-weight: bold;\r\n                    font-size: 2rem;\r\n                    user-select: none;\r\n\r\n                    &:active {\r\n                        color: rgba(255, 0, 0, .5)\r\n                    }\r\n                }\r\n            }\r\n\r\n        }\r\n    }\r\n\r\n    .event_deal_process_flow {\r\n        margin-top: 1rem;\r\n        width: 100%;\r\n\r\n        .context {\r\n            max-height: 4rem;\r\n            overflow-y: auto;\r\n            // font-size: .75rem;\r\n        }\r\n\r\n        .title {\r\n            font-size: 2.5rem;\r\n            background: url(\"@/assets/images/event/titleBgImg.png\") no-repeat center 100%;\r\n            background-size: 100% 100%;\r\n            padding: 1rem 0 1rem 3rem;\r\n            box-sizing: border-box;\r\n            margin-bottom: 3rem;\r\n\r\n            .txt {\r\n                font-family: Microsoft YaHei, Microsoft YaHei;\r\n                font-weight: 700;\r\n                background-image: -webkit-linear-gradient(90.00001393970153deg, #EBF3FF 0%, #A0E0FF 100%);\r\n                -webkit-background-clip: text;\r\n                -webkit-text-fill-color: transparent;\r\n            }\r\n        }\r\n\r\n        .process {\r\n            max-height: 122vh;\r\n            padding-top: 1.3rem;\r\n\r\n            ::v-deep .el-timeline {\r\n                padding-left: 15rem;\r\n\r\n                .el-timeline-item {\r\n                    position: relative;\r\n                }\r\n\r\n                .el-timeline-item__content {\r\n                    width: 28rem;\r\n                }\r\n            }\r\n\r\n            .titleTimeLine {\r\n                position: absolute;\r\n                left: -13rem;\r\n                top: -1rem;\r\n                font-size: 1.7rem;\r\n                color: #fff;\r\n                padding: 1rem 2rem 1rem 1rem;\r\n                background: url(\"@/assets/images/event/eventTitBg.png\") no-repeat center 100%;\r\n                background-size: 100% 100%;\r\n            }\r\n\r\n            .icon {\r\n                width: 2rem;\r\n                height: 2rem;\r\n                position: relative;\r\n                left: -2.5rem;\r\n            }\r\n\r\n            .bigIcon {\r\n                width: 3rem;\r\n                left: -2.8rem;\r\n\r\n            }\r\n\r\n            .card {\r\n                width: 100%;\r\n                box-sizing: border-box;\r\n                color: #fff;\r\n                background: rgba(0, 82, 129, 0.35);\r\n                box-shadow: inset 0px 1px 11px 3px rgba(0, 163, 255, 0.5);\r\n                border-radius: 3px 3px 3px 3px;\r\n                border: 1px solid;\r\n                border-image: linear-gradient(180deg, rgba(111, 137, 147, 1), rgba(92, 139, 152, 1)) 1 1;\r\n                padding: 1.2rem;\r\n                line-height: 2.5rem;\r\n                text-wrap: wrap;\r\n                margin: -3rem 0 3.5rem 2rem;\r\n                font-size: 1.7rem;\r\n\r\n                .info {\r\n                    width: 100%;\r\n                    max-height: 12rem;\r\n                    overflow-y: auto;\r\n\r\n                }\r\n\r\n                .dirInfo {\r\n                    .name {\r\n                        width: 11.5rem;\r\n                        font-size: 1.5rem;\r\n\r\n                    }\r\n\r\n                    .context {\r\n                        width: calc(100% - 4.5rem);\r\n                    }\r\n\r\n                    button {\r\n                        width: 280px;\r\n                        height: 60px;\r\n                        color: #fff;\r\n                        font-size: 20px;\r\n                        background-color: rgba(44, 55, 88, 0.7);\r\n                    }\r\n                }\r\n            }\r\n\r\n            .topTime {\r\n                margin-top: -2rem;\r\n                color: #fff;\r\n                padding-left: 2rem\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAyLA,SAAAA,QAAA;AACA,SAAAC,SAAA;AACA,SAAAC,MAAA;AAEA;EACAC,KAAA;IACA;MACAC,SAAA,GACA;MACAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACAC,eAAA;MACAC,QAAAC,EAAA;QACA;QACA;QACA,IAAAA,EAAA,EAAAC,GAAA,CAAAC,MAAA;UACA,IAAAF,EAAA,CAAAC,GAAA,CAAAE,SAAA;YACAZ,SAAA,EAAAS,EAAA,CAAAC,GAAA,GAAAG,IAAA,CAAAC,GAAA;cACA,IAAAA,GAAA,CAAAC,MAAA;gBACA,KAAAX,MAAA,GAAAU,GAAA,CAAAZ,IAAA,CAAAA,IAAA;cACA;YACA,GAAAc,KAAA,CAAAC,CAAA;cACA;YAAA,CACA;UACA;YACA,KAAAb,MAAA,kCAAAK,EAAA,CAAAC,GAAA;UACA;QACA;QACA,KAAAP,SAAA,GAAAM,EAAA;MAEA;MACAS,SAAA;IACA;IACAC,UAAA;MACAX,QAAAC,EAAA;QACA,KAAAJ,OAAA,GAAAI,EAAA;MACA;MACAS,SAAA;IACA;EACA;EAEAE,QAAA;IACA,GAAArB,QAAA;IACA,GAAAA,QAAA;EACA;EAEAsB,QAAA,GAEA;EAEAC,OAAA;IACAC,MAAA;MAEA,KAAAC,MAAA,CAAAC,MAAA;MACA;IACA;IACAC,aAAA;MACA,IAAAC,MAAA;QACAC,QAAA,OAAAzB,SAAA,CAAA0B,KAAA;QACAC,SAAA,OAAA3B,SAAA,CAAA4B;MACA;MACA,KAAAP,MAAA,CAAAQ,QAAA,iCAAAL,MAAA;MACA,KAAAH,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;IACA;IACAQ,OAAA;MACA,SAAA9B,SAAA,EAAA+B,QAAA;QACA,IAAAP,MAAA;UACAQ,IAAA;UACAC,EAAA,OAAAjC,SAAA,CAAA4B,KAAA;UACAM,EAAA,OAAAlC,SAAA,CAAA0B,KAAA;UACA5B,MAAA;QACA;QACA,KAAAqC,SAAA,CAAAC,KAAA,eAAAZ,MAAA;QACAa,OAAA,CAAAC,GAAA,CAAAd,MAAA;MACA;IACA;IACAe,OAAA;MACA,SAAAvC,SAAA,EAAA+B,QAAA;QACA,IAAAP,MAAA;UACAQ,IAAA;UACAC,EAAA,OAAAjC,SAAA,CAAA4B,KAAA;UACAM,EAAA,OAAAlC,SAAA,CAAA0B,KAAA;UACA5B,MAAA;QACA;QACA,KAAAqC,SAAA,CAAAC,KAAA,eAAAZ,MAAA;QACAa,OAAA,CAAAC,GAAA,CAAAd,MAAA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}