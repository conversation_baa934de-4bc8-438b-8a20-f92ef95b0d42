{"ast": null, "code": "import request from '@/utils/api/getApi.js';\n\n// 查询视频订阅\nexport function getDispatchDyList() {\n  return request.post('/za/getDispatchDyList');\n}\n// 分页查询51视频\nexport function getDispatchList() {\n  return request.post('/za/getDispatchList', {\n    \"pageNo\": 1,\n    \"pageSize\": 100,\n    \"cameraCode\": \"\"\n  });\n}\n// 查询视频流\nexport function getVideoUrlByCode(cameraCode) {\n  return request.post('/za/getDispatchUrl', {\n    cameraCode: cameraCode\n  });\n}\n// 视频保活\nexport function keepLive(cameraCode, sessionId) {\n  return request.post('/za/getDispatchBh', {\n    cameraCode: cameraCode,\n    sessionId: sessionId\n  });\n}", "map": {"version": 3, "names": ["request", "getDispatchDyList", "post", "getDispatchList", "getVideoUrlByCode", "cameraCode", "keepLive", "sessionId"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/video.js"], "sourcesContent": ["import request from '@/utils/api/getApi.js'\r\n\r\n// 查询视频订阅\r\nexport function getDispatchDyList() {\r\n   return request.post('/za/getDispatchDyList')\r\n}\r\n// 分页查询51视频\r\nexport function getDispatchList() {\r\n   return request.post('/za/getDispatchList', {\r\n      \"pageNo\": 1,\r\n      \"pageSize\": 100,\r\n      \"cameraCode\": \"\"\r\n   })\r\n}\r\n// 查询视频流\r\nexport function getVideoUrlByCode(cameraCode) {\r\n   return request.post('/za/getDispatchUrl', {\r\n      cameraCode: cameraCode\r\n   })\r\n}\r\n// 视频保活\r\nexport function keepLive(cameraCode, sessionId) {\r\n   return request.post('/za/getDispatchBh', {\r\n      cameraCode: cameraCode,\r\n      sessionId: sessionId\r\n   })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;;AAE3C;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EACjC,OAAOD,OAAO,CAACE,IAAI,CAAC,uBAAuB,CAAC;AAC/C;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAC/B,OAAOH,OAAO,CAACE,IAAI,CAAC,qBAAqB,EAAE;IACxC,QAAQ,EAAE,CAAC;IACX,UAAU,EAAE,GAAG;IACf,YAAY,EAAE;EACjB,CAAC,CAAC;AACL;AACA;AACA,OAAO,SAASE,iBAAiBA,CAACC,UAAU,EAAE;EAC3C,OAAOL,OAAO,CAACE,IAAI,CAAC,oBAAoB,EAAE;IACvCG,UAAU,EAAEA;EACf,CAAC,CAAC;AACL;AACA;AACA,OAAO,SAASC,QAAQA,CAACD,UAAU,EAAEE,SAAS,EAAE;EAC7C,OAAOP,OAAO,CAACE,IAAI,CAAC,mBAAmB,EAAE;IACtCG,UAAU,EAAEA,UAAU;IACtBE,SAAS,EAAEA;EACd,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}