{"ast": null, "code": "import { mapState } from 'vuex';\nimport { getHistoryAccumulation, getHistoryRiverQuality, getHistoryRiverLevelMonitoring } from '@/api/index.js';\nimport { getVideoOneToken } from '@/api/waterVideo.js';\nexport default {\n  name: 'warningHistory',\n  data() {\n    return {\n      tableData: [],\n      videoToken: ''\n    };\n  },\n  computed: {\n    ...mapState({\n      historyEventList: state => state.warningType.historyEventList,\n      currentEvent: state => state.warningType.currentEvent,\n      currentUrl: state => state.warningType.currentUrl\n    }),\n    ...mapState(['dataChannelText', 'larksr'])\n  },\n  mounted() {\n    this.initPlayer();\n  },\n  watch: {\n    dataChannelText(nv) {\n      // console.log('11',nv)\n      switch (nv.typename) {\n        case '积水预警事件':\n          let params = {\n            pageSize: 10,\n            pageNum: 1,\n            response_person_name: nv.object.responsePersonName\n          };\n          getHistoryAccumulation(params).then(res => {\n            // console.log('积水---', res)\n            this.$store.commit('warningType/getCurrentEvent', nv.typename);\n            this.$store.commit('warningType/getHistoryEventList', res.data.extra.data);\n            this.$store.commit('action/getIsScreenShow', false);\n            this.$store.commit('warningType/getIsShowHistoryEventList', true);\n          });\n          break;\n        case '河道液位预警':\n          let params2 = {\n            pageSize: 10,\n            pageNum: 1,\n            number: nv.object.number\n          };\n          getHistoryRiverQuality(params2).then(res => {\n            // console.log('河道---', res)\n            this.$store.commit('warningType/getCurrentEvent', nv.typename);\n            this.$store.commit('warningType/getHistoryEventList', res.data.extra.data);\n            this.$store.commit('action/getIsScreenShow', false);\n            this.$store.commit('warningType/getIsShowHistoryEventList', true);\n          });\n          break;\n        case '一体化智能杆':\n          this.$store.commit('warningType/getCurrentEvent', nv.typename);\n          this.$store.commit('warningType/getCurrentUrl', nv.url);\n          this.$store.commit('action/getIsScreenShow', false);\n          this.$store.commit('warningType/getIsShowAccumulationArr', false);\n          this.$store.commit('warningType/getIsShowHistoryEventList', true);\n          this.initPlayer();\n          break;\n        default:\n          break;\n      }\n    }\n  },\n  methods: {\n    formatStatus(row) {\n      return row.status == 1 ? '正常' : '离线';\n    },\n    getStatus(row) {\n      let statusV;\n      if (row.waterStatus == 1) statusV = '正常';else if (row.waterStatus == 2) statusV = '三级预警';else if (row.waterStatus == 3) statusV = '二级预警';else if (row.waterStatus == 4) statusV = '一级预警';\n      return statusV;\n    },\n    autoScroll(stop) {\n      const table = this.$refs.scroll_Table;\n      const divData = table.$refs.bodyWrapper;\n      if (stop) {\n        window.clearInterval(this.scrolltimer);\n      } else {\n        this.scrolltimer = window.setInterval(() => {\n          divData.scrollTop += 1;\n          if (divData.scrollTop >= divData.scrollHeight - divData.clientHeight) {\n            divData.scrollTop = 0;\n          }\n        }, 10);\n      }\n    },\n    closeFun() {\n      this.$store.commit('warningType/getIsShowHistoryEventList', false);\n      this.$store.commit('action/getIsScreenShow', true);\n    },\n    handleClick() {},\n    initPlayer() {\n      getVideoOneToken().then(res => {\n        this.videoToken = res.data.data.accessToken;\n        console.log('视频token', this.videoToken);\n      });\n      console.log('url', this.currentUrl);\n      // 创建 EZUIKitPlayer 实例\n      const player = new EZUIKit.EZUIKitPlayer({\n        id: 'ezuikit-player',\n        url: this.currentUrl,\n        // accessToken: 'at.a4j0gkxj798qzsch3tjarkww4v4seqch-77dkste2xf-1kcv6c7-czgaowkuh',\n        accessToken: this.videoToken,\n        width: 800,\n        height: 600\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "getHistoryAccumulation", "getHistoryRiverQuality", "getHistoryRiverLevelMonitoring", "getVideoOneToken", "name", "data", "tableData", "videoToken", "computed", "historyEventList", "state", "warningType", "currentEvent", "currentUrl", "mounted", "initPlayer", "watch", "dataChannelText", "nv", "typename", "params", "pageSize", "pageNum", "response_person_name", "object", "responsePersonName", "then", "res", "$store", "commit", "extra", "params2", "number", "url", "methods", "formatStatus", "row", "status", "getStatus", "statusV", "waterStatus", "autoScroll", "stop", "table", "$refs", "scroll_Table", "divData", "bodyWrapper", "window", "clearInterval", "scrolltimer", "setInterval", "scrollTop", "scrollHeight", "clientHeight", "closeFun", "handleClick", "accessToken", "console", "log", "player", "EZUIKit", "EZUIKitPlayer", "id", "width", "height"], "sources": ["src/views/dataScreen/fileType/waterWarningType/warningHistory.vue"], "sourcesContent": ["<template>\r\n    <div class=\"warningHistory\">\r\n        <div class=\"top\">\r\n            <div class=\"titleBox\">\r\n                <div class=\"title\" v-if=\"currentEvent != '一体化智能杆'\">\r\n                    <span>历史数据</span>\r\n                </div>\r\n                <div class=\"title\" v-if=\"currentEvent == '一体化智能杆'\">\r\n                    <span>视频播放</span>\r\n                </div>\r\n            </div>\r\n            <div class=\"delete\" @click=\"closeFun\">\r\n                <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\" width=\"80%\">\r\n            </div>\r\n        </div>\r\n        <div class=\"tableShowData\">\r\n\r\n            <el-table :data=\"historyEventList\" height=\"400\" style=\"width: 100%\"\r\n                :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                v-if=\"currentEvent == '积水预警事件'\" @mouseenter.native=\"autoScroll(true)\"\r\n                @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"responsePersonName\" label=\"名称\" width=\"240\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"addTime\" label=\"发生时间\" width=\"230\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"nameLocation\" label=\"地址\" width=\"400\">\r\n                </el-table-column>\r\n            </el-table>\r\n            <el-table :data=\"historyEventList\" height=\"400\" style=\"width: 100%\"\r\n                :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                v-if=\"currentEvent == '河道液位预警'\" @mouseenter.native=\"autoScroll(true)\"\r\n                @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"locationName\" label=\"名称\" width=\"250\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"dissolvedOxygen\" label=\"溶解氧\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"samplingTime\" label=\"时间\" width=\"230\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"permanganate\" label=\"高锰酸盐\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"nhn\" label=\"氨氢\" width=\"150\">\r\n                </el-table-column>\r\n            </el-table>\r\n            <!-- <el-table :data=\"historyEventList\" height=\"400\" style=\"width: 100%\"\r\n                :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                v-if=\"currentEvent == '剖面仪预警'\" @mouseenter.native=\"autoScroll(true)\"\r\n                @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"monitoringPointName\" label=\"名称\" width=\"230\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"status\" label=\"设备状态\" width=\"150\" :formatter=\"formatStatus\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"gatherTime\" label=\"时间\" width=\"230\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"waterLevelNow\" label=\"实时水位(m)\" width=\"150\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"waterStatus\" label=\"水位状态\" width=\"150\" :formatter=\"getStatus\">\r\n                </el-table-column>\r\n            </el-table> -->\r\n        </div>\r\n        <div id=\"ezuikit-player\" ref=\"playerContainer\" v-if=\"currentEvent == '一体化智能杆'\">\r\n        </div>\r\n    </div>\r\n\r\n</template>\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { getHistoryAccumulation, getHistoryRiverQuality, getHistoryRiverLevelMonitoring } from '@/api/index.js'\r\nimport { getVideoOneToken } from '@/api/waterVideo.js'\r\nexport default {\r\n    name: 'warningHistory',\r\n    data() {\r\n        return {\r\n            tableData: [],\r\n            videoToken: ''\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            historyEventList: state => state.warningType.historyEventList,\r\n            currentEvent: state => state.warningType.currentEvent,\r\n            currentUrl: state => state.warningType.currentUrl,\r\n        }),\r\n        ...mapState(['dataChannelText', 'larksr']),\r\n    },\r\n    mounted() {\r\n        this.initPlayer()\r\n    },\r\n    watch: {\r\n        \r\n        dataChannelText(nv) {\r\n            // console.log('11',nv)\r\n            switch (nv.typename) {\r\n                case '积水预警事件':\r\n                    let params = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        response_person_name: nv.object.responsePersonName\r\n                    }\r\n                    getHistoryAccumulation(params).then(res => {\r\n                        // console.log('积水---', res)\r\n                        this.$store.commit('warningType/getCurrentEvent', nv.typename)\r\n                        this.$store.commit('warningType/getHistoryEventList', res.data.extra.data)\r\n\r\n                        this.$store.commit('action/getIsScreenShow', false)\r\n                        this.$store.commit('warningType/getIsShowHistoryEventList', true)\r\n                    })\r\n                    break;\r\n                case '河道液位预警':\r\n                    let params2 = {\r\n                        pageSize: 10,\r\n                        pageNum: 1,\r\n                        number: nv.object.number\r\n                    };\r\n                    getHistoryRiverQuality(params2).then(res => {\r\n                        // console.log('河道---', res)\r\n                        this.$store.commit('warningType/getCurrentEvent', nv.typename)\r\n                        this.$store.commit('warningType/getHistoryEventList', res.data.extra.data)\r\n\r\n                        this.$store.commit('action/getIsScreenShow', false)\r\n                        this.$store.commit('warningType/getIsShowHistoryEventList', true)\r\n                    })\r\n                    break;\r\n                case '一体化智能杆':\r\n                    this.$store.commit('warningType/getCurrentEvent', nv.typename)\r\n                    this.$store.commit('warningType/getCurrentUrl', nv.url)\r\n                    this.$store.commit('action/getIsScreenShow', false)\r\n                    this.$store.commit('warningType/getIsShowAccumulationArr', false)\r\n                    this.$store.commit('warningType/getIsShowHistoryEventList', true)\r\n                    this.initPlayer()\r\n                    break;\r\n                default:\r\n                    break\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        formatStatus(row) {\r\n            return row.status == 1 ? '正常' : '离线'\r\n        },\r\n        getStatus(row) {\r\n            let statusV;\r\n            if (row.waterStatus == 1) statusV = '正常'\r\n            else if (row.waterStatus == 2) statusV = '三级预警'\r\n            else if (row.waterStatus == 3) statusV = '二级预警'\r\n            else if (row.waterStatus == 4) statusV = '一级预警'\r\n            return statusV;\r\n        },\r\n        autoScroll(stop) {\r\n            const table = this.$refs.scroll_Table\r\n            const divData = table.$refs.bodyWrapper\r\n            if (stop) {\r\n                window.clearInterval(this.scrolltimer)\r\n            } else {\r\n                this.scrolltimer = window.setInterval(() => {\r\n                    divData.scrollTop += 1\r\n                    if (divData.scrollTop >= divData.scrollHeight - divData.clientHeight) {\r\n                        divData.scrollTop = 0\r\n                    }\r\n                }, 10)\r\n            }\r\n        },\r\n        closeFun() {\r\n            this.$store.commit('warningType/getIsShowHistoryEventList', false)\r\n            this.$store.commit('action/getIsScreenShow', true)\r\n        },\r\n        handleClick() {\r\n\r\n        },\r\n        initPlayer() {\r\n            getVideoOneToken().then(res => {\r\n                this.videoToken = res.data.data.accessToken;\r\n                console.log('视频token', this.videoToken);\r\n            });\r\n            console.log('url', this.currentUrl)\r\n            // 创建 EZUIKitPlayer 实例\r\n            const player = new EZUIKit.EZUIKitPlayer({\r\n                id: 'ezuikit-player',\r\n                url: this.currentUrl,\r\n                // accessToken: 'at.a4j0gkxj798qzsch3tjarkww4v4seqch-77dkste2xf-1kcv6c7-czgaowkuh',\r\n                accessToken: this.videoToken,\r\n                width: 800,\r\n                height: 600\r\n            });\r\n        },\r\n    },\r\n\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.warningHistory {\r\n    pointer-events: stroke;\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 47%;\r\n    transform: translateX(-50%);\r\n    z-index: 100;\r\n    background-color: rgba(46, 101, 167, 0.3);\r\n\r\n    .top {\r\n        width: calc(100%);\r\n        display: flex;\r\n        margin-top: 1rem;\r\n\r\n        .titleBox {\r\n            width: 90%;\r\n            padding-top: .5rem;\r\n\r\n            .title {\r\n                width: calc(40%);\r\n                // margin-left: 400px;\r\n                // margin-top: 20px;\r\n                margin: 0 auto;\r\n                text-align: center;\r\n                background: url(\"@/assets/images/event/titleWing.png\") no-repeat 100%;\r\n                background-size: 100%;\r\n                // padding-left: calc(8%);\r\n\r\n                span {\r\n                    display: inline-block;\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 700;\r\n                    font-size: 43px;\r\n                    background-image: -webkit-linear-gradient(90.00001393970153deg, #fdfdfd 0%, #bbe8ff 100%);\r\n                    -webkit-background-clip: text;\r\n                    -webkit-text-fill-color: transparent;\r\n                }\r\n\r\n            }\r\n        }\r\n\r\n        .delete {\r\n            width: 5%;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            padding: 10px;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n::v-deep .el-table {\r\n    border: none !important;\r\n    background-color: rgba(46, 101, 167, 0)\r\n}\r\n\r\n::v-deep .el-table__body td {\r\n    font-size: 18px;\r\n    padding: 25px 0;\r\n    border: none !important;\r\n\r\n    div {\r\n        overflow: hidden;\r\n        /* 隐藏超出部分 */\r\n        white-space: nowrap;\r\n        /* 不允许换行 */\r\n        text-overflow: ellipsis;\r\n        /* 显示省略号 */\r\n    }\r\n}\r\n\r\n::v-deep .el-table__header {\r\n    tr {\r\n        background-color: rgba(46, 101, 167, 0.5);\r\n    }\r\n\r\n    th {\r\n        font-size: 23px;\r\n        border: none !important;\r\n    }\r\n}\r\n\r\n\r\n// ::v-deep .el-table__header \r\n::v-deep .el-table__row {\r\n    background-color: rgba(46, 101, 167, 0.1);\r\n}\r\n\r\n::v-deep .el-table__body-wrapper {\r\n    background-color: rgba(46, 101, 167, 0.1);\r\n}\r\n\r\n.tableShowData {\r\n    box-sizing: border-box;\r\n    padding: 5px 18px;\r\n    width: 100%;\r\n\r\n}\r\n</style>"], "mappings": "AA0EA,SAAAA,QAAA;AACA,SAAAC,sBAAA,EAAAC,sBAAA,EAAAC,8BAAA;AACA,SAAAC,gBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAT,QAAA;MACAU,gBAAA,EAAAC,KAAA,IAAAA,KAAA,CAAAC,WAAA,CAAAF,gBAAA;MACAG,YAAA,EAAAF,KAAA,IAAAA,KAAA,CAAAC,WAAA,CAAAC,YAAA;MACAC,UAAA,EAAAH,KAAA,IAAAA,KAAA,CAAAC,WAAA,CAAAE;IACA;IACA,GAAAd,QAAA;EACA;EACAe,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,KAAA;IAEAC,gBAAAC,EAAA;MACA;MACA,QAAAA,EAAA,CAAAC,QAAA;QACA;UACA,IAAAC,MAAA;YACAC,QAAA;YACAC,OAAA;YACAC,oBAAA,EAAAL,EAAA,CAAAM,MAAA,CAAAC;UACA;UACAzB,sBAAA,CAAAoB,MAAA,EAAAM,IAAA,CAAAC,GAAA;YACA;YACA,KAAAC,MAAA,CAAAC,MAAA,gCAAAX,EAAA,CAAAC,QAAA;YACA,KAAAS,MAAA,CAAAC,MAAA,oCAAAF,GAAA,CAAAtB,IAAA,CAAAyB,KAAA,CAAAzB,IAAA;YAEA,KAAAuB,MAAA,CAAAC,MAAA;YACA,KAAAD,MAAA,CAAAC,MAAA;UACA;UACA;QACA;UACA,IAAAE,OAAA;YACAV,QAAA;YACAC,OAAA;YACAU,MAAA,EAAAd,EAAA,CAAAM,MAAA,CAAAQ;UACA;UACA/B,sBAAA,CAAA8B,OAAA,EAAAL,IAAA,CAAAC,GAAA;YACA;YACA,KAAAC,MAAA,CAAAC,MAAA,gCAAAX,EAAA,CAAAC,QAAA;YACA,KAAAS,MAAA,CAAAC,MAAA,oCAAAF,GAAA,CAAAtB,IAAA,CAAAyB,KAAA,CAAAzB,IAAA;YAEA,KAAAuB,MAAA,CAAAC,MAAA;YACA,KAAAD,MAAA,CAAAC,MAAA;UACA;UACA;QACA;UACA,KAAAD,MAAA,CAAAC,MAAA,gCAAAX,EAAA,CAAAC,QAAA;UACA,KAAAS,MAAA,CAAAC,MAAA,8BAAAX,EAAA,CAAAe,GAAA;UACA,KAAAL,MAAA,CAAAC,MAAA;UACA,KAAAD,MAAA,CAAAC,MAAA;UACA,KAAAD,MAAA,CAAAC,MAAA;UACA,KAAAd,UAAA;UACA;QACA;UACA;MACA;IACA;EACA;EACAmB,OAAA;IACAC,aAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,MAAA;IACA;IACAC,UAAAF,GAAA;MACA,IAAAG,OAAA;MACA,IAAAH,GAAA,CAAAI,WAAA,OAAAD,OAAA,aACA,IAAAH,GAAA,CAAAI,WAAA,OAAAD,OAAA,eACA,IAAAH,GAAA,CAAAI,WAAA,OAAAD,OAAA,eACA,IAAAH,GAAA,CAAAI,WAAA,OAAAD,OAAA;MACA,OAAAA,OAAA;IACA;IACAE,WAAAC,IAAA;MACA,MAAAC,KAAA,QAAAC,KAAA,CAAAC,YAAA;MACA,MAAAC,OAAA,GAAAH,KAAA,CAAAC,KAAA,CAAAG,WAAA;MACA,IAAAL,IAAA;QACAM,MAAA,CAAAC,aAAA,MAAAC,WAAA;MACA;QACA,KAAAA,WAAA,GAAAF,MAAA,CAAAG,WAAA;UACAL,OAAA,CAAAM,SAAA;UACA,IAAAN,OAAA,CAAAM,SAAA,IAAAN,OAAA,CAAAO,YAAA,GAAAP,OAAA,CAAAQ,YAAA;YACAR,OAAA,CAAAM,SAAA;UACA;QACA;MACA;IACA;IACAG,SAAA;MACA,KAAA3B,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;IACA;IACA2B,YAAA,GAEA;IACAzC,WAAA;MACAZ,gBAAA,GAAAuB,IAAA,CAAAC,GAAA;QACA,KAAApB,UAAA,GAAAoB,GAAA,CAAAtB,IAAA,CAAAA,IAAA,CAAAoD,WAAA;QACAC,OAAA,CAAAC,GAAA,iBAAApD,UAAA;MACA;MACAmD,OAAA,CAAAC,GAAA,aAAA9C,UAAA;MACA;MACA,MAAA+C,MAAA,OAAAC,OAAA,CAAAC,aAAA;QACAC,EAAA;QACA9B,GAAA,OAAApB,UAAA;QACA;QACA4C,WAAA,OAAAlD,UAAA;QACAyD,KAAA;QACAC,MAAA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}