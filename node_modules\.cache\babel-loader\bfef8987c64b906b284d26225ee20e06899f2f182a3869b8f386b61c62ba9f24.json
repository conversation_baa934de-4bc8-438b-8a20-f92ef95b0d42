{"ast": null, "code": "import RotationData from '@/components/RotationData/RotationData.vue';\nimport { getWaterEasyAccumulation } from '@/api/index.js';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'warningTable',\n  components: {\n    RotationData\n  },\n  data() {\n    return {\n      allTime: \"\",\n      tableList: [],\n      scrollList: [],\n      hotT: true,\n      // headerList: [\"序号\", \"事件类型\", \"事件来源\", \"发生时间\", \"发生位置\"],\n      peopleList: [{\n        id: '1',\n        name: '俞骏',\n        data1: '5月1日',\n        data10: '5月12日',\n        data11: '5月23日',\n        data2: ' ',\n        data20: '6月3日',\n        data21: '6月14日',\n        data22: '6月25日',\n        data3: ' ',\n        data30: '7月6日',\n        data31: '7月17日',\n        data32: '7月28日',\n        data4: ' ',\n        data40: '8月7日',\n        data41: '8月16日',\n        data42: '8月26日',\n        data5: ' ',\n        data51: '9月6日',\n        data52: '9月13日',\n        data53: '9月26日'\n      }, {\n        id: '2',\n        name: '陆韬',\n        data1: '5月2日',\n        data10: '5月13日',\n        data11: '5月24日',\n        data2: ' ',\n        data20: '6月4日',\n        data21: '6月15日',\n        data22: '6月26日',\n        data3: ' ',\n        data30: '7月7日',\n        data31: '7月18日',\n        data32: '7月19日',\n        data4: ' ',\n        data40: '8月8日',\n        data41: '8月18日',\n        data42: '8月27日',\n        data5: ' ',\n        data51: '9月7日',\n        data52: '9月14日',\n        data53: '9月27日'\n      }, {\n        id: '3',\n        name: '冯毅峰',\n        data1: '5月3日',\n        data10: '5月14日',\n        data11: '5月26日',\n        data2: ' ',\n        data20: '6月5日',\n        data21: '6月16日',\n        data22: '6月27日',\n        data3: ' ',\n        data30: '7月8日',\n        data31: '7月19日',\n        data32: ' ',\n        data4: ' ',\n        data40: ' ',\n        data41: ' ',\n        data42: '',\n        data5: ' ',\n        data51: ' ',\n        data52: ' ',\n        data53: ' '\n      }, {\n        id: '4',\n        name: '蔡晨',\n        data1: '5月4日',\n        data10: '5月15日',\n        data11: '5月25日',\n        data2: ' ',\n        data20: '6月6日',\n        data21: '6月17日',\n        data22: '6月28日',\n        data3: ' ',\n        data30: '7月9日',\n        data31: '7月20日',\n        data32: '7月30日',\n        data4: ' ',\n        data40: '8月9日',\n        data41: '8月19日',\n        data42: '8月28日',\n        data5: ' ',\n        data51: '9月8日',\n        data52: '9月18日',\n        data53: '9月28日'\n      }, {\n        id: '5',\n        name: '蔡惠林',\n        data1: '5月5日',\n        data10: '5月16日',\n        data11: '5月27日',\n        data2: ' ',\n        data20: '6月7日',\n        data21: '6月18日',\n        data22: '6月29日',\n        data3: ' ',\n        data30: '7月10日',\n        data31: '7月27日',\n        data32: '7月31日',\n        data4: ' ',\n        data40: '8月10日',\n        data41: '8月20日',\n        data42: '8月29日',\n        data5: ' ',\n        data51: '9月9日',\n        data52: '9月19日',\n        data53: '9月29日'\n      }, {\n        id: '6',\n        name: '陶维林',\n        data1: '5月6日',\n        data10: '5月17日',\n        data11: '5月28日',\n        data2: ' ',\n        data20: '6月8日',\n        data21: '6月19日',\n        data22: '6月30日',\n        data3: ' ',\n        data30: '7月11日',\n        data31: '7月22日',\n        data32: ' ',\n        data4: '8月1日',\n        data40: '8月11日',\n        data41: '8月21日',\n        data42: '8月30日',\n        data5: ' ',\n        data51: '9月10日',\n        data52: '9月20日',\n        data53: '9月30日'\n      }, {\n        id: '7',\n        name: '张健',\n        data1: '5月7日',\n        data10: '5月18日',\n        data11: '5月29日',\n        data2: ' ',\n        data20: '6月9日',\n        data21: '6月20日',\n        data22: ' ',\n        data3: '7月1日',\n        data30: '7月12日',\n        data31: '7月23日',\n        data32: ' ',\n        data4: '8月2日',\n        data40: '8月12日',\n        data41: '8月22日',\n        data42: '8月31日',\n        data5: ' ',\n        data51: '9月11日',\n        data52: '9月21日',\n        data53: ' '\n      }, {\n        id: '8',\n        name: '张秋东',\n        data1: '5月8日',\n        data10: '5月21日',\n        data11: '5月30日',\n        data2: ' ',\n        data20: '6月10日',\n        data21: '6月21日',\n        data22: ' ',\n        data3: '7月2日',\n        data30: '7月13日',\n        data31: '7月24日',\n        data32: ' ',\n        data4: '8月3日',\n        data40: '8月13日',\n        data41: '8月23日',\n        data42: '',\n        data5: '9月1日',\n        data51: '9月12日',\n        data52: '9月22日',\n        data53: ' '\n      }, {\n        id: '9',\n        name: '沈逸潇',\n        data1: '5月9日',\n        data10: '5月20日',\n        data11: '5月31日',\n        data2: ' ',\n        data20: '6月11日',\n        data21: '6月22日',\n        data22: ' ',\n        data3: '7月3日',\n        data30: '7月14日',\n        data31: '7月25日',\n        data32: ' ',\n        data4: '8月4日',\n        data40: '8月14日',\n        data41: '8月24日',\n        data42: ' ',\n        data5: '9月2日',\n        data51: '9月15日',\n        data52: '9月23日',\n        data53: ' '\n      }, {\n        id: '10',\n        name: '杨晓华',\n        data1: '5月10日',\n        data10: '5月19日',\n        data11: ' ',\n        data2: '6月1日',\n        data20: '6月12日',\n        data21: '6月23日',\n        data22: ' ',\n        data3: '7月4日',\n        data30: '7月15日',\n        data31: '7月26日',\n        data32: ' ',\n        data4: '8月5日',\n        data40: '8月15日',\n        data41: '8月25日',\n        data42: ' ',\n        data5: '9月3日',\n        data51: '9月16日',\n        data52: '9月24日',\n        data53: ' '\n      }, {\n        id: '11',\n        name: '任俊宇',\n        data1: '5月11日',\n        data10: '5月22日',\n        data11: ' ',\n        data2: '6月2日',\n        data20: '6月13日',\n        data21: '6月24日',\n        data22: ' ',\n        data3: '7月5日',\n        data30: '7月16日',\n        data31: '7月21日',\n        data32: ' ',\n        data4: '8月6日',\n        data40: '8月17日',\n        data41: '8月26日',\n        data42: ' ',\n        data5: '9月4日',\n        data51: '9月17日',\n        data52: '9月25日',\n        data53: ' '\n      }, {\n        id: '12',\n        name: '张秋红',\n        data1: '5月12日',\n        data10: '5月26日',\n        data11: ' ',\n        data2: ' ',\n        data20: '6月16日',\n        data21: ' ',\n        data22: ' ',\n        data3: ' ',\n        data30: '7月14日',\n        data31: '7月28日',\n        data32: ' ',\n        data4: ' ',\n        data40: '8月11日',\n        data41: '8月25日',\n        data42: ' ',\n        data5: '9月5日',\n        data51: '9月8日',\n        data52: ' ',\n        data53: ' '\n      }, {\n        id: '13',\n        name: '周中英',\n        data1: '5月18日',\n        data10: ' ',\n        data11: ' ',\n        data2: '6月1日',\n        data20: '6月22日',\n        data21: ' ',\n        data22: ' ',\n        data3: '7月6日',\n        data30: '7月20日',\n        data31: ' ',\n        data32: ' ',\n        data4: '8月3日',\n        data40: '8月17日',\n        data41: '8月31日',\n        data42: ' ',\n        data5: ' ',\n        data51: '9月21日',\n        data52: ' ',\n        data53: ' '\n      }, {\n        id: '14',\n        name: '沈叶静',\n        data1: '5月19日',\n        data10: ' ',\n        data11: ' ',\n        data2: '6月2日',\n        data20: '6月23日',\n        data21: ' ',\n        data22: ' ',\n        data3: '7月7日',\n        data30: '7月21日',\n        data31: ' ',\n        data32: ' ',\n        data4: '8月4日',\n        data40: '8月18日',\n        data41: ' ',\n        data42: ' ',\n        data5: '9月1日',\n        data51: '9月22日',\n        data52: ' ',\n        data53: ' '\n      }, {\n        id: '15',\n        name: '彭金炎',\n        data1: '5月25日',\n        data10: ' ',\n        data11: ' ',\n        data2: '6月15日',\n        data20: '6月29日',\n        data21: ' ',\n        data22: ' ',\n        data3: ' ',\n        data30: '7月27日',\n        data31: ' ',\n        data32: ' ',\n        data4: '8月10日',\n        data40: '8月24日',\n        data41: ' ',\n        data42: ' ',\n        data5: '9月2日',\n        data51: '9月28日',\n        data52: ' ',\n        data53: ' '\n      }],\n      styleAll: {\n        width: '100%',\n        height: '25rem'\n      },\n      pageNum: 1,\n      pageSize: 10,\n      totalPage: 0,\n      total: 0,\n      SelectList: {},\n      //选中\n      arrName: '',\n      videoToken: '',\n      videoUrl: '',\n      scrolltimer: '',\n      Accumulation: []\n    };\n  },\n  mounted() {\n    // this.initPlayer();\n    this.autoScroll();\n  },\n  destroyed() {\n    this.autoScroll(true);\n  },\n  watch: {\n    // dataChannelText(nv) {\n    //     console.log(nv, 'table收到信息')\n\n    // },\n\n    arrName(nv) {\n      if (nv != '积水预警事件') {\n        this.hotT = true;\n      }\n    },\n    allTime(newVal) {\n      let params = '';\n      if (newVal) {\n        params = {\n          startTime: newVal[0],\n          endTime: newVal[1]\n        };\n      }\n      getWaterEasyAccumulation(params).then(res => {\n        if (res.data.code == 200) {\n          this.Accumulation = {\n            name: '积水预警事件',\n            data: res.data.extra\n          };\n          this.$store.commit('warningType/getAccumulationData', this.Accumulation);\n        }\n      });\n    },\n    accumulationArr: {\n      handler(val) {\n        this.scrollList = [];\n        let {\n          name,\n          data\n        } = val;\n        this.arrName = name;\n        // 表格数据展示\n        if (name == \"积水预警事件\") {\n          // this.headerList = [\"序号\", \"名称\", \"设备状态\", \"发生时间\", \"地址\"],\n          data.forEach((item, index) => {\n            let {\n              addTime,\n              responsePersonName,\n              nameLocation,\n              responseUnit,\n              id,\n              pointY,\n              pointX\n            } = item;\n            let isStatus = responseUnit == 1 ? '在线' : '离线';\n            item.isStatus = isStatus;\n            // this.scrollList.push([id, responsePersonName, isStatus, addTime, nameLocation])\n            this.scrollList.push({\n              id,\n              responsePersonName,\n              isStatus,\n              addTime,\n              nameLocation,\n              pointY,\n              pointX\n            });\n          });\n        } else if (name == \"河道液位预警\") {\n          // this.headerList = [\"序号\", \"名称\", \"溶解氧\", \"时间\", \"高锰酸盐\", \"氨氢\"]\n          data.forEach((item, index) => {\n            let {\n              samplingTime,\n              locationName,\n              dissolvedOxygen,\n              permanganate,\n              nhn,\n              id\n            } = item;\n            // this.scrollList.push([id, locationName, dissolvedOxygen, samplingTime, permanganate, nhn])\n            this.scrollList.push({\n              id,\n              locationName,\n              dissolvedOxygen,\n              samplingTime,\n              permanganate,\n              nhn\n            });\n          });\n        }\n        /* else if (name == \"剖面仪预警\") {\r\n           // this.headerList = [\"序号\", \"名称\", \"设备状态\", \"时间\", \"实时水位(m)\", \"水位状态\"]\r\n           data.forEach((item, index) => {\r\n               let { gatherTime, monitoringPointName, status, waterLevelNow, waterStatus, id } = item\r\n               let isStatus = status == 1 ? '在线' : '离线'\r\n               item.isStatus = isStatus\r\n               let statusV = this.getStatus(waterStatus)\r\n               item.statusVx = statusV\r\n               // this.scrollList.push([id, monitoringPointName, isStatus, gatherTime, waterLevelNow, statusV])\r\n               this.scrollList.push({ id, monitoringPointName, isStatus, gatherTime, waterLevelNow, statusV })\r\n           })\r\n        }  */else if (name == \"河道水位预警\") {\n          // this.headerList = [\"名称\", \"设备状态\", \"时间\", \"实时水位(m)\", \"水位状态\"]\n          data.forEach((item, index) => {\n            let {\n              name,\n              value,\n              road,\n              position,\n              recordTime\n            } = item;\n            let isValue = 0;\n            if (value <= 2.6) {\n              isValue = 2.6;\n            } else if (value > 2.6 && value <= 2.8) {\n              isValue = 3.0;\n            } else if (value > 2.8) {\n              isValue = 3.2;\n            }\n            item.isValue = parseFloat(isValue.toFixed(1));\n            isValue = parseFloat(isValue.toFixed(1));\n            // this.scrollList.push([  name, value, road, position])\n            this.scrollList.push({\n              name,\n              value,\n              isValue,\n              road,\n              position,\n              recordTime\n            });\n          });\n        } else if (name == \"一体化智能杆\") {\n          // this.headerList = [\"序号\", \"设备名称\", \"设备状态\", \"设备类别\", \"安装位置\"]\n          data.forEach((item, index) => {\n            let {\n              id,\n              equipmentName,\n              equipmentStatus,\n              equipmentCategoryName,\n              nameLocation,\n              updateTime\n            } = item;\n            let isStatus = equipmentStatus == 1 ? '正常' : '不正常';\n            item.isStatus = isStatus;\n            // this.scrollList.push([id, equipmentName, isStatus, equipmentCategoryName, nameLocation ])\n            this.scrollList.push({\n              id,\n              equipmentName,\n              isStatus,\n              equipmentCategoryName,\n              nameLocation,\n              updateTime\n            });\n          });\n        } else if (name == \"防汛值班表\") {\n          // this.headerList = [\"序号\", \"姓名\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\"]\n          this.scrollList = this.peopleList;\n          // this.$store.commit('action/getIsScreenShow', false)\n        }\n        this.tableList = val;\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState(['dataChannelText', 'larksr']),\n    ...mapState('warningType', ['accumulationArr', 'currentIndex'])\n  },\n  methods: {\n    // 点击行展示点位\n    handleRowClick(row, column, event, index) {\n      // // 清除点位\n      // let params = {\n      //     name: this.arrName,\n      //     flag: false,\n      // }\n      // this.$eventBus.$emit('senTxtToUe', params);\n      // //筛选数据并撒点\n      // this.scrollList.forEach(item => {\n      //     if (item.id == row.id) {\n      //         let params = {\n      //             name: this.arrName,\n      //             data: [item],\n      //             flag: true,\n      //         }\n      //         console.log(params, 'params')\n      //         this.$eventBus.$emit('senTxtToUe', params);\n      //     }\n      // })\n    },\n    autoScroll(stop) {\n      const table = this.$refs.scroll_Table;\n      const divData = table.$refs.bodyWrapper;\n      if (stop) {\n        window.clearInterval(this.scrolltimer);\n      } else {\n        this.scrolltimer = window.setInterval(() => {\n          divData.scrollTop += 1;\n          if (divData.scrollTop >= divData.scrollHeight - divData.clientHeight) {\n            divData.scrollTop = 0;\n          }\n        }, 10);\n      }\n    },\n    openHot(flag) {\n      this.hotT = !flag;\n      let params = {\n        name: \"积水热力图\",\n        number: 4,\n        flag\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n      console.log('params', params);\n    },\n    clearPoint(name) {\n      let params = {\n        name: name,\n        flag: false\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    getStatus(status) {\n      let statusV;\n      if (status == 1) statusV = '正常';else if (status == 2) statusV = '三级预警';else if (status == 3) statusV = '二级预警';else if (status == 4) statusV = '一级预警';\n      return statusV;\n    },\n    // 选中一个事件\n    getInfo_Option(val) {\n      this.SelectList = this.tableList.data[val.rowIndex];\n      this.SelectList.typename = this.tableList.name;\n      this.SelectList.Name = val.row[1];\n      this.SelectList.eventTime = val.row[3];\n      this.$store.commit(\"warningType/getSelectEventInfoOne\", this.SelectList);\n      this.$store.commit(\"warningType/getIsShowEventInfoOne\", true);\n    },\n    close() {\n      this.clearPoint(this.arrName);\n      this.$store.commit('warningType/getCurrentIndex', -1);\n      this.$store.commit('warningType/getIsShowAccumulationArr', false);\n      this.$store.commit('action/getIsScreenShow', true);\n      this.$store.commit('warningType/getAccumulationData', {\n        name: '事件列表',\n        data: ''\n      });\n      // this.$store.commit('warningType/getIsShowAccumulationArr', false)\n    }\n  }\n};", "map": {"version": 3, "names": ["RotationData", "getWaterEasyAccumulation", "mapState", "name", "components", "data", "allTime", "tableList", "scrollList", "hotT", "peopleList", "id", "data1", "data10", "data11", "data2", "data20", "data21", "data22", "data3", "data30", "data31", "data32", "data4", "data40", "data41", "data42", "data5", "data51", "data52", "data53", "styleAll", "width", "height", "pageNum", "pageSize", "totalPage", "total", "SelectList", "arrName", "videoToken", "videoUrl", "scrolltimer", "Accumulation", "mounted", "autoScroll", "destroyed", "watch", "nv", "newVal", "params", "startTime", "endTime", "then", "res", "code", "extra", "$store", "commit", "accumulationArr", "handler", "val", "for<PERSON>ach", "item", "index", "addTime", "responsePersonName", "nameLocation", "responseUnit", "pointY", "pointX", "isStatus", "push", "samplingTime", "locationName", "dissolvedOxygen", "permanganate", "nhn", "value", "road", "position", "recordTime", "isValue", "parseFloat", "toFixed", "equipmentName", "equipmentStatus", "equipmentCategoryName", "updateTime", "immediate", "computed", "methods", "handleRowClick", "row", "column", "event", "stop", "table", "$refs", "scroll_Table", "divData", "bodyWrapper", "window", "clearInterval", "setInterval", "scrollTop", "scrollHeight", "clientHeight", "openHot", "flag", "number", "$eventBus", "$emit", "console", "log", "clearPoint", "getStatus", "status", "statusV", "getInfo_Option", "rowIndex", "typename", "Name", "eventTime", "close"], "sources": ["src/views/dataScreen/fileType/waterWarningType/warningTable.vue"], "sourcesContent": ["<template>\r\n    <div class=\"warningTable boxBgStyle\">\r\n        <div class=\"top\">\r\n            <div class=\"titleBox\">\r\n                <div class=\"title\">\r\n                    <span>{{ arrName }}</span>\r\n                </div>\r\n                <div class=\"time\" v-if=\"arrName == '积水预警事件'\">\r\n                    <span class=\"timeTxt\">时间选择: </span>\r\n                    <el-date-picker v-model=\"allTime\" type=\"daterange\" range-separator=\"至\" start-placeholder=\"开始日期\"\r\n                        end-placeholder=\"结束日期\" format=\"yyyy 年 MM 月 dd 日\" value-format=\"yyyy-MM-dd\">\r\n                    </el-date-picker>\r\n                    <span class=\"hotT\" @click=\"openHot(true)\" v-show=\"hotT\">打开热力图</span>\r\n                    <span class=\"hotT\" @click=\"openHot(false)\" v-show=\"!hotT\">关闭热力图</span>\r\n                </div>\r\n            </div>\r\n            <div class=\"delete\" @click=\"close\">\r\n                <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\" width=\"50px\">\r\n            </div>\r\n        </div>\r\n        <div class=\"mainContent\">\r\n\r\n            <div class=\"tableShowData\">\r\n\r\n                <!-- <RotationData :data=\"scrollList\" :header=\"headerList\" :styleAll=\"styleAll\"\r\n                    @sendInfo_Option=\"getInfo_Option\" :SelectList=\"SelectList\"></RotationData> -->\r\n\r\n                <el-table :data=\"scrollList\" height=\"650\" style=\"width: 100%\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '积水预警事件'\" @mouseenter.native=\"autoScroll(true)\"\r\n                    @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n\r\n                    <el-table-column prop=\"id\" label=\"序号\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"responsePersonName\" label=\"名称\" width=\"220\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"isStatus\" label=\"设备状态\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"addTime\" label=\"发生时间\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nameLocation\" label=\"地址\" width=\"500\">\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-table :data=\"scrollList\" height=\"650\" style=\"width: 100%\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '河道液位预警'\" @mouseenter.native=\"autoScroll(true)\"\r\n                    @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                    <el-table-column prop=\"id\" label=\"序号\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"locationName\" label=\"名称\" width=\"350\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dissolvedOxygen\" label=\"溶解氧\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"samplingTime\" label=\"时间\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"permanganate\" label=\"高锰酸盐\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nhn\" label=\"氨氢\" width=\"250\">\r\n                    </el-table-column>\r\n                </el-table>\r\n                <!-- <el-table :data=\"scrollList\" height=\"400\" style=\"width: 100%\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '剖面仪预警'\" @mouseenter.native=\"autoScroll(true)\"\r\n                    @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                    <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"monitoringPointName\" label=\"名称\" width=\"230\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"isStatus\" label=\"设备状态\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"gatherTime\" label=\"时间\" width=\"230\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"waterLevelNow\" label=\"实时水位(m)\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"statusV\" label=\"水位状态\" width=\"150\">\r\n                    </el-table-column>\r\n                </el-table> -->\r\n                <el-table :data=\"scrollList\" height=\"650\" style=\"width: 100%\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '河道水位预警'\" @mouseenter.native=\"autoScroll(true)\"\r\n                    @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                    <el-table-column prop=\"name\" label=\"水位点名称\" width=\"360\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"value\" label=\"实时水位(m)\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"预警水位(m)\" width=\"250\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{ scope.row.isValue.toFixed(1) }}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"recordTime\" label=\"时间\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"road\" label=\"道路\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"position\" label=\"位置\" width=\"330\">\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-table :data=\"scrollList\" height=\"650\" style=\"width: 100%\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff' }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '一体化智能杆'\" @mouseenter.native=\"autoScroll(true)\"\r\n                    @mouseleave.native=\"autoScroll(false)\" ref=\"scroll_Table\">\r\n                    <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"equipmentName\" label=\"设备名称\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"isStatus\" label=\"设备状态\" width=\"250\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"updateTime\" label=\"时间\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"equipmentCategoryName\" label=\"设备类别\" width=\"330\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"nameLocation\" label=\"安装位置\" width=\"360\">\r\n                    </el-table-column>\r\n                </el-table>\r\n                <!-- <el-table-column prop=\"data1\" :label=\"''\" width=\"180\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"data10\" label=\"5月\"  show-overflow-tooltip width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"data11\" :label=\"''\" show-overflow-tooltip width=\"150\">\r\n                    </el-table-column> -->\r\n                <el-table :data=\"scrollList\" height=\"650\" style=\"width: 100%;\" class=\"people\"\r\n                    :header-cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0.5)', color: '#fff', }\"\r\n                    :cell-style=\"{ 'text-align': 'center', background: 'rgba(46,101,167,0)', color: '#eee', border: 0 }\"\r\n                    v-if=\"arrName == '防汛值班表'\">\r\n                    <el-table-column prop=\"id\" label=\"序号\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"name\" label=\"姓名\" width=\"150\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"5月\" colspan=\"3\" width=\"350\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data1 }}</span>\r\n                            <span>{{ scope.row.data10 }}</span>\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data11 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"6月\" colspan=\"4\" width=\"450\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data2 }}</span>\r\n                            <span>{{ scope.row.data20 }}</span>\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data21 }}</span>\r\n                            <span>{{ scope.row.data22 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"7月\" colspan=\"4\" width=\"450\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data3 }}</span>\r\n                            <span>{{ scope.row.data30 }}</span>\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data31 }}</span>\r\n                            <span>{{ scope.row.data32 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"8月\" colspan=\"4\" width=\"450\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data4 }}</span>\r\n                            <span>{{ scope.row.data40 }}</span>\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data41 }}</span>\r\n                            <span>{{ scope.row.data42 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"9月\" colspan=\"4\" width=\"450\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data5 }}</span>\r\n                            <span>{{ scope.row.data51 }}</span>\r\n                            <span style=\"padding: 20px;\">{{ scope.row.data52 }}</span>\r\n                            <span>{{ scope.row.data53 }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport RotationData from '@/components/RotationData/RotationData.vue'\r\nimport { getWaterEasyAccumulation } from '@/api/index.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'warningTable',\r\n    components: {\r\n        RotationData\r\n    },\r\n    data() {\r\n        return {\r\n            allTime: \"\",\r\n            tableList: [],\r\n            scrollList: [],\r\n            hotT: true,\r\n            // headerList: [\"序号\", \"事件类型\", \"事件来源\", \"发生时间\", \"发生位置\"],\r\n            peopleList: [\r\n                { id: '1', name: '俞骏', data1: '5月1日', data10: '5月12日', data11: '5月23日', data2: ' ', data20: '6月3日', data21: '6月14日', data22: '6月25日', data3: ' ', data30: '7月6日', data31: '7月17日', data32: '7月28日', data4: ' ', data40: '8月7日', data41: '8月16日', data42: '8月26日', data5: ' ', data51: '9月6日', data52: '9月13日', data53: '9月26日' },\r\n                { id: '2', name: '陆韬', data1: '5月2日', data10: '5月13日', data11: '5月24日', data2: ' ', data20: '6月4日', data21: '6月15日', data22: '6月26日', data3: ' ', data30: '7月7日', data31: '7月18日', data32: '7月19日', data4: ' ', data40: '8月8日', data41: '8月18日', data42: '8月27日', data5: ' ', data51: '9月7日', data52: '9月14日', data53: '9月27日' },\r\n                { id: '3', name: '冯毅峰', data1: '5月3日', data10: '5月14日', data11: '5月26日', data2: ' ', data20: '6月5日', data21: '6月16日', data22: '6月27日', data3: ' ', data30: '7月8日', data31: '7月19日', data32: ' ', data4: ' ', data40: ' ', data41: ' ', data42: '', data5: ' ', data51: ' ', data52: ' ', data53: ' ' },\r\n                { id: '4', name: '蔡晨', data1: '5月4日', data10: '5月15日', data11: '5月25日', data2: ' ', data20: '6月6日', data21: '6月17日', data22: '6月28日', data3: ' ', data30: '7月9日', data31: '7月20日', data32: '7月30日', data4: ' ', data40: '8月9日', data41: '8月19日', data42: '8月28日', data5: ' ', data51: '9月8日', data52: '9月18日', data53: '9月28日' },\r\n                { id: '5', name: '蔡惠林', data1: '5月5日', data10: '5月16日', data11: '5月27日', data2: ' ', data20: '6月7日', data21: '6月18日', data22: '6月29日', data3: ' ', data30: '7月10日', data31: '7月27日', data32: '7月31日', data4: ' ', data40: '8月10日', data41: '8月20日', data42: '8月29日', data5: ' ', data51: '9月9日', data52: '9月19日', data53: '9月29日' },\r\n                { id: '6', name: '陶维林', data1: '5月6日', data10: '5月17日', data11: '5月28日', data2: ' ', data20: '6月8日', data21: '6月19日', data22: '6月30日', data3: ' ', data30: '7月11日', data31: '7月22日', data32: ' ', data4: '8月1日', data40: '8月11日', data41: '8月21日', data42: '8月30日', data5: ' ', data51: '9月10日', data52: '9月20日', data53: '9月30日' },\r\n                { id: '7', name: '张健', data1: '5月7日', data10: '5月18日', data11: '5月29日', data2: ' ', data20: '6月9日', data21: '6月20日', data22: ' ', data3: '7月1日', data30: '7月12日', data31: '7月23日', data32: ' ', data4: '8月2日', data40: '8月12日', data41: '8月22日', data42: '8月31日', data5: ' ', data51: '9月11日', data52: '9月21日', data53: ' ' },\r\n                { id: '8', name: '张秋东', data1: '5月8日', data10: '5月21日', data11: '5月30日', data2: ' ', data20: '6月10日', data21: '6月21日', data22: ' ', data3: '7月2日', data30: '7月13日', data31: '7月24日', data32: ' ', data4: '8月3日', data40: '8月13日', data41: '8月23日', data42: '', data5: '9月1日', data51: '9月12日', data52: '9月22日', data53: ' ' },\r\n                { id: '9', name: '沈逸潇', data1: '5月9日', data10: '5月20日', data11: '5月31日', data2: ' ', data20: '6月11日', data21: '6月22日', data22: ' ', data3: '7月3日', data30: '7月14日', data31: '7月25日', data32: ' ', data4: '8月4日', data40: '8月14日', data41: '8月24日', data42: ' ', data5: '9月2日', data51: '9月15日', data52: '9月23日', data53: ' ' },\r\n                { id: '10', name: '杨晓华', data1: '5月10日', data10: '5月19日', data11: ' ', data2: '6月1日', data20: '6月12日', data21: '6月23日', data22: ' ', data3: '7月4日', data30: '7月15日', data31: '7月26日', data32: ' ', data4: '8月5日', data40: '8月15日', data41: '8月25日', data42: ' ', data5: '9月3日', data51: '9月16日', data52: '9月24日', data53: ' ' },\r\n                { id: '11', name: '任俊宇', data1: '5月11日', data10: '5月22日', data11: ' ', data2: '6月2日', data20: '6月13日', data21: '6月24日', data22: ' ', data3: '7月5日', data30: '7月16日', data31: '7月21日', data32: ' ', data4: '8月6日', data40: '8月17日', data41: '8月26日', data42: ' ', data5: '9月4日', data51: '9月17日', data52: '9月25日', data53: ' ' },\r\n                { id: '12', name: '张秋红', data1: '5月12日', data10: '5月26日', data11: ' ', data2: ' ', data20: '6月16日', data21: ' ', data22: ' ', data3: ' ', data30: '7月14日', data31: '7月28日', data32: ' ', data4: ' ', data40: '8月11日', data41: '8月25日', data42: ' ', data5: '9月5日', data51: '9月8日', data52: ' ', data53: ' ' },\r\n                { id: '13', name: '周中英', data1: '5月18日', data10: ' ', data11: ' ', data2: '6月1日', data20: '6月22日', data21: ' ', data22: ' ', data3: '7月6日', data30: '7月20日', data31: ' ', data32: ' ', data4: '8月3日', data40: '8月17日', data41: '8月31日', data42: ' ', data5: ' ', data51: '9月21日', data52: ' ', data53: ' ' },\r\n                { id: '14', name: '沈叶静', data1: '5月19日', data10: ' ', data11: ' ', data2: '6月2日', data20: '6月23日', data21: ' ', data22: ' ', data3: '7月7日', data30: '7月21日', data31: ' ', data32: ' ', data4: '8月4日', data40: '8月18日', data41: ' ', data42: ' ', data5: '9月1日', data51: '9月22日', data52: ' ', data53: ' ' },\r\n                { id: '15', name: '彭金炎', data1: '5月25日', data10: ' ', data11: ' ', data2: '6月15日', data20: '6月29日', data21: ' ', data22: ' ', data3: ' ', data30: '7月27日', data31: ' ', data32: ' ', data4: '8月10日', data40: '8月24日', data41: ' ', data42: ' ', data5: '9月2日', data51: '9月28日', data52: ' ', data53: ' ' },\r\n\r\n            ],\r\n            styleAll: {\r\n                width: '100%',\r\n                height: '25rem'\r\n            },\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            total: 0,\r\n            SelectList: {},//选中\r\n            arrName: '',\r\n            videoToken: '',\r\n            videoUrl: '',\r\n            scrolltimer: '',\r\n            Accumulation: []\r\n        }\r\n    },\r\n    mounted() {\r\n        // this.initPlayer();\r\n        this.autoScroll()\r\n    },\r\n    destroyed() {\r\n        this.autoScroll(true)\r\n    },\r\n    watch: {\r\n        // dataChannelText(nv) {\r\n        //     console.log(nv, 'table收到信息')\r\n\r\n\r\n        // },\r\n\r\n        arrName(nv) {\r\n            if (nv != '积水预警事件') {\r\n                this.hotT = true\r\n            }\r\n        },\r\n        allTime(newVal) {\r\n            let params = ''\r\n            if (newVal) {\r\n                params = {\r\n                    startTime: newVal[0],\r\n                    endTime: newVal[1]\r\n                }\r\n            }\r\n            getWaterEasyAccumulation(params).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.Accumulation = { name: '积水预警事件', data: res.data.extra }\r\n                    this.$store.commit('warningType/getAccumulationData', this.Accumulation)\r\n                }\r\n            })\r\n        },\r\n        accumulationArr: {\r\n            handler(val) {\r\n                this.scrollList = []\r\n                let { name, data } = val\r\n                this.arrName = name\r\n                // 表格数据展示\r\n                if (name == \"积水预警事件\") {\r\n                    // this.headerList = [\"序号\", \"名称\", \"设备状态\", \"发生时间\", \"地址\"],\r\n                    data.forEach((item, index) => {\r\n                        let { addTime, responsePersonName, nameLocation, responseUnit, id, pointY, pointX } = item\r\n                        let isStatus = responseUnit == 1 ? '在线' : '离线'\r\n                        item.isStatus = isStatus\r\n                        // this.scrollList.push([id, responsePersonName, isStatus, addTime, nameLocation])\r\n                        this.scrollList.push({ id, responsePersonName, isStatus, addTime, nameLocation, pointY, pointX })\r\n                    })\r\n                } else if (name == \"河道液位预警\") {\r\n                    // this.headerList = [\"序号\", \"名称\", \"溶解氧\", \"时间\", \"高锰酸盐\", \"氨氢\"]\r\n                    data.forEach((item, index) => {\r\n                        let { samplingTime, locationName, dissolvedOxygen, permanganate, nhn, id } = item\r\n                        // this.scrollList.push([id, locationName, dissolvedOxygen, samplingTime, permanganate, nhn])\r\n                        this.scrollList.push({ id, locationName, dissolvedOxygen, samplingTime, permanganate, nhn })\r\n                    })\r\n                }\r\n                /* else if (name == \"剖面仪预警\") {\r\n                   // this.headerList = [\"序号\", \"名称\", \"设备状态\", \"时间\", \"实时水位(m)\", \"水位状态\"]\r\n                   data.forEach((item, index) => {\r\n                       let { gatherTime, monitoringPointName, status, waterLevelNow, waterStatus, id } = item\r\n                       let isStatus = status == 1 ? '在线' : '离线'\r\n                       item.isStatus = isStatus\r\n                       let statusV = this.getStatus(waterStatus)\r\n                       item.statusVx = statusV\r\n                       // this.scrollList.push([id, monitoringPointName, isStatus, gatherTime, waterLevelNow, statusV])\r\n                       this.scrollList.push({ id, monitoringPointName, isStatus, gatherTime, waterLevelNow, statusV })\r\n                   })\r\n               }  */\r\n                else if (name == \"河道水位预警\") {\r\n                    // this.headerList = [\"名称\", \"设备状态\", \"时间\", \"实时水位(m)\", \"水位状态\"]\r\n                    data.forEach((item, index) => {\r\n                        let { name, value, road, position, recordTime } = item\r\n                        let isValue = 0\r\n                        if (value <= 2.6) {\r\n                            isValue = 2.6\r\n                        } else if (value > 2.6 && value <= 2.8) {\r\n                            isValue = 3.0\r\n                        } else if (value > 2.8) {\r\n                            isValue = 3.2\r\n                        }\r\n                        item.isValue = parseFloat(isValue.toFixed(1))\r\n                        isValue = parseFloat(isValue.toFixed(1));\r\n                        // this.scrollList.push([  name, value, road, position])\r\n                        this.scrollList.push({ name, value, isValue, road, position, recordTime })\r\n                    })\r\n                }\r\n                else if (name == \"一体化智能杆\") {\r\n                    // this.headerList = [\"序号\", \"设备名称\", \"设备状态\", \"设备类别\", \"安装位置\"]\r\n                    data.forEach((item, index) => {\r\n                        let { id, equipmentName, equipmentStatus, equipmentCategoryName, nameLocation, updateTime } = item\r\n                        let isStatus = equipmentStatus == 1 ? '正常' : '不正常'\r\n                        item.isStatus = isStatus\r\n                        // this.scrollList.push([id, equipmentName, isStatus, equipmentCategoryName, nameLocation ])\r\n                        this.scrollList.push({ id, equipmentName, isStatus, equipmentCategoryName, nameLocation, updateTime })\r\n                    })\r\n                } else if (name == \"防汛值班表\") {\r\n                    // this.headerList = [\"序号\", \"姓名\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\"]\r\n                    this.scrollList = this.peopleList\r\n                    // this.$store.commit('action/getIsScreenShow', false)\r\n                }\r\n                this.tableList = val\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['dataChannelText', 'larksr']),\r\n        ...mapState('warningType', ['accumulationArr', 'currentIndex']),\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 点击行展示点位\r\n        handleRowClick(row, column, event, index) {\r\n            // // 清除点位\r\n            // let params = {\r\n            //     name: this.arrName,\r\n            //     flag: false,\r\n            // }\r\n            // this.$eventBus.$emit('senTxtToUe', params);\r\n            // //筛选数据并撒点\r\n            // this.scrollList.forEach(item => {\r\n            //     if (item.id == row.id) {\r\n            //         let params = {\r\n            //             name: this.arrName,\r\n            //             data: [item],\r\n            //             flag: true,\r\n            //         }\r\n            //         console.log(params, 'params')\r\n            //         this.$eventBus.$emit('senTxtToUe', params);\r\n            //     }\r\n            // })\r\n        },\r\n        autoScroll(stop) {\r\n            const table = this.$refs.scroll_Table\r\n            const divData = table.$refs.bodyWrapper\r\n            if (stop) {\r\n                window.clearInterval(this.scrolltimer)\r\n            } else {\r\n                this.scrolltimer = window.setInterval(() => {\r\n                    divData.scrollTop += 1\r\n                    if (divData.scrollTop >= divData.scrollHeight - divData.clientHeight) {\r\n                        divData.scrollTop = 0\r\n                    }\r\n                }, 10)\r\n            }\r\n        },\r\n        openHot(flag) {\r\n            this.hotT = !flag\r\n            let params = {\r\n                name: \"积水热力图\",\r\n                number: 4,\r\n                flag\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n            console.log('params', params)\r\n        },\r\n        clearPoint(name) {\r\n            let params = {\r\n                name: name,\r\n                flag: false\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        getStatus(status) {\r\n            let statusV;\r\n            if (status == 1) statusV = '正常'\r\n            else if (status == 2) statusV = '三级预警'\r\n            else if (status == 3) statusV = '二级预警'\r\n            else if (status == 4) statusV = '一级预警'\r\n            return statusV;\r\n        },\r\n        // 选中一个事件\r\n        getInfo_Option(val) {\r\n            this.SelectList = this.tableList.data[val.rowIndex]\r\n            this.SelectList.typename = this.tableList.name\r\n            this.SelectList.Name = val.row[1]\r\n            this.SelectList.eventTime = val.row[3]\r\n\r\n            this.$store.commit(\"warningType/getSelectEventInfoOne\", this.SelectList)\r\n\r\n\r\n            this.$store.commit(\"warningType/getIsShowEventInfoOne\", true)\r\n        },\r\n        close() {\r\n            this.clearPoint(this.arrName)\r\n            this.$store.commit('warningType/getCurrentIndex', -1)\r\n            this.$store.commit('warningType/getIsShowAccumulationArr', false)\r\n            this.$store.commit('action/getIsScreenShow', true)\r\n            this.$store.commit('warningType/getAccumulationData', { name: '事件列表', data: '' })\r\n            // this.$store.commit('warningType/getIsShowAccumulationArr', false)\r\n        },\r\n\r\n    },\r\n\r\n}\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.time {\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n\r\n    .timeTxt {\r\n        margin-left: 20px;\r\n        margin-right: 20px;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .hotT {\r\n        margin-bottom: 5px;\r\n        float: right;\r\n        margin-right: 10px;\r\n        font-size: 24px;\r\n        width: 180px;\r\n        height: 50px;\r\n        line-height: 50px;\r\n        background-color: rgba(46, 101, 167, 0.8);\r\n        text-align: center;\r\n    }\r\n}\r\n\r\n\r\n::v-deep .el-input__inner {\r\n    border: none !important;\r\n    // color: #fff !important;\r\n    background-color: rgba(46, 101, 167, 0.5) !important;\r\n}\r\n\r\n::v-deep .el-date-editor {\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n::v-deep .el-range-input,\r\n::v-deep .el-range-separator,\r\n::v-deep .el-range__icon {\r\n    color: #fff !important;\r\n    background-color: rgba(46, 101, 167, 0.5) !important;\r\n}\r\n\r\n::v-deep .el-range-separator {\r\n    line-height: 50px !important;\r\n}\r\n\r\n/deep/ .el-date-editor input::placeholder {\r\n    color: #fff;\r\n}\r\n\r\n::v-deep .el-range__close-icon {\r\n    background-color: rgba(46, 101, 167, 0.5) !important;\r\n}\r\n\r\n\r\n\r\n::v-deep .el-table {\r\n    border: none !important;\r\n    background-color: rgba(46, 101, 167, 0)\r\n}\r\n\r\n::v-deep .el-table__body td {\r\n    // font-size: 18px;\r\n    padding: 25px 0;\r\n    border: none !important;\r\n\r\n    div {\r\n        overflow: hidden;\r\n        /* 隐藏超出部分 */\r\n        white-space: nowrap;\r\n        /* 不允许换行 */\r\n        text-overflow: ellipsis;\r\n        /* 显示省略号 */\r\n    }\r\n}\r\n\r\n::v-deep .el-table__body td {\r\n    font-size: 26px;\r\n}\r\n\r\n::v-deep .el-table__header th {\r\n    font-size: 28px;\r\n\r\n    .cell {\r\n        width: 100%;\r\n        height: 100%;\r\n        line-height: 28px;\r\n    }\r\n}\r\n\r\n::v-deep .el-table__header {\r\n    tr {\r\n        background-color: rgba(46, 101, 167, 0.5);\r\n    }\r\n\r\n    th {\r\n        font-size: 28px;\r\n        border: none !important;\r\n    }\r\n}\r\n\r\n\r\n// ::v-deep .el-table__header \r\n::v-deep .el-table__row {\r\n    background-color: rgba(46, 101, 167, 0.1);\r\n}\r\n\r\n::v-deep .el-table__body-wrapper {\r\n    background-color: rgba(46, 101, 167, 0.1);\r\n}\r\n\r\n.warningTable {\r\n    position: absolute;\r\n    top: 45%;\r\n    left: 55%;\r\n    transform: translateX(-50%);\r\n    pointer-events: stroke;\r\n\r\n    .top {\r\n        width: calc(100%);\r\n        display: flex;\r\n        margin-top: 1rem;\r\n\r\n        .titleBox {\r\n            width: 90%;\r\n            padding-top: .5rem;\r\n\r\n            .title {\r\n                width: calc(40%);\r\n                // margin-left: 400px;\r\n                // margin-top: 20px;\r\n                margin: 10px auto;\r\n                text-align: center;\r\n                background: url(\"@/assets/images/event/titleWing.png\") no-repeat 100%;\r\n                background-size: 100%;\r\n                // padding-left: calc(8%);\r\n\r\n                span {\r\n                    display: inline-block;\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 700;\r\n                    font-size: 43px;\r\n                    background-image: -webkit-linear-gradient(90.00001393970153deg, #fdfdfd 0%, #bbe8ff 100%);\r\n                    -webkit-background-clip: text;\r\n                    -webkit-text-fill-color: transparent;\r\n                }\r\n\r\n            }\r\n        }\r\n\r\n        .delete {\r\n            width: 5%;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            padding: 10px;\r\n        }\r\n    }\r\n\r\n    .mainContent {\r\n        .tableShowData {\r\n            box-sizing: border-box;\r\n            padding: 5px 18px;\r\n            width: 100%;\r\n            // font-size: 32px;\r\n        }\r\n\r\n    }\r\n\r\n    .btnPage_num {\r\n\r\n        .leftArrow,\r\n        .rightArrow {\r\n            width: 3rem;\r\n            margin: 0 .5rem;\r\n            font-size: 1rem;\r\n        }\r\n\r\n        img {\r\n            width: 100%;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n}\r\n\r\n#ezuikit-player {\r\n    width: 800px;\r\n    height: 400px;\r\n}\r\n</style>"], "mappings": "AAkLA,OAAAA,YAAA;AACA,SAAAC,wBAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAJ;EACA;EACAK,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,IAAA;MACA;MACAC,UAAA,GACA;QAAAC,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAnB,EAAA;QAAAR,IAAA;QAAAS,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,EAEA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,UAAA;MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAC,UAAA;EACA;EACAC,UAAA;IACA,KAAAD,UAAA;EACA;EACAE,KAAA;IACA;IACA;;IAGA;;IAEAR,QAAAS,EAAA;MACA,IAAAA,EAAA;QACA,KAAAvC,IAAA;MACA;IACA;IACAH,QAAA2C,MAAA;MACA,IAAAC,MAAA;MACA,IAAAD,MAAA;QACAC,MAAA;UACAC,SAAA,EAAAF,MAAA;UACAG,OAAA,EAAAH,MAAA;QACA;MACA;MACAhD,wBAAA,CAAAiD,MAAA,EAAAG,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAjD,IAAA,CAAAkD,IAAA;UACA,KAAAZ,YAAA;YAAAxC,IAAA;YAAAE,IAAA,EAAAiD,GAAA,CAAAjD,IAAA,CAAAmD;UAAA;UACA,KAAAC,MAAA,CAAAC,MAAA,yCAAAf,YAAA;QACA;MACA;IACA;IACAgB,eAAA;MACAC,QAAAC,GAAA;QACA,KAAArD,UAAA;QACA;UAAAL,IAAA;UAAAE;QAAA,IAAAwD,GAAA;QACA,KAAAtB,OAAA,GAAApC,IAAA;QACA;QACA,IAAAA,IAAA;UACA;UACAE,IAAA,CAAAyD,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACA;cAAAC,OAAA;cAAAC,kBAAA;cAAAC,YAAA;cAAAC,YAAA;cAAAzD,EAAA;cAAA0D,MAAA;cAAAC;YAAA,IAAAP,IAAA;YACA,IAAAQ,QAAA,GAAAH,YAAA;YACAL,IAAA,CAAAQ,QAAA,GAAAA,QAAA;YACA;YACA,KAAA/D,UAAA,CAAAgE,IAAA;cAAA7D,EAAA;cAAAuD,kBAAA;cAAAK,QAAA;cAAAN,OAAA;cAAAE,YAAA;cAAAE,MAAA;cAAAC;YAAA;UACA;QACA,WAAAnE,IAAA;UACA;UACAE,IAAA,CAAAyD,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACA;cAAAS,YAAA;cAAAC,YAAA;cAAAC,eAAA;cAAAC,YAAA;cAAAC,GAAA;cAAAlE;YAAA,IAAAoD,IAAA;YACA;YACA,KAAAvD,UAAA,CAAAgE,IAAA;cAAA7D,EAAA;cAAA+D,YAAA;cAAAC,eAAA;cAAAF,YAAA;cAAAG,YAAA;cAAAC;YAAA;UACA;QACA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAXA,KAYA,IAAA1E,IAAA;UACA;UACAE,IAAA,CAAAyD,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACA;cAAA7D,IAAA;cAAA2E,KAAA;cAAAC,IAAA;cAAAC,QAAA;cAAAC;YAAA,IAAAlB,IAAA;YACA,IAAAmB,OAAA;YACA,IAAAJ,KAAA;cACAI,OAAA;YACA,WAAAJ,KAAA,UAAAA,KAAA;cACAI,OAAA;YACA,WAAAJ,KAAA;cACAI,OAAA;YACA;YACAnB,IAAA,CAAAmB,OAAA,GAAAC,UAAA,CAAAD,OAAA,CAAAE,OAAA;YACAF,OAAA,GAAAC,UAAA,CAAAD,OAAA,CAAAE,OAAA;YACA;YACA,KAAA5E,UAAA,CAAAgE,IAAA;cAAArE,IAAA;cAAA2E,KAAA;cAAAI,OAAA;cAAAH,IAAA;cAAAC,QAAA;cAAAC;YAAA;UACA;QACA,OACA,IAAA9E,IAAA;UACA;UACAE,IAAA,CAAAyD,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACA;cAAArD,EAAA;cAAA0E,aAAA;cAAAC,eAAA;cAAAC,qBAAA;cAAApB,YAAA;cAAAqB;YAAA,IAAAzB,IAAA;YACA,IAAAQ,QAAA,GAAAe,eAAA;YACAvB,IAAA,CAAAQ,QAAA,GAAAA,QAAA;YACA;YACA,KAAA/D,UAAA,CAAAgE,IAAA;cAAA7D,EAAA;cAAA0E,aAAA;cAAAd,QAAA;cAAAgB,qBAAA;cAAApB,YAAA;cAAAqB;YAAA;UACA;QACA,WAAArF,IAAA;UACA;UACA,KAAAK,UAAA,QAAAE,UAAA;UACA;QACA;QACA,KAAAH,SAAA,GAAAsD,GAAA;MACA;MACA4B,SAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAxF,QAAA;IACA,GAAAA,QAAA;EAEA;EAEAyF,OAAA;IACA;IACAC,eAAAC,GAAA,EAAAC,MAAA,EAAAC,KAAA,EAAA/B,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAnB,WAAAmD,IAAA;MACA,MAAAC,KAAA,QAAAC,KAAA,CAAAC,YAAA;MACA,MAAAC,OAAA,GAAAH,KAAA,CAAAC,KAAA,CAAAG,WAAA;MACA,IAAAL,IAAA;QACAM,MAAA,CAAAC,aAAA,MAAA7D,WAAA;MACA;QACA,KAAAA,WAAA,GAAA4D,MAAA,CAAAE,WAAA;UACAJ,OAAA,CAAAK,SAAA;UACA,IAAAL,OAAA,CAAAK,SAAA,IAAAL,OAAA,CAAAM,YAAA,GAAAN,OAAA,CAAAO,YAAA;YACAP,OAAA,CAAAK,SAAA;UACA;QACA;MACA;IACA;IACAG,QAAAC,IAAA;MACA,KAAApG,IAAA,IAAAoG,IAAA;MACA,IAAA3D,MAAA;QACA/C,IAAA;QACA2G,MAAA;QACAD;MACA;MACA,KAAAE,SAAA,CAAAC,KAAA,eAAA9D,MAAA;MACA+D,OAAA,CAAAC,GAAA,WAAAhE,MAAA;IACA;IACAiE,WAAAhH,IAAA;MACA,IAAA+C,MAAA;QACA/C,IAAA,EAAAA,IAAA;QACA0G,IAAA;MACA;MACA,KAAAE,SAAA,CAAAC,KAAA,eAAA9D,MAAA;IACA;IACAkE,UAAAC,MAAA;MACA,IAAAC,OAAA;MACA,IAAAD,MAAA,OAAAC,OAAA,aACA,IAAAD,MAAA,OAAAC,OAAA,eACA,IAAAD,MAAA,OAAAC,OAAA,eACA,IAAAD,MAAA,OAAAC,OAAA;MACA,OAAAA,OAAA;IACA;IACA;IACAC,eAAA1D,GAAA;MACA,KAAAvB,UAAA,QAAA/B,SAAA,CAAAF,IAAA,CAAAwD,GAAA,CAAA2D,QAAA;MACA,KAAAlF,UAAA,CAAAmF,QAAA,QAAAlH,SAAA,CAAAJ,IAAA;MACA,KAAAmC,UAAA,CAAAoF,IAAA,GAAA7D,GAAA,CAAAgC,GAAA;MACA,KAAAvD,UAAA,CAAAqF,SAAA,GAAA9D,GAAA,CAAAgC,GAAA;MAEA,KAAApC,MAAA,CAAAC,MAAA,2CAAApB,UAAA;MAGA,KAAAmB,MAAA,CAAAC,MAAA;IACA;IACAkE,MAAA;MACA,KAAAT,UAAA,MAAA5E,OAAA;MACA,KAAAkB,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;QAAAvD,IAAA;QAAAE,IAAA;MAAA;MACA;IACA;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}