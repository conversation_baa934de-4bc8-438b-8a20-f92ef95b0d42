{"ast": null, "code": "import \"@/assets/css/btnStyle.css\";\nexport default {\n  name: 'FootBtn',\n  props: {\n    changeScreenValue: {\n      type: String,\n      default: \"\"\n    }\n  },\n  data() {\n    return {\n      menuList: [{\n        name: \"智慧水务\",\n        router: '/home/<USER>'\n      }, {\n        name: '综合态势',\n        router: '/home/<USER>'\n      }, {\n        name: \"民生诉求\",\n        router: \"/home/<USER>\"\n      }]\n    };\n  },\n  mounted() {},\n  beforeDestroy() {},\n  methods: {\n    clickBtn(e) {\n      this.$emit('sendScreenBtn', e.target.innerText);\n      this.menuList.map(item => {\n        if (item.name === e.target.innerText && item.name != this.changeScreenValue) {\n          this.$router.push({\n            path: item.router\n          });\n        }\n      });\n      if (e.target.classList.contains(\"selected\")) {\n        e.target.classList.remove(\"selected\");\n        if (!e.target.parentNode.classList.contains(\"radmenu\")) {\n          e.target.parentNode.parentNode.parentNode.querySelector(\"a\").classList.add(\"selected\");\n        } else {\n          e.target.classList.add(\"show\");\n        }\n      } else {\n        e.target.classList.add(\"selected\");\n        if (!e.target.parentNode.classList.contains(\"radmenu\")) {\n          e.target.parentNode.parentNode.parentNode.querySelector(\"a\").classList.remove(\"selected\");\n        } else {\n          e.target.classList.remove(\"show\");\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "changeScreenValue", "type", "String", "default", "data", "menuList", "router", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "clickBtn", "e", "$emit", "target", "innerText", "map", "item", "$router", "push", "path", "classList", "contains", "remove", "parentNode", "querySelector", "add"], "sources": ["src/components/FootBtn.vue"], "sourcesContent": ["<template>\r\n    <div class=\"annular\">\r\n        <div class=\"radmenu\"><a href=\"#\" class=\"selected\" @click=\"clickBtn\">{{changeScreenValue}}</a>\r\n\r\n            <ul>\r\n                <li v-for=\"item of menuList\"><a href=\"#\" @click=\"clickBtn\">{{item.name}}</a></li>\r\n            </ul>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport \"@/assets/css/btnStyle.css\"\r\n\r\nexport default {\r\n    name: 'FootBtn',\r\n    props: {\r\n        changeScreenValue:{\r\n            type:String,\r\n            default:\"\",\r\n            \r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            menuList:[{\r\n                name:\"智慧水务\",\r\n                router:'/home/<USER>'\r\n            },{\r\n                name:'综合态势',\r\n                router:'/home/<USER>'\r\n            },{\r\n                name:\"民生诉求\",\r\n                router:\"/home/<USER>\"\r\n            }]\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n    },\r\n\r\n\r\n    beforeDestroy() {\r\n\r\n    },\r\n    methods: {\r\n\r\n        clickBtn(e) {\r\n            this.$emit('sendScreenBtn',e.target.innerText)\r\n             this.menuList.map(item=>{\r\n                if(item.name===e.target.innerText && item.name != this.changeScreenValue){\r\n                    this.$router.push({\r\n                        path:item.router\r\n                    })\r\n                }\r\n             })\r\n            if (e.target.classList.contains(\"selected\")) {\r\n                e.target.classList.remove(\"selected\");\r\n                if (!e.target.parentNode.classList.contains(\"radmenu\")) {\r\n                    e.target.parentNode.parentNode.parentNode.querySelector(\"a\").classList.add(\"selected\")\r\n                } else {\r\n                    e.target.classList.add(\"show\");\r\n                }\r\n            } else {\r\n                e.target.classList.add(\"selected\");\r\n                if (!e.target.parentNode.classList.contains(\"radmenu\")) {\r\n                    e.target.parentNode.parentNode.parentNode.querySelector(\"a\").classList.remove(\"selected\")\r\n                } else {\r\n                    e.target.classList.remove(\"show\");\r\n                }\r\n            }\r\n        }\r\n    },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped></style>"], "mappings": "AAYA;AAEA;EACAA,IAAA;EACAC,KAAA;IACAC,iBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IAEA;EACA;EACAC,KAAA;IACA;MACAC,QAAA;QACAP,IAAA;QACAQ,MAAA;MACA;QACAR,IAAA;QACAQ,MAAA;MACA;QACAR,IAAA;QACAQ,MAAA;MACA;IACA;EACA;EAEAC,QAAA,GACA;EAGAC,cAAA,GAEA;EACAC,OAAA;IAEAC,SAAAC,CAAA;MACA,KAAAC,KAAA,kBAAAD,CAAA,CAAAE,MAAA,CAAAC,SAAA;MACA,KAAAT,QAAA,CAAAU,GAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAlB,IAAA,KAAAa,CAAA,CAAAE,MAAA,CAAAC,SAAA,IAAAE,IAAA,CAAAlB,IAAA,SAAAE,iBAAA;UACA,KAAAiB,OAAA,CAAAC,IAAA;YACAC,IAAA,EAAAH,IAAA,CAAAV;UACA;QACA;MACA;MACA,IAAAK,CAAA,CAAAE,MAAA,CAAAO,SAAA,CAAAC,QAAA;QACAV,CAAA,CAAAE,MAAA,CAAAO,SAAA,CAAAE,MAAA;QACA,KAAAX,CAAA,CAAAE,MAAA,CAAAU,UAAA,CAAAH,SAAA,CAAAC,QAAA;UACAV,CAAA,CAAAE,MAAA,CAAAU,UAAA,CAAAA,UAAA,CAAAA,UAAA,CAAAC,aAAA,MAAAJ,SAAA,CAAAK,GAAA;QACA;UACAd,CAAA,CAAAE,MAAA,CAAAO,SAAA,CAAAK,GAAA;QACA;MACA;QACAd,CAAA,CAAAE,MAAA,CAAAO,SAAA,CAAAK,GAAA;QACA,KAAAd,CAAA,CAAAE,MAAA,CAAAU,UAAA,CAAAH,SAAA,CAAAC,QAAA;UACAV,CAAA,CAAAE,MAAA,CAAAU,UAAA,CAAAA,UAAA,CAAAA,UAAA,CAAAC,aAAA,MAAAJ,SAAA,CAAAE,MAAA;QACA;UACAX,CAAA,CAAAE,MAAA,CAAAO,SAAA,CAAAE,MAAA;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}