{"ast": null, "code": "//城市管理\nimport cityManageLeft from './components/city-manage/left-view.vue';\nimport cityManageRight from './components/city-manage/right-view.vue';\n//城市感知\nimport cityPerceptionLeft from \"./components/city-perception/left-view.vue\";\n//联合指挥\nimport jointCommandLeft from \"./components/joint-command/left-view.vue\";\nimport jointCommandRight from \"./components/joint-command/right-view.vue\";\n//决策分析\nimport decisionAnalysisLeft from \"./components/decision-analysis/left-view.vue\";\nimport { mapState } from 'vuex';\nexport default {\n  name: 'comprehensiveSituation',\n  data() {\n    return {\n      leftPanel: true,\n      rightPanel: false,\n      rightText: '重点区域',\n      leftView: cityPerceptionLeft,\n      rightView: null\n    };\n  },\n  components: {},\n  watch: {\n    // 监听tabs切换组件\n    footerTbsItem: {\n      handler(nv) {\n        this.$store.commit(\"action/getEventType\", false);\n        this.leftPanel = true;\n        this.rightPanel = false;\n        this.deletePoi();\n        switch (nv?.item) {\n          case \"综合态势\":\n            this.leftView = cityPerceptionLeft;\n            this.rightView = null;\n            this.rightText = null;\n            break;\n          case \"城市管理\":\n            this.leftView = cityManageLeft;\n            this.rightView = cityManageRight;\n            this.rightText = \"重点区域\";\n            break;\n          case \"城市感知\":\n            this.leftView = cityPerceptionLeft;\n            this.rightView = null;\n            break;\n          case \"联合指挥\":\n            this.leftView = jointCommandLeft;\n            this.rightView = jointCommandRight;\n            this.rightText = \"事件监控\";\n            break;\n          case \"决策分析\":\n            this.leftView = decisionAnalysisLeft;\n            this.rightView = null;\n            break;\n        }\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState({\n      footerTbsItem: state => state.footerTbsItem\n    })\n  },\n  mounted() {\n    if (this.$route.query.refresh) {}\n  },\n  updated() {},\n  methods: {\n    showEvent(e) {\n      this.rightPanel = false;\n    },\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    isShowView(type) {\n      if (type == 1) {\n        this.leftPanel = !this.leftPanel;\n      } else {\n        this.rightPanel = !this.rightPanel;\n        this.$store.commit(\"action/getEventType\", false);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["cityManageLeft", "cityManageRight", "cityPerceptionLeft", "jointCommandLeft", "jointCommandRight", "decisionAnalysisLeft", "mapState", "name", "data", "leftPanel", "rightPanel", "rightText", "leftView", "<PERSON><PERSON><PERSON><PERSON>", "components", "watch", "footerTbsItem", "handler", "nv", "$store", "commit", "deletePoi", "item", "immediate", "computed", "state", "mounted", "$route", "query", "refresh", "updated", "methods", "showEvent", "e", "params", "$eventBus", "$emit", "isShowView", "type"], "sources": ["src/views/comprehensiveSituation/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <div class=\"left-box\" :style=\"{ left: leftPanel ? '20px' : '-750px' }\">\r\n            <component :is=\"leftView\"></component>\r\n            <img v-if=\"leftPanel\" @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/left-btn.png\"\r\n                alt=\"\">\r\n            <img v-else @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n        </div>\r\n        <div class=\"right-box\" v-if=\"rightView\" :style=\"{ right: rightPanel ? '20px' : '-750px' }\">\r\n            <component :is=\"rightView\" @showEvent=\"showEvent\"></component>\r\n            <p v-if=\"rightPanel\" @click=\"isShowView(2)\">\r\n                <!-- <span>{{ rightText }}</span> -->\r\n                <img src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n            </p>\r\n            <p v-else @click=\"isShowView(2)\">\r\n                <span>{{ rightText }}</span>\r\n                <img src=\"@/assets/images/comprehensiveSituation/long-left-btn.png\" alt=\"\">\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n//城市管理\r\nimport cityManageLeft from './components/city-manage/left-view.vue';\r\nimport cityManageRight from './components/city-manage/right-view.vue';\r\n//城市感知\r\nimport cityPerceptionLeft from \"./components/city-perception/left-view.vue\"\r\n//联合指挥\r\nimport jointCommandLeft from \"./components/joint-command/left-view.vue\"\r\nimport jointCommandRight from \"./components/joint-command/right-view.vue\"\r\n//决策分析\r\nimport decisionAnalysisLeft from \"./components/decision-analysis/left-view.vue\"\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'comprehensiveSituation',\r\n    data() {\r\n        return {\r\n            leftPanel: true,\r\n            rightPanel: false,\r\n            rightText: '重点区域',\r\n            leftView: cityPerceptionLeft,\r\n            rightView: null\r\n        };\r\n    },\r\n\r\n    components: {\r\n    },\r\n    watch: {\r\n        // 监听tabs切换组件\r\n        footerTbsItem: {\r\n            handler(nv) {\r\n                this.$store.commit(\"action/getEventType\", false)\r\n                this.leftPanel = true;\r\n                this.rightPanel = false;\r\n                this.deletePoi();\r\n                switch (nv?.item) {\r\n                    case \"综合态势\":\r\n                        this.leftView = cityPerceptionLeft;\r\n                        this.rightView = null;\r\n                        this.rightText = null\r\n                        break;\r\n                    case \"城市管理\":\r\n                        this.leftView = cityManageLeft;\r\n                        this.rightView = cityManageRight;\r\n                        this.rightText = \"重点区域\"\r\n                        break;\r\n                    case \"城市感知\": this.leftView = cityPerceptionLeft; this.rightView = null;\r\n                        break;\r\n                    case \"联合指挥\":\r\n                        this.leftView = jointCommandLeft;\r\n                        this.rightView = jointCommandRight;\r\n                        this.rightText = \"事件监控\";\r\n                        break;\r\n                    case \"决策分析\":\r\n                        this.leftView = decisionAnalysisLeft;\r\n                        this.rightView = null;\r\n                        break;\r\n                }\r\n            },\r\n            immediate: true\r\n\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            footerTbsItem: state => state.footerTbsItem,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n        if (this.$route.query.refresh) {\r\n        }\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        showEvent(e) {\r\n            this.rightPanel = false;\r\n\r\n        },\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        isShowView(type) {\r\n            if (type == 1) {\r\n                this.leftPanel = !this.leftPanel;\r\n            } else {\r\n                this.rightPanel = !this.rightPanel;\r\n                this.$store.commit(\"action/getEventType\", false)\r\n            }\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.container {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    .left-box {\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        width: 750px;\r\n        height: 1960px;\r\n        position: fixed;\r\n        background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        left: 20px;\r\n        // margin-left: 20px;\r\n\r\n        >img {\r\n            cursor: pointer;\r\n            width: 103px;\r\n            height: 93px;\r\n            position: absolute;\r\n            left: 750px;\r\n            top: calc(50% - 46.5px);\r\n\r\n        }\r\n    }\r\n\r\n    .right-box {\r\n        // margin-right: 20px;\r\n        width: 750px;\r\n        height: 1960px;\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        position: fixed;\r\n        background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        right: 20px;\r\n\r\n        >p {\r\n            width: 103px;\r\n            height: 338px;\r\n            position: absolute;\r\n            right: 750px;\r\n            top: calc(50% - 169px);\r\n            cursor: pointer;\r\n\r\n            >span {\r\n                width: 40px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 48px;\r\n                color: #FFFFFF;\r\n                line-height: 56px;\r\n                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\r\n                position: absolute;\r\n                top: 96px;\r\n                left: 28px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAwBA;AACA,OAAAA,cAAA;AACA,OAAAC,eAAA;AACA;AACA,OAAAC,kBAAA;AACA;AACA,OAAAC,gBAAA;AACA,OAAAC,iBAAA;AACA;AACA,OAAAC,oBAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA,EAAAV,kBAAA;MACAW,SAAA;IACA;EACA;EAEAC,UAAA,GACA;EACAC,KAAA;IACA;IACAC,aAAA;MACAC,QAAAC,EAAA;QACA,KAAAC,MAAA,CAAAC,MAAA;QACA,KAAAX,SAAA;QACA,KAAAC,UAAA;QACA,KAAAW,SAAA;QACA,QAAAH,EAAA,EAAAI,IAAA;UACA;YACA,KAAAV,QAAA,GAAAV,kBAAA;YACA,KAAAW,SAAA;YACA,KAAAF,SAAA;YACA;UACA;YACA,KAAAC,QAAA,GAAAZ,cAAA;YACA,KAAAa,SAAA,GAAAZ,eAAA;YACA,KAAAU,SAAA;YACA;UACA;YAAA,KAAAC,QAAA,GAAAV,kBAAA;YAAA,KAAAW,SAAA;YACA;UACA;YACA,KAAAD,QAAA,GAAAT,gBAAA;YACA,KAAAU,SAAA,GAAAT,iBAAA;YACA,KAAAO,SAAA;YACA;UACA;YACA,KAAAC,QAAA,GAAAP,oBAAA;YACA,KAAAQ,SAAA;YACA;QACA;MACA;MACAU,SAAA;IAEA;EACA;EACAC,QAAA;IACA,GAAAlB,QAAA;MACAU,aAAA,EAAAS,KAAA,IAAAA,KAAA,CAAAT;IACA;EACA;EAEAU,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,GACA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,UAAAC,CAAA;MACA,KAAAvB,UAAA;IAEA;IACAW,UAAA;MACA,IAAAa,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAG,WAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA7B,SAAA,SAAAA,SAAA;MACA;QACA,KAAAC,UAAA,SAAAA,UAAA;QACA,KAAAS,MAAA,CAAAC,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}