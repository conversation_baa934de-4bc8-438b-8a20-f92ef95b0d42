{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"container\"\n  }, [_c(\"div\", {\n    staticClass: \"tabs disFlex\"\n  }, _vm._l(_vm.tabsList, function (item, index) {\n    return _c(\"div\", {\n      key: index,\n      staticClass: \"tabItems\",\n      class: {\n        activeTab: _vm.currentTabsValue == item\n      },\n      on: {\n        click: function ($event) {\n          return _vm.changeValue(item);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item) + \" \")]);\n  }), 0), _c(\"keep-alive\", [_c(_vm.setComponent, {\n    tag: \"component\"\n  })], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_l", "tabsList", "item", "index", "key", "class", "activeTab", "currentTabsValue", "on", "click", "$event", "changeValue", "_v", "_s", "setComponent", "tag", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/videoTypeOption/videoPlayerTree.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"container\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"tabs disFlex\" },\n        _vm._l(_vm.tabsList, function (item, index) {\n          return _c(\n            \"div\",\n            {\n              key: index,\n              staticClass: \"tabItems\",\n              class: { activeTab: _vm.currentTabsValue == item },\n              on: {\n                click: function ($event) {\n                  return _vm.changeValue(item)\n                },\n              },\n            },\n            [_vm._v(\" \" + _vm._s(item) + \" \")]\n          )\n        }),\n        0\n      ),\n      _c(\"keep-alive\", [_c(_vm.setComponent, { tag: \"component\" })], 1),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,QAAQ,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAC1C,OAAON,EAAE,CACP,KAAK,EACL;MACEO,GAAG,EAAED,KAAK;MACVJ,WAAW,EAAE,UAAU;MACvBM,KAAK,EAAE;QAAEC,SAAS,EAAEV,GAAG,CAACW,gBAAgB,IAAIL;MAAK,CAAC;MAClDM,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOd,GAAG,CAACe,WAAW,CAACT,IAAI,CAAC;QAC9B;MACF;IACF,CAAC,EACD,CAACN,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACX,IAAI,CAAC,GAAG,GAAG,CAAC,CACnC,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDL,EAAE,CAAC,YAAY,EAAE,CAACA,EAAE,CAACD,GAAG,CAACkB,YAAY,EAAE;IAAEC,GAAG,EAAE;EAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAClE,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBrB,MAAM,CAACsB,aAAa,GAAG,IAAI;AAE3B,SAAStB,MAAM,EAAEqB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}