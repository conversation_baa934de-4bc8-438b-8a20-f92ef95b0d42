{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"player-container\",\n    attrs: {\n      id: _vm.id + \"-container\"\n    }\n  }, [_vm.loading ? _c(\"div\", {\n    staticClass: \"player-loading mask\",\n    on: {\n      mouseover: function ($event) {\n        _vm.loadingHoverBottom = true;\n      },\n      mouseout: function ($event) {\n        _vm.loadingHoverBottom = false;\n      }\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.loadingHoverBottom && _vm.isFuse,\n      expression: \"loadingHoverBottom && isFuse\"\n    }],\n    staticClass: \"hover-top no-play-top\",\n    style: {\n      backgroundColor: _vm.topHoverColor\n    }\n  }, [_c(\"div\", {\n    staticClass: \"hover-top-title\"\n  }, [_vm._v(_vm._s(_vm.nameFieldDisplay === \"0\" ? _vm.cameraName || _vm.aliasName : _vm.aliasName || _vm.cameraName) + \" \")])]), _c(\"div\", {\n    staticClass: \"player-loading-icon\"\n  }, [_c(\"div\", {\n    staticClass: \"loading-icon\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.loadingTypeMap[_vm.loadingType][\"img\"]\n    }\n  })]), _c(\"div\", {\n    staticClass: \"loading-text\",\n    domProps: {\n      innerHTML: _vm._s(_vm.loadingTypeMap[_vm.loadingType][\"text\"])\n    }\n  })])]) : _vm._e(), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.enterCanvas,\n      expression: \"enterCanvas\"\n    }],\n    staticClass: \"player-mask mask\",\n    on: {\n      mouseover: function ($event) {\n        return _vm.hoverHandler(true);\n      },\n      mouseout: function ($event) {\n        return _vm.hoverHandler(false);\n      }\n    }\n  }, [_c(\"div\", {\n    staticClass: \"hover-top\",\n    style: {\n      backgroundColor: _vm.topHoverColor\n    }\n  }, [_c(\"div\", {\n    staticClass: \"hover-top-title\"\n  }, [_vm._v(_vm._s(_vm.nameFieldDisplay === \"0\" ? _vm.cameraName || _vm.aliasName : _vm.aliasName || _vm.cameraName) + \" \")]), _vm.showFull ? _c(\"div\", {\n    staticClass: \"fullscreen btn\",\n    on: {\n      click: _vm.fullScreen\n    }\n  }) : _vm._e(), _vm.showClose ? _c(\"div\", {\n    staticClass: \"close btn\",\n    on: {\n      click: _vm.close\n    }\n  }) : _vm._e()]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showStopAndPlay,\n      expression: \"showStopAndPlay\"\n    }],\n    staticClass: \"hover-center\"\n  }, [_c(\"div\", {\n    class: [\"player-btn\", _vm.playerType ? \"play\" : \"stop\"],\n    on: {\n      click: _vm.handleChangePlayType\n    }\n  })]), _c(\"div\", {\n    staticClass: \"hover-bottom\",\n    style: {\n      backgroundColor: _vm.bottomHoverColor\n    }\n  }, [_vm.cameraCode && _vm.bottomList && _vm.bottomList.length > 0 && !_vm.hideAllFunc ? _c(\"div\", {\n    staticClass: \"hover-bottom-inner\"\n  }) : _vm._e()])]), _c(\"canvas\", {\n    staticClass: \"waterText\",\n    attrs: {\n      id: \"water\" + _vm.id,\n      width: _vm.width,\n      height: _vm.getNewHeight\n    },\n    on: {\n      mouseover: function ($event) {\n        return _vm.hoverHandler(true);\n      },\n      mouseout: function ($event) {\n        return _vm.hoverHandler(false);\n      }\n    }\n  }), _c(\"canvas\", {\n    attrs: {\n      id: _vm.id,\n      width: _vm.width,\n      height: _vm.getNewHeight\n    }\n  }), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.components && _vm.components.length > 0 && _vm.show && !_vm.hideAllFunc,\n      expression: \"components && components.length > 0 && show && !hideAllFunc\"\n    }],\n    staticClass: \"canvasPlayer-slot\"\n  }, _vm._l(_vm.components, function (component, index) {\n    return _c(component.components ? component.components : null, {\n      key: index,\n      ref: \"bottomBtn\" + index,\n      refInFor: true,\n      tag: \"component\",\n      attrs: {\n        isFull: _vm.isFull\n      },\n      on: {\n        handleClick: _vm.handleComponentClick\n      }\n    });\n  }), 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "id", "loading", "on", "mouseover", "$event", "loadingHoverBottom", "mouseout", "directives", "name", "rawName", "value", "isFuse", "expression", "style", "backgroundColor", "topHoverColor", "_v", "_s", "nameFieldDisplay", "cameraName", "<PERSON><PERSON><PERSON>", "src", "loadingTypeMap", "loadingType", "domProps", "innerHTML", "_e", "enterCanvas", "hoverHandler", "showFull", "click", "fullScreen", "showClose", "close", "showStopAndPlay", "class", "playerType", "handleChangePlayType", "bottomHoverColor", "cameraCode", "bottomList", "length", "hideAllFunc", "width", "height", "getNewHeight", "components", "show", "_l", "component", "index", "key", "ref", "refInFor", "tag", "isFull", "handleClick", "handleComponentClick", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/rslPlayer/components/canvasPlayerItem.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"player-container\", attrs: { id: _vm.id + \"-container\" } },\n    [\n      _vm.loading\n        ? _c(\n            \"div\",\n            {\n              staticClass: \"player-loading mask\",\n              on: {\n                mouseover: function ($event) {\n                  _vm.loadingHoverBottom = true\n                },\n                mouseout: function ($event) {\n                  _vm.loadingHoverBottom = false\n                },\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.loadingHoverBottom && _vm.isFuse,\n                      expression: \"loadingHoverBottom && isFuse\",\n                    },\n                  ],\n                  staticClass: \"hover-top no-play-top\",\n                  style: { backgroundColor: _vm.topHoverColor },\n                },\n                [\n                  _c(\"div\", { staticClass: \"hover-top-title\" }, [\n                    _vm._v(\n                      _vm._s(\n                        _vm.nameFieldDisplay === \"0\"\n                          ? _vm.cameraName || _vm.aliasName\n                          : _vm.aliasName || _vm.cameraName\n                      ) + \" \"\n                    ),\n                  ]),\n                ]\n              ),\n              _c(\"div\", { staticClass: \"player-loading-icon\" }, [\n                _c(\"div\", { staticClass: \"loading-icon\" }, [\n                  _c(\"img\", {\n                    attrs: { src: _vm.loadingTypeMap[_vm.loadingType][\"img\"] },\n                  }),\n                ]),\n                _c(\"div\", {\n                  staticClass: \"loading-text\",\n                  domProps: {\n                    innerHTML: _vm._s(\n                      _vm.loadingTypeMap[_vm.loadingType][\"text\"]\n                    ),\n                  },\n                }),\n              ]),\n            ]\n          )\n        : _vm._e(),\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.enterCanvas,\n              expression: \"enterCanvas\",\n            },\n          ],\n          staticClass: \"player-mask mask\",\n          on: {\n            mouseover: function ($event) {\n              return _vm.hoverHandler(true)\n            },\n            mouseout: function ($event) {\n              return _vm.hoverHandler(false)\n            },\n          },\n        },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"hover-top\",\n              style: { backgroundColor: _vm.topHoverColor },\n            },\n            [\n              _c(\"div\", { staticClass: \"hover-top-title\" }, [\n                _vm._v(\n                  _vm._s(\n                    _vm.nameFieldDisplay === \"0\"\n                      ? _vm.cameraName || _vm.aliasName\n                      : _vm.aliasName || _vm.cameraName\n                  ) + \" \"\n                ),\n              ]),\n              _vm.showFull\n                ? _c(\"div\", {\n                    staticClass: \"fullscreen btn\",\n                    on: { click: _vm.fullScreen },\n                  })\n                : _vm._e(),\n              _vm.showClose\n                ? _c(\"div\", {\n                    staticClass: \"close btn\",\n                    on: { click: _vm.close },\n                  })\n                : _vm._e(),\n            ]\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.showStopAndPlay,\n                  expression: \"showStopAndPlay\",\n                },\n              ],\n              staticClass: \"hover-center\",\n            },\n            [\n              _c(\"div\", {\n                class: [\"player-btn\", _vm.playerType ? \"play\" : \"stop\"],\n                on: { click: _vm.handleChangePlayType },\n              }),\n            ]\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"hover-bottom\",\n              style: { backgroundColor: _vm.bottomHoverColor },\n            },\n            [\n              _vm.cameraCode &&\n              _vm.bottomList &&\n              _vm.bottomList.length > 0 &&\n              !_vm.hideAllFunc\n                ? _c(\"div\", { staticClass: \"hover-bottom-inner\" })\n                : _vm._e(),\n            ]\n          ),\n        ]\n      ),\n      _c(\"canvas\", {\n        staticClass: \"waterText\",\n        attrs: {\n          id: \"water\" + _vm.id,\n          width: _vm.width,\n          height: _vm.getNewHeight,\n        },\n        on: {\n          mouseover: function ($event) {\n            return _vm.hoverHandler(true)\n          },\n          mouseout: function ($event) {\n            return _vm.hoverHandler(false)\n          },\n        },\n      }),\n      _c(\"canvas\", {\n        attrs: { id: _vm.id, width: _vm.width, height: _vm.getNewHeight },\n      }),\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value:\n                _vm.components &&\n                _vm.components.length > 0 &&\n                _vm.show &&\n                !_vm.hideAllFunc,\n              expression:\n                \"components && components.length > 0 && show && !hideAllFunc\",\n            },\n          ],\n          staticClass: \"canvasPlayer-slot\",\n        },\n        _vm._l(_vm.components, function (component, index) {\n          return _c(component.components ? component.components : null, {\n            key: index,\n            ref: \"bottomBtn\" + index,\n            refInFor: true,\n            tag: \"component\",\n            attrs: { isFull: _vm.isFull },\n            on: { handleClick: _vm.handleComponentClick },\n          })\n        }),\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE,kBAAkB;IAAEC,KAAK,EAAE;MAAEC,EAAE,EAAEL,GAAG,CAACK,EAAE,GAAG;IAAa;EAAE,CAAC,EACzE,CACEL,GAAG,CAACM,OAAO,GACPL,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCI,EAAE,EAAE;MACFC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAC3BT,GAAG,CAACU,kBAAkB,GAAG,IAAI;MAC/B,CAAC;MACDC,QAAQ,EAAE,SAAAA,CAAUF,MAAM,EAAE;QAC1BT,GAAG,CAACU,kBAAkB,GAAG,KAAK;MAChC;IACF;EACF,CAAC,EACD,CACET,EAAE,CACA,KAAK,EACL;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEf,GAAG,CAACU,kBAAkB,IAAIV,GAAG,CAACgB,MAAM;MAC3CC,UAAU,EAAE;IACd,CAAC,CACF;IACDd,WAAW,EAAE,uBAAuB;IACpCe,KAAK,EAAE;MAAEC,eAAe,EAAEnB,GAAG,CAACoB;IAAc;EAC9C,CAAC,EACD,CACEnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAACsB,EAAE,CACJtB,GAAG,CAACuB,gBAAgB,KAAK,GAAG,GACxBvB,GAAG,CAACwB,UAAU,IAAIxB,GAAG,CAACyB,SAAS,GAC/BzB,GAAG,CAACyB,SAAS,IAAIzB,GAAG,CAACwB,UAC3B,CAAC,GAAG,GACN,CAAC,CACF,CAAC,CAEN,CAAC,EACDvB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MAAEsB,GAAG,EAAE1B,GAAG,CAAC2B,cAAc,CAAC3B,GAAG,CAAC4B,WAAW,CAAC,CAAC,KAAK;IAAE;EAC3D,CAAC,CAAC,CACH,CAAC,EACF3B,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,cAAc;IAC3B0B,QAAQ,EAAE;MACRC,SAAS,EAAE9B,GAAG,CAACsB,EAAE,CACftB,GAAG,CAAC2B,cAAc,CAAC3B,GAAG,CAAC4B,WAAW,CAAC,CAAC,MAAM,CAC5C;IACF;EACF,CAAC,CAAC,CACH,CAAC,CAEN,CAAC,GACD5B,GAAG,CAAC+B,EAAE,CAAC,CAAC,EACZ9B,EAAE,CACA,KAAK,EACL;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEf,GAAG,CAACgC,WAAW;MACtBf,UAAU,EAAE;IACd,CAAC,CACF;IACDd,WAAW,EAAE,kBAAkB;IAC/BI,EAAE,EAAE;MACFC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAC3B,OAAOT,GAAG,CAACiC,YAAY,CAAC,IAAI,CAAC;MAC/B,CAAC;MACDtB,QAAQ,EAAE,SAAAA,CAAUF,MAAM,EAAE;QAC1B,OAAOT,GAAG,CAACiC,YAAY,CAAC,KAAK,CAAC;MAChC;IACF;EACF,CAAC,EACD,CACEhC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBe,KAAK,EAAE;MAAEC,eAAe,EAAEnB,GAAG,CAACoB;IAAc;EAC9C,CAAC,EACD,CACEnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACqB,EAAE,CACJrB,GAAG,CAACsB,EAAE,CACJtB,GAAG,CAACuB,gBAAgB,KAAK,GAAG,GACxBvB,GAAG,CAACwB,UAAU,IAAIxB,GAAG,CAACyB,SAAS,GAC/BzB,GAAG,CAACyB,SAAS,IAAIzB,GAAG,CAACwB,UAC3B,CAAC,GAAG,GACN,CAAC,CACF,CAAC,EACFxB,GAAG,CAACkC,QAAQ,GACRjC,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,gBAAgB;IAC7BI,EAAE,EAAE;MAAE4B,KAAK,EAAEnC,GAAG,CAACoC;IAAW;EAC9B,CAAC,CAAC,GACFpC,GAAG,CAAC+B,EAAE,CAAC,CAAC,EACZ/B,GAAG,CAACqC,SAAS,GACTpC,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,WAAW;IACxBI,EAAE,EAAE;MAAE4B,KAAK,EAAEnC,GAAG,CAACsC;IAAM;EACzB,CAAC,CAAC,GACFtC,GAAG,CAAC+B,EAAE,CAAC,CAAC,CAEhB,CAAC,EACD9B,EAAE,CACA,KAAK,EACL;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEf,GAAG,CAACuC,eAAe;MAC1BtB,UAAU,EAAE;IACd,CAAC,CACF;IACDd,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,KAAK,EAAE;IACRuC,KAAK,EAAE,CAAC,YAAY,EAAExC,GAAG,CAACyC,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC;IACvDlC,EAAE,EAAE;MAAE4B,KAAK,EAAEnC,GAAG,CAAC0C;IAAqB;EACxC,CAAC,CAAC,CAEN,CAAC,EACDzC,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,cAAc;IAC3Be,KAAK,EAAE;MAAEC,eAAe,EAAEnB,GAAG,CAAC2C;IAAiB;EACjD,CAAC,EACD,CACE3C,GAAG,CAAC4C,UAAU,IACd5C,GAAG,CAAC6C,UAAU,IACd7C,GAAG,CAAC6C,UAAU,CAACC,MAAM,GAAG,CAAC,IACzB,CAAC9C,GAAG,CAAC+C,WAAW,GACZ9C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,GAChDH,GAAG,CAAC+B,EAAE,CAAC,CAAC,CAEhB,CAAC,CAEL,CAAC,EACD9B,EAAE,CAAC,QAAQ,EAAE;IACXE,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE;MACLC,EAAE,EAAE,OAAO,GAAGL,GAAG,CAACK,EAAE;MACpB2C,KAAK,EAAEhD,GAAG,CAACgD,KAAK;MAChBC,MAAM,EAAEjD,GAAG,CAACkD;IACd,CAAC;IACD3C,EAAE,EAAE;MACFC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAC3B,OAAOT,GAAG,CAACiC,YAAY,CAAC,IAAI,CAAC;MAC/B,CAAC;MACDtB,QAAQ,EAAE,SAAAA,CAAUF,MAAM,EAAE;QAC1B,OAAOT,GAAG,CAACiC,YAAY,CAAC,KAAK,CAAC;MAChC;IACF;EACF,CAAC,CAAC,EACFhC,EAAE,CAAC,QAAQ,EAAE;IACXG,KAAK,EAAE;MAAEC,EAAE,EAAEL,GAAG,CAACK,EAAE;MAAE2C,KAAK,EAAEhD,GAAG,CAACgD,KAAK;MAAEC,MAAM,EAAEjD,GAAG,CAACkD;IAAa;EAClE,CAAC,CAAC,EACFjD,EAAE,CACA,KAAK,EACL;IACEW,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EACHf,GAAG,CAACmD,UAAU,IACdnD,GAAG,CAACmD,UAAU,CAACL,MAAM,GAAG,CAAC,IACzB9C,GAAG,CAACoD,IAAI,IACR,CAACpD,GAAG,CAAC+C,WAAW;MAClB9B,UAAU,EACR;IACJ,CAAC,CACF;IACDd,WAAW,EAAE;EACf,CAAC,EACDH,GAAG,CAACqD,EAAE,CAACrD,GAAG,CAACmD,UAAU,EAAE,UAAUG,SAAS,EAAEC,KAAK,EAAE;IACjD,OAAOtD,EAAE,CAACqD,SAAS,CAACH,UAAU,GAAGG,SAAS,CAACH,UAAU,GAAG,IAAI,EAAE;MAC5DK,GAAG,EAAED,KAAK;MACVE,GAAG,EAAE,WAAW,GAAGF,KAAK;MACxBG,QAAQ,EAAE,IAAI;MACdC,GAAG,EAAE,WAAW;MAChBvD,KAAK,EAAE;QAAEwD,MAAM,EAAE5D,GAAG,CAAC4D;MAAO,CAAC;MAC7BrD,EAAE,EAAE;QAAEsD,WAAW,EAAE7D,GAAG,CAAC8D;MAAqB;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CAEL,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBhE,MAAM,CAACiE,aAAa,GAAG,IAAI;AAE3B,SAASjE,MAAM,EAAEgE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}