/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../FunctionWrapper.js";import{j as n,o as t,A as r,f as o,q as a,b as s,a as l}from"../../../chunks/languageUtils.js";import u from"../../../geometry/Geometry.js";function i(e,n){let t="{";for(let r=0;r<n.properties.length;r++){const o=n.properties[r];t+=("Identifier"===o.key.type?JSON.stringify(o.key.name):O(e,o.key))+":"+O(e,o.value)}return t+="}",t}function c(e,n){let t="{\n";for(let r=0;r<n.body.length;r++)t+=O(e,n.body[r])+" \n";return t+="}\n",t}function f(e,n){if(null===n.argument)return"return;";return"return "+O(e,n.argument)+";\n"}function p(e,n){let t="function "+A(n.id.name,e.globalMangles,{});const r=[];let o={};o={};const a={};for(const s of n.params){const e=T(s.name,a);r.push(e),o[s.name]=1}t+="("+r.join(",")+") ";return t+=O({functions:e.functions,globals:e.globals,symbols:e.symbols,context:e.context,globalMangles:e.globalMangles,localMangles:a,locals:o},n.body),t}function m(e,n){const t=[];for(let r=0;r<n.declarations.length;r++)t.push(O(e,n.declarations[r]));return"var "+t.join(",")+"; "}function g(e,n){const t=null===n.init?null:O(e,n.init),r=T(n.name,e.mangles);return""===t?r:r+" = "+t}function b(){throw new Error("Not Supported")}function y(e,n){return"true"===n.computed?O(e,n.object)+"["+O(e,n.property)+"]":O(e,n.object)+"."+n.property.name}function d(e,n){return"for( "+O(e,n.left)+" in "+O(e,n.right)+") "+O(e,n.body)+"\n"}function h(e,n){let t="for(";return null!==n.init&&(t+=O(e,n.init)),t+=";",null!==n.test?t+=O(e,n.test):t+=" ;",null!==n.update?t+=O(e,n.update):t+=" ;",t+=") ",t+=O(e,n.body)+"\n",t}function x(e,n){return!0===n.prefix?n.operator+O(e,n.argument):O(e,n.argument)+n.operator}function E(e,n){return"if "+O(e,n.test)+" "+O(e,n.consequent)+(null!==n.alternate?"else "+O(e,n.alternate):"")}function v(e,n){return O(e,n.expression)+";"}function S(e,n){return"("+O(e,n.left)+" "+n.operator+" "+O(e,n.right)+")"}function j(e,n){return!0===n.prefix?n.operator+O(e,n.argument):O(e,n.argument)+n.operator}function k(e,n){const t=[];for(let r=0;r<n.elements.length;r++)t.push(O(e,n.elements[r]));return"["+t.join(",")+"]"}function w(e,n){return O(e,n.left)+" "+n.operator+" "+O(e,n.right)}function M(e,n){return O(e,n.left)+" "+n.operator+" "+O(e,n.right)}function N(e,n){let t=O(e,n.callee)+"(";const r=[];for(const o of n.arguments)r.push(O(e,o));return t+=r.join(",")+")",t}function O(e,n){switch(n.type){case"EmptyStatement":return"";case"VariableDeclarator":return g(e,n);case"VariableDeclaration":return m(e,n);case"BlockStatement":return c(e,n);case"FunctionDeclaration":return p(e,n);case"ReturnStatement":return f(e,n);case"IfStatement":return E(e,n);case"ExpressionStatement":return v(e,n);case"AssignmentExpression":return M(e,n);case"UpdateExpression":return x(e,n);case"BreakStatement":return"break;";case"ContinueStatement":return"continue;";case"ForStatement":return h(e,n);case"ForInStatement":return d(e,n);case"Identifier":return U(e,n);case"MemberExpression":return y(e,n);case"Literal":return null===n.value||void 0===n.value?"null":JSON.stringify(n.value);case"ThisExpression":case"ConditionalExpression":case"Array":throw new Error("NOTSUPPORTED");case"CallExpression":return N(e,n);case"UnaryExpression":return j(e,n);case"BinaryExpression":return S(e,n);case"LogicalExpression":return w(e,n);case"ArrayExpression":return k(e,n);case"ObjectExpression":return i(e,n);case"Property":return b();default:throw new Error("UNREOGNISED")}}function U(i,c){const f=c.name.toLowerCase();if(void 0!==i.locals[f])return A(c.name,i.globalMangles,i.localMangles);{const p=i.context.globalScope[f];if(void 0===p)return c.name;if(null!==p){if(p.value instanceof e){return C(p.value,i.functions,i.globals,i.symbols)}if(null==p){const e=D(i.symbols);return i.globals.push({name:e,type:"null",params:{value:null}}),e}if(p instanceof u){const e=D(i.symbols);return i.globals.push({name:e,type:"geometry",params:{value:p}}),e}if(n(p)){const e=D(i.symbols);return i.globals.push({name:e,type:"number",params:{value:p}}),e}if(t(p)){const e=D(i.symbols);return i.globals.push({name:e,type:"date",params:{value:r(p).getTime()}}),e}if(o(p)){const e=D(i.symbols);return i.globals.push({name:e,type:"string",params:{value:p}}),e}if(a(p));else if(s(p));else if(!l(p))throw new Error("Unsupported Type")}}return c.name}function D(e){let n=!1,t="";for(;!1===n;)t="$gi_"+I.toString(),I++,void 0===e.syms[t]&&(n=!0);return t}let I=0;function T(e,n){const t="$$fc_"+I.toString();return I++,n[e]=t,t}function A(e,n,t){return void 0===t[e]?void 0===n[e]?e:n[e]:t[e]}function C(e,n,t,r){const o={functions:n,globals:t,symbols:r,context:e.context,locals:{},globalMangles:{},localMangles:{}},a=e.definition.id.name;if(void 0!==n.lkp[a])return n.lkp[a].mangledname;const s=T(a,o.globalMangles),l={name:a,mangledname:s,script:""};return l.script=O(o,e.definition),n.lkp[l.name]=l,n.stack.push(l),s}export{C as exportFunctionAsArcade};
