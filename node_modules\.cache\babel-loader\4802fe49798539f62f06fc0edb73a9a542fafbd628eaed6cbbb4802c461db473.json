{"ast": null, "code": "import { getCimLayer } from '@/api/userMenu';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'TimeBaseLayer',\n  data() {\n    return {\n      dataList: [],\n      defaultProps: {\n        children: 'layers',\n        label: 'content'\n      },\n      newArr: [],\n      checkedKeys: [],\n      currentNode: null\n    };\n  },\n  computed: {\n    ...mapState(\"action\", [\"TypeVideoAll\", 'isFull']),\n    ...mapState(['isShowPlay'])\n  },\n  mounted() {\n    this.initData();\n  },\n  beforeDestroy() {\n    this.$eventBus.$emit('senTxtToUe', '清除');\n  },\n  methods: {\n    handleNodeClick(val) {\n      this.$emit('changeDataLayer', val);\n      // if (val.layers) {\n\n      //     if (flag) {\n      //         this.checkedKeys.push(val.id)\n      //     } else {\n      //         if (this.checkedKeys.indexOf(val.id) != -1)\n      //             this.checkedKeys.splice(this.checkedKeys.indexOf(val.id), 1)\n      //     }\n      // } else {\n\n      //     let params = {\n      //         ...val,\n      //         flag: flag,\n      //     }\n      //     this.$emit('changeDataLayer', params)\n      // }\n      // if (!flag) {\n      //     this.$store.commit(\"action/getIsShowData\", false)\n      // }\n    },\n    handleCheckAll(val, flag) {\n      this.dataList.forEach((v, i) => {\n        if (v.category == val.category) {\n          val.index = v.id;\n        }\n      });\n      if (!this.isShowPlay) {\n        let isFlag;\n        if (flag.checkedKeys.indexOf(val.id) != -1) {\n          isFlag = true;\n        } else {\n          isFlag = false;\n        }\n        let params = {\n          ...val,\n          isFlag\n        };\n        this.$parent.changeData2DLayer(params);\n      }\n    },\n    async initData() {\n      await getCimLayer().then(res => {\n        this.dataList = res.data.extra;\n        let i = 1;\n        for (var v of res.data.extra) {\n          v.content = v.category;\n          v.id = i;\n          i++;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mapState", "name", "data", "dataList", "defaultProps", "children", "label", "newArr", "checked<PERSON>eys", "currentNode", "computed", "mounted", "initData", "<PERSON><PERSON><PERSON><PERSON>", "$eventBus", "$emit", "methods", "handleNodeClick", "val", "handleCheckAll", "flag", "for<PERSON>ach", "v", "i", "category", "index", "id", "isShowPlay", "isFlag", "indexOf", "params", "$parent", "changeData2DLayer", "then", "res", "extra", "content"], "sources": ["src/views/mainPage/components/timeBaseLayer.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\" :class=\"isFull ? ' ' : 'allScreen'\">\r\n        <!-- <div class=\"title\">\r\n            数据图层\r\n        </div> -->\r\n        <div class=\"treeShow\">\r\n            <el-tree ref=\"Tree\" :data=\"dataList\" node-key=\"data\" :default-expanded-keys=\"checkedKeys\"\r\n                :props=\"defaultProps\" @node-click=\"handleNodeClick\">\r\n                <span slot-scope=\"{node,data}\" class=\"slotTxt\">\r\n                    <img v-if=\"node.level == 1\" :src=\"'http://***********:81' + data.image\" alt=\"\" width=\"20px\">\r\n                    <span class=\"pr10\">{{ data.content }}</span>\r\n                    <span class=\"checkedTxt\" v-if=\"node.level == 1 && node.expanded\">收起</span>\r\n                    <span class=\"checkedTxt\" v-if=\"node.level == 1 && !node.expanded\">展开</span>\r\n\r\n                </span>\r\n            </el-tree>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCimLayer } from '@/api/userMenu'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'TimeBaseLayer',\r\n\r\n    data() {\r\n        return {\r\n            dataList: [],\r\n            defaultProps: {\r\n                children: 'layers',\r\n                label: 'content'\r\n            },\r\n            newArr: [],\r\n            checkedKeys: [],\r\n            currentNode: null\r\n        };\r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"TypeVideoAll\", 'isFull'\r\n        ]),\r\n        ...mapState(['isShowPlay'])\r\n    },\r\n\r\n    mounted() {\r\n        this.initData()\r\n    },\r\n    beforeDestroy() {\r\n        this.$eventBus.$emit('senTxtToUe', '清除')\r\n    },\r\n\r\n    methods: {\r\n\r\n        handleNodeClick(val) {\r\n            this.$emit('changeDataLayer', val)\r\n            // if (val.layers) {\r\n\r\n            //     if (flag) {\r\n            //         this.checkedKeys.push(val.id)\r\n            //     } else {\r\n            //         if (this.checkedKeys.indexOf(val.id) != -1)\r\n            //             this.checkedKeys.splice(this.checkedKeys.indexOf(val.id), 1)\r\n            //     }\r\n            // } else {\r\n\r\n            //     let params = {\r\n            //         ...val,\r\n            //         flag: flag,\r\n            //     }\r\n            //     this.$emit('changeDataLayer', params)\r\n            // }\r\n            // if (!flag) {\r\n            //     this.$store.commit(\"action/getIsShowData\", false)\r\n            // }\r\n        },\r\n        handleCheckAll(val, flag) {\r\n            this.dataList.forEach((v, i) => {\r\n                if (v.category == val.category) {\r\n                    val.index = v.id\r\n                }\r\n            })\r\n            if (!this.isShowPlay) {\r\n                let isFlag;\r\n                if (flag.checkedKeys.indexOf(val.id) != -1) {\r\n                    isFlag = true\r\n                } else {\r\n                    isFlag = false\r\n                }\r\n                let params = {\r\n                    ...val,\r\n                    isFlag\r\n                }\r\n                this.$parent.changeData2DLayer(params)\r\n\r\n            }\r\n        },\r\n\r\n\r\n        async initData() {\r\n            await getCimLayer().then(res => {\r\n                this.dataList = res.data.extra\r\n\r\n                let i = 1\r\n                for (var v of res.data.extra) {\r\n                    v.content = v.category\r\n                    v.id = i;\r\n                    i++;\r\n                }\r\n            })\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.allScreen {\r\n    display: none;\r\n}\r\n\r\n.container {\r\n    z-index: 88;\r\n    position: absolute;\r\n    // left: 0;\r\n    margin-top: 4%;\r\n    width: 100%;\r\n    height: 100%;\r\n    // background: url('@/assets/images/mainPics/bgicon6.png') no-repeat center;\r\n    background-size: 100% 100%;\r\n    color: #fff;\r\n    overflow: auto;\r\n    transform-origin: 0 0;\r\n    transform: scale(var(--scaleX), var(--scaleY));\r\n    transition: 0.3s;\r\n\r\n    .title {\r\n\r\n        line-height: 75px;\r\n        font-size: 30px;\r\n        padding-left: 50px;\r\n    }\r\n\r\n    .treeShow {\r\n        width: 100%;\r\n        height: 1254px;\r\n        overflow: auto;\r\n        padding: 0 10px;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    ::v-deep .el-tree {\r\n        width: 100%;\r\n        background: transparent;\r\n        color: #fff;\r\n\r\n        .el-tree-node.is-focusable {\r\n            margin-top: 10px;\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n            margin-left: 20px;\r\n            width: 25px;\r\n            height: 25px;\r\n        }\r\n\r\n        .el-tree-node__children .is-current {\r\n            background-color: rgba(27, 137, 225, 1) !important;\r\n        }\r\n\r\n        /* 对勾样式 */\r\n        .el-checkbox__inner::after {\r\n            left: 10px;\r\n            top: 5px;\r\n        }\r\n\r\n        .el-checkbox__input.is-checked .el-checkbox__inner::after {\r\n            transform: rotate(50deg) scaleY(1.8);\r\n        }\r\n\r\n        img {\r\n            margin-right: 5px;\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n        }\r\n\r\n        >.el-tree-node>.el-tree-node__content {\r\n            position: relative;\r\n            font-size: 30px;\r\n            height: 60px;\r\n            background: rgba(27, 137, 225, 0.3);\r\n            padding: 0 10px !important;\r\n\r\n            >.el-icon-caret-right {\r\n                position: absolute;\r\n                right: 75px;\r\n                font-size: 30px;\r\n                color: #fff;\r\n            }\r\n\r\n            .el-tree-node__label {\r\n                font-size: 20px;\r\n                font-weight: bold;\r\n            }\r\n        }\r\n\r\n        .el-tree-node__children .el-tree-node__content {\r\n            background: rgba(255, 255, 255, 0);\r\n            height: 60px;\r\n            font-size: 30px;\r\n\r\n            .el-tree-node__label {\r\n                font-weight: 400;\r\n            }\r\n\r\n        }\r\n\r\n        .checkedTxt {\r\n            position: absolute;\r\n            right: 25px;\r\n            font-size: 25px;\r\n        }\r\n\r\n    }\r\n}\r\n\r\n/* 整个滚动条 */\r\n::-webkit-scrollbar {\r\n    position: absolute;\r\n    width: 5px !important;\r\n    height: 10px !important;\r\n}\r\n\r\n/* 滚动条上的滚动滑块 */\r\n::-webkit-scrollbar-thumb {\r\n    background: rgba(27, 137, 225, 0.4);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 滚动条上的滚动滑块悬浮效果 */\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(27, 137, 225, 0.4);\r\n\r\n}\r\n\r\n/* 滚动条没有滑块的轨道部分 */\r\n::-webkit-scrollbar-track-piece {\r\n    background-color: rgba(255, 255, 255, .3);\r\n}\r\n\r\n/*滚动条上的按钮(上下箭头)  */\r\n::-webkit-scrollbar-button {\r\n    background-color: transparent;\r\n}\r\n</style>"], "mappings": "AAqBA,SAAAA,WAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,MAAA;MACAC,WAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAV,QAAA,YACA,yBACA;IACA,GAAAA,QAAA;EACA;EAEAW,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,cAAA;IACA,KAAAC,SAAA,CAAAC,KAAA;EACA;EAEAC,OAAA;IAEAC,gBAAAC,GAAA;MACA,KAAAH,KAAA,oBAAAG,GAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,eAAAD,GAAA,EAAAE,IAAA;MACA,KAAAjB,QAAA,CAAAkB,OAAA,EAAAC,CAAA,EAAAC,CAAA;QACA,IAAAD,CAAA,CAAAE,QAAA,IAAAN,GAAA,CAAAM,QAAA;UACAN,GAAA,CAAAO,KAAA,GAAAH,CAAA,CAAAI,EAAA;QACA;MACA;MACA,UAAAC,UAAA;QACA,IAAAC,MAAA;QACA,IAAAR,IAAA,CAAAZ,WAAA,CAAAqB,OAAA,CAAAX,GAAA,CAAAQ,EAAA;UACAE,MAAA;QACA;UACAA,MAAA;QACA;QACA,IAAAE,MAAA;UACA,GAAAZ,GAAA;UACAU;QACA;QACA,KAAAG,OAAA,CAAAC,iBAAA,CAAAF,MAAA;MAEA;IACA;IAGA,MAAAlB,SAAA;MACA,MAAAb,WAAA,GAAAkC,IAAA,CAAAC,GAAA;QACA,KAAA/B,QAAA,GAAA+B,GAAA,CAAAhC,IAAA,CAAAiC,KAAA;QAEA,IAAAZ,CAAA;QACA,SAAAD,CAAA,IAAAY,GAAA,CAAAhC,IAAA,CAAAiC,KAAA;UACAb,CAAA,CAAAc,OAAA,GAAAd,CAAA,CAAAE,QAAA;UACAF,CAAA,CAAAI,EAAA,GAAAH,CAAA;UACAA,CAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}