{"ast": null, "code": "export default {\n  data() {\n    return {\n      filters: {},\n      filtersBackUps: {},\n      pageParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      currentRowId: null\n    };\n  },\n  props: {\n    popFalg: {\n      type: <PERSON><PERSON>an,\n      default: false\n    },\n    filterConfig: {\n      type: Object,\n      default() {\n        return {};\n      }\n    },\n    tableHeader: {\n      type: Object,\n      default() {\n        return {};\n      }\n    },\n    tableData: {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    totalCount: {\n      type: Number\n    },\n    tableType: {\n      type: Number\n    }\n  },\n  watch: {\n    popFalg(nv) {\n      if (nv) {\n        this.$emit('getList', this.pageParams, this.tableType);\n      }\n    }\n  },\n  mounted() {\n    this.filters = this.filtersBackUps;\n  },\n  methods: {\n    // 获取筛选组件\n    getFilterComponent(type) {\n      switch (type) {\n        case 'input':\n          return 'el-input';\n        case 'select':\n          return 'el-select';\n        case 'date-range':\n          return 'el-date-picker';\n        default:\n          return 'el-input';\n      }\n    },\n    closeFn() {\n      this.filtersBackUps = this.filters;\n      this.$emit('closeDialog');\n    },\n    // 搜索\n    handleSearch() {\n      const params = {\n        ...this.pageParams\n      };\n      // 处理日期范围筛选\n      if (this.filters.alertTime) {\n        params.startTime = this.filters.alertTime[0];\n        params.endTime = this.filters.alertTime[1];\n      }\n      // 处理其他筛选条件\n      Object.keys(this.filters).forEach(key => {\n        if (key !== 'alertTime' && this.filters[key]) {\n          params[key] = this.filters[key];\n        }\n      });\n      // 调用接口获取数据\n      this.$emit('getList', params, this.tableType);\n    },\n    // 重置\n    handleReset() {\n      this.filters = {};\n      this.pageParams.pageNum = 1;\n      this.handleSearch();\n    },\n    // 分页\n    handlePageChange(page) {\n      this.pageParams.pageNum = page;\n      this.handleSearch();\n    },\n    // 点击表格行处理函数\n    async handleRowClick(row) {\n      this.currentRowId = row.id;\n      // this.$emit('closeDialog')\n      this.$emit('getList', row, '撒点');\n      // 将选中行的 ID 存储到 localStorage 中\n      localStorage.setItem('selectedRowId', row.id);\n    },\n    getRowClassName({\n      row\n    }) {\n      return row.id == this.currentRowId ? 'row_active' : ''; // 如果是当前选中的行，添加高亮样式\n    }\n  }\n};", "map": {"version": 3, "names": ["data", "filters", "filtersBackUps", "pageParams", "pageNum", "pageSize", "currentRowId", "props", "popFalg", "type", "Boolean", "default", "filterConfig", "Object", "tableHeader", "tableData", "Array", "totalCount", "Number", "tableType", "watch", "nv", "$emit", "mounted", "methods", "getFilterComponent", "closeFn", "handleSearch", "params", "alertTime", "startTime", "endTime", "keys", "for<PERSON>ach", "key", "handleReset", "handlePageChange", "page", "handleRowClick", "row", "id", "localStorage", "setItem", "getRowClassName"], "sources": ["src/components/WarningDialog.vue"], "sourcesContent": ["<template>\r\n    <div class=\"warning-list-container\" v-if=\"popFalg\">\r\n        <div class=\"title\">\r\n            <span>{{ tableHeader.name }}列表</span>\r\n            <img @click=\"closeFn\" src=\"@/assets/close2.png\" alt=\"\">\r\n        </div>\r\n        <!-- 筛选区域 -->\r\n        <div class=\"filter-section\">\r\n            <div v-for=\"(filterItem, index) in filterConfig.data\" :key=\"index\" class=\"filter-item\">\r\n                <span class=\"filter-label\">{{ filterItem.label }}</span>\r\n                <component :is=\"getFilterComponent(filterItem.type)\" v-model=\"filters[filterItem.key]\"\r\n                    :options=\"filterItem.options\" :placeholder=\"filterItem.placeholder\"\r\n                    :type=\"filterItem.type === 'date-range' ? 'daterange' : ''\"\r\n                    :format=\"filterItem.type === 'date-range' ? 'yyyy-MM-dd' : ''\"\r\n                    :value-format=\"filterItem.type === 'date-range' ? 'yyyy-MM-dd' : ''\"\r\n                    :start-placeholder=\"filterItem.type === 'date-range' ? filterItem.placeholder.split(',')[0] : ''\"\r\n                    :end-placeholder=\"filterItem.type === 'date-range' ? filterItem.placeholder.split(',')[1] : ''\">\r\n                    <!-- 处理 select 组件的选项 -->\r\n                    <template v-if=\"filterItem.type === 'select'\">\r\n                        <el-option v-for=\"option in filterItem.options\" :key=\"option.value\" :label=\"option.label\"\r\n                            :value=\"option.value\">\r\n                        </el-option>\r\n                    </template>\r\n                </component>\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"event-box\">\r\n            <el-button type=\"primary\" @click=\"handleSearch\" class=\"search-btn\">搜索</el-button>\r\n            <el-button @click=\"handleReset\" class=\"reset-btn\">重置</el-button>\r\n        </div>\r\n\r\n        <!-- 表格区域 -->\r\n        <el-table :data=\"tableData\" class=\"warning-table\" @row-click=\"handleRowClick\" :row-class-name=\"getRowClassName\"\r\n            header-cell-class-name=\"fixed-header\">\r\n            <el-table-column align=\"center\" :show-overflow-tooltip=\"true\" width=\"auto\" min-width=\"200\"\r\n                v-for=\"(column, index) in tableHeader.data\" :key=\"index\" :label=\"column.label\" :prop=\"column.prop\">\r\n                <!-- <template v-if=\"column.prop == 'status'\" slot-scope=\"scope\">\r\n                    <span v-if=\"scope.row.status == 2\">待办理</span>\r\n                    <span v-if=\"scope.row.status == 3\">已办结</span>\r\n                </template> -->\r\n\r\n                <template #default=\"scope\">\r\n                    <template v-if=\"column.prop === 'status'\">\r\n                        <span v-if=\"scope.row.status == 2\">未处理</span>\r\n                        <span v-if=\"scope.row.status == 3\">已处理</span>\r\n                    </template>\r\n                    <template v-else-if=\"column.prop === 'onlineStatus'\">\r\n                        <span v-if=\"scope.row.onlineStatus == 0\">离线</span>\r\n                        <span v-if=\"scope.row.onlineStatus == 1\">在线</span>\r\n                    </template>\r\n                    <template v-else-if=\"column.prop === 'runtimeStatus'\">\r\n                        <span v-if=\"scope.row.runtimeStatus == 1\">空闲</span>\r\n                        <span v-if=\"scope.row.runtimeStatus == 2\">充电中</span>\r\n                        <span v-if=\"scope.row.runtimeStatus == 3\">故障</span>\r\n                        <span v-if=\"scope.row.runtimeStatus == 4\">维护中</span>\r\n                    </template>\r\n                    <template v-else-if=\"column.prop === 'repeatUrl'\">\r\n                        <el-image v-if=\"scope.row[column.prop]\" @click.stop :src=\"scope.row[column.prop]\"\r\n                            :preview-src-list=\"[scope.row[column.prop]]\">\r\n                        </el-image>\r\n                        <span v-else>暂无图片</span>\r\n                    </template>\r\n                    <template v-else-if=\"column.prop === 'firstEvent'\">\r\n                        <span v-if=\"scope.row.firstEvent == 1\">蓝色预警</span>\r\n                        <span v-if=\"scope.row.firstEvent == 2\">红色预警</span>\r\n                        <span v-if=\"scope.row.firstEvent == 3\">黄色预警</span>\r\n                    </template>\r\n                    <template v-else>\r\n                        {{ scope.row[column.prop] }}\r\n                    </template>\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <el-pagination @current-change=\"handlePageChange\" :current-page=\"pageParams.pageNum\"\r\n            :page-size=\"pageParams.pageSize\" layout=\"total, prev, pager, next, jumper\" :total=\"totalCount\">\r\n        </el-pagination>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\r\n    data() {\r\n        return {\r\n            filters: {},\r\n            filtersBackUps: {},\r\n            pageParams: {\r\n                pageNum: 1,\r\n                pageSize: 10\r\n            },\r\n            currentRowId: null,\r\n        };\r\n    },\r\n    props: {\r\n        popFalg: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        filterConfig: {\r\n            type: Object,\r\n            default() {\r\n                return {}\r\n            }\r\n        },\r\n        tableHeader: {\r\n            type: Object,\r\n            default() {\r\n                return {}\r\n            }\r\n        },\r\n        tableData: {\r\n            type: Array,\r\n            default() {\r\n                return []\r\n            }\r\n        },\r\n        totalCount: {\r\n            type: Number,\r\n        }, tableType: {\r\n            type: Number,\r\n        }\r\n    },\r\n    watch: {\r\n        popFalg(nv) {\r\n            if (nv) {\r\n                this.$emit('getList', this.pageParams, this.tableType);\r\n            }\r\n        },\r\n    },\r\n    mounted() {\r\n        this.filters = this.filtersBackUps;\r\n    },\r\n    methods: {\r\n        // 获取筛选组件\r\n        getFilterComponent(type) {\r\n            switch (type) {\r\n                case 'input': return 'el-input';\r\n                case 'select': return 'el-select';\r\n                case 'date-range': return 'el-date-picker';\r\n                default: return 'el-input';\r\n            }\r\n        },\r\n        closeFn() {\r\n            this.filtersBackUps = this.filters;\r\n            this.$emit('closeDialog')\r\n        },\r\n        // 搜索\r\n        handleSearch() {\r\n            const params = {\r\n                ...this.pageParams\r\n            };\r\n            // 处理日期范围筛选\r\n            if (this.filters.alertTime) {\r\n                params.startTime = this.filters.alertTime[0];\r\n                params.endTime = this.filters.alertTime[1];\r\n            }\r\n            // 处理其他筛选条件\r\n            Object.keys(this.filters).forEach(key => {\r\n                if (key !== 'alertTime' && this.filters[key]) {\r\n                    params[key] = this.filters[key];\r\n                }\r\n            });\r\n            // 调用接口获取数据\r\n            this.$emit('getList', params, this.tableType);\r\n        },\r\n        // 重置\r\n        handleReset() {\r\n            this.filters = {};\r\n            this.pageParams.pageNum = 1;\r\n            this.handleSearch();\r\n        },\r\n        // 分页\r\n        handlePageChange(page) {\r\n            this.pageParams.pageNum = page;\r\n            this.handleSearch();\r\n        },\r\n        // 点击表格行处理函数\r\n        async handleRowClick(row) {\r\n            this.currentRowId = row.id;\r\n            // this.$emit('closeDialog')\r\n            this.$emit('getList', row, '撒点');\r\n            // 将选中行的 ID 存储到 localStorage 中\r\n            localStorage.setItem('selectedRowId', row.id);\r\n        },\r\n        getRowClassName({ row }) {\r\n            return row.id == this.currentRowId ? 'row_active' : ''; // 如果是当前选中的行，添加高亮样式\r\n        },\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.warning-list-container {\r\n    position: fixed;\r\n    width: 2011px;\r\n    height: 1119px;\r\n    left: calc(50% - 1005px);\r\n    bottom: 280px;\r\n    padding: 10px;\r\n    background: url('@/assets/images/ChongDianZhuang/warning-list.png');\r\n    background-size: 100% 100%;\r\n}\r\n\r\n.title {\r\n    display: flex;\r\n    margin-top: 69px;\r\n    position: relative;\r\n    height: 39px;\r\n\r\n    >span {\r\n        font-family: Source Han Sans CN, Source Han Sans CN;\r\n        font-weight: bold;\r\n        font-size: 36px;\r\n        color: #FFFFFF;\r\n        text-align: left;\r\n        font-style: normal;\r\n        text-transform: none;\r\n        left: 930px;\r\n        position: absolute;\r\n    }\r\n\r\n    >img {\r\n        position: absolute;\r\n        right: 30px;\r\n        width: 39px;\r\n        height: 39px;\r\n        cursor: pointer;\r\n        top: -20px;\r\n    }\r\n}\r\n\r\n/deep/.el-date-editor .el-range-input {\r\n    font-size: 25px;\r\n    color: #fff;\r\n}\r\n\r\n/deep/ .el-table th.el-table__cell {\r\n    background: none !important;\r\n}\r\n\r\n/deep/ .el-table__body-wrapper {\r\n    // max-width: 2000px;\r\n    height: 560px;\r\n    overflow: auto;\r\n}\r\n\r\n/deep/ .el-table__row,\r\n/deep/ .el-table__row--striped {\r\n    background: none !important;\r\n    cursor: pointer;\r\n\r\n    td {\r\n        background: none !important;\r\n    }\r\n\r\n    td.el-table__cell {\r\n        background: none !important;\r\n\r\n        .cell {\r\n            min-height: 66px !important;\r\n            line-height: 66px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n\r\n        }\r\n    }\r\n}\r\n\r\n/deep/ .el-table .row_active {\r\n    background-color: rgba(46, 101, 167, 0.69) !important;\r\n    transform: translateZ(0);\r\n    /* 触发硬件加速 */\r\n    will-change: background-color;\r\n    /* 提前告知浏览器该属性会变化 */\r\n}\r\n\r\n/* 输入框背景 */\r\n/deep/ .el-input__inner {\r\n    background: rgba(71, 101, 144, 0.69) !important;\r\n    border-color: transparent !important;\r\n}\r\n\r\n/* 下拉框输入框背景 */\r\n/deep/ .el-select__input {\r\n    background: rgba(71, 101, 144, 0.69) !important;\r\n    border-color: transparent !important;\r\n}\r\n\r\n/* 日期选择框输入背景 */\r\n/deep/ .el-date-picker__input {\r\n    background: rgba(71, 101, 144, 0.69) !important;\r\n    border-color: transparent !important;\r\n}\r\n\r\n/deep/ .el-range-input {\r\n    background: none;\r\n    border-color: transparent !important;\r\n}\r\n\r\n/* 下拉选项背景 */\r\n/deep/ .el-select-dropdown__item {\r\n    background: rgba(71, 101, 144, 0.69) !important;\r\n}\r\n\r\n/deep/ .el-select-dropdown__item:hover {\r\n    background: rgba(71, 101, 144, 0.8) !important;\r\n}\r\n\r\n\r\n\r\n.filter-section {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-bottom: 15px;\r\n    padding: 0 30px;\r\n    margin-top: 20px;\r\n\r\n    // input框\r\n    /deep/ .el-select,\r\n    /deep/ .el-input,\r\n    /deep/ .el-input__inner {\r\n        width: 400px;\r\n        height: 64px;\r\n        color: #fff;\r\n        border-color: #9B9B9B;\r\n        text-align: left;\r\n        border-radius: 6px;\r\n        font-size: 26px;\r\n    }\r\n}\r\n\r\n.event-box {\r\n    // display: flex;\r\n    position: absolute;\r\n    top: 140px;\r\n    right: 30px;\r\n    padding-right: 30px;\r\n    // justify-content: right;\r\n    // margin-top: -90px\r\n}\r\n\r\n.filter-item {\r\n    margin-right: 15px;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    height: 63px;\r\n    min-width: 400px;\r\n}\r\n\r\n.filter-label {\r\n    margin-right: 5px;\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 32px;\r\n    color: #FFFFFF;\r\n    line-height: 56px;\r\n    text-align: left;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n.el-date-picker {\r\n    width: 180px;\r\n}\r\n\r\n.el-select {\r\n    width: 150px;\r\n}\r\n\r\n.el-input {\r\n    width: 150px;\r\n}\r\n\r\n.search-btn {\r\n    width: 114px;\r\n    height: 63px;\r\n    background: #6495ed;\r\n    border-color: #6495ed;\r\n    margin-right: 5px;\r\n    font-weight: 400;\r\n    font-size: 32px;\r\n    color: #FFFFFF;\r\n}\r\n\r\n.reset-btn {\r\n    width: 114px;\r\n    height: 63px;\r\n    background: #4CAF50;\r\n    border-color: #4CAF50;\r\n    font-weight: 400;\r\n    font-size: 32px;\r\n    color: #FFFFFF;\r\n}\r\n\r\n.warning-table {\r\n    margin-top: 80px;\r\n    padding: 0 30px;\r\n    background: none;\r\n    color: white;\r\n    font-size: 26px !important;\r\n}\r\n\r\n\r\n\r\n/deep/ .has-gutter {\r\n    tr {\r\n        height: 76px;\r\n        background: rgba(18, 125, 210, 0.4);\r\n        color: white;\r\n        font-size: 26px;\r\n    }\r\n}\r\n\r\n\r\n\r\n.view-btn {\r\n    color: white;\r\n    padding: 0 5px;\r\n}\r\n\r\n\r\n\r\n/deep/ .el-pagination {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 40px;\r\n    margin-bottom: 30px;\r\n    font-size: 25px;\r\n    justify-content: center;\r\n\r\n    .el-pagination__total {\r\n        color: #fff;\r\n        font-size: 25px;\r\n    }\r\n\r\n\r\n    .el-pagination__sizes {\r\n        input {\r\n            background: #072C44;\r\n            border-radius: 4px;\r\n            border: 1px solid #4BDBFF;\r\n            color: #fff;\r\n        }\r\n    }\r\n\r\n    >button {\r\n        color: #fff;\r\n        width: 30px;\r\n        height: 30px;\r\n        background-color: transparent;\r\n\r\n        .el-icon {\r\n            font-size: 25px;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            background-color: transparent;\r\n            font-size: 25px;\r\n            color: #fff;\r\n        }\r\n    }\r\n\r\n    .el-pager .active {\r\n        color: #4BDBFF;\r\n    }\r\n\r\n    .el-pagination__jump {\r\n        color: #fff;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 25px;\r\n\r\n        input {\r\n            background: #072C44;\r\n            border-radius: 4px;\r\n            border: 1px solid #4BDBFF;\r\n            color: #4BDBFF;\r\n            font-size: 25px;\r\n\r\n        }\r\n    }\r\n\r\n\r\n}\r\n</style>"], "mappings": "AAmFA;EAEAA,KAAA;IACA;MACAC,OAAA;MACAC,cAAA;MACAC,UAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;QACA;MACA;IACA;IACAG,WAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,QAAA;QACA;MACA;IACA;IACAI,SAAA;MACAN,IAAA,EAAAO,KAAA;MACAL,QAAA;QACA;MACA;IACA;IACAM,UAAA;MACAR,IAAA,EAAAS;IACA;IAAAC,SAAA;MACAV,IAAA,EAAAS;IACA;EACA;EACAE,KAAA;IACAZ,QAAAa,EAAA;MACA,IAAAA,EAAA;QACA,KAAAC,KAAA,iBAAAnB,UAAA,OAAAgB,SAAA;MACA;IACA;EACA;EACAI,QAAA;IACA,KAAAtB,OAAA,QAAAC,cAAA;EACA;EACAsB,OAAA;IACA;IACAC,mBAAAhB,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACAiB,QAAA;MACA,KAAAxB,cAAA,QAAAD,OAAA;MACA,KAAAqB,KAAA;IACA;IACA;IACAK,aAAA;MACA,MAAAC,MAAA;QACA,QAAAzB;MACA;MACA;MACA,SAAAF,OAAA,CAAA4B,SAAA;QACAD,MAAA,CAAAE,SAAA,QAAA7B,OAAA,CAAA4B,SAAA;QACAD,MAAA,CAAAG,OAAA,QAAA9B,OAAA,CAAA4B,SAAA;MACA;MACA;MACAhB,MAAA,CAAAmB,IAAA,MAAA/B,OAAA,EAAAgC,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,yBAAAjC,OAAA,CAAAiC,GAAA;UACAN,MAAA,CAAAM,GAAA,SAAAjC,OAAA,CAAAiC,GAAA;QACA;MACA;MACA;MACA,KAAAZ,KAAA,YAAAM,MAAA,OAAAT,SAAA;IACA;IACA;IACAgB,YAAA;MACA,KAAAlC,OAAA;MACA,KAAAE,UAAA,CAAAC,OAAA;MACA,KAAAuB,YAAA;IACA;IACA;IACAS,iBAAAC,IAAA;MACA,KAAAlC,UAAA,CAAAC,OAAA,GAAAiC,IAAA;MACA,KAAAV,YAAA;IACA;IACA;IACA,MAAAW,eAAAC,GAAA;MACA,KAAAjC,YAAA,GAAAiC,GAAA,CAAAC,EAAA;MACA;MACA,KAAAlB,KAAA,YAAAiB,GAAA;MACA;MACAE,YAAA,CAAAC,OAAA,kBAAAH,GAAA,CAAAC,EAAA;IACA;IACAG,gBAAA;MAAAJ;IAAA;MACA,OAAAA,GAAA,CAAAC,EAAA,SAAAlC,YAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}