/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{X as t}from"../../../chunks/languageUtils.js";import{isNumber as n,isString as e,equalityTest as r}from"./shared.js";import{isSingleField as u,predictType as l}from"./sqlUtils.js";import{reject as c,create as a}from"../../../core/promiseUtils.js";function s(t){return t=+t,isFinite(t)?t-t%1||(t<0?-0:0===t?t:0):t}function i(t){let n=0;for(let e=0;e<t.length;e++)n+=t[e];return n/t.length}function o(t){const n=i(t);let e=0;for(let r=0;r<t.length;r++)e+=(n-t[r])**2;return e/t.length}function h(t){const n=i(t);let e=0;for(let r=0;r<t.length;r++)e+=(n-t[r])**2;return e/(t.length-1)}function f(t){let n=0;for(let e=0;e<t.length;e++)n+=t[e];return n}function g(t,u){const l=[],c={},a=[];for(let s=0;s<t.length;s++){if(void 0!==t[s]&&null!==t[s]){const u=t[s];if(n(u)||e(u))void 0===c[u]&&(l.push(u),c[u]=1);else{let t=!1;for(let n=0;n<a.length;n++)!0===r(a[n],u)&&(t=!0);!1===t&&(a.push(u),l.push(u))}}if(l.length>=u&&-1!==u)return l}return l}function m(t){switch(t.toLowerCase()){case"distinct":return"distinct";case"avg":case"mean":return"avg";case"min":return"min";case"sum":return"sum";case"max":return"max";case"stdev":case"stddev":return"stddev";case"var":case"variance":return"var";case"count":return"count"}return""}function p(t,n,e=1e3){switch(t.toLowerCase()){case"distinct":return g(n,e);case"avg":case"mean":return i(n);case"min":return Math.min.apply(Math,n);case"sum":return f(n);case"max":return Math.max.apply(Math,n);case"stdev":case"stddev":return Math.sqrt(o(n));case"var":case"variance":return o(n);case"count":return n.length}return 0}function d(t,n,e){return q(t,n,e,!0).then((t=>0===t.length?null:Math.min.apply(Math,t)))}function v(t,n,e){return q(t,n,e,!0).then((t=>0===t.length?null:Math.max.apply(Math,t)))}function M(t,n,e){let r="";return!1===u(n)&&(r=l(n,t.fields,null)),q(t,n,e,!0).then((t=>{if(0===t.length)return null;const n=i(t);return null===n?n:"integer"===r?s(n):n}))}function x(t,n,e){return q(t,n,e,!0).then((t=>0===t.length?null:h(t)))}function y(t,n,e){return q(t,n,e,!0).then((t=>0===t.length?null:Math.sqrt(h(t))))}function j(t,n,e){return q(t,n,e,!0).then((t=>0===t.length?null:f(t)))}function w(t,n){try{return t.iterator(n).count()}catch(e){return c(e)}}function q(t,n,e,r=!1){try{const u=t.iterator(e);return a(((t,e)=>{U(u,[],n,r,t,e)}))}catch(u){return c(u)}}function U(n,e,r,u,l,c){t(n.next().then((t=>{try{if(null!==t){const a=r.calculateValue(t);return null===a?!1===u&&(e[e.length]=a):e[e.length]=a,U(n,e,r,u,l,c)}l(e)}catch(a){c(a)}}),c))}function C(t,n,e=1e3,r=null){return L(t,n,e,r)}function L(t,n,e,r){try{return V(t.iterator(r),{},[],n,e)}catch(u){return c(u)}}function V(t,n,e,r,u){return t.next().then((l=>{if(null!==l){const c=r.calculateValue(l);return null!=c&&void 0===n[c]&&(e.push(c),n[c]=1),e.length>=u&&-1!==u?e:V(t,n,e,r,u)}return e}))}export{p as calculateStat,w as count,m as decodeStatType,C as distinct,v as max,M as mean,d as min,y as stdev,j as sum,x as variance};
