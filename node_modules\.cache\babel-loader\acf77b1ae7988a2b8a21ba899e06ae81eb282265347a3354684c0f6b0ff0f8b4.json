{"ast": null, "code": "import { getUserMenuInfo, getUserMenuInfoJc } from '@/api/userMenu.js';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'MainHeader',\n  data() {\n    return {\n      currentPage: '',\n      timer: '',\n      date: '',\n      week: '',\n      currentTime: '',\n      tableList: [],\n      currentOneMenu: '',\n      //点击选中权限\n      currentTwoMenu: '',\n      currentThreeMenu: '',\n      topFlag: false,\n      // 是否显示权限\n      manageBtnFlag: false,\n      // 权限管理按钮\n      fullScreenFlag: false,\n      // 全屏按钮\n      img: require('@/assets/images/slices/big.png'),\n      layer: '',\n      isBodyChild: true,\n      isNet: null,\n      sourceBtnShow: true,\n      backBtnShow: false,\n      nodeFlag: false\n    };\n  },\n  watch: {\n    currentOneMenu(nv) {\n      if (nv == '视频点位') {\n        this.$emit('changerHeaderData', nv);\n      }\n    },\n    dataChannelText(nv) {\n      if (nv == '管网') {\n        this.isNet = nv;\n      }\n    }\n  },\n  computed: {\n    ...mapState(['isShowPlay']),\n    ...mapState(\"action\", ['isFull']),\n    ...mapState(['dataChannelText'])\n  },\n  created() {\n    this.$store.dispatch('getUser');\n    this.$store.dispatch('getUserInfo');\n  },\n  mounted() {\n    this.layer = this.isShowPlay;\n    this.currentPage = this.$route.name;\n    this.$nextTick(() => {\n      this.TodaysDat();\n      this.initMenu();\n      if (this.$route.meta.name != '权限管理') {\n        this.manageBtnFlag = false;\n      } else {\n        this.manageBtnFlag = true;\n      }\n      // 监听页面是否全屏\n    });\n    window.addEventListener('resize', this.fullResize);\n  },\n  destroyed() {\n    clearInterval(this.timer);\n    this.$store.commit('getIsShowPlay', \"3d\");\n    window.removeEventListener('resize', this.fullResize);\n  },\n  methods: {\n    //页面刷新\n    reloadFn() {\n      window.location.reload();\n      // this.$router.push({path:\"/\"})\n    },\n    //切换节点\n    switchNode(index) {\n      this.$store.commit('setNodeIndex', index);\n    },\n    ueEventFn(name) {\n      this.$eventBus.$emit(\"senTxtToUe\", name);\n    },\n    // 切换菜单的数据\n    gotoreSource() {\n      getUserMenuInfoJc().then(res => {\n        if (res.data.code == 200) {\n          this.tableList = res.data.extra[0].children;\n        }\n      });\n      this.sourceBtnShow = false;\n      this.backBtnShow = true;\n      this.$emit('changerHeaderData', '专题图层', true);\n      this.changeTopItem({\n        label: '专题图层'\n      }, 1);\n      this.$store.commit('getIsShowPlay', null);\n    },\n    backFn() {\n      this.topFlag = !this.topFlag;\n      this.initMenu();\n      this.sourceBtnShow = true;\n      this.backBtnShow = false;\n      this.$emit('changerHeaderData', '首页', true);\n      this.$store.commit('getIsShowPlay', \"3d\");\n    },\n    testVideo() {\n      this.$emit('changerHeaderData', '视频漫游', true);\n    },\n    async initMenu() {\n      // 查询用户权限\n      await getUserMenuInfo().then(res => {\n        this.tableList = res.data.extra;\n\n        // this.tableList.push({\n        //     label: \"接口规范\"\n        // })\n      }).catch(e => {\n        console.log(e);\n      });\n    },\n    TodaysDat() {\n      var _this = this;\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');\n      var now = new Date();\n      var day = now.getDay();\n      _this.week = weeks[day]; // 设置星期几\n      _this.date = new Date().getFullYear() + ' / ' + _this.appendZero(new Date().getMonth() + 1) + ' / ' + _this.appendZero(new Date().getDate()); // 设置年月日\n      _this.timer = setInterval(function () {\n        _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes()) + ':' + _this.appendZero(new Date().getSeconds()); //修改数据date\n      }, 1000);\n    },\n    appendZero(num) {\n      if (num < 10) {\n        return '0' + num;\n      } else {\n        return num;\n      }\n    },\n    changeTopItem(val, level) {\n      if (level && level == 1) {\n        this.$store.commit('action/getRegionalFlag', {\n          flag: false,\n          name: ''\n        });\n        this.isBodyChild = true;\n        if (this.currentOneMenu == val.label || this.currentOneMenu != '') {\n          this.topFlag = !this.topFlag;\n          if (this.currentOneMenu != val.label) {\n            this.topFlag = true;\n          }\n          this.currentOneMenu = val.label;\n          this.currentTwoMenu = '';\n          this.currentThreeMenu = '';\n        } else {\n          this.topFlag = !this.topFlag;\n          this.currentOneMenu = val.label;\n          this.currentTwoMenu = '';\n          this.currentThreeMenu = '';\n        }\n      } else if (level && level == 2) {\n        this.currentTwoMenu = val.label;\n        this.currentThreeMenu = '';\n      } else if (level && level == 3) {\n        this.currentThreeMenu = val.label;\n      }\n    },\n    // 头部菜单权限点击\n    changeName(val, level) {\n      if (this.isNet == '管网') {\n        this.$eventBus.$emit('senTxtToUe', '关闭管网UI');\n        this.isNet = null;\n      }\n      this.changeTopItem(val, level);\n      switch (val.label) {\n        case '数据看板':\n          this.$router.push({\n            name: 'ComprehensiveSituation'\n          });\n          this.$eventBus.$emit(\"senTxtToUe\", \"数据看板\");\n          this.$store.commit('action/getIsScreenShow', true);\n          break;\n        case '地图切换':\n          this.changeTwoThreeLayer();\n          break;\n        default:\n          break;\n      }\n      if (this.currentTwoMenu == '建筑衍生' && level == 3) {\n        let string = '建筑衍生' + val.label;\n        this.$emit('changerHeaderData', string);\n      } else if (this.currentTwoMenu == '楼层视图' && level == 3) {\n        let params = {\n          type: \"楼层视图\",\n          name: val.label\n        };\n        this.$emit('changerHeaderData', params);\n      } else if (val.label == '数据管理' && level == 1) {\n        this.$emit('changerHeaderData', '图层管理', true);\n        this.changeTopItem({\n          label: '图层管理'\n        }, 2);\n      } else if (this.currentOneMenu == '全景展示' && (level == 2 || level == 3)) {\n        let list = this.flattenedArray(this.tableList[1]?.children);\n        list.forEach(item => {\n          if (item.label == val.label) {\n            this.$store.commit('action/getRegionalFlag', {\n              flag: true,\n              name: val.label\n            });\n            this.$emit('changerHeaderData', val.label, this.topFlag);\n          }\n        });\n      } else {\n        this.$emit('changerHeaderData', val.label, this.topFlag);\n      }\n    },\n    //数组扁平化\n    flattenedArray(arr) {\n      let result = [];\n      arr.forEach(item => {\n        if (item.children && item.children.length > 0) {\n          result = result.concat(this.flattenedArray(item.children));\n        } else {\n          result.push(item);\n        }\n      });\n      return result;\n    },\n    // 权限管理按钮\n    showManageBtn() {\n      if (this.$store.state.user.userInfo.userId == 6) {\n        if (this.$route.meta.name != '权限管理') {\n          this.manageBtnFlag = true;\n          this.$router.push({\n            name: 'AuthorityManagement'\n          });\n        }\n      } else {\n        this.$message({\n          message: '暂无权限进入权限管理',\n          type: 'error'\n        });\n      }\n    },\n    // 数字花桥按钮\n    goLivePage() {\n      let token = JSON.parse(localStorage.getItem('token'));\n      if (token) {\n        // 页面跳转\n        window.location.href = `http://2.36.27.133:5002/dataLake/dataService?token=${token}`;\n      } else {\n        this.$message({\n          message: '请先登录',\n          type: 'error'\n        });\n      }\n    },\n    // 放大屏幕\n    fullScreenBtn() {\n      let element = document.documentElement;\n      if (this.fullScreenFlag) {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitCancelFullScreen) {\n          document.webkitCancelFullScreen();\n        } else if (document.mozCancelFullScreen) {\n          document.mozCancelFullScreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      } else {\n        if (element.requestFullscreen) {\n          element.requestFullscreen();\n        } else if (element.webkitRequestFullScreen) {\n          element.webkitRequestFullScreen();\n        } else if (element.mozRequestFullScreen) {\n          element.mozRequestFullScreen();\n        } else if (element.msRequestFullscreen) {\n          // IE11\n          element.msRequestFullscreen();\n        }\n      }\n      this.fullScreenFlag = !this.fullScreenFlag;\n    },\n    // 判断是否为全屏状态\n    isFullScreen() {\n      let fullFlag = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement ? true : false;\n      if (!fullFlag) {\n        this.img = require('@/assets/images/slices/big.png');\n      } else {\n        this.img = require('@/assets/images/slices/small.png');\n      }\n      return fullFlag;\n    },\n    fullResize() {\n      let that = this;\n      if (!that.isFullScreen() && that.fullScreenFlag) {\n        that.fullScreenFlag = false;\n      }\n    },\n    // 二三维切换按钮\n    changeTwoThreeLayer(val) {\n      // if(val==null || val==undefined){}\n      this.layer = !this.layer;\n      this.$parent.isShowBaseLayer = false;\n      this.$store.commit('getIsShowPlay', this.layer ? \"3d\" : \"2d\");\n    },\n    // 鼠标离开隐藏\n    hideName() {\n      this.isBodyChild = false;\n    },\n    // 鼠标经过展示\n    showName(item) {\n      if (item == this.currentOneMenu && this.topFlag) {\n        this.isBodyChild = true;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserMenuInfo", "getUserMenuInfoJc", "mapState", "name", "data", "currentPage", "timer", "date", "week", "currentTime", "tableList", "currentOneMenu", "currentTwoMenu", "currentThreeMenu", "topFlag", "manageBtnFlag", "fullScreenFlag", "img", "require", "layer", "isBodyChild", "isNet", "sourceBtnShow", "backBtnShow", "nodeFlag", "watch", "nv", "$emit", "dataChannelText", "computed", "created", "$store", "dispatch", "mounted", "isShowPlay", "$route", "$nextTick", "TodaysDat", "initMenu", "meta", "window", "addEventListener", "fullResize", "destroyed", "clearInterval", "commit", "removeEventListener", "methods", "reloadFn", "location", "reload", "switchNode", "index", "ueEventFn", "$eventBus", "gotoreSource", "then", "res", "code", "extra", "children", "changeTopItem", "label", "backFn", "testVideo", "catch", "e", "console", "log", "_this", "weeks", "Array", "now", "Date", "day", "getDay", "getFullYear", "appendZero", "getMonth", "getDate", "setInterval", "getHours", "getMinutes", "getSeconds", "num", "val", "level", "flag", "changeName", "$router", "push", "changeTwoThreeLayer", "string", "params", "type", "list", "flattenedArray", "for<PERSON>ach", "item", "arr", "result", "length", "concat", "showManageBtn", "state", "user", "userInfo", "userId", "$message", "message", "goLivePage", "token", "JSON", "parse", "localStorage", "getItem", "href", "fullScreenBtn", "element", "document", "documentElement", "exitFullscreen", "webkitCancelFullScreen", "mozCancelFullScreen", "msExitFullscreen", "requestFullscreen", "webkitRequestFullScreen", "mozRequestFullScreen", "msRequestFullscreen", "isFullScreen", "fullFlag", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "that", "$parent", "isShowBaseLayer", "<PERSON><PERSON><PERSON>", "showName"], "sources": ["src/views/mainPage/components/mainHeader.vue"], "sourcesContent": ["<template>\r\n    <div class=\"mainHeader\" :class=\"isFull ? ' ' : 'allScreen'\">\r\n        <div class=\"container_main\" ref=\"container_main\">\r\n            <div class=\"leftTit\">\r\n                <div class=\"pic\">\r\n                    <img src=\"@/assets/images/mainPics/titleLog.png\" width=\"100%\">\r\n                </div>\r\n                <div class=\"titleTxt\">\r\n                    花桥经济开发区集成指挥平台\r\n                </div>\r\n            </div>\r\n            <div class=\"mainList\">\r\n                <ul>\r\n                    <li v-for=\"(item, index) of tableList\" :key=\"1 + index\">\r\n                        <div class=\"topLevelMeun\" :class=\"{ activeItem: item.label == currentOneMenu && topFlag }\"\r\n                            @mouseenter=\"showName(item.label)\">\r\n                            <span @click=\"changeName(item, 1)\" class=\"itemTxt\">{{ item.label }}</span>\r\n                        </div>\r\n                        <div class=\"bodyChild\" v-show=\"isBodyChild\" @mouseleave=\"hideName\">\r\n                            <div class=\"showNameItems\" v-show=\"item.label == currentOneMenu && topFlag\">\r\n                                <div class=\"children\" v-for=\"(val, i) of item.children\" :key=\"2 + i\">\r\n                                    <div class=\"child\"\r\n                                        :class=\"val.label == currentTwoMenu ? 'childAction' : 'childNoAction'\">\r\n                                        <div class=\"txt\" @click=\"changeName(val, 2)\">{{ val.label }}</div>\r\n                                    </div>\r\n                                    <div class=\"littleBox\" v-show=\"val.label == currentTwoMenu\">\r\n                                        <div class=\"childItems\" v-for=\"(child, num) of val.children\" :key=\"3 + num\"\r\n                                            :class=\"child.label == currentThreeMenu ? 'childAction' : 'childNoAction'\">\r\n                                            <div class=\"txt2\" @click=\"changeName(child, 3)\">\r\n                                                {{ child.label }}\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </li>\r\n\r\n\r\n                </ul>\r\n\r\n                <div class=\"rightBtns\">\r\n                    <div @click=\"gotoreSource\" v-show=\"sourceBtnShow\">\r\n                        <div class=\"back-btn\">\r\n                            决策资源中心\r\n                        </div>\r\n                    </div>\r\n                    <div @click=\"backFn\" v-show=\"backBtnShow\">\r\n                        <div class=\"back-btn\">\r\n                            数字孪生系统\r\n                        </div>\r\n                    </div>\r\n                    <!-- <div class=\"managementBtn\" :class=\"{ managementBtnAction: manageBtnFlag }\" @click=\"showManageBtn\">\r\n                        <div class=\"btn\">\r\n                            权限管理\r\n                        </div>\r\n                    </div> -->\r\n\r\n                    <!-- <div class=\"returnLiveBtn\" @click=\"goLivePage\">\r\n                        <div class=\"btn\">\r\n                            数字花桥\r\n                        </div>\r\n                    </div> -->\r\n\r\n                    <!-- <div class=\"returnLiveBtn\" @click=\"testVideo\">\r\n                    <div class=\"btn\">\r\n                       视频漫游\r\n                    </div>\r\n                </div> -->\r\n                    <div class=\"icons\" v-show=\"sourceBtnShow\">\r\n                        <!-- <i class=\"el-icon-microphone\" style=\"font-size: 30px;\" ></i> -->\r\n                        <img @click=\"ueEventFn('首页')\" title=\"首页\" src=\"@/assets/sy.png\" alt=\"\">\r\n                        <i @click=\"ueEventFn('天气模拟')\" title=\"天气\" style=\"font-size: 30px;\" class=\"el-icon-sunrise\"></i>\r\n                        <img @click=\"ueEventFn('清除所有')\" title=\"清除所有\" src=\"@/assets/qc1-icon.png\" alt=\"\">\r\n                        <img @click=\"ueEventFn('全局视角')\" title=\"全局视角\" src=\"@/assets/sj-icon.png\" alt=\"\">\r\n                        <img @click=\"ueEventFn('无人机')\" title=\"无人机\" src=\"@/assets/wrj-icon.png\" alt=\"\">\r\n                        <img @click=\"ueEventFn('清除')\" title=\"清除\" src=\"@/assets/qc-icon.png\" alt=\"\">\r\n                    </div>\r\n                    <div class=\"bigScreen\" @click=\"fullScreenBtn\">\r\n                        <img :src=\"img\" alt=\"\">\r\n                    </div>\r\n                    <div class=\"change\" @click=\"nodeFlag = !nodeFlag\" v-show=\"sourceBtnShow\">\r\n                        <img src=\"@/assets/change.png\" title=\"切换节点\" alt=\"\">\r\n                    </div>\r\n                    <!-- <div class=\"change\" style=\"width: 30px;\" @click=\"reloadFn\">\r\n                        <img src=\"@/assets/reload.png\" title=\"刷新\" alt=\"\">\r\n                    </div> -->\r\n                    <div class=\"changebox\" v-show=\"nodeFlag\">\r\n                        <span @click=\"switchNode(0)\">节点一</span>\r\n                        <span @click=\"switchNode(1)\">节点二</span>\r\n                    </div>\r\n                </div>\r\n\r\n\r\n            </div>\r\n\r\n\r\n            <div class=\"rightTimer\">\r\n                <div class=\"date\">\r\n                    <span>{{ week }} </span>\r\n                    <span>{{ date }}</span>\r\n                </div>\r\n                <div class=\"timer\">{{ currentTime }}</div>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { getUserMenuInfo, getUserMenuInfoJc } from '@/api/userMenu.js'\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    name: 'MainHeader',\r\n    data() {\r\n        return {\r\n            currentPage: '',\r\n            timer: '',\r\n            date: '',\r\n            week: '',\r\n            currentTime: '',\r\n            tableList: [],\r\n            currentOneMenu: '', //点击选中权限\r\n            currentTwoMenu: '',\r\n            currentThreeMenu: '',\r\n            topFlag: false, // 是否显示权限\r\n            manageBtnFlag: false, // 权限管理按钮\r\n            fullScreenFlag: false, // 全屏按钮\r\n            img: require('@/assets/images/slices/big.png'),\r\n            layer: '',\r\n            isBodyChild: true,\r\n            isNet: null,\r\n            sourceBtnShow: true,\r\n            backBtnShow: false,\r\n            nodeFlag: false,\r\n\r\n        };\r\n    },\r\n    watch: {\r\n        currentOneMenu(nv) {\r\n            if (nv == '视频点位') {\r\n                this.$emit('changerHeaderData', nv)\r\n            }\r\n        },\r\n        dataChannelText(nv) {\r\n            if (nv == '管网') {\r\n                this.isNet = nv\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['isShowPlay']),\r\n        ...mapState(\"action\", ['isFull']),\r\n        ...mapState(['dataChannelText']),\r\n    },\r\n\r\n    created() {\r\n        this.$store.dispatch('getUser');\r\n        this.$store.dispatch('getUserInfo')\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.layer = this.isShowPlay\r\n        this.currentPage = this.$route.name\r\n        this.$nextTick(() => {\r\n            this.TodaysDat();\r\n            this.initMenu();\r\n            if (this.$route.meta.name != '权限管理') {\r\n                this.manageBtnFlag = false\r\n            } else {\r\n                this.manageBtnFlag = true\r\n            }\r\n            // 监听页面是否全屏\r\n        })\r\n\r\n        window.addEventListener('resize', this.fullResize)\r\n\r\n    },\r\n    destroyed() {\r\n        clearInterval(this.timer);\r\n        this.$store.commit('getIsShowPlay', \"3d\")\r\n        window.removeEventListener('resize', this.fullResize)\r\n\r\n    },\r\n    methods: {\r\n        //页面刷新\r\n        reloadFn(){\r\n            window.location.reload();\r\n            // this.$router.push({path:\"/\"})\r\n        },\r\n        //切换节点\r\n        switchNode(index) {\r\n            this.$store.commit('setNodeIndex', index)\r\n        },\r\n        ueEventFn(name) {\r\n            this.$eventBus.$emit(\"senTxtToUe\", name)\r\n        },\r\n        // 切换菜单的数据\r\n        gotoreSource() {\r\n\r\n            getUserMenuInfoJc().then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.tableList = res.data.extra[0].children;\r\n                }\r\n            })\r\n            this.sourceBtnShow = false;\r\n            this.backBtnShow = true;\r\n            this.$emit('changerHeaderData', '专题图层', true)\r\n            this.changeTopItem({ label: '专题图层' }, 1)\r\n            this.$store.commit('getIsShowPlay', null)\r\n\r\n\r\n        },\r\n        backFn() {\r\n            this.topFlag = !this.topFlag;\r\n            this.initMenu();\r\n            this.sourceBtnShow = true;\r\n            this.backBtnShow = false;\r\n            this.$emit('changerHeaderData', '首页', true)\r\n            this.$store.commit('getIsShowPlay', \"3d\")\r\n        },\r\n        testVideo() {\r\n            this.$emit('changerHeaderData', '视频漫游', true)\r\n        },\r\n        async initMenu() {\r\n            // 查询用户权限\r\n            await getUserMenuInfo().then(res => {\r\n                this.tableList = res.data.extra\r\n\r\n\r\n                // this.tableList.push({\r\n                //     label: \"接口规范\"\r\n                // })\r\n\r\n            }).catch(e => { console.log(e) })\r\n        },\r\n\r\n        TodaysDat() {\r\n            var _this = this\r\n            var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')\r\n            var now = new Date()\r\n            var day = now.getDay()\r\n            _this.week = weeks[day] // 设置星期几\r\n            _this.date = new Date().getFullYear() + ' / ' + _this.appendZero(new Date().getMonth() + 1) + ' / ' + _this.appendZero(new Date().getDate()); // 设置年月日\r\n            _this.timer = setInterval(function () {\r\n                _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes()) + ':' + _this.appendZero(new Date().getSeconds()) //修改数据date\r\n            }, 1000);\r\n        },\r\n\r\n        appendZero(num) {\r\n            if (num < 10) {\r\n                return '0' + num\r\n            } else {\r\n                return num;\r\n            }\r\n        },\r\n        changeTopItem(val, level) {\r\n\r\n            if (level && level == 1) {\r\n                this.$store.commit('action/getRegionalFlag', { flag: false, name: '' });\r\n                this.isBodyChild = true\r\n                if (this.currentOneMenu == val.label || this.currentOneMenu != '') {\r\n                    this.topFlag = !this.topFlag;\r\n                    if (this.currentOneMenu != val.label) {\r\n                        this.topFlag = true\r\n                    }\r\n                    this.currentOneMenu = val.label\r\n                    this.currentTwoMenu = '';\r\n                    this.currentThreeMenu = '';\r\n                }\r\n                else {\r\n                    this.topFlag = !this.topFlag;\r\n                    this.currentOneMenu = val.label\r\n                    this.currentTwoMenu = '';\r\n                    this.currentThreeMenu = '';\r\n                }\r\n            } else if (level && level == 2) {\r\n                this.currentTwoMenu = val.label\r\n                this.currentThreeMenu = ''\r\n            } else if (level && level == 3) {\r\n                this.currentThreeMenu = val.label\r\n            }\r\n\r\n        },\r\n        // 头部菜单权限点击\r\n        changeName(val, level) {\r\n            if (this.isNet == '管网') {\r\n                this.$eventBus.$emit('senTxtToUe', '关闭管网UI')\r\n                this.isNet = null\r\n            }\r\n            this.changeTopItem(val, level)\r\n\r\n            switch (val.label) {\r\n                case '数据看板':\r\n                    this.$router.push({\r\n                        name: 'ComprehensiveSituation'\r\n                    })\r\n                    this.$eventBus.$emit(\"senTxtToUe\", \"数据看板\")\r\n                    this.$store.commit('action/getIsScreenShow', true)\r\n                    break;\r\n                case '地图切换':\r\n                    this.changeTwoThreeLayer()\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n            if (this.currentTwoMenu == '建筑衍生' && level == 3) {\r\n                let string = '建筑衍生' + val.label\r\n                this.$emit('changerHeaderData', string)\r\n            } else if (this.currentTwoMenu == '楼层视图' && level == 3) {\r\n                let params = { type: \"楼层视图\", name: val.label }\r\n                this.$emit('changerHeaderData', params)\r\n            } else if (val.label == '数据管理' && level == 1) {\r\n                this.$emit('changerHeaderData', '图层管理', true)\r\n                this.changeTopItem({ label: '图层管理' }, 2)\r\n            }\r\n            else if (this.currentOneMenu == '全景展示' && (level == 2 || level == 3)) {\r\n                let list = this.flattenedArray(this.tableList[1]?.children)\r\n                list.forEach(item => {\r\n                    if (item.label == val.label) {\r\n                        this.$store.commit('action/getRegionalFlag', { flag: true, name: val.label });\r\n                        this.$emit('changerHeaderData', val.label, this.topFlag)\r\n                    }\r\n                })\r\n            }\r\n            else {\r\n                this.$emit('changerHeaderData', val.label, this.topFlag)\r\n            }\r\n\r\n\r\n        },\r\n        //数组扁平化\r\n        flattenedArray(arr) {\r\n            let result = [];\r\n            arr.forEach(item => {\r\n                if (item.children && item.children.length > 0) {\r\n                    result = result.concat(this.flattenedArray(item.children));\r\n                } else {\r\n                    result.push(item);\r\n                }\r\n            });\r\n            return result;\r\n        },\r\n        // 权限管理按钮\r\n        showManageBtn() {\r\n            if (this.$store.state.user.userInfo.userId == 6) {\r\n                if (this.$route.meta.name != '权限管理') {\r\n                    this.manageBtnFlag = true\r\n                    this.$router.push({\r\n                        name: 'AuthorityManagement'\r\n                    })\r\n                }\r\n            } else {\r\n                this.$message({\r\n                    message: '暂无权限进入权限管理',\r\n                    type: 'error'\r\n                });\r\n            }\r\n        },\r\n        // 数字花桥按钮\r\n        goLivePage() {\r\n            let token = JSON.parse(localStorage.getItem('token'))\r\n            if (token) {\r\n                // 页面跳转\r\n                window.location.href = `http://2.36.27.133:5002/dataLake/dataService?token=${token}`\r\n\r\n            } else {\r\n                this.$message({\r\n                    message: '请先登录',\r\n                    type: 'error'\r\n                })\r\n            }\r\n\r\n        },\r\n        // 放大屏幕\r\n        fullScreenBtn() {\r\n            let element = document.documentElement;\r\n            if (this.fullScreenFlag) {\r\n                if (document.exitFullscreen) {\r\n                    document.exitFullscreen();\r\n                } else if (document.webkitCancelFullScreen) {\r\n                    document.webkitCancelFullScreen();\r\n                } else if (document.mozCancelFullScreen) {\r\n                    document.mozCancelFullScreen();\r\n                } else if (document.msExitFullscreen) {\r\n                    document.msExitFullscreen();\r\n                }\r\n            } else {\r\n                if (element.requestFullscreen) {\r\n                    element.requestFullscreen();\r\n                } else if (element.webkitRequestFullScreen) {\r\n                    element.webkitRequestFullScreen();\r\n                } else if (element.mozRequestFullScreen) {\r\n                    element.mozRequestFullScreen();\r\n                } else if (element.msRequestFullscreen) {\r\n                    // IE11\r\n                    element.msRequestFullscreen();\r\n                }\r\n\r\n            }\r\n            \r\n            this.fullScreenFlag = !this.fullScreenFlag;\r\n        },\r\n\r\n        // 判断是否为全屏状态\r\n        isFullScreen() {\r\n            let fullFlag = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement ? true : false;\r\n            if (!fullFlag) {\r\n                this.img = require('@/assets/images/slices/big.png');\r\n\r\n            } else {\r\n                this.img = require('@/assets/images/slices/small.png');\r\n            }\r\n            return fullFlag;\r\n        },\r\n        fullResize() {\r\n            let that = this;\r\n            if (!that.isFullScreen() && that.fullScreenFlag) {\r\n                that.fullScreenFlag = false\r\n            }\r\n\r\n        },\r\n\r\n        // 二三维切换按钮\r\n        changeTwoThreeLayer(val) {\r\n            // if(val==null || val==undefined){}\r\n            this.layer = !this.layer;\r\n            this.$parent.isShowBaseLayer = false\r\n            this.$store.commit('getIsShowPlay', this.layer ? \"3d\" : \"2d\")\r\n\r\n        },\r\n        // 鼠标离开隐藏\r\n        hideName() {\r\n            this.isBodyChild = false\r\n        },\r\n        // 鼠标经过展示\r\n        showName(item) {\r\n            if (item == this.currentOneMenu && this.topFlag) {\r\n                this.isBodyChild = true\r\n            }\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.allScreen {\r\n    display: none;\r\n}\r\n\r\n.icons {\r\n    display: flex;\r\n    margin-left: 20px;\r\n\r\n    >img,\r\n    >i {\r\n        cursor: pointer;\r\n        width: 30px;\r\n        height: 30px;\r\n        margin-right: 20px;\r\n    }\r\n\r\n}\r\n\r\n.mainHeader {\r\n    user-select: none;\r\n    position: absolute;\r\n    top: 0;\r\n    color: #fff;\r\n    width: 100%;\r\n    height: calc(6vh);\r\n    box-sizing: border-box;\r\n    z-index: 10;\r\n    background: url('@/assets/images/mainPics/topBg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n\r\n    .container_main {\r\n        user-select: none;\r\n        position: absolute;\r\n        top: 0;\r\n        display: flex;\r\n        padding-top: 10px;\r\n        color: #fff;\r\n        height: 100%;\r\n        box-sizing: border-box;\r\n        z-index: 10;\r\n        transform-origin: 0 0;\r\n        transform: scale(var(--scaleX), var(--scaleY));\r\n        transition: 0.3s;\r\n\r\n    }\r\n\r\n    .leftTit {\r\n        padding-top: 25px;\r\n        width: 1100px;\r\n        height: fit-content;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 30px;\r\n        // justify-content: space-evenly;\r\n\r\n        .pic {\r\n            width: 150px;\r\n        }\r\n\r\n        .titleTxt {\r\n            font-size: 50px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: bold;\r\n            margin-left: 30px;\r\n            font-family: Source Han Sans CN;\r\n            background: linear-gradient(0deg, rgba(139, 222, 255, 1) 50%, rgba(255, 255, 255, .8) 50%);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n        }\r\n    }\r\n\r\n    .mainList {\r\n        width: 2400px;\r\n        height: fit-content;\r\n        padding-left: 100px;\r\n        display: flex;\r\n        align-items: center;\r\n        box-sizing: border-box;\r\n\r\n\r\n        ul {\r\n            display: flex;\r\n            align-items: center;\r\n            height: 80px;\r\n            font-size: 30px;\r\n\r\n            /* padding-top: .8rem; */\r\n            li {\r\n                position: relative;\r\n\r\n            }\r\n\r\n            .topLevelMeun {\r\n                position: relative;\r\n                width: 200px;\r\n                line-height: 65px;\r\n                // padding: .35rem 0;\r\n                text-align: center;\r\n                margin-left: -20px;\r\n                cursor: pointer;\r\n                background: url('@/assets/images/mainPics/listBgc.png') no-repeat center;\r\n                background-size: 100% 100%;\r\n                color: #fff;\r\n\r\n                .itemTxt {\r\n                    display: inline-block;\r\n                    width: 200px;\r\n                    height: 65px;\r\n                    // padding: 2px 3px 4px 4px;\r\n                    user-select: none;\r\n                }\r\n\r\n                i:hover {\r\n                    color: #20abff;\r\n                }\r\n\r\n                /* .showNameItems {\r\n                        width: 100%;\r\n                        position: absolute;\r\n                        background-color: pink;\r\n                    } */\r\n            }\r\n\r\n            .showNameItems {\r\n                position: absolute;\r\n                top: 150%;\r\n                left: -20%;\r\n                // height: 200px;\r\n                /* display: flex; */\r\n                user-select: none;\r\n\r\n                .child {\r\n                    width: 180px;\r\n                    height: fit-content;\r\n                    padding: 15px 0;\r\n                    // line-height:60px;\r\n                    text-align: center;\r\n                    cursor: pointer;\r\n                    margin: 0 10px;\r\n                    margin-bottom: 20px;\r\n\r\n                }\r\n\r\n                .littleBox {\r\n                    /* display: flex; */\r\n                    position: absolute;\r\n                    top: 0%;\r\n                    left: 100%;\r\n\r\n                    .childItems {\r\n                        width: 270px;\r\n                        height: fit-content;\r\n                        padding: 18px 0;\r\n                        text-align: center;\r\n                        cursor: pointer;\r\n                        margin-right: 10px;\r\n                        margin-bottom: 20px;\r\n\r\n                    }\r\n                }\r\n            }\r\n\r\n            .childAction {\r\n                background: url('@/assets/images/mainPics/bgIcon2.png') no-repeat center;\r\n                background-size: 100% 100%;\r\n            }\r\n\r\n            .childNoAction {\r\n                background: url('@/assets/images/mainPics/bgIcon.png') no-repeat center;\r\n                background-size: 100% 100%;\r\n            }\r\n\r\n            .activeItem {\r\n                background: url('@/assets/images/mainPics/activeBg.png') no-repeat center;\r\n                background-size: 100% 100%;\r\n            }\r\n\r\n\r\n            .noActiveItem {\r\n                background: url('@/assets/images/mainPics/listBgc.png') no-repeat center;\r\n                background-size: 100%;\r\n            }\r\n\r\n\r\n            // .topLevelMeun:active {\r\n            //     background: url('@/assets/images/mainPics/activeBg.png') no-repeat center;\r\n            //     background-size: 100%;\r\n            // }\r\n\r\n\r\n\r\n        }\r\n\r\n        .rightBtns {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-left: 350px;\r\n        }\r\n\r\n        .managementBtn,\r\n        .returnLiveBtn,\r\n        .changeTwoThreeLayer {\r\n            padding: 0 10px;\r\n            height: 50px;\r\n            line-height: 50px;\r\n            background: linear-gradient(0deg, rgba(1, 210, 255, .4) 0%, rgb(57, 124, 213) 100%);\r\n            font-size: 25px;\r\n            cursor: pointer;\r\n            color: #fff;\r\n            margin: 0 .2rem;\r\n        }\r\n\r\n        .changeTwoThreeLayer {\r\n            font-size: 30px;\r\n        }\r\n\r\n        .back-btn {\r\n            background-image: url(\"@/assets/images/mainPics/back-btn.png\");\r\n            background-size: 100% 100%;\r\n            width: 257px;\r\n            height: 65px;\r\n            padding: 0 13px;\r\n            line-height: 60px;\r\n            font-size: 25px;\r\n            cursor: pointer;\r\n            color: #fff;\r\n            margin-right: 200px;\r\n        }\r\n\r\n        .managementBtn:active {\r\n            background: linear-gradient(0deg, rgba(1, 210, 255) 0%, rgb(57, 124, 213) 100%);\r\n\r\n        }\r\n\r\n        .returnLiveBtn:active {\r\n            background: linear-gradient(0deg, rgba(1, 210, 255) 0%, rgb(57, 124, 213) 100%);\r\n\r\n        }\r\n\r\n        .managementBtnAction {\r\n            background: linear-gradient(0deg, rgba(1, 210, 255) 0%, rgb(57, 124, 213) 100%);\r\n\r\n        }\r\n\r\n        .bigScreen {\r\n            width: 30px;\r\n            margin-left: 10px;\r\n            cursor: pointer;\r\n\r\n            img {\r\n                width: 100%;\r\n                height: 100%;\r\n            }\r\n        }\r\n\r\n        .change {\r\n            width: 40px;\r\n            margin-left: 20px;\r\n            cursor: pointer;\r\n\r\n            img {\r\n                width: 100%;\r\n                height: 100%;\r\n            }\r\n        }\r\n    }\r\n\r\n    .changebox {\r\n        position: fixed;\r\n        top: 96px;\r\n        right: 330px;\r\n        width: 200px;\r\n        height: 120px;\r\n        background: linear-gradient(152deg, rgba(8, 68, 184, 0.46) 0%, rgba(18, 76, 111, 0.37) 100%), rgba(0, 0, 0, 0.2);\r\n        box-shadow: inset 9px 9px 15px 0px rgba(0, 208, 255, 0.2), inset -9px -9px 15px 0px rgba(0, 208, 255, 0.2);\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding-top: 20px;\r\n\r\n        >span {\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 25px;\r\n            color: #FFFFFF;\r\n            line-height: 42px;\r\n            text-align: center;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            cursor: pointer;\r\n        }\r\n\r\n        >span:hover {\r\n            background: linear-gradient(93deg, #00C6C1 0%, #2E5E7D 100%);\r\n            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\r\n        }\r\n    }\r\n\r\n    .rightTimer {\r\n        margin-left: 50px;\r\n        padding-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        width: 250px;\r\n        height: 100%;\r\n        text-align: center;\r\n        color: #fff;\r\n\r\n        .date {\r\n            font-size: 20px;\r\n        }\r\n\r\n        .timer {\r\n            margin-top: 15px;\r\n            text-align: center;\r\n            font-family: Adobe Heiti Std;\r\n            font-weight: normal;\r\n            font-size: 30px;\r\n            color: #FFFFFF;\r\n\r\n        }\r\n\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAiHA,SAAAA,eAAA,EAAAC,iBAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;MACAC,cAAA;MAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,OAAA;MAAA;MACAC,aAAA;MAAA;MACAC,cAAA;MAAA;MACAC,GAAA,EAAAC,OAAA;MACAC,KAAA;MACAC,WAAA;MACAC,KAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;IAEA;EACA;EACAC,KAAA;IACAd,eAAAe,EAAA;MACA,IAAAA,EAAA;QACA,KAAAC,KAAA,sBAAAD,EAAA;MACA;IACA;IACAE,gBAAAF,EAAA;MACA,IAAAA,EAAA;QACA,KAAAL,KAAA,GAAAK,EAAA;MACA;IACA;EACA;EACAG,QAAA;IACA,GAAA3B,QAAA;IACA,GAAAA,QAAA;IACA,GAAAA,QAAA;EACA;EAEA4B,QAAA;IACA,KAAAC,MAAA,CAAAC,QAAA;IACA,KAAAD,MAAA,CAAAC,QAAA;EAEA;EAEAC,QAAA;IACA,KAAAd,KAAA,QAAAe,UAAA;IACA,KAAA7B,WAAA,QAAA8B,MAAA,CAAAhC,IAAA;IACA,KAAAiC,SAAA;MACA,KAAAC,SAAA;MACA,KAAAC,QAAA;MACA,SAAAH,MAAA,CAAAI,IAAA,CAAApC,IAAA;QACA,KAAAY,aAAA;MACA;QACA,KAAAA,aAAA;MACA;MACA;IACA;IAEAyB,MAAA,CAAAC,gBAAA,gBAAAC,UAAA;EAEA;EACAC,UAAA;IACAC,aAAA,MAAAtC,KAAA;IACA,KAAAyB,MAAA,CAAAc,MAAA;IACAL,MAAA,CAAAM,mBAAA,gBAAAJ,UAAA;EAEA;EACAK,OAAA;IACA;IACAC,SAAA;MACAR,MAAA,CAAAS,QAAA,CAAAC,MAAA;MACA;IACA;IACA;IACAC,WAAAC,KAAA;MACA,KAAArB,MAAA,CAAAc,MAAA,iBAAAO,KAAA;IACA;IACAC,UAAAlD,IAAA;MACA,KAAAmD,SAAA,CAAA3B,KAAA,eAAAxB,IAAA;IACA;IACA;IACAoD,aAAA;MAEAtD,iBAAA,GAAAuD,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArD,IAAA,CAAAsD,IAAA;UACA,KAAAhD,SAAA,GAAA+C,GAAA,CAAArD,IAAA,CAAAuD,KAAA,IAAAC,QAAA;QACA;MACA;MACA,KAAAtC,aAAA;MACA,KAAAC,WAAA;MACA,KAAAI,KAAA;MACA,KAAAkC,aAAA;QAAAC,KAAA;MAAA;MACA,KAAA/B,MAAA,CAAAc,MAAA;IAGA;IACAkB,OAAA;MACA,KAAAjD,OAAA,SAAAA,OAAA;MACA,KAAAwB,QAAA;MACA,KAAAhB,aAAA;MACA,KAAAC,WAAA;MACA,KAAAI,KAAA;MACA,KAAAI,MAAA,CAAAc,MAAA;IACA;IACAmB,UAAA;MACA,KAAArC,KAAA;IACA;IACA,MAAAW,SAAA;MACA;MACA,MAAAtC,eAAA,GAAAwD,IAAA,CAAAC,GAAA;QACA,KAAA/C,SAAA,GAAA+C,GAAA,CAAArD,IAAA,CAAAuD,KAAA;;QAGA;QACA;QACA;MAEA,GAAAM,KAAA,CAAAC,CAAA;QAAAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;MAAA;IACA;IAEA7B,UAAA;MACA,IAAAgC,KAAA;MACA,IAAAC,KAAA,OAAAC,KAAA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,IAAAC,GAAA,GAAAF,GAAA,CAAAG,MAAA;MACAN,KAAA,CAAA7D,IAAA,GAAA8D,KAAA,CAAAI,GAAA;MACAL,KAAA,CAAA9D,IAAA,OAAAkE,IAAA,GAAAG,WAAA,aAAAP,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAK,QAAA,kBAAAT,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAM,OAAA;MACAV,KAAA,CAAA/D,KAAA,GAAA0E,WAAA;QACAX,KAAA,CAAA5D,WAAA,GAAA4D,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAQ,QAAA,YAAAZ,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAS,UAAA,YAAAb,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAU,UAAA;MACA;IACA;IAEAN,WAAAO,GAAA;MACA,IAAAA,GAAA;QACA,aAAAA,GAAA;MACA;QACA,OAAAA,GAAA;MACA;IACA;IACAvB,cAAAwB,GAAA,EAAAC,KAAA;MAEA,IAAAA,KAAA,IAAAA,KAAA;QACA,KAAAvD,MAAA,CAAAc,MAAA;UAAA0C,IAAA;UAAApF,IAAA;QAAA;QACA,KAAAiB,WAAA;QACA,SAAAT,cAAA,IAAA0E,GAAA,CAAAvB,KAAA,SAAAnD,cAAA;UACA,KAAAG,OAAA,SAAAA,OAAA;UACA,SAAAH,cAAA,IAAA0E,GAAA,CAAAvB,KAAA;YACA,KAAAhD,OAAA;UACA;UACA,KAAAH,cAAA,GAAA0E,GAAA,CAAAvB,KAAA;UACA,KAAAlD,cAAA;UACA,KAAAC,gBAAA;QACA,OACA;UACA,KAAAC,OAAA,SAAAA,OAAA;UACA,KAAAH,cAAA,GAAA0E,GAAA,CAAAvB,KAAA;UACA,KAAAlD,cAAA;UACA,KAAAC,gBAAA;QACA;MACA,WAAAyE,KAAA,IAAAA,KAAA;QACA,KAAA1E,cAAA,GAAAyE,GAAA,CAAAvB,KAAA;QACA,KAAAjD,gBAAA;MACA,WAAAyE,KAAA,IAAAA,KAAA;QACA,KAAAzE,gBAAA,GAAAwE,GAAA,CAAAvB,KAAA;MACA;IAEA;IACA;IACA0B,WAAAH,GAAA,EAAAC,KAAA;MACA,SAAAjE,KAAA;QACA,KAAAiC,SAAA,CAAA3B,KAAA;QACA,KAAAN,KAAA;MACA;MACA,KAAAwC,aAAA,CAAAwB,GAAA,EAAAC,KAAA;MAEA,QAAAD,GAAA,CAAAvB,KAAA;QACA;UACA,KAAA2B,OAAA,CAAAC,IAAA;YACAvF,IAAA;UACA;UACA,KAAAmD,SAAA,CAAA3B,KAAA;UACA,KAAAI,MAAA,CAAAc,MAAA;UACA;QACA;UACA,KAAA8C,mBAAA;UACA;QACA;UACA;MACA;MACA,SAAA/E,cAAA,cAAA0E,KAAA;QACA,IAAAM,MAAA,YAAAP,GAAA,CAAAvB,KAAA;QACA,KAAAnC,KAAA,sBAAAiE,MAAA;MACA,gBAAAhF,cAAA,cAAA0E,KAAA;QACA,IAAAO,MAAA;UAAAC,IAAA;UAAA3F,IAAA,EAAAkF,GAAA,CAAAvB;QAAA;QACA,KAAAnC,KAAA,sBAAAkE,MAAA;MACA,WAAAR,GAAA,CAAAvB,KAAA,cAAAwB,KAAA;QACA,KAAA3D,KAAA;QACA,KAAAkC,aAAA;UAAAC,KAAA;QAAA;MACA,OACA,SAAAnD,cAAA,eAAA2E,KAAA,SAAAA,KAAA;QACA,IAAAS,IAAA,QAAAC,cAAA,MAAAtF,SAAA,KAAAkD,QAAA;QACAmC,IAAA,CAAAE,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAApC,KAAA,IAAAuB,GAAA,CAAAvB,KAAA;YACA,KAAA/B,MAAA,CAAAc,MAAA;cAAA0C,IAAA;cAAApF,IAAA,EAAAkF,GAAA,CAAAvB;YAAA;YACA,KAAAnC,KAAA,sBAAA0D,GAAA,CAAAvB,KAAA,OAAAhD,OAAA;UACA;QACA;MACA,OACA;QACA,KAAAa,KAAA,sBAAA0D,GAAA,CAAAvB,KAAA,OAAAhD,OAAA;MACA;IAGA;IACA;IACAkF,eAAAG,GAAA;MACA,IAAAC,MAAA;MACAD,GAAA,CAAAF,OAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAtC,QAAA,IAAAsC,IAAA,CAAAtC,QAAA,CAAAyC,MAAA;UACAD,MAAA,GAAAA,MAAA,CAAAE,MAAA,MAAAN,cAAA,CAAAE,IAAA,CAAAtC,QAAA;QACA;UACAwC,MAAA,CAAAV,IAAA,CAAAQ,IAAA;QACA;MACA;MACA,OAAAE,MAAA;IACA;IACA;IACAG,cAAA;MACA,SAAAxE,MAAA,CAAAyE,KAAA,CAAAC,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,SAAAxE,MAAA,CAAAI,IAAA,CAAApC,IAAA;UACA,KAAAY,aAAA;UACA,KAAA0E,OAAA,CAAAC,IAAA;YACAvF,IAAA;UACA;QACA;MACA;QACA,KAAAyG,QAAA;UACAC,OAAA;UACAf,IAAA;QACA;MACA;IACA;IACA;IACAgB,WAAA;MACA,IAAAC,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,IAAAJ,KAAA;QACA;QACAvE,MAAA,CAAAS,QAAA,CAAAmE,IAAA,yDAAAL,KAAA;MAEA;QACA,KAAAH,QAAA;UACAC,OAAA;UACAf,IAAA;QACA;MACA;IAEA;IACA;IACAuB,cAAA;MACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,eAAA;MACA,SAAAxG,cAAA;QACA,IAAAuG,QAAA,CAAAE,cAAA;UACAF,QAAA,CAAAE,cAAA;QACA,WAAAF,QAAA,CAAAG,sBAAA;UACAH,QAAA,CAAAG,sBAAA;QACA,WAAAH,QAAA,CAAAI,mBAAA;UACAJ,QAAA,CAAAI,mBAAA;QACA,WAAAJ,QAAA,CAAAK,gBAAA;UACAL,QAAA,CAAAK,gBAAA;QACA;MACA;QACA,IAAAN,OAAA,CAAAO,iBAAA;UACAP,OAAA,CAAAO,iBAAA;QACA,WAAAP,OAAA,CAAAQ,uBAAA;UACAR,OAAA,CAAAQ,uBAAA;QACA,WAAAR,OAAA,CAAAS,oBAAA;UACAT,OAAA,CAAAS,oBAAA;QACA,WAAAT,OAAA,CAAAU,mBAAA;UACA;UACAV,OAAA,CAAAU,mBAAA;QACA;MAEA;MAEA,KAAAhH,cAAA,SAAAA,cAAA;IACA;IAEA;IACAiH,aAAA;MACA,IAAAC,QAAA,GAAAX,QAAA,CAAAY,iBAAA,IAAAZ,QAAA,CAAAa,uBAAA,IAAAb,QAAA,CAAAc,oBAAA,IAAAd,QAAA,CAAAe,mBAAA;MACA,KAAAJ,QAAA;QACA,KAAAjH,GAAA,GAAAC,OAAA;MAEA;QACA,KAAAD,GAAA,GAAAC,OAAA;MACA;MACA,OAAAgH,QAAA;IACA;IACAxF,WAAA;MACA,IAAA6F,IAAA;MACA,KAAAA,IAAA,CAAAN,YAAA,MAAAM,IAAA,CAAAvH,cAAA;QACAuH,IAAA,CAAAvH,cAAA;MACA;IAEA;IAEA;IACA2E,oBAAAN,GAAA;MACA;MACA,KAAAlE,KAAA,SAAAA,KAAA;MACA,KAAAqH,OAAA,CAAAC,eAAA;MACA,KAAA1G,MAAA,CAAAc,MAAA,uBAAA1B,KAAA;IAEA;IACA;IACAuH,SAAA;MACA,KAAAtH,WAAA;IACA;IACA;IACAuH,SAAAzC,IAAA;MACA,IAAAA,IAAA,SAAAvF,cAAA,SAAAG,OAAA;QACA,KAAAM,WAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}