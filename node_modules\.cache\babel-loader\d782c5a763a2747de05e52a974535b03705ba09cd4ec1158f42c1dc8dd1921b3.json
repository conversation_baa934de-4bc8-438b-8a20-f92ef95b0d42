{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getCoreIndex, getOrgAllCount, getHiddenStatus, getFireStatus, getSiteStatistic, getPortStatistic, getDeviceStatistic, getAlarmStatistic, getCountStatistic } from '@/api/chargingPiles.js';\nimport { getchargePointsTwoPoints, getchargePointsOnePoints, getkeyArea } from '@/api/index.js';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'RiskWarning',\n  props: {\n    tabsActive: {\n      type: Number,\n      default: 0\n    },\n    type: {\n      type: String,\n      default: '智行充'\n    }\n  },\n  data() {\n    return {\n      risk: null,\n      list: [],\n      countObj: {\n        siteNum: null,\n        portNum: null,\n        deviceNum: null,\n        unsolvedAlarmNum: null\n      },\n      count: null,\n      typeName: null,\n      orgIds: ''\n    };\n  },\n  beforeDestroy() {\n    if (this.risk) {\n      this.risk.clear();\n    }\n  },\n  watch: {\n    ueTxt(nv) {\n      if (nv && nv.levelType == 1) {\n        this.deletePoi();\n        this.getLevelTwoPointsList(nv.POIType);\n      }\n    },\n    tabsActive: {\n      handler(nv) {\n        this.getCountStatisticObj();\n      }\n    },\n    type: {\n      handler(nv) {\n        if (nv == '智行充') {\n          this.zxcHanleTab(0);\n        } else {\n          this.xfHandleTab(0);\n        }\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      ueTxt: state => state.dataChannelText\n    })\n  },\n  created() {\n    for (let i = 166; i <= 279; i++) {\n      this.orgIds += i + ',';\n    }\n  },\n  mounted() {\n    this.getCountStatisticObj(); //智行充设备总数\n  },\n  methods: {\n    //智行充图表切换\n    zxcHanleTab(nv) {\n      if (nv == '0') {\n        //站点数据\n        getSiteStatistic().then(res => {\n          if (res.data.code == 200) {\n            this.count = this.countObj.siteNum;\n            this.typeName = \"站点总数\";\n            this.list = res.data.extra;\n            this.init();\n          }\n        });\n      } else if (nv == '1') {\n        //设备数据\n        getDeviceStatistic().then(res => {\n          if (res.data.code == 200) {\n            this.count = this.countObj.deviceNum;\n            this.typeName = \"设备总数\";\n            this.list = res.data.extra;\n            this.init();\n          }\n        });\n      } else if (nv == '2') {\n        // 插座数据\n        getPortStatistic().then(res => {\n          if (res.data.code == 200) {\n            this.count = this.countObj.portNum;\n            this.typeName = \"插座总数\";\n            this.list = res.data.extra;\n            this.init();\n          }\n        });\n      } else if (nv == '3') {\n        // 预警数据\n        getAlarmStatistic().then(res => {\n          if (res.data.code == 200) {\n            this.count = this.countObj.unsolvedAlarmNum;\n            this.typeName = \"预警总数\";\n            this.list = res.data.extra;\n            this.init();\n          }\n        });\n      }\n    },\n    //消防图表切换\n    xfHandleTab(nv) {\n      if (nv == '0') {\n        getOrgAllCount({\n          orgIds: this.orgIds\n        }).then(res => {\n          //接入单位\n          if (res.data.status == 200) {\n            let data = res.data.data;\n            this.count = data.joinUpCount;\n            this.typeName = \"单位总数\";\n            let list = [{\n              name: '报警单位数量',\n              value: data.fireAlarmOrgCount,\n              per: (data.fireAlarmOrgCount / this.count * 100).toFixed(2)\n            }, {\n              name: '其他',\n              value: this.count - data.fireAlarmOrgCount,\n              per: ((this.count - data.fireAlarmOrgCount) / this.count * 100).toFixed(2)\n            }];\n            this.init(list);\n          }\n        });\n      } else if (nv == '1') {\n        getCoreIndex({\n          orgIds: this.orgIds\n        }).then(res => {\n          //核心指标\n          console.log(res, 'data');\n          if (res.data.status == 200) {\n            let data = res.data.data;\n            this.count = data.highRiskFireCount + data.importantCount;\n            this.typeName = \"指标总数\";\n            let list = [{\n              name: '重大问题数量',\n              value: data.highRiskFireCount,\n              per: (data.highRiskFireCount / this.count * 100).toFixed(2)\n            }, {\n              name: '高危信号数量',\n              value: data.importantCount,\n              per: (data.importantCount / this.count * 100).toFixed(2)\n            }];\n            this.init(list);\n          }\n        });\n      } else if (nv == '2') {\n        getHiddenStatus({\n          orgIds: this.orgIds\n        }).then(res => {\n          //隐患统计\n          if (res.data.status == 200) {\n            let data = res.data.data;\n            this.count = data.totalNum;\n            this.typeName = \"隐患总数\";\n            let list = [{\n              name: '已处理',\n              value: data.completeNum,\n              per: (data.completeNum / this.count * 100).toFixed(2)\n            }, {\n              name: '整改中数量',\n              value: data.continueNum,\n              per: (data.continueNum / this.count * 100).toFixed(2)\n            }, {\n              name: '核实中数量',\n              value: data.assignNum,\n              per: (data.assignNum / this.count * 100).toFixed(2)\n            }];\n            this.init(list);\n          }\n        });\n      } else if (nv == '3') {\n        getFireStatus({\n          orgIds: this.orgIds\n        }).then(res => {\n          //警情统计\n          if (res.data.status == 200) {\n            let data = res.data.data;\n            this.count = data.totalCount;\n            this.typeName = \"警情总数\";\n            let list = [{\n              name: '报警数量',\n              value: data.fireAlarmCount,\n              per: (data.fireAlarmCount / this.count * 100).toFixed(2)\n            }, {\n              name: '故障数量',\n              value: data.faultAlarmCount,\n              per: (data.faultAlarmCount / this.count * 100).toFixed(2)\n            }];\n            this.init(list);\n          }\n        });\n      }\n    },\n    handleTabsChange(nv) {\n      if (this.type == '智行充') {\n        this.zxcHanleTab(nv);\n      } else if (this.type == '消防') {\n        this.xfHandleTab(nv);\n      }\n    },\n    init(list) {\n      this.$nextTick(() => {\n        setTimeout(() => {\n          if (this.risk) {\n            this.risk.clear();\n          }\n          if (this.type == '智行充') {\n            this.list.forEach(item => {\n              item.value = item.count;\n              item.per = item.percent;\n            });\n          } else {\n            this.list = list;\n          }\n          this.myChart(this.list);\n        }, 200);\n      });\n    },\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    getCountStatisticObj() {\n      getCountStatistic().then(res => {\n        if (res.data.code == 200) {\n          this.countObj = res.data.extra;\n          this.handleTabsChange(this.tabsActive); //初始化\n        }\n      });\n    },\n    //站点一级撒点数据\n    getLevelOnePointsList() {\n      getchargePointsOnePoints().then(res => {\n        if (res.data.code == 200) {\n          this.addPoiFn(res.data.extra);\n        }\n      });\n    },\n    //站点二级撒点数据\n    getLevelTwoPointsList(community) {\n      getchargePointsTwoPoints({\n        community\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.addPoiFn(res.data.extra);\n        }\n      });\n    },\n    //单位总数撒点\n    getkeyAreaList(name) {\n      getkeyArea({\n        name\n      }).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra.data[0].points;\n          this.addPoiFn(JSON.parse(list));\n        }\n      });\n    },\n    addPoiFn(list) {\n      list.forEach((item, index) => {\n        item.Type = item.community;\n      });\n      let params = {\n        \"mode\": \"add\",\n        \"sources\": list\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    myChart(data) {\n      let that = this;\n      let legendData = [];\n      data.forEach(item => legendData.push(item.name));\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        this.risk = echarts.init(this.$refs.risk);\n        let option = {\n          color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n            offset: 0,\n            color: 'rgba(0, 255, 138, 1)'\n          }, {\n            offset: 1,\n            color: 'rgba(124, 157, 171, 0.27)'\n          }]), new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n            offset: 0,\n            color: 'rgba(136, 120, 23, 1)'\n          }, {\n            offset: 1,\n            color: 'rgba(255, 216, 0, 1)'\n          }]), new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n            offset: 0,\n            color: 'rgba(0, 216, 255, 1)'\n          }, {\n            offset: 1,\n            color: 'rgba(124, 157, 171, 0.27)'\n          }])],\n          legend: {\n            type: 'scroll',\n            orient: 'vertical',\n            right: '5%',\n            top: 'center',\n            bottom: '20%',\n            itemWidth: 20,\n            itemHeight: 20,\n            itemGap: 40,\n            height: '250px',\n            pageIconColor: '#fff',\n            // 翻页图标颜色\n            pageIconInactiveColor: '#aaa',\n            // 翻页图标未激活颜色\n            pageTextStyle: {\n              color: '#fff',\n              // 翻页文本颜色\n              fontSize: 20,\n              fontWeight: 'bold'\n            },\n            formatter: val => {\n              let index = legendData.indexOf(val);\n              return '{nameV|' + val + '}' + '\\t\\t\\t\\t' + '{txt|' + data[index]?.value + '/' + data[index]?.per + '%' + '}';\n            },\n            textStyle: {\n              color: [],\n              padding: [0, 0, 0, 18],\n              fontSize: 23,\n              fontWeight: 700,\n              // padding: [0, 0, 20, 5],\n\n              rich: {\n                nameV: {\n                  fontSize: 23,\n                  color: '#fff'\n                },\n                txt: {\n                  fontSize: 23\n                }\n              }\n            },\n            data: legendData\n          },\n          graphic: {\n            type: 'text',\n            left: '17%',\n            top: '43%',\n            z: 1000,\n            style: {\n              text: that.typeName + '\\n' + that.count,\n              textAlign: 'center',\n              fill: '#fff',\n              fontSize: 28,\n              fontWeight: 700\n            },\n            // 设置点击事件\n            onclick: function (val) {\n              if (val.target.parent.style.text.indexOf('站点总数') != -1) {\n                that.deletePoi();\n                that.getLevelOnePointsList(); //撒点\n                that.$eventBus.$emit('senTxtToUe', \"全局视角\");\n              } else if (val.target.parent.style.text.indexOf('单位总数') != -1) {\n                that.deletePoi();\n                that.getkeyAreaList('接入单位');\n                that.$eventBus.$emit('senTxtToUe', \"全局视角\");\n              }\n            },\n            cursor: 'pointer' // 鼠标悬浮时变为手型\n          },\n          series: [{\n            name: '环形图',\n            type: 'pie',\n            zlevel: 3,\n            //层级\n            radius: ['80%', '60%'],\n            left: 0,\n            left: '-50%',\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              // 鼠标移上去 圆环中间显示信息\n              position: 'center',\n              formatter: () => {\n                return '{name}';\n              },\n              rich: {\n                legend: {\n                  fontSize: 25,\n                  color: '#00DD00',\n                  align: 'right'\n                },\n                name: {\n                  fontSize: 12,\n                  color: '#666'\n                }\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: data\n          }, {\n            type: 'pie',\n            zlevel: 0,\n            silent: true,\n            radius: ['0%', '50%'],\n            left: '-50%',\n            color: '#FFB3FF',\n            label: {\n              show: false,\n              position: 'center',\n              formatter: () => {\n                return '{name}';\n              },\n              rich: {\n                legend: {\n                  fontSize: 30,\n                  color: 'white',\n                  align: 'right'\n                },\n                name: {\n                  fontSize: 12,\n                  color: '#FF0000'\n                }\n              }\n            },\n            data: [1]\n          }, {\n            type: 'pie',\n            zlevel: 1,\n            silent: true,\n            radius: ['100%', '90%'],\n            left: '-50%',\n            color: 'rgba(45, 46, 131, 0.28)',\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              color: {\n                x: 1,\n                y: 0,\n                x2: 1,\n                y2: 1,\n                type: 'linear',\n                global: false,\n                colorStops: [{\n                  offset: 0,\n                  color: 'rgba(4, 163, 236, 1)'\n                }, {\n                  offset: 1,\n                  color: 'rgba(4, 163, 236, 0.28)'\n                }]\n              }\n            },\n            data: [1]\n          }]\n        };\n        this.risk.setOption(option);\n        window.onresize = this.risk.resize;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getCoreIndex", "getOrgAllCount", "getHiddenStatus", "getFireStatus", "getSiteStatistic", "getPortStatistic", "getDeviceStatistic", "getAlarmStatistic", "getCountStatistic", "getchargePointsTwoPoints", "getchargePointsOnePoints", "getkeyArea", "mapState", "name", "props", "tabsActive", "type", "Number", "default", "String", "data", "risk", "list", "count<PERSON>b<PERSON>", "siteNum", "portNum", "deviceNum", "unsolvedAlarmNum", "count", "typeName", "orgIds", "<PERSON><PERSON><PERSON><PERSON>", "clear", "watch", "ueTxt", "nv", "levelType", "deletePoi", "getLevelTwoPointsList", "POIType", "handler", "getCountStatisticObj", "zxcHanleTab", "xfHandleTab", "computed", "state", "dataChannelText", "created", "i", "mounted", "methods", "then", "res", "code", "extra", "init", "status", "joinUpCount", "value", "fireAlarmOrgCount", "per", "toFixed", "console", "log", "highRiskFireCount", "importantCount", "totalNum", "completeNum", "continueNum", "assignNum", "totalCount", "fireAlarmCount", "faultAlarmCount", "handleTabsChange", "$nextTick", "setTimeout", "for<PERSON>ach", "item", "percent", "myChart", "params", "$eventBus", "$emit", "getLevelOnePointsList", "addPoiFn", "community", "getkeyAreaList", "points", "JSON", "parse", "index", "Type", "that", "legendData", "push", "Promise", "resolve", "$refs", "option", "color", "graphic", "LinearGradient", "offset", "legend", "orient", "right", "top", "bottom", "itemWidth", "itemHeight", "itemGap", "height", "pageIconColor", "pageIconInactiveColor", "pageTextStyle", "fontSize", "fontWeight", "formatter", "val", "indexOf", "textStyle", "padding", "rich", "nameV", "txt", "left", "z", "style", "text", "textAlign", "fill", "onclick", "target", "parent", "cursor", "series", "zlevel", "radius", "avoidLabelOverlap", "label", "show", "position", "align", "labelLine", "silent", "itemStyle", "x", "y", "x2", "y2", "global", "colorStops", "setOption", "window", "onresize", "resize"], "sources": ["src/components/comprehensive/RiskWarning.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"risk\" ref=\"risk\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { getCoreIndex, getOrgAllCount, getHiddenStatus, getFireStatus, getSiteStatistic, getPortStatistic, getDeviceStatistic, getAlarmStatistic, getCountStatistic } from '@/api/chargingPiles.js'\r\nimport { getchargePointsTwoPoints, getchargePointsOnePoints, getkeyArea } from '@/api/index.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'RiskWarning',\r\n    props: {\r\n        tabsActive: {\r\n            type: Number,\r\n            default: 0\r\n        },\r\n        type: {\r\n            type: String,\r\n            default: '智行充'\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            risk: null,\r\n            list: [],\r\n            countObj: {\r\n                siteNum: null,\r\n                portNum: null,\r\n                deviceNum: null,\r\n                unsolvedAlarmNum: null,\r\n            },\r\n            count: null,\r\n            typeName: null,\r\n            orgIds: '',\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.risk) {\r\n            this.risk.clear()\r\n        }\r\n    },\r\n    watch: {\r\n        ueTxt(nv) {\r\n            if (nv && nv.levelType == 1) {\r\n                this.deletePoi();\r\n                this.getLevelTwoPointsList(nv.POIType)\r\n            }\r\n        },\r\n        tabsActive: {\r\n            handler(nv) {\r\n                this.getCountStatisticObj();\r\n            }\r\n        },\r\n        type: {\r\n            handler(nv) {\r\n                if (nv == '智行充') {\r\n                    this.zxcHanleTab(0);\r\n                } else {\r\n                    this.xfHandleTab(0);\r\n                }\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            ueTxt: state => state.dataChannelText,\r\n        }),\r\n    },\r\n    created() {\r\n        for (let i = 166; i <= 279; i++) {\r\n            this.orgIds += i + ',';\r\n        }\r\n\r\n    },\r\n    mounted() {\r\n        this.getCountStatisticObj();//智行充设备总数\r\n    },\r\n\r\n    methods: {\r\n        //智行充图表切换\r\n        zxcHanleTab(nv) {\r\n            if (nv == '0') {//站点数据\r\n                getSiteStatistic().then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.count = this.countObj.siteNum;\r\n                        this.typeName = \"站点总数\"\r\n                        this.list = res.data.extra\r\n                        this.init();\r\n                    }\r\n                })\r\n            } else if (nv == '1') {//设备数据\r\n                getDeviceStatistic().then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.count = this.countObj.deviceNum;\r\n                        this.typeName = \"设备总数\"\r\n                        this.list = res.data.extra\r\n                        this.init();\r\n                    }\r\n                })\r\n            } else if (nv == '2') {// 插座数据\r\n                getPortStatistic().then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.count = this.countObj.portNum;\r\n                        this.typeName = \"插座总数\"\r\n                        this.list = res.data.extra\r\n                        this.init();\r\n                    }\r\n                })\r\n            } else if (nv == '3') {// 预警数据\r\n                getAlarmStatistic().then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.count = this.countObj.unsolvedAlarmNum;\r\n                        this.typeName = \"预警总数\"\r\n                        this.list = res.data.extra\r\n                        this.init();\r\n                    }\r\n                })\r\n            }\r\n        },\r\n        //消防图表切换\r\n        xfHandleTab(nv) {\r\n            if (nv == '0') {\r\n                getOrgAllCount({ orgIds: this.orgIds }).then(res => {//接入单位\r\n                    if (res.data.status == 200) {\r\n                        let data = res.data.data;\r\n                        this.count = data.joinUpCount;\r\n                        this.typeName = \"单位总数\"\r\n                        let list = [\r\n                            { name: '报警单位数量', value: data.fireAlarmOrgCount, per: ((data.fireAlarmOrgCount / this.count) * 100).toFixed(2) },\r\n                            { name: '其他', value: (this.count - data.fireAlarmOrgCount), per: (((this.count - data.fireAlarmOrgCount) / this.count) * 100).toFixed(2) },\r\n                        ]\r\n                        this.init(list);\r\n                    }\r\n                })\r\n            } else if (nv == '1') {\r\n                getCoreIndex({ orgIds: this.orgIds }).then(res => {//核心指标\r\n                    console.log(res, 'data')\r\n                    if (res.data.status == 200) {\r\n                        let data = res.data.data;\r\n                        this.count = data.highRiskFireCount + data.importantCount;\r\n                        this.typeName = \"指标总数\"\r\n                        let list = [\r\n                            { name: '重大问题数量', value: data.highRiskFireCount, per: ((data.highRiskFireCount / this.count) * 100).toFixed(2) },\r\n                            { name: '高危信号数量', value: data.importantCount, per: ((data.importantCount / this.count) * 100).toFixed(2) },\r\n                        ]\r\n                        this.init(list);\r\n                    }\r\n                })\r\n            } else if (nv == '2') {\r\n                getHiddenStatus({ orgIds: this.orgIds }).then(res => {//隐患统计\r\n                    if (res.data.status == 200) {\r\n                        let data = res.data.data;\r\n                        this.count = data.totalNum;\r\n                        this.typeName = \"隐患总数\"\r\n                        let list = [\r\n                            { name: '已处理', value: data.completeNum, per: ((data.completeNum / this.count) * 100).toFixed(2) },\r\n                            { name: '整改中数量', value: data.continueNum, per: ((data.continueNum / this.count) * 100).toFixed(2) },\r\n                            { name: '核实中数量', value: data.assignNum, per: ((data.assignNum / this.count) * 100).toFixed(2) },\r\n                        ]\r\n                        this.init(list);\r\n                    }\r\n                })\r\n            } else if (nv == '3') {\r\n                getFireStatus({ orgIds: this.orgIds }).then(res => {//警情统计\r\n                    if (res.data.status == 200) {\r\n                        let data = res.data.data;\r\n                        this.count = data.totalCount;\r\n                        this.typeName = \"警情总数\"\r\n                        let list = [\r\n                            { name: '报警数量', value: data.fireAlarmCount, per: ((data.fireAlarmCount / this.count) * 100).toFixed(2) },\r\n                            { name: '故障数量', value: data.faultAlarmCount, per: ((data.faultAlarmCount / this.count) * 100).toFixed(2) },\r\n                        ]\r\n                        this.init(list);\r\n                    }\r\n                })\r\n            }\r\n        },\r\n        handleTabsChange(nv) {\r\n            if (this.type == '智行充') {\r\n                this.zxcHanleTab(nv);\r\n\r\n            } else if (this.type == '消防') {\r\n                this.xfHandleTab(nv);\r\n            }\r\n\r\n        },\r\n        init(list) {\r\n            this.$nextTick(() => {\r\n                setTimeout(() => {\r\n                    if (this.risk) {\r\n                        this.risk.clear()\r\n                    }\r\n                    if (this.type == '智行充') {\r\n                        this.list.forEach(item => {\r\n                            item.value = item.count;\r\n                            item.per = item.percent;\r\n                        })\r\n                    } else {\r\n                        this.list = list;\r\n                    }\r\n                    this.myChart(this.list);\r\n                }, 200)\r\n\r\n            })\r\n        },\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        getCountStatisticObj() {\r\n            getCountStatistic().then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.countObj = res.data.extra;\r\n                    this.handleTabsChange(this.tabsActive);//初始化\r\n                }\r\n            })\r\n        },\r\n        //站点一级撒点数据\r\n        getLevelOnePointsList() {\r\n            getchargePointsOnePoints().then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.addPoiFn(res.data.extra)\r\n                }\r\n            })\r\n        },\r\n        //站点二级撒点数据\r\n        getLevelTwoPointsList(community) {\r\n            getchargePointsTwoPoints({ community }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.addPoiFn(res.data.extra)\r\n                }\r\n            })\r\n        },\r\n        //单位总数撒点\r\n        getkeyAreaList(name) {\r\n            getkeyArea({ name }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra.data[0].points;\r\n                    this.addPoiFn(JSON.parse(list))\r\n                }\r\n            })\r\n        },\r\n        addPoiFn(list) {\r\n            list.forEach((item, index) => {\r\n                item.Type = item.community;\r\n            });\r\n            let params = {\r\n                \"mode\": \"add\",\r\n                \"sources\": list,\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        myChart(data) {\r\n            let that = this;\r\n            let legendData = []\r\n            data.forEach(item => legendData.push(item.name))\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                this.risk = echarts.init(this.$refs.risk);\r\n                let option = {\r\n\r\n                    color: [\r\n                        new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                            { offset: 0, color: 'rgba(0, 255, 138, 1)' },\r\n                            { offset: 1, color: 'rgba(124, 157, 171, 0.27)' }\r\n                        ]),\r\n                        new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                            { offset: 0, color: 'rgba(136, 120, 23, 1)' },\r\n                            { offset: 1, color: 'rgba(255, 216, 0, 1)' }\r\n                        ]),\r\n                        new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                            { offset: 0, color: 'rgba(0, 216, 255, 1)' },\r\n                            { offset: 1, color: 'rgba(124, 157, 171, 0.27)' }\r\n                        ]),\r\n\r\n                    ],\r\n                    legend: {\r\n                        type: 'scroll',\r\n                        orient: 'vertical',\r\n                        right: '5%',\r\n                        top: 'center',\r\n                        bottom: '20%',\r\n                        itemWidth: 20,\r\n                        itemHeight: 20,\r\n                        itemGap: 40,\r\n                        height: '250px',\r\n                        pageIconColor: '#fff', // 翻页图标颜色\r\n                        pageIconInactiveColor: '#aaa', // 翻页图标未激活颜色\r\n                        pageTextStyle: {\r\n                            color: '#fff', // 翻页文本颜色\r\n                            fontSize: 20,\r\n                            fontWeight: 'bold'\r\n                        },\r\n                        formatter: (val) => {\r\n                            let index = legendData.indexOf(val);\r\n                            return '{nameV|' + val + '}' + '\\t\\t\\t\\t' + '{txt|' + data[index]?.value + '/' + data[index]?.per + '%' + '}';\r\n                        },\r\n                        textStyle: {\r\n                            color: [],\r\n                            padding: [0, 0, 0, 18],\r\n                            fontSize: 23,\r\n                            fontWeight: 700,\r\n                            // padding: [0, 0, 20, 5],\r\n\r\n                            rich: {\r\n                                nameV: {\r\n                                    fontSize: 23,\r\n                                    color: '#fff'\r\n                                },\r\n                                txt: {\r\n                                    fontSize: 23,\r\n\r\n                                }\r\n                            }\r\n                        },\r\n                        data: legendData,\r\n                    },\r\n                    graphic: {\r\n                        type: 'text',\r\n                        left: '17%',\r\n                        top: '43%',\r\n                        z: 1000,\r\n                        style: {\r\n                            text: that.typeName + '\\n' + that.count,\r\n                            textAlign: 'center',\r\n                            fill: '#fff',\r\n                            fontSize: 28,\r\n                            fontWeight: 700,\r\n                        }, // 设置点击事件\r\n                        onclick: function (val) {\r\n                            if (val.target.parent.style.text.indexOf('站点总数') != -1) {\r\n                                that.deletePoi();\r\n                                that.getLevelOnePointsList();//撒点\r\n                                that.$eventBus.$emit('senTxtToUe', \"全局视角\");\r\n                            } else if (val.target.parent.style.text.indexOf('单位总数') != -1) {\r\n                                that.deletePoi();\r\n                                that.getkeyAreaList('接入单位')\r\n                                that.$eventBus.$emit('senTxtToUe', \"全局视角\");\r\n                            }\r\n                        },\r\n                        cursor: 'pointer' // 鼠标悬浮时变为手型\r\n\r\n                    },\r\n                    series: [\r\n                        {\r\n                            name: '环形图',\r\n                            type: 'pie',\r\n                            zlevel: 3, //层级\r\n                            radius: ['80%', '60%'],\r\n                            left: 0,\r\n                            left: '-50%',\r\n                            avoidLabelOverlap: false,\r\n                            label: {\r\n                                show: false, // 鼠标移上去 圆环中间显示信息\r\n                                position: 'center',\r\n                                formatter: () => {\r\n                                    return '{name}';\r\n                                },\r\n                                rich: {\r\n                                    legend: {\r\n                                        fontSize: 25,\r\n                                        color: '#00DD00',\r\n                                        align: 'right'\r\n                                    },\r\n                                    name: {\r\n                                        fontSize: 12,\r\n                                        color: '#666'\r\n                                    }\r\n                                }\r\n                            },\r\n                            labelLine: {\r\n                                show: false\r\n                            },\r\n                            data: data\r\n                        },\r\n                        {\r\n                            type: 'pie',\r\n                            zlevel: 0,\r\n                            silent: true,\r\n                            radius: ['0%', '50%'],\r\n                            left: '-50%',\r\n                            color: '#FFB3FF',\r\n                            label: {\r\n                                show: false,\r\n                                position: 'center',\r\n                                formatter: () => {\r\n                                    return '{name}';\r\n                                },\r\n\r\n                                rich: {\r\n                                    legend: {\r\n                                        fontSize: 30,\r\n                                        color: 'white',\r\n                                        align: 'right'\r\n                                    },\r\n                                    name: {\r\n                                        fontSize: 12,\r\n                                        color: '#FF0000'\r\n                                    }\r\n                                }\r\n                            },\r\n                            data: [1]\r\n                        },\r\n                        {\r\n                            type: 'pie',\r\n\r\n                            zlevel: 1,\r\n                            silent: true,\r\n                            radius: ['100%', '90%'],\r\n                            left: '-50%',\r\n                            color: 'rgba(45, 46, 131, 0.28)',\r\n                            label: {\r\n                                show: false\r\n                            },\r\n                            labelLine: {\r\n                                show: false\r\n                            },\r\n                            itemStyle: {\r\n                                color: {\r\n                                    x: 1,\r\n                                    y: 0,\r\n                                    x2: 1,\r\n                                    y2: 1,\r\n                                    type: 'linear',\r\n                                    global: false,\r\n                                    colorStops: [\r\n                                        {\r\n                                            offset: 0,\r\n                                            color: 'rgba(4, 163, 236, 1)'\r\n                                        },\r\n                                        {\r\n                                            offset: 1,\r\n                                            color: 'rgba(4, 163, 236, 0.28)'\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            },\r\n                            data: [1]\r\n                        },\r\n\r\n                    ]\r\n                };\r\n\r\n                this.risk.setOption(option);\r\n                window.onresize = this.risk.resize;\r\n\r\n            })\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.risk {\r\n    width: 100%;\r\n    height: 300px;\r\n    margin-top: 60px;\r\n    overflow-y: auto;\r\n    /* 添加垂直滚动条 */\r\n\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,YAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,iBAAA;AACA,SAAAC,wBAAA,EAAAC,wBAAA,EAAAC,UAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAG,MAAA;MACAD,OAAA;IACA;EACA;EACAE,KAAA;IACA;MACAC,IAAA;MACAC,IAAA;MACAC,QAAA;QACAC,OAAA;QACAC,OAAA;QACAC,SAAA;QACAC,gBAAA;MACA;MACAC,KAAA;MACAC,QAAA;MACAC,MAAA;IACA;EACA;EACAC,cAAA;IACA,SAAAV,IAAA;MACA,KAAAA,IAAA,CAAAW,KAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAAC,EAAA;MACA,IAAAA,EAAA,IAAAA,EAAA,CAAAC,SAAA;QACA,KAAAC,SAAA;QACA,KAAAC,qBAAA,CAAAH,EAAA,CAAAI,OAAA;MACA;IACA;IACAxB,UAAA;MACAyB,QAAAL,EAAA;QACA,KAAAM,oBAAA;MACA;IACA;IACAzB,IAAA;MACAwB,QAAAL,EAAA;QACA,IAAAA,EAAA;UACA,KAAAO,WAAA;QACA;UACA,KAAAC,WAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAhC,QAAA;MACAsB,KAAA,EAAAW,KAAA,IAAAA,KAAA,CAAAC;IACA;EACA;EACAC,QAAA;IACA,SAAAC,CAAA,QAAAA,CAAA,SAAAA,CAAA;MACA,KAAAlB,MAAA,IAAAkB,CAAA;IACA;EAEA;EACAC,QAAA;IACA,KAAAR,oBAAA;EACA;EAEAS,OAAA;IACA;IACAR,YAAAP,EAAA;MACA,IAAAA,EAAA;QAAA;QACA/B,gBAAA,GAAA+C,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;YACA,KAAAzB,KAAA,QAAAL,QAAA,CAAAC,OAAA;YACA,KAAAK,QAAA;YACA,KAAAP,IAAA,GAAA8B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;YACA,KAAAC,IAAA;UACA;QACA;MACA,WAAApB,EAAA;QAAA;QACA7B,kBAAA,GAAA6C,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;YACA,KAAAzB,KAAA,QAAAL,QAAA,CAAAG,SAAA;YACA,KAAAG,QAAA;YACA,KAAAP,IAAA,GAAA8B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;YACA,KAAAC,IAAA;UACA;QACA;MACA,WAAApB,EAAA;QAAA;QACA9B,gBAAA,GAAA8C,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;YACA,KAAAzB,KAAA,QAAAL,QAAA,CAAAE,OAAA;YACA,KAAAI,QAAA;YACA,KAAAP,IAAA,GAAA8B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;YACA,KAAAC,IAAA;UACA;QACA;MACA,WAAApB,EAAA;QAAA;QACA5B,iBAAA,GAAA4C,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;YACA,KAAAzB,KAAA,QAAAL,QAAA,CAAAI,gBAAA;YACA,KAAAE,QAAA;YACA,KAAAP,IAAA,GAAA8B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;YACA,KAAAC,IAAA;UACA;QACA;MACA;IACA;IACA;IACAZ,YAAAR,EAAA;MACA,IAAAA,EAAA;QACAlC,cAAA;UAAA6B,MAAA,OAAAA;QAAA,GAAAqB,IAAA,CAAAC,GAAA;UAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;YACA,IAAApC,IAAA,GAAAgC,GAAA,CAAAhC,IAAA,CAAAA,IAAA;YACA,KAAAQ,KAAA,GAAAR,IAAA,CAAAqC,WAAA;YACA,KAAA5B,QAAA;YACA,IAAAP,IAAA,IACA;cAAAT,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAAuC,iBAAA;cAAAC,GAAA,GAAAxC,IAAA,CAAAuC,iBAAA,QAAA/B,KAAA,QAAAiC,OAAA;YAAA,GACA;cAAAhD,IAAA;cAAA6C,KAAA,OAAA9B,KAAA,GAAAR,IAAA,CAAAuC,iBAAA;cAAAC,GAAA,SAAAhC,KAAA,GAAAR,IAAA,CAAAuC,iBAAA,SAAA/B,KAAA,QAAAiC,OAAA;YAAA,EACA;YACA,KAAAN,IAAA,CAAAjC,IAAA;UACA;QACA;MACA,WAAAa,EAAA;QACAnC,YAAA;UAAA8B,MAAA,OAAAA;QAAA,GAAAqB,IAAA,CAAAC,GAAA;UAAA;UACAU,OAAA,CAAAC,GAAA,CAAAX,GAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;YACA,IAAApC,IAAA,GAAAgC,GAAA,CAAAhC,IAAA,CAAAA,IAAA;YACA,KAAAQ,KAAA,GAAAR,IAAA,CAAA4C,iBAAA,GAAA5C,IAAA,CAAA6C,cAAA;YACA,KAAApC,QAAA;YACA,IAAAP,IAAA,IACA;cAAAT,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAA4C,iBAAA;cAAAJ,GAAA,GAAAxC,IAAA,CAAA4C,iBAAA,QAAApC,KAAA,QAAAiC,OAAA;YAAA,GACA;cAAAhD,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAA6C,cAAA;cAAAL,GAAA,GAAAxC,IAAA,CAAA6C,cAAA,QAAArC,KAAA,QAAAiC,OAAA;YAAA,EACA;YACA,KAAAN,IAAA,CAAAjC,IAAA;UACA;QACA;MACA,WAAAa,EAAA;QACAjC,eAAA;UAAA4B,MAAA,OAAAA;QAAA,GAAAqB,IAAA,CAAAC,GAAA;UAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;YACA,IAAApC,IAAA,GAAAgC,GAAA,CAAAhC,IAAA,CAAAA,IAAA;YACA,KAAAQ,KAAA,GAAAR,IAAA,CAAA8C,QAAA;YACA,KAAArC,QAAA;YACA,IAAAP,IAAA,IACA;cAAAT,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAA+C,WAAA;cAAAP,GAAA,GAAAxC,IAAA,CAAA+C,WAAA,QAAAvC,KAAA,QAAAiC,OAAA;YAAA,GACA;cAAAhD,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAAgD,WAAA;cAAAR,GAAA,GAAAxC,IAAA,CAAAgD,WAAA,QAAAxC,KAAA,QAAAiC,OAAA;YAAA,GACA;cAAAhD,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAAiD,SAAA;cAAAT,GAAA,GAAAxC,IAAA,CAAAiD,SAAA,QAAAzC,KAAA,QAAAiC,OAAA;YAAA,EACA;YACA,KAAAN,IAAA,CAAAjC,IAAA;UACA;QACA;MACA,WAAAa,EAAA;QACAhC,aAAA;UAAA2B,MAAA,OAAAA;QAAA,GAAAqB,IAAA,CAAAC,GAAA;UAAA;UACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAoC,MAAA;YACA,IAAApC,IAAA,GAAAgC,GAAA,CAAAhC,IAAA,CAAAA,IAAA;YACA,KAAAQ,KAAA,GAAAR,IAAA,CAAAkD,UAAA;YACA,KAAAzC,QAAA;YACA,IAAAP,IAAA,IACA;cAAAT,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAAmD,cAAA;cAAAX,GAAA,GAAAxC,IAAA,CAAAmD,cAAA,QAAA3C,KAAA,QAAAiC,OAAA;YAAA,GACA;cAAAhD,IAAA;cAAA6C,KAAA,EAAAtC,IAAA,CAAAoD,eAAA;cAAAZ,GAAA,GAAAxC,IAAA,CAAAoD,eAAA,QAAA5C,KAAA,QAAAiC,OAAA;YAAA,EACA;YACA,KAAAN,IAAA,CAAAjC,IAAA;UACA;QACA;MACA;IACA;IACAmD,iBAAAtC,EAAA;MACA,SAAAnB,IAAA;QACA,KAAA0B,WAAA,CAAAP,EAAA;MAEA,gBAAAnB,IAAA;QACA,KAAA2B,WAAA,CAAAR,EAAA;MACA;IAEA;IACAoB,KAAAjC,IAAA;MACA,KAAAoD,SAAA;QACAC,UAAA;UACA,SAAAtD,IAAA;YACA,KAAAA,IAAA,CAAAW,KAAA;UACA;UACA,SAAAhB,IAAA;YACA,KAAAM,IAAA,CAAAsD,OAAA,CAAAC,IAAA;cACAA,IAAA,CAAAnB,KAAA,GAAAmB,IAAA,CAAAjD,KAAA;cACAiD,IAAA,CAAAjB,GAAA,GAAAiB,IAAA,CAAAC,OAAA;YACA;UACA;YACA,KAAAxD,IAAA,GAAAA,IAAA;UACA;UACA,KAAAyD,OAAA,MAAAzD,IAAA;QACA;MAEA;IACA;IACAe,UAAA;MACA,IAAA2C,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAvC,qBAAA;MACAjC,iBAAA,GAAA2C,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,KAAA9B,QAAA,GAAA6B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;UACA,KAAAmB,gBAAA,MAAA1D,UAAA;QACA;MACA;IACA;IACA;IACAoE,sBAAA;MACAzE,wBAAA,GAAAyC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,KAAA+B,QAAA,CAAAhC,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;QACA;MACA;IACA;IACA;IACAhB,sBAAA+C,SAAA;MACA5E,wBAAA;QAAA4E;MAAA,GAAAlC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,KAAA+B,QAAA,CAAAhC,GAAA,CAAAhC,IAAA,CAAAkC,KAAA;QACA;MACA;IACA;IACA;IACAgC,eAAAzE,IAAA;MACAF,UAAA;QAAAE;MAAA,GAAAsC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,IAAA/B,IAAA,GAAA8B,GAAA,CAAAhC,IAAA,CAAAkC,KAAA,CAAAlC,IAAA,IAAAmE,MAAA;UACA,KAAAH,QAAA,CAAAI,IAAA,CAAAC,KAAA,CAAAnE,IAAA;QACA;MACA;IACA;IACA8D,SAAA9D,IAAA;MACAA,IAAA,CAAAsD,OAAA,EAAAC,IAAA,EAAAa,KAAA;QACAb,IAAA,CAAAc,IAAA,GAAAd,IAAA,CAAAQ,SAAA;MACA;MACA,IAAAL,MAAA;QACA;QACA,WAAA1D;MACA;MACA,KAAA2D,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAD,QAAA3D,IAAA;MACA,IAAAwE,IAAA;MACA,IAAAC,UAAA;MACAzE,IAAA,CAAAwD,OAAA,CAAAC,IAAA,IAAAgB,UAAA,CAAAC,IAAA,CAAAjB,IAAA,CAAAhE,IAAA;MACA,IAAAkF,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAA7C,IAAA;QACA,KAAA9B,IAAA,GAAAtB,OAAA,CAAAwD,IAAA,MAAA0C,KAAA,CAAA5E,IAAA;QACA,IAAA6E,MAAA;UAEAC,KAAA,GACA,IAAApG,OAAA,CAAAqG,OAAA,CAAAC,cAAA,cACA;YAAAC,MAAA;YAAAH,KAAA;UAAA,GACA;YAAAG,MAAA;YAAAH,KAAA;UAAA,EACA,GACA,IAAApG,OAAA,CAAAqG,OAAA,CAAAC,cAAA,cACA;YAAAC,MAAA;YAAAH,KAAA;UAAA,GACA;YAAAG,MAAA;YAAAH,KAAA;UAAA,EACA,GACA,IAAApG,OAAA,CAAAqG,OAAA,CAAAC,cAAA,cACA;YAAAC,MAAA;YAAAH,KAAA;UAAA,GACA;YAAAG,MAAA;YAAAH,KAAA;UAAA,EACA,EAEA;UACAI,MAAA;YACAvF,IAAA;YACAwF,MAAA;YACAC,KAAA;YACAC,GAAA;YACAC,MAAA;YACAC,SAAA;YACAC,UAAA;YACAC,OAAA;YACAC,MAAA;YACAC,aAAA;YAAA;YACAC,qBAAA;YAAA;YACAC,aAAA;cACAf,KAAA;cAAA;cACAgB,QAAA;cACAC,UAAA;YACA;YACAC,SAAA,EAAAC,GAAA;cACA,IAAA5B,KAAA,GAAAG,UAAA,CAAA0B,OAAA,CAAAD,GAAA;cACA,mBAAAA,GAAA,gCAAAlG,IAAA,CAAAsE,KAAA,GAAAhC,KAAA,SAAAtC,IAAA,CAAAsE,KAAA,GAAA9B,GAAA;YACA;YACA4D,SAAA;cACArB,KAAA;cACAsB,OAAA;cACAN,QAAA;cACAC,UAAA;cACA;;cAEAM,IAAA;gBACAC,KAAA;kBACAR,QAAA;kBACAhB,KAAA;gBACA;gBACAyB,GAAA;kBACAT,QAAA;gBAEA;cACA;YACA;YACA/F,IAAA,EAAAyE;UACA;UACAO,OAAA;YACApF,IAAA;YACA6G,IAAA;YACAnB,GAAA;YACAoB,CAAA;YACAC,KAAA;cACAC,IAAA,EAAApC,IAAA,CAAA/D,QAAA,UAAA+D,IAAA,CAAAhE,KAAA;cACAqG,SAAA;cACAC,IAAA;cACAf,QAAA;cACAC,UAAA;YACA;YAAA;YACAe,OAAA,WAAAA,CAAAb,GAAA;cACA,IAAAA,GAAA,CAAAc,MAAA,CAAAC,MAAA,CAAAN,KAAA,CAAAC,IAAA,CAAAT,OAAA;gBACA3B,IAAA,CAAAvD,SAAA;gBACAuD,IAAA,CAAAT,qBAAA;gBACAS,IAAA,CAAAX,SAAA,CAAAC,KAAA;cACA,WAAAoC,GAAA,CAAAc,MAAA,CAAAC,MAAA,CAAAN,KAAA,CAAAC,IAAA,CAAAT,OAAA;gBACA3B,IAAA,CAAAvD,SAAA;gBACAuD,IAAA,CAAAN,cAAA;gBACAM,IAAA,CAAAX,SAAA,CAAAC,KAAA;cACA;YACA;YACAoD,MAAA;UAEA;UACAC,MAAA,GACA;YACA1H,IAAA;YACAG,IAAA;YACAwH,MAAA;YAAA;YACAC,MAAA;YACAZ,IAAA;YACAA,IAAA;YACAa,iBAAA;YACAC,KAAA;cACAC,IAAA;cAAA;cACAC,QAAA;cACAxB,SAAA,EAAAA,CAAA;gBACA;cACA;cACAK,IAAA;gBACAnB,MAAA;kBACAY,QAAA;kBACAhB,KAAA;kBACA2C,KAAA;gBACA;gBACAjI,IAAA;kBACAsG,QAAA;kBACAhB,KAAA;gBACA;cACA;YACA;YACA4C,SAAA;cACAH,IAAA;YACA;YACAxH,IAAA,EAAAA;UACA,GACA;YACAJ,IAAA;YACAwH,MAAA;YACAQ,MAAA;YACAP,MAAA;YACAZ,IAAA;YACA1B,KAAA;YACAwC,KAAA;cACAC,IAAA;cACAC,QAAA;cACAxB,SAAA,EAAAA,CAAA;gBACA;cACA;cAEAK,IAAA;gBACAnB,MAAA;kBACAY,QAAA;kBACAhB,KAAA;kBACA2C,KAAA;gBACA;gBACAjI,IAAA;kBACAsG,QAAA;kBACAhB,KAAA;gBACA;cACA;YACA;YACA/E,IAAA;UACA,GACA;YACAJ,IAAA;YAEAwH,MAAA;YACAQ,MAAA;YACAP,MAAA;YACAZ,IAAA;YACA1B,KAAA;YACAwC,KAAA;cACAC,IAAA;YACA;YACAG,SAAA;cACAH,IAAA;YACA;YACAK,SAAA;cACA9C,KAAA;gBACA+C,CAAA;gBACAC,CAAA;gBACAC,EAAA;gBACAC,EAAA;gBACArI,IAAA;gBACAsI,MAAA;gBACAC,UAAA,GACA;kBACAjD,MAAA;kBACAH,KAAA;gBACA,GACA;kBACAG,MAAA;kBACAH,KAAA;gBACA;cAEA;YACA;YACA/E,IAAA;UACA;QAGA;QAEA,KAAAC,IAAA,CAAAmI,SAAA,CAAAtD,MAAA;QACAuD,MAAA,CAAAC,QAAA,QAAArI,IAAA,CAAAsI,MAAA;MAEA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}