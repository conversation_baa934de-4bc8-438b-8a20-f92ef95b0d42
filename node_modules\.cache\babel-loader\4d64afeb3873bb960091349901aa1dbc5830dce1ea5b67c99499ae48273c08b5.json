{"ast": null, "code": "import Swiper from 'swiper';\nimport 'swiper/css/swiper.min.css';\nimport popTitle from '@/components/popTitle/popTitle.vue';\nexport default {\n  name: 'WaterEventInfo',\n  props: {\n    WaterEventFlag: {\n      type: Boolean,\n      default: false\n    },\n    WaterEventData: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  components: {\n    popTitle\n  },\n  data() {\n    return {\n      mySwiper: null,\n      classList: {\n        fonSize: 'fz37',\n        width: 'w30'\n      },\n      title: '事件处理',\n      imgList: []\n    };\n  },\n  watch: {\n    WaterEventFlag(nv) {\n      if (nv) {\n        this.$nextTick(() => {\n          this.initSwiper();\n        });\n      }\n    },\n    WaterEventData(nv) {\n      if (nv?.dealImage) {\n        this.imgList = nv.dealImage.split('，');\n      }\n    }\n  },\n  computed: {},\n  mounted() {},\n  methods: {\n    initSwiper() {\n      this.mySwiper = new Swiper('.swiper-container', {\n        effect: 'fade',\n        fadeEffect: {\n          crossFade: true\n        },\n        observer: true,\n        //修改swiper自己或子元素时，自动初始化swiper\n        observeParents: true,\n        //修改swiper的父元素时，自动初始化swiper\n        loop: true,\n        // 无缝\n        autoplay: {\n          //自动开始\n          delay: 3000,\n          //时间间隔\n          disableOnInteraction: false //*手动操作轮播图后不会暂停*\n        },\n        paginationClickable: true,\n        slidesPerView: 1,\n        // 一组三个\n        spaceBetween: 30,\n        // 间隔\n        // 如果需要前进后退按钮\n        navigation: {\n          nextEl: '.swiper-button-next',\n          prevEl: '.swiper-button-prev'\n        },\n        // 窗口变化,重新init,针对F11全屏和放大缩小,必须加\n        observer: true,\n        observeParents: true,\n        // 如果需要分页器\n        pagination: {\n          el: '.swiper-pagination',\n          clickable: true // 分页器可以点击\n        }\n      });\n    },\n    closeFun() {\n      this.$emit('sendWaterEventFlag', false);\n    }\n  }\n};", "map": {"version": 3, "names": ["Swiper", "popTitle", "name", "props", "WaterEventFlag", "type", "Boolean", "default", "WaterEventData", "Object", "components", "data", "mySwiper", "classList", "fonSize", "width", "title", "imgList", "watch", "nv", "$nextTick", "initSwiper", "dealImage", "split", "computed", "mounted", "methods", "effect", "fadeEffect", "crossFade", "observer", "observeParents", "loop", "autoplay", "delay", "disableOnInteraction", "paginationClickable", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "navigation", "nextEl", "prevEl", "pagination", "el", "clickable", "closeFun", "$emit"], "sources": ["src/views/dataScreen/fileType/WaterEventInfo/WaterEventInfo.vue"], "sourcesContent": ["<template>\r\n\r\n    <div class=\"WaterEventInfo boxBgStyle\" v-if=\"WaterEventFlag\">\r\n        <popTitle :classList=\"classList\" :title=\"title\"></popTitle>\r\n        <div class=\"main \">\r\n            <div class=\"item\">\r\n                <div class=\"tit\">处理图片：</div>\r\n                <div class=\"picSwiper swiper-container\">\r\n                    <div class=\"swiper-wrapper gallery-top\">\r\n                        <div class=\"swiper-slide\" v-for=\"(item, index) of imgList\" :key=\"index\">\r\n                            <!-- dil{{ index }} -->\r\n                            <img :src=\"item\" alt=\"!\" width=\"100%\">\r\n                        </div>\r\n\r\n                    </div>\r\n                    <!-- 如果需要分页器 -->\r\n                    <div class=\"swiper-pagination\"></div>\r\n                    <!-- 如果需要导航按钮 -->\r\n                    <div class=\"swiper-button-prev\"></div>\r\n                    <div class=\"swiper-button-next\"></div>\r\n                </div>\r\n\r\n            </div>\r\n            <div class=\"item\">\r\n                <div class=\"tit\">处理时间：</div>\r\n                <div class=\"context\">{{ WaterEventData.dealTime }}</div>\r\n            </div>\r\n            <div class=\"item\">\r\n                <div class=\"tit\">处理人：</div>\r\n                <div class=\"context\">{{ WaterEventData.dealPersonName }}</div>\r\n            </div>\r\n            <div class=\"item\">\r\n                <div class=\"tit\">分配单位：</div>\r\n                <div class=\"context\">{{ WaterEventData.distributionUnit }}</div>\r\n            </div>\r\n            <div class=\"item\">\r\n                <div class=\"tit\">河流：</div>\r\n                <div class=\"context\">{{ WaterEventData.location }}</div>\r\n            </div>\r\n            <div class=\"item\">\r\n                <div class=\"tit\">处理内容：</div>\r\n                <div class=\"context\">{{ WaterEventData.edescribe }}</div>\r\n            </div>\r\n\r\n            <div class=\"item\">\r\n                <div class=\"tit\">上报：</div>\r\n                <div class=\"context\">{{ WaterEventData.reportContent }}</div>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nimport Swiper from 'swiper'\r\nimport 'swiper/css/swiper.min.css'\r\nimport popTitle from '@/components/popTitle/popTitle.vue'\r\nexport default {\r\n    name: 'WaterEventInfo',\r\n    props: {\r\n        WaterEventFlag: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        WaterEventData: {\r\n            type: Object,\r\n            default: () => { }\r\n        }\r\n    },\r\n    components: {\r\n        popTitle\r\n    },\r\n    data() {\r\n        return {\r\n            mySwiper: null,\r\n            classList: {\r\n                fonSize: 'fz37',\r\n                width: 'w30'\r\n            },\r\n            title: '事件处理',\r\n            imgList: [],\r\n        }\r\n    },\r\n\r\n    watch: {\r\n        WaterEventFlag(nv) {\r\n            if (nv) {\r\n                this.$nextTick(() => {\r\n                    this.initSwiper()\r\n\r\n                })\r\n            }\r\n        },\r\n        WaterEventData(nv) {\r\n            if (nv?.dealImage) {\r\n                this.imgList = nv.dealImage.split('，')\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    methods: {\r\n        initSwiper() {\r\n            this.mySwiper = new Swiper('.swiper-container', {\r\n                effect: 'fade',\r\n                fadeEffect: {\r\n                    crossFade: true,\r\n                }, \r\n                observer: true,//修改swiper自己或子元素时，自动初始化swiper\r\n                observeParents: true,//修改swiper的父元素时，自动初始化swiper\r\n                loop: true, // 无缝\r\n                autoplay: { //自动开始\r\n                    delay: 3000, //时间间隔\r\n                    disableOnInteraction: false, //*手动操作轮播图后不会暂停*\r\n                },\r\n                paginationClickable: true,\r\n                slidesPerView: 1, // 一组三个\r\n                spaceBetween: 30, // 间隔\r\n                // 如果需要前进后退按钮\r\n                navigation: {\r\n                    nextEl: '.swiper-button-next',\r\n                    prevEl: '.swiper-button-prev',\r\n                },\r\n                // 窗口变化,重新init,针对F11全屏和放大缩小,必须加\r\n                observer: true,\r\n                observeParents: true,\r\n                // 如果需要分页器\r\n                pagination: {\r\n                    el: '.swiper-pagination',\r\n                    clickable: true, // 分页器可以点击\r\n                },\r\n            })\r\n        },\r\n        closeFun() {\r\n            this.$emit('sendWaterEventFlag', false)\r\n\r\n        }\r\n    },\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.WaterEventInfo {\r\n    pointer-events: stroke;\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 800px;\r\n    height: fit-content;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n\r\n    .swiper-container {\r\n        margin: 10px auto;\r\n        width: 60%;\r\n        height: 300px;\r\n    }\r\n\r\n    .context {\r\n        margin: 10px auto;\r\n        width: 60%;\r\n    }\r\n\r\n    .main {\r\n        padding: 10px 20px;\r\n\r\n        .item {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            font-size: 25px;\r\n\r\n            .tit {\r\n                width: 20%;\r\n            }\r\n\r\n            .context {\r\n                text-wrap: wrap;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAwDA,OAAAA,MAAA;AACA;AACA,OAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,cAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;IACA;EACA;EACAG,UAAA;IACAT;EACA;EACAU,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACAC,KAAA;MACAC,OAAA;IACA;EACA;EAEAC,KAAA;IACAd,eAAAe,EAAA;MACA,IAAAA,EAAA;QACA,KAAAC,SAAA;UACA,KAAAC,UAAA;QAEA;MACA;IACA;IACAb,eAAAW,EAAA;MACA,IAAAA,EAAA,EAAAG,SAAA;QACA,KAAAL,OAAA,GAAAE,EAAA,CAAAG,SAAA,CAAAC,KAAA;MACA;IACA;EACA;EACAC,QAAA,GAEA;EACAC,QAAA,GAEA;EACAC,OAAA;IACAL,WAAA;MACA,KAAAT,QAAA,OAAAZ,MAAA;QACA2B,MAAA;QACAC,UAAA;UACAC,SAAA;QACA;QACAC,QAAA;QAAA;QACAC,cAAA;QAAA;QACAC,IAAA;QAAA;QACAC,QAAA;UAAA;UACAC,KAAA;UAAA;UACAC,oBAAA;QACA;QACAC,mBAAA;QACAC,aAAA;QAAA;QACAC,YAAA;QAAA;QACA;QACAC,UAAA;UACAC,MAAA;UACAC,MAAA;QACA;QACA;QACAX,QAAA;QACAC,cAAA;QACA;QACAW,UAAA;UACAC,EAAA;UACAC,SAAA;QACA;MACA;IACA;IACAC,SAAA;MACA,KAAAC,KAAA;IAEA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}