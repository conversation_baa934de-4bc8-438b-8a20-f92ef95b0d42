/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import"../geometry.js";import{ClonableMixin as t}from"../core/Clonable.js";import{JSONSupport as r}from"../core/JSONSupport.js";import{equalsMaybe as e}from"../core/maybe.js";import{property as s}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import{cast as i}from"../core/accessorSupport/decorators/cast.js";import{subclass as p}from"../core/accessorSupport/decorators/subclass.js";import{ensureNumber as a}from"../core/accessorSupport/ensureType.js";import{persistable as m}from"../core/accessorSupport/decorators/persistable.js";import{cyclicalDeg as l}from"../views/3d/support/mathUtils.js";import n from"../geometry/Point.js";let c=class extends(t(r)){constructor(o){super(o),this.type="plane",this.position=null,this.heading=0,this.tilt=0,this.width=10,this.height=10}equals(o){return this.heading===o.heading&&this.tilt===o.tilt&&e(this.position,o.position)&&this.width===o.width&&this.height===o.height}};o([s({readOnly:!0,json:{read:!1,write:!0}})],c.prototype,"type",void 0),o([s({type:n}),m()],c.prototype,"position",void 0),o([s({type:Number,nonNullable:!0,range:{min:0,max:360}}),m(),i((o=>l.normalize(a(o),0,!0)))],c.prototype,"heading",void 0),o([s({type:Number,nonNullable:!0,range:{min:0,max:360}}),m(),i((o=>l.normalize(a(o),0,!0)))],c.prototype,"tilt",void 0),o([s({type:Number,nonNullable:!0}),m()],c.prototype,"width",void 0),o([s({type:Number,nonNullable:!0}),m()],c.prototype,"height",void 0),c=o([p("esri.analysis.SlicePlane")],c);const h=c;export{h as default};
