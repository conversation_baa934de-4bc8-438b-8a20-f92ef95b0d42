/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../support/FeatureSet.js";import t from"../support/IdSet.js";import{layerGeometryEsriConstants as s,IdState as i}from"../support/shared.js";import{combine as r}from"../support/sqlUtils.js";import{resolve as n,isPromiseLike as a,reject as l,all as h}from"../../../core/promiseUtils.js";import{WhereClause as u}from"../../../core/sql/WhereClause.js";import c from"../../../geometry/SpatialReference.js";class _ extends e{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.actions.AttributeFilter",this._maxProcessing=1e3,this._parent=e.parentfeatureset,e.whereclause instanceof u?(this._whereclause=e.whereclause,this._whereClauseFunction=null):(this._whereClauseFunction=e.whereclause,this._whereclause=null)}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new c({wkid:4326}),this.geometryType=s.point)}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._parent._getFilteredSet("",null,this._whereclause,null,e))).then((s=>(this._checkCancelled(e),null!==this._whereClauseFunction?this._wset=new t(s._candidates.slice(0).concat(s._known.slice(0)),[],s._ordered,this._clonePageDefinition(s.pagesDefinition)):this._wset=new t(s._candidates.slice(0),s._known.slice(0),s._ordered,this._clonePageDefinition(s.pagesDefinition)),this._wset))):n(this._wset)}_isInFeatureSet(e){let t=this._parent._isInFeatureSet(e);return t===i.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?i.Unknown:t)}_getFeature(e,t,s){return this._parent._getFeature(e,t,s)}_getFeatures(e,t,s,i){return this._parent._getFeatures(e,t,s,i)}_featureFromCache(e){return this._parent._featureFromCache(e)}executeWhereClause(e){return this._whereclause.testFeature(e)}executeWhereClauseDeferred(e){if(null!==this._whereClauseFunction)try{const t=this._whereClauseFunction(e);return a(t)?t:n(t)}catch(t){return l(t)}return n(this.executeWhereClause(e))}_fetchAndRefineFeatures(e,s,r){const n=new t([],e,!1,null),a=Math.min(s,e.length);return this._parent._getFeatures(n,-1,a,r).then((()=>{if(this._checkCancelled(r),null==this._whereClauseFunction){for(let t=0;t<a;t++){const s=this._parent._featureFromCache(e[t]);!0===this.executeWhereClause(s)?this._idstates[e[t]]=i.InFeatureSet:this._idstates[e[t]]=i.NotInFeatureSet}return"success"}const t=[];for(let s=0;s<a;s++){const i=this._parent._featureFromCache(e[s]);t.push(this.executeWhereClauseDeferred(i))}return h(t).then((t=>{for(let r=0;r<s;r++)!0===t[r]?this._idstates[e[r]]=i.InFeatureSet:this._idstates[e[r]]=i.NotInFeatureSet;return"success"}))}))}_getFilteredSet(e,s,i,n,a){return null!==this._whereClauseFunction||(null!==i?null!==this._whereclause&&(i=r(this._whereclause,i)):i=this._whereclause),this._ensureLoaded().then((()=>this._parent._getFilteredSet(e,s,i,n,a))).then((e=>{let s;return this._checkCancelled(a),s=null!==this._whereClauseFunction?new t(e._candidates.slice(0).concat(e._known.slice(0)),[],e._ordered,this._clonePageDefinition(e.pagesDefinition)):new t(e._candidates.slice(0),e._known.slice(0),e._ordered,this._clonePageDefinition(e.pagesDefinition)),s}))}_stat(e,t,s,i,a,l,h){if(null!==this._whereClauseFunction)return null===a&&""===s&&null===i?this._manualStat(e,t,l,h):n({calculated:!1});let u=this._whereclause;return null!==a&&null!==this._whereclause&&(u=r(this._whereclause,a)),this._parent._stat(e,t,s,i,u,l,h).then((r=>!1===r.calculated?null===a&&""===s&&null===i?this._manualStat(e,t,l,h):{calculated:!1}:r))}_canDoAggregates(e,t,s,i,a){return null!==this._whereClauseFunction?n(!1):(null!==a?null!==this._whereclause&&(a=r(this._whereclause,a)):a=this._whereclause,null===this._parent?n(!1):this._parent._canDoAggregates(e,t,s,i,a))}_getAggregatePagesDataSourceDefinition(e,t,s,i,n,a,h){return null===this._parent?l(new Error("Should never be called")):(null!==n?null!==this._whereclause&&(n=r(this._whereclause,n)):n=this._whereclause,this._parent._getAggregatePagesDataSourceDefinition(e,t,s,i,n,a,h))}static registerAction(){e._featuresetFunctions.filter=function(e){if("function"==typeof e)return new _({parentfeatureset:this,whereclause:e});let t=null;return e instanceof u&&(t=e),new _({parentfeatureset:this,whereclause:t})}}}export{_ as default};
