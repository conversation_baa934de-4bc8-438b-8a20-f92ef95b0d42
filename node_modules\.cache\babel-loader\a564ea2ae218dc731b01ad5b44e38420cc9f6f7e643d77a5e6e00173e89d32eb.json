{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getHiddenDangerCountList } from '@/api/bigScreen';\nexport default {\n  name: 'RecentlyDanger',\n  data() {\n    return {\n      recently: null,\n      dangerData1: [],\n      dangerData2: [],\n      dataX: [],\n      legend: [\"上报总数\", \"处理总数\"]\n    };\n  },\n  beforeDestroy() {\n    if (this.recently) {\n      this.recently.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.recently != null && this.recently != \"\" && this.recently != undefined) {\n          this.recently.dispose();\n        }\n        this.recently = echarts.init(this.$refs.recently);\n        this.recently.clear();\n        getHiddenDangerCountList().then(res => {\n          let dataList = res.data.data;\n          let dealSum = 0;\n          for (var i of dataList) {\n            this.dataX.push(i.analysis_time.split(' ')[0]);\n            this.dangerData1.push(i.report_num);\n            this.dangerData2.push(i.handle_num);\n            dealSum += i.handle_num * 1;\n          }\n          // 变为字符串\n          dealSum = dealSum.toString();\n          this.$parent.dealDangerNum = dealSum;\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        color: ['rgba(72, 232, 238)', 'rgba(40, 99, 201)'],\n        grid: {\n          left: \"0\",\n          top: '25%',\n          bottom: '0%',\n          right: '0%',\n          containLabel: true\n        },\n        title: {\n          text: \"隐患上报 -- 统计分析\",\n          textStyle: {\n            color: \"#fff\",\n            fontSize: 22,\n            fontWeight: 500\n          }\n        },\n        tooltip: {\n          trigger: 'axis',\n          extraCssText: 'box-shadow: 0 0 10px #0F7CA4',\n          backgroundColor: \"rgba(16, 64, 100, 1)\",\n          borderColor: \"#0F7CA4\",\n          //设置边框颜色\n          textStyle: {\n            fontSize: 20,\n            fontWeight: 700,\n            color: \"#eee\" //设置文字颜色\n          }\n        },\n        legend: {\n          show: true,\n          right: '2%',\n          top: \"10%\",\n          itemStyle: {\n            borderType: ''\n          },\n          textStyle: {\n            color: \"#fff\",\n            fontSize: 21,\n            padding: 12\n          },\n          data: this.legend\n        },\n        xAxis: {\n          type: 'category',\n          data: this.dataX,\n          axisLabel: {\n            show: true,\n            color: \"#fff\",\n            fontSize: 20,\n            formatter: v => {\n              if (v < 10) return '0' + v;else return v;\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          interval: 50,\n          splitLine: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: \"#fff\",\n            fontSize: 20\n          }\n        },\n        series: [{\n          type: 'line',\n          name: \"上报总数\",\n          data: this.dangerData1,\n          symbolSize: 10,\n          symbol: \"circle\",\n          lineStyle: {\n            width: 2\n          },\n          areaStyle: {\n            // normal: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: \"rgba(73, 236, 243, 0.6)\"\n            }, {\n              offset: 1,\n              color: \"rgba(109, 126, 0, 0)\"\n            }], false)\n\n            // shadowColor: \"rgba(109, 126, 0, 0.5)\", //阴影颜色\n            // shadowBlur: 10, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。\n            // },\n          }\n        }, {\n          type: 'line',\n          name: \"处理总数\",\n          data: this.dangerData2,\n          symbolSize: 10,\n          symbol: \"circle\",\n          lineStyle: {\n            width: 2 // 0.1的线条是非常细的了\n          },\n          areaStyle: {\n            // normal: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: \"rgba(36, 90, 167, 1)\"\n            }, {\n              offset: 1,\n              color: \"rgba(36, 90, 167, .1)\"\n            }], false)\n\n            // shadowColor: \"rgba(109, 126, 0, 0.5)\", //阴影颜色\n            // shadowBlur: 10, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。\n            // },\n          }\n        }]\n      };\n      this.recently.setOption(option, true);\n      window.onresize = this.recently.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getHiddenDangerCountList", "name", "data", "recently", "dangerData1", "dangerData2", "dataX", "legend", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "dealSum", "i", "push", "analysis_time", "split", "report_num", "handle_num", "toString", "$parent", "dealDangerNum", "setOption", "option", "color", "grid", "left", "top", "bottom", "right", "containLabel", "title", "text", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "extraCssText", "backgroundColor", "borderColor", "show", "itemStyle", "borderType", "padding", "xAxis", "type", "axisLabel", "formatter", "v", "yAxis", "interval", "splitLine", "series", "symbolSize", "symbol", "lineStyle", "width", "areaStyle", "graphic", "LinearGradient", "offset", "window", "onresize", "resize"], "sources": ["src/components/comprehensive/RecentlyDanger.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"recently\" ref=\"recently\"></div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import * as echarts from 'echarts';\r\n    import { getHiddenDangerCountList } from '@/api/bigScreen'\r\n    export default {\r\n        name: 'RecentlyDanger',\r\n\r\n        data() {\r\n            return {\r\n                recently: null,\r\n                dangerData1: [],\r\n                dangerData2: [],\r\n                dataX: [],\r\n                legend: [\"上报总数\", \"处理总数\"]\r\n            };\r\n        },\r\n        beforeDestroy() {\r\n        if(this.recently){\r\n            this.recently.clear()\r\n        }\r\n    },\r\n        mounted() {\r\n            this.$nextTick(() => {\r\n                this.myChart()\r\n\r\n\r\n            })\r\n        },\r\n\r\n        methods: {\r\n            myChart() {\r\n\r\n                new Promise((resolve) => {\r\n                    resolve()\r\n                }).then(() => {\r\n                    if (this.recently != null && this.recently != \"\" && this.recently != undefined) {\r\n                        this.recently.dispose();\r\n                    }\r\n                    this.recently = echarts.init(this.$refs.recently);\r\n                    this.recently.clear();\r\n\r\n                    getHiddenDangerCountList().then(res => {\r\n                        let dataList = res.data.data;\r\n                        let dealSum=0\r\n                        for (var i of dataList) {\r\n                            this.dataX.push(i.analysis_time.split(' ')[0])\r\n                            this.dangerData1.push(i.report_num)\r\n                            this.dangerData2.push(i.handle_num)\r\n                            dealSum +=i.handle_num*1\r\n                        }\r\n                        // 变为字符串\r\n                        dealSum = dealSum.toString()\r\n                        this.$parent.dealDangerNum=dealSum\r\n                        this.setOption()\r\n                    })\r\n                })\r\n            },\r\n            setOption() {\r\n\r\n                let option = {\r\n                    color: [\r\n                        'rgba(72, 232, 238)',\r\n                        'rgba(40, 99, 201)'\r\n                    ],\r\n                    grid: {\r\n                        left: \"0\",\r\n                        top: '25%',\r\n                        bottom: '0%',\r\n                        right: '0%',\r\n                        containLabel: true\r\n                    },\r\n                    title: {\r\n                        text: \"隐患上报 -- 统计分析\",\r\n                        textStyle: {\r\n                            color: \"#fff\",\r\n                            fontSize: 22,\r\n                            fontWeight: 500,\r\n                        }\r\n                    },\r\n                    tooltip: {\r\n                        trigger: 'axis',\r\n                        extraCssText: 'box-shadow: 0 0 10px #0F7CA4',\r\n                        backgroundColor: \"rgba(16, 64, 100, 1)\",\r\n                        borderColor: \"#0F7CA4\", //设置边框颜色\r\n                        textStyle: {\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"#eee\" //设置文字颜色\r\n                        },\r\n                    },\r\n                    legend: {\r\n                        show: true,\r\n                        right: '2%',\r\n                        top: \"10%\",\r\n                        itemStyle: {\r\n                            borderType: ''\r\n                        },\r\n                        textStyle: {\r\n                            color: \"#fff\",\r\n                            fontSize: 21,\r\n                            padding: 12,\r\n                        },\r\n                        data: this.legend\r\n                    },\r\n                    xAxis: {\r\n                        type: 'category',\r\n                        data: this.dataX,\r\n                        axisLabel: {\r\n                            show: true,\r\n                            color: \"#fff\",\r\n                            fontSize: 20,\r\n                            formatter: (v) => {\r\n                                if (v < 10) return '0' + v;\r\n                                else return v;\r\n                            }\r\n                        }\r\n\r\n                    },\r\n                    yAxis: {\r\n                        type: 'value',\r\n                        interval: 50,\r\n                        splitLine: {\r\n                            show: false\r\n                        },\r\n                        axisLabel: {\r\n                            show: true,\r\n                            color: \"#fff\",\r\n                            fontSize: 20,\r\n\r\n                        }\r\n\r\n                    },\r\n                    series: [\r\n                        {\r\n                            type: 'line',\r\n                            name: \"上报总数\",\r\n                            data: this.dangerData1,\r\n                            symbolSize: 10,\r\n                            symbol: \"circle\",\r\n                            lineStyle: {\r\n                                width: 2\r\n                            },\r\n                            areaStyle: {\r\n                                // normal: {\r\n                                    color: new echarts.graphic.LinearGradient(\r\n                                        0,\r\n                                        0,\r\n                                        0,\r\n                                        1,\r\n                                        [\r\n                                            {\r\n                                                offset: 0,\r\n                                                color: \"rgba(73, 236, 243, 0.6)\",\r\n                                            },\r\n                                            {\r\n                                                offset: 1,\r\n                                                color: \"rgba(109, 126, 0, 0)\",\r\n                                            },\r\n                                        ],\r\n                                        false\r\n                                    ),\r\n\r\n                                    // shadowColor: \"rgba(109, 126, 0, 0.5)\", //阴影颜色\r\n                                    // shadowBlur: 10, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。\r\n                                // },\r\n                            },\r\n\r\n                        },\r\n                        {\r\n                            type: 'line',\r\n                            name: \"处理总数\",\r\n                            data: this.dangerData2,\r\n                            symbolSize: 10,\r\n                            symbol: \"circle\",\r\n                            lineStyle: {\r\n                                width: 2// 0.1的线条是非常细的了\r\n                            },\r\n                            areaStyle: {\r\n                                // normal: {\r\n                                    color: new echarts.graphic.LinearGradient(\r\n                                        0,\r\n                                        0,\r\n                                        0,\r\n                                        1,\r\n                                        [\r\n                                            {\r\n                                                offset: 0,\r\n                                                color: \"rgba(36, 90, 167, 1)\",\r\n                                            },\r\n                                            {\r\n                                                offset: 1,\r\n                                                color: \"rgba(36, 90, 167, .1)\",\r\n                                            },\r\n                                        ],\r\n                                        false\r\n                                    ),\r\n\r\n                                    // shadowColor: \"rgba(109, 126, 0, 0.5)\", //阴影颜色\r\n                                    // shadowBlur: 10, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。\r\n                                // },\r\n                            },\r\n                        }\r\n                    ]\r\n                };\r\n                this.recently.setOption(option, true);\r\n                window.onresize = this.recently.resize;\r\n            }\r\n        }\r\n    };\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n    .recently {\r\n        width: 100%;\r\n        height: 400px;\r\n    }\r\n</style>"], "mappings": "AAQA,YAAAA,OAAA;AACA,SAAAC,wBAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,QAAA;MACAC,WAAA;MACAC,WAAA;MACAC,KAAA;MACAC,MAAA;IACA;EACA;EACAC,cAAA;IACA,SAAAL,QAAA;MACA,KAAAA,QAAA,CAAAM,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IAGA;EACA;EAEAC,OAAA;IACAD,QAAA;MAEA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAb,QAAA,iBAAAA,QAAA,eAAAA,QAAA,IAAAc,SAAA;UACA,KAAAd,QAAA,CAAAe,OAAA;QACA;QACA,KAAAf,QAAA,GAAAJ,OAAA,CAAAoB,IAAA,MAAAC,KAAA,CAAAjB,QAAA;QACA,KAAAA,QAAA,CAAAM,KAAA;QAEAT,wBAAA,GAAAgB,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAnB,IAAA,CAAAA,IAAA;UACA,IAAAqB,OAAA;UACA,SAAAC,CAAA,IAAAF,QAAA;YACA,KAAAhB,KAAA,CAAAmB,IAAA,CAAAD,CAAA,CAAAE,aAAA,CAAAC,KAAA;YACA,KAAAvB,WAAA,CAAAqB,IAAA,CAAAD,CAAA,CAAAI,UAAA;YACA,KAAAvB,WAAA,CAAAoB,IAAA,CAAAD,CAAA,CAAAK,UAAA;YACAN,OAAA,IAAAC,CAAA,CAAAK,UAAA;UACA;UACA;UACAN,OAAA,GAAAA,OAAA,CAAAO,QAAA;UACA,KAAAC,OAAA,CAAAC,aAAA,GAAAT,OAAA;UACA,KAAAU,SAAA;QACA;MACA;IACA;IACAA,UAAA;MAEA,IAAAC,MAAA;QACAC,KAAA,GACA,sBACA,oBACA;QACAC,IAAA;UACAC,IAAA;UACAC,GAAA;UACAC,MAAA;UACAC,KAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAC,SAAA;YACAT,KAAA;YACAU,QAAA;YACAC,UAAA;UACA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,YAAA;UACAC,eAAA;UACAC,WAAA;UAAA;UACAP,SAAA;YACAC,QAAA;YACAC,UAAA;YACAX,KAAA;UACA;QACA;QACA5B,MAAA;UACA6C,IAAA;UACAZ,KAAA;UACAF,GAAA;UACAe,SAAA;YACAC,UAAA;UACA;UACAV,SAAA;YACAT,KAAA;YACAU,QAAA;YACAU,OAAA;UACA;UACArD,IAAA,OAAAK;QACA;QACAiD,KAAA;UACAC,IAAA;UACAvD,IAAA,OAAAI,KAAA;UACAoD,SAAA;YACAN,IAAA;YACAjB,KAAA;YACAU,QAAA;YACAc,SAAA,EAAAC,CAAA;cACA,IAAAA,CAAA,oBAAAA,CAAA,MACA,OAAAA,CAAA;YACA;UACA;QAEA;QACAC,KAAA;UACAJ,IAAA;UACAK,QAAA;UACAC,SAAA;YACAX,IAAA;UACA;UACAM,SAAA;YACAN,IAAA;YACAjB,KAAA;YACAU,QAAA;UAEA;QAEA;QACAmB,MAAA,GACA;UACAP,IAAA;UACAxD,IAAA;UACAC,IAAA,OAAAE,WAAA;UACA6D,UAAA;UACAC,MAAA;UACAC,SAAA;YACAC,KAAA;UACA;UACAC,SAAA;YACA;YACAlC,KAAA,MAAApC,OAAA,CAAAuE,OAAA,CAAAC,cAAA,CACA,GACA,GACA,GACA,GACA,CACA;cACAC,MAAA;cACArC,KAAA;YACA,GACA;cACAqC,MAAA;cACArC,KAAA;YACA,EACA,EACA,KACA;;YAEA;YACA;YACA;UACA;QAEA,GACA;UACAsB,IAAA;UACAxD,IAAA;UACAC,IAAA,OAAAG,WAAA;UACA4D,UAAA;UACAC,MAAA;UACAC,SAAA;YACAC,KAAA;UACA;UACAC,SAAA;YACA;YACAlC,KAAA,MAAApC,OAAA,CAAAuE,OAAA,CAAAC,cAAA,CACA,GACA,GACA,GACA,GACA,CACA;cACAC,MAAA;cACArC,KAAA;YACA,GACA;cACAqC,MAAA;cACArC,KAAA;YACA,EACA,EACA,KACA;;YAEA;YACA;YACA;UACA;QACA;MAEA;MACA,KAAAhC,QAAA,CAAA8B,SAAA,CAAAC,MAAA;MACAuC,MAAA,CAAAC,QAAA,QAAAvE,QAAA,CAAAwE,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}