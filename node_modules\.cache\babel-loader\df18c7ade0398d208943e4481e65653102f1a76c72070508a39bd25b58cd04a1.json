{"ast": null, "code": "import { mapState } from 'vuex';\nexport default {\n  name: 'HqkfHeader',\n  data() {\n    return {\n      timer: '',\n      date: '',\n      week: '',\n      currentTime: '',\n      isFull: false\n      // isScreenShow:true\n    };\n  },\n  computed: {\n    isScreenShow() {\n      // 获取在action模块中的state\n      return this.$store.state.isScreenShow;\n    },\n    ...mapState({\n      ueTxt: state => state.dataChannelText\n    })\n  },\n  watch: {\n    ueTxt(nv) {\n      if (nv && nv.POIType == \"视频监控\") {\n        this.$store.commit('action/getIsShowSearch', false);\n      }\n    }\n  },\n  mounted() {\n    this.TodaysDat();\n    this.$nextTick(() => {\n      this.$store.commit('action/getIsScreenShow', true);\n    });\n  },\n  destroyed() {\n    clearInterval(this.timer);\n    this.$store.commit('action/getIsShowSearch', false);\n  },\n  methods: {\n    //页面刷新\n    reloadFn() {\n      if (this.$route.path != '/home/<USER>') {\n        this.$router.push({\n          path: \"/home/<USER>\"\n        });\n      }\n      this.$store.commit('action/getChangeScreenValue', '综合态势');\n      this.$eventBus.$emit(\"senTxtToUe\", \"综合态势\");\n    },\n    searchFn() {\n      this.$store.commit('action/getIsShowSearch', true);\n    },\n    enterFullscreen() {\n      const element = document.documentElement;\n      if (element.requestFullscreen) {\n        element.requestFullscreen();\n      } else if (element.mozRequestFullScreen) {\n        element.mozRequestFullScreen(); // Firefox\n      } else if (element.webkitRequestFullscreen) {\n        element.webkitRequestFullscreen(); // Chrome, Safari\n      } else if (element.msRequestFullscreen) {\n        element.msRequestFullscreen(); // IE/Edge\n      }\n      this.isFull = true;\n    },\n    exitFullscreen() {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        document.mozCancelFullScreen(); // Firefox\n      } else if (document.webkitExitFullscreen) {\n        document.webkitExitFullscreen(); // Chrome, Safari\n      } else if (document.msExitFullscreen) {\n        document.msExitFullscreen(); // IE/Edge\n      }\n      this.isFull = false;\n    },\n    ueEventFn(name) {\n      this.$eventBus.$emit(\"senTxtToUe\", name);\n    },\n    TodaysDat() {\n      var _this = this;\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');\n      var now = new Date();\n      var day = now.getDay();\n      _this.week = weeks[day]; // 设置星期几\n      _this.date = new Date().getFullYear() + ' / ' + _this.appendZero(new Date().getMonth() + 1) + ' / ' + _this.appendZero(new Date().getDate()); // 设置年月日\n      _this.timer = setInterval(function () {\n        _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes()) + ':' + _this.appendZero(new Date().getSeconds()); //修改数据date\n      }, 1000);\n    },\n    appendZero(num) {\n      if (num < 10) {\n        return '0' + num;\n      } else {\n        return num;\n      }\n    },\n    // 点击大标题\n    clickTitle() {\n      // webrtc.webrtc.emitUIInteraction({\n      //     ueFunction: \"CallTitleFunc\",\n      // })\n    },\n    // 点击返回主页\n    returnMainBtn() {\n      this.$eventBus.$emit(\"senTxtToUe\", \"首页\");\n      this.$store.commit('action/getBufferShow', {\n        flag: false,\n        bufferGeometry: {}\n      });\n      this.$router.push('/');\n    },\n    isScreenFun() {\n      this.$store.commit('action/getIsScreenShow', null);\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "name", "data", "timer", "date", "week", "currentTime", "isFull", "computed", "isScreenShow", "$store", "state", "ueTxt", "dataChannelText", "watch", "nv", "POIType", "commit", "mounted", "TodaysDat", "$nextTick", "destroyed", "clearInterval", "methods", "reloadFn", "$route", "path", "$router", "push", "$eventBus", "$emit", "searchFn", "enterFullscreen", "element", "document", "documentElement", "requestFullscreen", "mozRequestFullScreen", "webkitRequestFullscreen", "msRequestFullscreen", "exitFullscreen", "mozCancelFullScreen", "webkitExitFullscreen", "msExitFullscreen", "ueEventFn", "_this", "weeks", "Array", "now", "Date", "day", "getDay", "getFullYear", "appendZero", "getMonth", "getDate", "setInterval", "getHours", "getMinutes", "getSeconds", "num", "clickTitle", "returnMainBtn", "flag", "bufferGeometry", "isScreenFun"], "sources": ["src/components/Header.vue"], "sourcesContent": ["<template>\r\n    <!-- 头部 -->\r\n\r\n    <div class=\"top\" ref=\"top\">\r\n        <div class=\"leftTop\">\r\n            <div class=\"weater\">\r\n                多云\r\n            </div>\r\n            <div class=\"temperature\">\r\n                8°C~21°C\r\n            </div>\r\n            <!-- <div class=\"rainNum\">\r\n                0mm\r\n            </div> -->\r\n            <div class=\"returnPage\" @click=\"returnMainBtn\">\r\n                返回首页\r\n            </div>\r\n            <div class=\"returnPage\" @click=\"isScreenFun\">\r\n                大屏打开/关闭\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"titTop\" @click=\"clickTitle\">\r\n            花 桥 经 济 开 发 区 集 成 指 挥 平 台\r\n        </div>\r\n        <div class=\"rightTop\">\r\n            <div class=\"time\">\r\n                {{ currentTime }}\r\n            </div>\r\n            <div class=\"data\">\r\n                {{ date }}\r\n            </div>\r\n            <div class=\"day\">\r\n                {{ week }}\r\n            </div>\r\n        </div>\r\n        <div class=\"icons\">\r\n            <i @click=\"searchFn\" title=\"搜索\" style=\"font-size: 30px;\" class=\"el-icon-search\"></i>\r\n            <i @click=\"ueEventFn('天气模拟')\" title=\"天气\" style=\"font-size: 30px;\" class=\"el-icon-sunrise\"></i>\r\n            <img @click=\"ueEventFn('清除所有')\" title=\"清除所有\" src=\"@/assets/qc1-icon.png\" alt=\"\">\r\n            <img @click=\"ueEventFn('全局视角')\" title=\"全局视角\" src=\"@/assets/sj-icon.png\" alt=\"\">\r\n            <img @click=\"ueEventFn('无人机')\" title=\"无人机\" src=\"@/assets/wrj-icon.png\" alt=\"\">\r\n            <img @click=\"ueEventFn('清除')\" title=\"清除\" src=\"@/assets/qc-icon.png\" alt=\"\">\r\n            <!-- <img @click=\"reloadFn\" src=\"@/assets/reload.png\" title=\"刷新\" alt=\"\"> -->\r\n            <img v-if=\"isFull\" @click=\"exitFullscreen()\" title=\"退出全屏\" src=\"@/assets/sx-icon.png\" alt=\"\">\r\n            <img v-else @click=\"enterFullscreen()\" title=\"全屏\" src=\"@/assets/qp-icon.png\" alt=\"\">\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'HqkfHeader',\r\n\r\n    data() {\r\n        return {\r\n            timer: '',\r\n            date: '',\r\n            week: '',\r\n            currentTime: '',\r\n            isFull: false\r\n            // isScreenShow:true\r\n        };\r\n    },\r\n    computed: {\r\n        isScreenShow() {\r\n            // 获取在action模块中的state\r\n            return this.$store.state.isScreenShow;\r\n        },\r\n        ...mapState({\r\n            ueTxt: state => state.dataChannelText,\r\n        }),\r\n    },\r\n    watch: {\r\n        ueTxt(nv) {\r\n            if (nv && nv.POIType == \"视频监控\") {\r\n                this.$store.commit('action/getIsShowSearch', false)\r\n            }\r\n        },\r\n    },\r\n    mounted() {\r\n        this.TodaysDat();\r\n        this.$nextTick(() => {\r\n            this.$store.commit('action/getIsScreenShow', true)\r\n\r\n        })\r\n    },\r\n    destroyed() {\r\n        clearInterval(this.timer)\r\n        this.$store.commit('action/getIsShowSearch', false)\r\n    },\r\n\r\n    methods: {\r\n        //页面刷新\r\n        reloadFn(){\r\n            if(this.$route.path!='/home/<USER>'){\r\n                this.$router.push({path:\"/home/<USER>\"})\r\n            }\r\n            this.$store.commit('action/getChangeScreenValue', '综合态势')\r\n            this.$eventBus.$emit(\"senTxtToUe\", \"综合态势\")\r\n        },\r\n        searchFn() {\r\n            this.$store.commit('action/getIsShowSearch', true)\r\n        },\r\n        enterFullscreen() {\r\n            const element = document.documentElement;\r\n            if (element.requestFullscreen) {\r\n                element.requestFullscreen();\r\n            } else if (element.mozRequestFullScreen) {\r\n                element.mozRequestFullScreen(); // Firefox\r\n            } else if (element.webkitRequestFullscreen) {\r\n                element.webkitRequestFullscreen(); // Chrome, Safari\r\n            } else if (element.msRequestFullscreen) {\r\n                element.msRequestFullscreen(); // IE/Edge\r\n            }\r\n            this.isFull = true;\r\n        },\r\n        exitFullscreen() {\r\n            if (document.exitFullscreen) {\r\n                document.exitFullscreen();\r\n            } else if (document.mozCancelFullScreen) {\r\n                document.mozCancelFullScreen(); // Firefox\r\n            } else if (document.webkitExitFullscreen) {\r\n                document.webkitExitFullscreen(); // Chrome, Safari\r\n            } else if (document.msExitFullscreen) {\r\n                document.msExitFullscreen(); // IE/Edge\r\n            }\r\n            this.isFull = false;\r\n        },\r\n        ueEventFn(name) {\r\n            this.$eventBus.$emit(\"senTxtToUe\", name)\r\n        },\r\n        TodaysDat() {\r\n            var _this = this\r\n            var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')\r\n            var now = new Date()\r\n            var day = now.getDay()\r\n            _this.week = weeks[day] // 设置星期几\r\n            _this.date = new Date().getFullYear() + ' / ' + _this.appendZero(new Date().getMonth() + 1) + ' / ' + _this.appendZero(new Date().getDate()); // 设置年月日\r\n            _this.timer = setInterval(function () {\r\n                _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes()) + ':' + _this.appendZero(new Date().getSeconds()) //修改数据date\r\n            }, 1000);\r\n        },\r\n\r\n        appendZero(num) {\r\n            if (num < 10) {\r\n                return '0' + num\r\n            } else {\r\n                return num;\r\n            }\r\n        },\r\n        // 点击大标题\r\n        clickTitle() {\r\n            // webrtc.webrtc.emitUIInteraction({\r\n            //     ueFunction: \"CallTitleFunc\",\r\n            // })\r\n        },\r\n        // 点击返回主页\r\n        returnMainBtn() {\r\n            this.$eventBus.$emit(\"senTxtToUe\", \"首页\")\r\n            this.$store.commit('action/getBufferShow', { flag: false, bufferGeometry: {} })\r\n            this.$router.push('/')\r\n        },\r\n        isScreenFun() {\r\n            this.$store.commit('action/getIsScreenShow', null)\r\n        },\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/* 头部 */\r\n.top {\r\n    pointer-events: stroke;\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 7.5rem;\r\n    background: url(\"@/assets/images/slices/topBgc.png\") no-repeat;\r\n    background-size: 100%;\r\n    font-size: 1.5rem;\r\n    box-sizing: border-box;\r\n\r\n    /* border-image: linear-gradient(270deg, rgba(3, 207, 230, 0.16), rgba(13, 237, 255, 1), rgba(8, 199, 255, 0.16)) 1 1; */\r\n    .leftTop {\r\n        position: absolute;\r\n        left: 12rem;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        width: 30rem;\r\n        line-height: 7rem;\r\n\r\n        .returnPage {\r\n            width: fit-content;\r\n            height: 3.4rem;\r\n            line-height: 3.4rem;\r\n            background: rgba(16, 107, 171, 0.5);\r\n            cursor: pointer;\r\n            padding: 0 10px;\r\n            user-select: none;\r\n        }\r\n\r\n        .returnPage:active {\r\n            background: rgba(16, 107, 171, 1);\r\n\r\n        }\r\n\r\n    }\r\n\r\n    .titTop {\r\n        position: absolute;\r\n        left: 28.5%;\r\n        margin: 0 400px;\r\n        text-align: center;\r\n        font-size: 3.2rem;\r\n        font-weight: bold;\r\n        line-height: 7.9rem;\r\n        font-family: Source Han Sans CN;\r\n        background: linear-gradient(0deg, #8BDEFF 30%, #FFFFFF 50%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n    }\r\n\r\n    .rightTop {\r\n        position: absolute;\r\n        right: 28rem;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        width: 25rem;\r\n        line-height: 7rem;\r\n        /* border: 1px solid pink; */\r\n    }\r\n\r\n    .icons {\r\n        position: absolute;\r\n        display: flex;\r\n        right: 20px;\r\n        top: 40px;\r\n\r\n        >img,\r\n        i {\r\n            cursor: pointer;\r\n            width: 30px;\r\n            height: 30px;\r\n            margin-right: 20px;\r\n        }\r\n\r\n    }\r\n}\r\n</style>"], "mappings": "AAmDA,SAAAA,QAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,WAAA;MACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAF,YAAA;IACA;IACA,GAAAT,QAAA;MACAY,KAAA,EAAAD,KAAA,IAAAA,KAAA,CAAAE;IACA;EACA;EACAC,KAAA;IACAF,MAAAG,EAAA;MACA,IAAAA,EAAA,IAAAA,EAAA,CAAAC,OAAA;QACA,KAAAN,MAAA,CAAAO,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,SAAA;MACA,KAAAV,MAAA,CAAAO,MAAA;IAEA;EACA;EACAI,UAAA;IACAC,aAAA,MAAAnB,KAAA;IACA,KAAAO,MAAA,CAAAO,MAAA;EACA;EAEAM,OAAA;IACA;IACAC,SAAA;MACA,SAAAC,MAAA,CAAAC,IAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UAAAF,IAAA;QAAA;MACA;MACA,KAAAhB,MAAA,CAAAO,MAAA;MACA,KAAAY,SAAA,CAAAC,KAAA;IACA;IACAC,SAAA;MACA,KAAArB,MAAA,CAAAO,MAAA;IACA;IACAe,gBAAA;MACA,MAAAC,OAAA,GAAAC,QAAA,CAAAC,eAAA;MACA,IAAAF,OAAA,CAAAG,iBAAA;QACAH,OAAA,CAAAG,iBAAA;MACA,WAAAH,OAAA,CAAAI,oBAAA;QACAJ,OAAA,CAAAI,oBAAA;MACA,WAAAJ,OAAA,CAAAK,uBAAA;QACAL,OAAA,CAAAK,uBAAA;MACA,WAAAL,OAAA,CAAAM,mBAAA;QACAN,OAAA,CAAAM,mBAAA;MACA;MACA,KAAAhC,MAAA;IACA;IACAiC,eAAA;MACA,IAAAN,QAAA,CAAAM,cAAA;QACAN,QAAA,CAAAM,cAAA;MACA,WAAAN,QAAA,CAAAO,mBAAA;QACAP,QAAA,CAAAO,mBAAA;MACA,WAAAP,QAAA,CAAAQ,oBAAA;QACAR,QAAA,CAAAQ,oBAAA;MACA,WAAAR,QAAA,CAAAS,gBAAA;QACAT,QAAA,CAAAS,gBAAA;MACA;MACA,KAAApC,MAAA;IACA;IACAqC,UAAA3C,IAAA;MACA,KAAA4B,SAAA,CAAAC,KAAA,eAAA7B,IAAA;IACA;IACAkB,UAAA;MACA,IAAA0B,KAAA;MACA,IAAAC,KAAA,OAAAC,KAAA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,IAAAC,GAAA,GAAAF,GAAA,CAAAG,MAAA;MACAN,KAAA,CAAAxC,IAAA,GAAAyC,KAAA,CAAAI,GAAA;MACAL,KAAA,CAAAzC,IAAA,OAAA6C,IAAA,GAAAG,WAAA,aAAAP,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAK,QAAA,kBAAAT,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAM,OAAA;MACAV,KAAA,CAAA1C,KAAA,GAAAqD,WAAA;QACAX,KAAA,CAAAvC,WAAA,GAAAuC,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAQ,QAAA,YAAAZ,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAS,UAAA,YAAAb,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAU,UAAA;MACA;IACA;IAEAN,WAAAO,GAAA;MACA,IAAAA,GAAA;QACA,aAAAA,GAAA;MACA;QACA,OAAAA,GAAA;MACA;IACA;IACA;IACAC,WAAA;MACA;MACA;MACA;IAAA,CACA;IACA;IACAC,cAAA;MACA,KAAAjC,SAAA,CAAAC,KAAA;MACA,KAAApB,MAAA,CAAAO,MAAA;QAAA8C,IAAA;QAAAC,cAAA;MAAA;MACA,KAAArC,OAAA,CAAAC,IAAA;IACA;IACAqC,YAAA;MACA,KAAAvD,MAAA,CAAAO,MAAA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}