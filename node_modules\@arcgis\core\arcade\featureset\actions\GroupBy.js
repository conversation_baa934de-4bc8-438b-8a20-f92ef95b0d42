/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{X as t}from"../../../chunks/languageUtils.js";import{SqlExpressionAdapted as i,OriginalField as s,AdaptedFeatureSet as n}from"./Adapted.js";import r from"./AttributeFilter.js";import a from"./OrderBy.js";import l from"../support/FeatureSet.js";import o from"../support/IdSet.js";import d from"../support/OrderbyClause.js";import{IdState as u,layerGeometryEsriConstants as f,FeatureServiceDatabaseType as h}from"../support/shared.js";import{isSingleField as c,toWhereClause as p,predictType as g,scanForField as _,reformulateWithoutField as m}from"../support/sqlUtils.js";import{calculateStat as y}from"../support/stats.js";import b from"../support/StatsField.js";import{createMD5Hash as w,outputTypes as F}from"../../../core/MD5.js";import{resolve as I,reject as S,create as x}from"../../../core/promiseUtils.js";import{WhereClause as k}from"../../../core/sql/WhereClause.js";import D from"../../../geometry/SpatialReference.js";import A from"../../../layers/support/Field.js";import j from"../../../layers/support/FieldsIndex.js";function C(e){if(!e)return"COUNT";switch(e.toLowerCase()){case"max":return"MAX";case"var":case"variance":return"VAR";case"avg":case"average":case"mean":return"AVG";case"min":return"MIN";case"sum":return"SUM";case"stdev":case"stddev":return"STDDEV";case"count":return"COUNT"}return"COUNT"}class G extends l{constructor(e){super(e),this._decodedStatsfield=[],this._decodedGroupbyfield=[],this._candosimplegroupby=!0,this.phsyicalgroupbyfields=[],this.objectIdField="ROW__ID",this._internalObjectIdField="ROW__ID",this._adaptedFields=[],this.declaredClass="esri.arcade.featureset.actions.Aggregate",this._uniqueIds=1,this._maxQuery=10,this._maxProcessing=10,this._parent=e.parentfeatureset,this._config=e}isTable(){return!0}_getSet(e){return null===this._wset?this._getFilteredSet("",null,null,null,e).then((e=>(this._wset=e,this._wset))):I(this._wset)}_isInFeatureSet(){return u.InFeatureSet}_nextUniqueName(e){for(;1===e["T"+this._uniqueIds.toString()];)this._uniqueIds++;const t="T"+this._uniqueIds.toString();return e[t]=1,t}_convertToEsriFieldType(e){return e}_initialiseFeatureSet(){const e={};let t=!1,n=1;const r=this._parent?this._parent.getFieldsIndex():new j([]);for(this.objectIdField="ROW__ID",this.globalIdField="";!1===t;){let e=!1;for(let t=0;t<this._config.groupbyfields.length;t++)if(this._config.groupbyfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}if(!1===e)for(let t=0;t<this._config.statsfields.length;t++)if(this._config.statsfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}!1===e?t=!0:(this.objectIdField="ROW__ID"+n.toString(),n++)}for(const i of this._config.statsfields){const e=new b;e.field=i.name,e.tofieldname=i.name,e.workingexpr=i.expression instanceof k?i.expression:k.create(i.expression,r),e.typeofstat=C(i.statistic),this._decodedStatsfield.push(e)}this._decodedGroupbyfield=[];for(const i of this._config.groupbyfields){const e={name:i.name,singlefield:null,tofieldname:i.name,expression:i.expression instanceof k?i.expression:k.create(i.expression,r)};this._decodedGroupbyfield.push(e)}if(null!==this._parent){this.geometryType=this._parent.geometryType,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField="";for(const t of this._parent.fields)e[t.name.toUpperCase()]=1;this.types=null}else this.geometryType=f.point,this.typeIdField="",this.types=null,this.spatialReference=new D({wkid:4326});this.fields=[];const a=new b;a.field=this._nextUniqueName(e),a.tofieldname=this.objectIdField,a.workingexpr=k.create(this._parent.objectIdField,this._parent.getFieldsIndex()),a.typeofstat="MIN",this._decodedStatsfield.push(a);for(const s of this._decodedGroupbyfield){const t=new A;if(s.name=this._nextUniqueName(e),t.name=s.tofieldname,t.alias=t.name,c(s.expression)){const e=this._parent.getField(p(s.expression,h.Standardised));if(!e)throw new Error("Field is not present for Aggregation");s.name=e.name,s.singlefield=e.name,this.phsyicalgroupbyfields.push(e.name),t.type=e.type}else{t.type=this._convertToEsriFieldType(g(s.expression,this._parent.fields));const e=new A;e.name=s.name,e.alias=e.name,this.phsyicalgroupbyfields.push(s.name),this._adaptedFields.push(new i(e,s.expression)),this._candosimplegroupby=!1}this.fields.push(t)}if(this._adaptedFields.length>0)for(const i of this._parent.fields)this._adaptedFields.push(new s(i));for(let i=0;i<this._decodedStatsfield.length;i++){const t=new A;let s=null;const n=this._decodedStatsfield[i];n.field=this._nextUniqueName(e),n.tofieldname===this.objectIdField&&(this._internalObjectIdField=n.field),t.name=n.tofieldname,t.alias=t.name;const r=null!==n.workingexpr&&c(n.workingexpr)?p(n.workingexpr,h.Standardised):"";switch(this._decodedStatsfield[i].typeofstat){case"SUM":if(""!==r){if(s=this._parent.getField(r),!s)throw new Error("Field is not present for Aggregation");t.type=s.type}else t.type="double";break;case"MIN":case"MAX":if(""!==r){if(s=this._parent.getField(r),!s)throw new Error("Field is not present for Aggregation");t.type=s.type}else t.type="double";break;case"COUNT":t.type="integer";break;case"STDDEV":case"VAR":case"AVG":if(""!==r&&(s=this._parent.getField(r),!s))throw new Error("Field is not present for Aggregation");t.type="double"}this.fields.push(t)}}_canDoAggregates(){return I(!1)}_getFeatures(e,t,i,s){-1!==t&&this._featureCache[t];const n=this._maxQuery;return!0===this._checkIfNeedToExpandKnownPage(e,n)?this._expandPagedSet(e,n,0,0,s).then((()=>this._getFeatures(e,t,i,s))):I("success")}_getFilteredSet(e,t,i,s,l){if(""!==e)return I(new o([],[],!0,null));let u=null;const f={ordered:!1,nowhereclause:!1};return this._ensureLoaded().then((()=>{if(null!==i)for(let e=0;e<this._decodedStatsfield.length;e++)if(!0===_(i,this._decodedStatsfield[e].tofieldname)){f.nowhereclause=!0,i=null;break}if(null!==s){f.ordered=!0;for(let e=0;e<this._decodedStatsfield.length;e++)if(!0===s.scanForField(this._decodedStatsfield[e].tofieldname)){s=null,f.ordered=!1;break}if(null!==s)for(const e of this._decodedGroupbyfield)if(null===e.singlefield&&!0===s.scanForField(e.tofieldname)){s=null,f.ordered=!1;break}}return!1===this._candosimplegroupby?I(!1):this._parent._canDoAggregates(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,null)})).then((e=>{if(e){let e=null;i&&(e=this._reformulateWhereClauseWithoutGroupByFields(i));let t=null;return s&&(t=this._reformulateOrderClauseWithoutGroupByFields(s)),this._parent._getAggregatePagesDataSourceDefinition(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,e,t,this._internalObjectIdField).then((e=>(this._checkCancelled(l),u=!0===f.nowhereclause?new o(e._candidates.slice(0).concat(e._known.slice(0)),[],!0===f.ordered&&e._ordered,this._clonePageDefinition(e.pagesDefinition)):new o(e._candidates.slice(0),e._known.slice(0),!0===f.ordered&&e._ordered,this._clonePageDefinition(e.pagesDefinition)),u)))}let t=this._parent;if(this._adaptedFields.length>0&&(t=new n({parentfeatureset:this._parent,adaptedFields:this._adaptedFields,extraFilter:null})),!0===f.nowhereclause)u=new o(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new a({parentfeatureset:t,orderbyclause:new d(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}});else{let e=t;if(null!==i){let t=null;i&&(t=this._reformulateWhereClauseWithoutGroupByFields(i)),e=new r({parentfeatureset:e,whereclause:t})}u=new o(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new a({parentfeatureset:e,orderbyclause:new d(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}})}return u}))}_reformulateWhereClauseWithoutStatsFields(e){for(const t of this._decodedStatsfield)e=m(e,t.tofieldname,p(t.workingexpr,h.Standardised),this._parent.getFieldsIndex());return e}_reformulateWhereClauseWithoutGroupByFields(e){for(const t of this._decodedGroupbyfield)t.tofieldname!==t.name&&(e=m(e,t.tofieldname,p(t.expression,h.Standardised),this._parent.getFieldsIndex()));return e}_reformulateOrderClauseWithoutGroupByFields(e){const t=[];for(const i of this._decodedGroupbyfield)i.tofieldname!==i.name&&t.push({field:i.tofieldname,newfield:i.name});return t.length>0?e.replaceFields(t):e}_clonePageDefinition(e){return null===e?null:!0===e.aggregatefeaturesetpagedefinition?{aggregatefeaturesetpagedefinition:!0,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,internal:e.internal}:this._parent._clonePageDefinition(e)}_refineSetBlock(e,t,i){try{if(!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQuery))return this._expandPagedSet(e,this._maxQuery,0,0,i).then((()=>this._refineSetBlock(e,t,i)));this._checkCancelled(i);const s=e._candidates.length;this._refineKnowns(e,t);e._candidates.length;return e._candidates.length,I(e)}catch(s){return S(s)}}_expandPagedSet(e,t,i,s,n){return this._expandPagedSetFeatureSet(e,t,i,s,n)}_getPhysicalPage(t,i,s){return!0===t.pagesDefinition.aggregatefeaturesetpagedefinition?x(((e,i)=>{this._sequentialGetPhysicalItem(t,t.pagesDefinition.resultRecordCount,s,[]).then((t=>{e(t)}),i)})):this._getAgregagtePhysicalPage(t,i,s).then((t=>{for(const i of t){const t={geometry:i.geometry,attributes:{}};for(const e of this._decodedGroupbyfield)t.attributes[e.tofieldname]=i.attributes[e.name];for(const e of this._decodedStatsfield)t.attributes[e.tofieldname]=i.attributes[e.field];this._featureCache[t.attributes[this.objectIdField]]=new e(t)}return t.length}))}_sequentialGetPhysicalItem(e,t,i,s){return x(((n,r)=>{null===e.pagesDefinition.internal.iterator&&(e.pagesDefinition.internal.iterator=e.pagesDefinition.internal.subfeatureset.iterator(i)),!0===e.pagesDefinition.internal.fullyResolved||0===t?n(s.length):this._nextAggregateItem(e,t,i,s,(r=>{null===r?n(s.length):(t-=1,n(this._sequentialGetPhysicalItem(e,t,i,s)))}),r)}))}_nextAggregateItem(e,i,s,n,r,a){try{t(e.pagesDefinition.internal.iterator.next()).then((t=>{if(null===t)if(null!==e.pagesDefinition.internal.workingItem){const t=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);n.push(t),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(t.attributes[this.objectIdField]),e.pagesDefinition.internal.fullyResolved=!0,r(null)}else e.pagesDefinition.internal.fullyResolved=!0,r(null);else{const l=this._generateAggregateHash(t);if(null===e.pagesDefinition.internal.workingItem)e.pagesDefinition.internal.workingItem={features:[t],id:l};else{if(l!==e.pagesDefinition.internal.workingItem.id){const s=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);return n.push(s),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(s.attributes[this.objectIdField]),i-=1,e.pagesDefinition.internal.workingItem={features:[t],id:l},void r(s)}e.pagesDefinition.internal.workingItem.features.push(t)}this._nextAggregateItem(e,i,s,n,r,a)}}),a)}catch(l){a(l)}}_calculateFieldStat(e,t,i){const s=[];for(let n=0;n<e.features.length;n++)if(null!==t.workingexpr){const i=t.workingexpr.calculateValue(e.features[n]);null!==i&&s.push(i)}else s.push(null);switch(t.typeofstat){case"MIN":i.attributes[t.tofieldname]=y("min",s,-1);break;case"MAX":i.attributes[t.tofieldname]=y("max",s,-1);break;case"SUM":i.attributes[t.tofieldname]=y("sum",s,-1);break;case"COUNT":i.attributes[t.tofieldname]=s.length;break;case"VAR":i.attributes[t.tofieldname]=y("var",s,-1);break;case"STDDEV":i.attributes[t.tofieldname]=y("stddev",s,-1);break;case"AVG":i.attributes[t.tofieldname]=y("avg",s,-1)}return!0}_calculateAndAppendAggregateItem(t){const i={attributes:{},geometry:null};for(const e of this._decodedGroupbyfield){const s=e.singlefield?t.features[0].attributes[e.singlefield]:e.expression.calculateValue(t.features[0]);i.attributes[e.tofieldname]=s}for(const e of this._decodedStatsfield)this._calculateFieldStat(t,e,i);const s=[];for(let e=0;e<this._decodedStatsfield.length;e++)s.push(this._calculateFieldStat(t,this._decodedStatsfield[e],i));return this._featureCache[i.attributes[this.objectIdField]]=new e({attributes:i.attributes,geometry:i.geometry}),i}_generateAggregateHash(e){let t="";for(const i of this._decodedGroupbyfield){const s=i.singlefield?e.attributes[i.singlefield]:i.expression.calculateValue(e);t+=null==s?":":":"+s.toString()}return w(t,F.String)}_stat(){return I({calculated:!1})}getFeatureByObjectId(){return I(null)}static registerAction(){l._featuresetFunctions.groupby=function(e,t){return new G({parentfeatureset:this,groupbyfields:e,statsfields:t})}}}export{G as default};
