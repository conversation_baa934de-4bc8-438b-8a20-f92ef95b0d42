{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"Dialog\"\n  }, [_c(\"el-dialog\", {\n    staticClass: \"DialogType_Box\",\n    attrs: {\n      title: _vm.dialogType.name,\n      visible: _vm.dialogVisible,\n      width: _vm.dialogType.width,\n      fullscreen: false,\n      \"close-on-press-escape\": false,\n      \"show-close\": \"\",\n      \"close-on-click-modal\": false,\n      \"before-close\": _vm.closeDialog,\n      \"append-to-body\": true\n    },\n    on: {\n      \"update:visible\": function ($event) {\n        _vm.dialogVisible = $event;\n      }\n    }\n  }, [!_vm.dialogType.title ? _c(\"el-table\", {\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      data: _vm.tableData,\n      stripe: true,\n      \"max-height\": _vm.dialogType.height,\n      \"header-cell-style\": {\n        color: \"#fff\",\n        fontWeight: \"700\",\n        backgroundColor: \"rgba(18, 76, 111, .45)\"\n      }\n    }\n  }, [_vm._l(_vm.dialogType.data, function (val, i) {\n    return _c(\"el-table-column\", {\n      key: i,\n      attrs: {\n        align: \"center\",\n        prop: val.prop,\n        label: val.label,\n        \"show-overflow-tooltip\": true\n      }\n    });\n  }), _c(\"el-table-column\", {\n    attrs: {\n      fixed: \"right\",\n      label: \"操作\",\n      width: \"100\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function (scope) {\n        return [_c(\"el-button\", {\n          attrs: {\n            type: \"text\",\n            size: \"small\"\n          },\n          nativeOn: {\n            click: function ($event) {\n              $event.preventDefault();\n              return _vm.handlePosition(scope.$index, scope.row);\n            }\n          }\n        }, [_vm._v(\" 定位 \")])];\n      }\n    }], null, false, 2767611251)\n  })], 2) : _vm._e(), _vm.dialogType.title ? _c(\"el-table\", {\n    staticClass: \"warning\",\n    staticStyle: {\n      width: \"100%\"\n    },\n    attrs: {\n      data: _vm.tableListData,\n      stripe: true,\n      \"max-height\": _vm.dialogType.height,\n      \"header-cell-style\": {\n        color: \"#fff\",\n        fontWeight: \"700\",\n        backgroundColor: \"rgba(18, 76, 111, .45)\"\n      },\n      \"span-method\": _vm.arraySpanMethod\n    }\n  }, _vm._l(_vm.dialogType.data, function (item, index) {\n    return _c(\"el-table-column\", {\n      key: index,\n      attrs: {\n        align: \"center\",\n        prop: item.prop,\n        label: item.label,\n        width: item.prop == \"index\" ? \"70\" : \"\"\n      }\n    });\n  }), 1) : _vm._e()], 1), _c(\"div\", {\n    staticClass: \"IntegratedRod\"\n  }, [_vm.IntegratedRodOneFlag ? _c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"clearfix\",\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.IntegratedRodOneInfo.stnm))]), _c(\"el-button\", {\n    staticStyle: {\n      float: \"right\",\n      padding: \"3px 10px\",\n      \"font-size\": \"28px\"\n    },\n    attrs: {\n      icon: \"el-icon-close\",\n      type: \"text\"\n    },\n    on: {\n      click: function ($event) {\n        _vm.IntegratedRodOneFlag = false;\n      }\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 测站名称： \" + _vm._s(_vm.IntegratedRodOneInfo.stnm) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 水文站: \" + _vm._s(_vm.IntegratedRodOneInfo.addvnm1) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 电压: \" + _vm._s(_vm.IntegratedRodOneInfo.voltage) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 总流量: \" + _vm._s(_vm.IntegratedRodOneInfo.qa) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 瞬时流量: \" + _vm._s(_vm.IntegratedRodOneInfo.q) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 水位: \" + _vm._s(_vm.IntegratedRodOneInfo.z) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 信号强度: \" + _vm._s(_vm.IntegratedRodOneInfo.signalinten) + \" \")]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_vm._v(\" 采集时间: \" + _vm._s(_vm.IntegratedRodOneInfo.tm) + \" \")])]) : _vm._e()], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "dialogType", "name", "visible", "dialogVisible", "width", "fullscreen", "closeDialog", "on", "update:visible", "$event", "staticStyle", "data", "tableData", "stripe", "height", "color", "fontWeight", "backgroundColor", "_l", "val", "i", "key", "align", "prop", "label", "fixed", "scopedSlots", "_u", "fn", "scope", "type", "size", "nativeOn", "click", "preventDefault", "handlePosition", "$index", "row", "_v", "_e", "tableListData", "arraySpanMethod", "item", "index", "IntegratedRodOneFlag", "slot", "_s", "IntegratedRodOneInfo", "stnm", "float", "padding", "icon", "addvnm1", "voltage", "qa", "q", "z", "signalinten", "tm", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/Dialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"Dialog\" },\n    [\n      _c(\n        \"el-dialog\",\n        {\n          staticClass: \"DialogType_Box\",\n          attrs: {\n            title: _vm.dialogType.name,\n            visible: _vm.dialogVisible,\n            width: _vm.dialogType.width,\n            fullscreen: false,\n            \"close-on-press-escape\": false,\n            \"show-close\": \"\",\n            \"close-on-click-modal\": false,\n            \"before-close\": _vm.closeDialog,\n            \"append-to-body\": true,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          !_vm.dialogType.title\n            ? _c(\n                \"el-table\",\n                {\n                  staticStyle: { width: \"100%\" },\n                  attrs: {\n                    data: _vm.tableData,\n                    stripe: true,\n                    \"max-height\": _vm.dialogType.height,\n                    \"header-cell-style\": {\n                      color: \"#fff\",\n                      fontWeight: \"700\",\n                      backgroundColor: \"rgba(18, 76, 111, .45)\",\n                    },\n                  },\n                },\n                [\n                  _vm._l(_vm.dialogType.data, function (val, i) {\n                    return _c(\"el-table-column\", {\n                      key: i,\n                      attrs: {\n                        align: \"center\",\n                        prop: val.prop,\n                        label: val.label,\n                        \"show-overflow-tooltip\": true,\n                      },\n                    })\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { fixed: \"right\", label: \"操作\", width: \"100\" },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: { type: \"text\", size: \"small\" },\n                                  nativeOn: {\n                                    click: function ($event) {\n                                      $event.preventDefault()\n                                      return _vm.handlePosition(\n                                        scope.$index,\n                                        scope.row\n                                      )\n                                    },\n                                  },\n                                },\n                                [_vm._v(\" 定位 \")]\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      2767611251\n                    ),\n                  }),\n                ],\n                2\n              )\n            : _vm._e(),\n          _vm.dialogType.title\n            ? _c(\n                \"el-table\",\n                {\n                  staticClass: \"warning\",\n                  staticStyle: { width: \"100%\" },\n                  attrs: {\n                    data: _vm.tableListData,\n                    stripe: true,\n                    \"max-height\": _vm.dialogType.height,\n                    \"header-cell-style\": {\n                      color: \"#fff\",\n                      fontWeight: \"700\",\n                      backgroundColor: \"rgba(18, 76, 111, .45)\",\n                    },\n                    \"span-method\": _vm.arraySpanMethod,\n                  },\n                },\n                _vm._l(_vm.dialogType.data, function (item, index) {\n                  return _c(\"el-table-column\", {\n                    key: index,\n                    attrs: {\n                      align: \"center\",\n                      prop: item.prop,\n                      label: item.label,\n                      width: item.prop == \"index\" ? \"70\" : \"\",\n                    },\n                  })\n                }),\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"IntegratedRod\" },\n        [\n          _vm.IntegratedRodOneFlag\n            ? _c(\"el-card\", { staticClass: \"box-card\" }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"clearfix\",\n                    attrs: { slot: \"header\" },\n                    slot: \"header\",\n                  },\n                  [\n                    _c(\"span\", [_vm._v(_vm._s(_vm.IntegratedRodOneInfo.stnm))]),\n                    _c(\"el-button\", {\n                      staticStyle: {\n                        float: \"right\",\n                        padding: \"3px 10px\",\n                        \"font-size\": \"28px\",\n                      },\n                      attrs: { icon: \"el-icon-close\", type: \"text\" },\n                      on: {\n                        click: function ($event) {\n                          _vm.IntegratedRodOneFlag = false\n                        },\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 测站名称： \" + _vm._s(_vm.IntegratedRodOneInfo.stnm) + \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 水文站: \" + _vm._s(_vm.IntegratedRodOneInfo.addvnm1) + \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 电压: \" + _vm._s(_vm.IntegratedRodOneInfo.voltage) + \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 总流量: \" + _vm._s(_vm.IntegratedRodOneInfo.qa) + \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 瞬时流量: \" + _vm._s(_vm.IntegratedRodOneInfo.q) + \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\" 水位: \" + _vm._s(_vm.IntegratedRodOneInfo.z) + \" \"),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 信号强度: \" +\n                      _vm._s(_vm.IntegratedRodOneInfo.signalinten) +\n                      \" \"\n                  ),\n                ]),\n                _c(\"div\", { staticClass: \"text item\" }, [\n                  _vm._v(\n                    \" 采集时间: \" + _vm._s(_vm.IntegratedRodOneInfo.tm) + \" \"\n                  ),\n                ]),\n              ])\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAS,CAAC,EACzB,CACEF,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MACLC,KAAK,EAAEL,GAAG,CAACM,UAAU,CAACC,IAAI;MAC1BC,OAAO,EAAER,GAAG,CAACS,aAAa;MAC1BC,KAAK,EAAEV,GAAG,CAACM,UAAU,CAACI,KAAK;MAC3BC,UAAU,EAAE,KAAK;MACjB,uBAAuB,EAAE,KAAK;MAC9B,YAAY,EAAE,EAAE;MAChB,sBAAsB,EAAE,KAAK;MAC7B,cAAc,EAAEX,GAAG,CAACY,WAAW;MAC/B,gBAAgB,EAAE;IACpB,CAAC;IACDC,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAC,CAAUC,MAAM,EAAE;QAClCf,GAAG,CAACS,aAAa,GAAGM,MAAM;MAC5B;IACF;EACF,CAAC,EACD,CACE,CAACf,GAAG,CAACM,UAAU,CAACD,KAAK,GACjBJ,EAAE,CACA,UAAU,EACV;IACEe,WAAW,EAAE;MAAEN,KAAK,EAAE;IAAO,CAAC;IAC9BN,KAAK,EAAE;MACLa,IAAI,EAAEjB,GAAG,CAACkB,SAAS;MACnBC,MAAM,EAAE,IAAI;MACZ,YAAY,EAAEnB,GAAG,CAACM,UAAU,CAACc,MAAM;MACnC,mBAAmB,EAAE;QACnBC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,KAAK;QACjBC,eAAe,EAAE;MACnB;IACF;EACF,CAAC,EACD,CACEvB,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACM,UAAU,CAACW,IAAI,EAAE,UAAUQ,GAAG,EAAEC,CAAC,EAAE;IAC5C,OAAOzB,EAAE,CAAC,iBAAiB,EAAE;MAC3B0B,GAAG,EAAED,CAAC;MACNtB,KAAK,EAAE;QACLwB,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAEJ,GAAG,CAACI,IAAI;QACdC,KAAK,EAAEL,GAAG,CAACK,KAAK;QAChB,uBAAuB,EAAE;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF7B,EAAE,CAAC,iBAAiB,EAAE;IACpBG,KAAK,EAAE;MAAE2B,KAAK,EAAE,OAAO;MAAED,KAAK,EAAE,IAAI;MAAEpB,KAAK,EAAE;IAAM,CAAC;IACpDsB,WAAW,EAAEhC,GAAG,CAACiC,EAAE,CACjB,CACE;MACEN,GAAG,EAAE,SAAS;MACdO,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACLlC,EAAE,CACA,WAAW,EACX;UACEG,KAAK,EAAE;YAAEgC,IAAI,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAQ,CAAC;UACtCC,QAAQ,EAAE;YACRC,KAAK,EAAE,SAAAA,CAAUxB,MAAM,EAAE;cACvBA,MAAM,CAACyB,cAAc,CAAC,CAAC;cACvB,OAAOxC,GAAG,CAACyC,cAAc,CACvBN,KAAK,CAACO,MAAM,EACZP,KAAK,CAACQ,GACR,CAAC;YACH;UACF;QACF,CAAC,EACD,CAAC3C,GAAG,CAAC4C,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACD5C,GAAG,CAAC6C,EAAE,CAAC,CAAC,EACZ7C,GAAG,CAACM,UAAU,CAACD,KAAK,GAChBJ,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE,SAAS;IACtBa,WAAW,EAAE;MAAEN,KAAK,EAAE;IAAO,CAAC;IAC9BN,KAAK,EAAE;MACLa,IAAI,EAAEjB,GAAG,CAAC8C,aAAa;MACvB3B,MAAM,EAAE,IAAI;MACZ,YAAY,EAAEnB,GAAG,CAACM,UAAU,CAACc,MAAM;MACnC,mBAAmB,EAAE;QACnBC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,KAAK;QACjBC,eAAe,EAAE;MACnB,CAAC;MACD,aAAa,EAAEvB,GAAG,CAAC+C;IACrB;EACF,CAAC,EACD/C,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACM,UAAU,CAACW,IAAI,EAAE,UAAU+B,IAAI,EAAEC,KAAK,EAAE;IACjD,OAAOhD,EAAE,CAAC,iBAAiB,EAAE;MAC3B0B,GAAG,EAAEsB,KAAK;MACV7C,KAAK,EAAE;QACLwB,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAEmB,IAAI,CAACnB,IAAI;QACfC,KAAK,EAAEkB,IAAI,CAAClB,KAAK;QACjBpB,KAAK,EAAEsC,IAAI,CAACnB,IAAI,IAAI,OAAO,GAAG,IAAI,GAAG;MACvC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,GACD7B,GAAG,CAAC6C,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACD5C,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEH,GAAG,CAACkD,oBAAoB,GACpBjD,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE;MAAE+C,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACElD,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC4C,EAAE,CAAC5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC3DrD,EAAE,CAAC,WAAW,EAAE;IACde,WAAW,EAAE;MACXuC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,UAAU;MACnB,WAAW,EAAE;IACf,CAAC;IACDpD,KAAK,EAAE;MAAEqD,IAAI,EAAE,eAAe;MAAErB,IAAI,EAAE;IAAO,CAAC;IAC9CvB,EAAE,EAAE;MACF0B,KAAK,EAAE,SAAAA,CAAUxB,MAAM,EAAE;QACvBf,GAAG,CAACkD,oBAAoB,GAAG,KAAK;MAClC;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDjD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,SAAS,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACC,IAAI,CAAC,GAAG,GACtD,CAAC,CACF,CAAC,EACFrD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,QAAQ,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACK,OAAO,CAAC,GAAG,GACxD,CAAC,CACF,CAAC,EACFzD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,OAAO,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACM,OAAO,CAAC,GAAG,GACvD,CAAC,CACF,CAAC,EACF1D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,QAAQ,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACO,EAAE,CAAC,GAAG,GACnD,CAAC,CACF,CAAC,EACF3D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,SAAS,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACQ,CAAC,CAAC,GAAG,GACnD,CAAC,CACF,CAAC,EACF5D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CAAC,OAAO,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACS,CAAC,CAAC,GAAG,GAAG,CAAC,CAC3D,CAAC,EACF7D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,SAAS,GACP5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACU,WAAW,CAAC,GAC5C,GACJ,CAAC,CACF,CAAC,EACF9D,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAAC4C,EAAE,CACJ,SAAS,GAAG5C,GAAG,CAACoD,EAAE,CAACpD,GAAG,CAACqD,oBAAoB,CAACW,EAAE,CAAC,GAAG,GACpD,CAAC,CACF,CAAC,CACH,CAAC,GACFhE,GAAG,CAAC6C,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIoB,eAAe,GAAG,EAAE;AACxBlE,MAAM,CAACmE,aAAa,GAAG,IAAI;AAE3B,SAASnE,MAAM,EAAEkE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}