{"ast": null, "code": "import informationType from '@/components/videoTypeOption/informationType';\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue';\nimport eventSubscription from '@/components/eventSubscription.vue';\nimport warmingInfoPop from './warmingInfoPop.vue';\nimport eventTable from './eventListProcessFlow/eventTable.vue';\nimport eventType from './eventListProcessFlow/eventType.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'pointPop',\n  components: {\n    eventSubscription,\n    informationType,\n    singleHlsVideo,\n    warmingInfoPop,\n    eventTable,\n    eventType\n  },\n  data() {\n    return {\n      sizeStyle: {\n        transform: 'translate(-10%, 50%) scale(2.2)',\n        left: '10%'\n      },\n      allStyle: {\n        width: '1050px',\n        height: '800px'\n      },\n      sizeStyle2: {\n        transform: 'translate(-10%, 50%) scale(2.1)',\n        left: '45%'\n      },\n      oneWarnPushInfo: {},\n      minWidth: '35rem'\n    };\n  },\n  computed: {\n    ...mapState('action', [\"eventSubBoxFlag\", \"isScreenShow\", \"playerList\", \"eventSubBoxInfoFlag\", 'eventTable', 'eventType']),\n    getUeTxt() {\n      return this.$store.state.dataChannelText;\n    }\n  },\n  watch: {\n    getUeTxt(nv) {\n      if (nv?.typename == '事件推送') {\n        // 关闭事件推送弹窗\n        // this.$store.commit('action/getEventSu bBoxFlag', false)\n        // 关闭看板\n        this.$store.commit('action/getIsScreenShow', false);\n        // 打开视频列表\n        this.$store.commit('action/getEventSubBoxInfoFlag', true);\n        this.oneWarnPushInfo = nv;\n        // this.openInfo(true, nv)\n      }\n    },\n    playerList: {\n      handler(nv) {},\n      immediate: true\n    }\n  },\n  mounted() {},\n  beforeDestroy() {\n    this.$eventBus.$emit('senTxtToUe', '清除');\n  },\n  methods: {\n    handleStyle(newStyle) {\n      this.allStyle = {\n        ...this.allStyle,\n        ...newStyle\n      };\n    },\n    // 打开监控\n    openVideo() {\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n    }\n  }\n};", "map": {"version": 3, "names": ["informationType", "singleHlsVideo", "eventSubscription", "warmingInfoPop", "eventTable", "eventType", "mapState", "name", "components", "data", "sizeStyle", "transform", "left", "allStyle", "width", "height", "sizeStyle2", "oneWarnPushInfo", "min<PERSON><PERSON><PERSON>", "computed", "getUeTxt", "$store", "state", "dataChannelText", "watch", "nv", "typename", "commit", "playerList", "handler", "immediate", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "$eventBus", "$emit", "methods", "handleStyle", "newStyle", "openVideo"], "sources": ["src/views/dataScreen/fileType/pointPop.vue"], "sourcesContent": ["<template>\r\n    <v-scale-screen width=\"3840\" height=\"2160\">\r\n        <div>\r\n            <!--事件详情-->\r\n            <div class=\"eventInfo\">\r\n                <warmingInfoPop v-if=\"eventSubBoxInfoFlag\" :oneWarnPushInfo=\"oneWarnPushInfo\"></warmingInfoPop>\r\n            </div>\r\n            <!-- 事件订阅 -->\r\n            <div class=\"eventSubscriptionDialog\">\r\n                <div class=\"event_info_pop\" v-if=\"eventSubBoxFlag\">\r\n                    <!-- <eventSubscription></eventSubscription> -->\r\n                    <eventTable></eventTable>\r\n                </div>\r\n            </div>\r\n            <div class=\"eventTypeShow\" v-if=\"eventType\">\r\n                <eventType></eventType>\r\n            </div>\r\n            <!-- 视频列表 -->\r\n            <div class=\"playList\">\r\n                <!-- 详情 -->\r\n                <informationType :sizeStyle=\"sizeStyle\" v-if=\"playerList.informationBox\">\r\n                </informationType>\r\n                <singleHlsVideo :allStyle=\"allStyle\" :sizeStyle=\"sizeStyle2\" v-if=\"playerList.videoPlayerBox\"\r\n                    @updateStyle=\"handleStyle\" :minWidth=\"minWidth\">\r\n                </singleHlsVideo>\r\n\r\n            </div>\r\n        </div>\r\n    </v-scale-screen>\r\n</template>\r\n<script>\r\nimport informationType from '@/components/videoTypeOption/informationType'\r\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue'\r\nimport eventSubscription from '@/components/eventSubscription.vue'\r\nimport warmingInfoPop from './warmingInfoPop.vue'\r\nimport eventTable from './eventListProcessFlow/eventTable.vue'\r\nimport eventType from './eventListProcessFlow/eventType.vue'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'pointPop',\r\n    components: {\r\n        eventSubscription,\r\n        informationType,\r\n        singleHlsVideo,\r\n        warmingInfoPop,\r\n        eventTable,\r\n        eventType\r\n    },\r\n    data() {\r\n        return {\r\n            sizeStyle: {\r\n                transform: 'translate(-10%, 50%) scale(2.2)',\r\n                left: '10%',\r\n\r\n            },\r\n            allStyle: {\r\n                width: '1050px',\r\n                height: '800px',\r\n            },\r\n            sizeStyle2: {\r\n                transform: 'translate(-10%, 50%) scale(2.1)',\r\n                left: '45%',\r\n            },\r\n            oneWarnPushInfo: {},\r\n            minWidth: '35rem',\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState('action', [\"eventSubBoxFlag\", \"isScreenShow\", \"playerList\", \"eventSubBoxInfoFlag\", 'eventTable', 'eventType',]),\r\n        getUeTxt() {\r\n            return this.$store.state.dataChannelText\r\n        },\r\n    },\r\n    watch: {\r\n        getUeTxt(nv) {\r\n            if (nv?.typename == '事件推送') {\r\n                // 关闭事件推送弹窗\r\n                // this.$store.commit('action/getEventSu bBoxFlag', false)\r\n                // 关闭看板\r\n                this.$store.commit('action/getIsScreenShow', false)\r\n                // 打开视频列表\r\n                this.$store.commit('action/getEventSubBoxInfoFlag', true)\r\n                this.oneWarnPushInfo = nv\r\n                // this.openInfo(true, nv)\r\n            }\r\n        },\r\n        playerList: {\r\n            handler(nv) {\r\n            },\r\n            immediate: true,\r\n        }\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    beforeDestroy() {\r\n        this.$eventBus.$emit('senTxtToUe', '清除')\r\n\r\n    },\r\n    methods: {\r\n        handleStyle(newStyle) {\r\n            this.allStyle = { ...this.allStyle, ...newStyle };\r\n        },\r\n        // 打开监控\r\n        openVideo() {\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n        },\r\n\r\n    },\r\n\r\n}\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.screen-box {\r\n    position: fixed;\r\n    pointer-events: none;\r\n    background: none !important;\r\n    z-index: 9999;\r\n}\r\n</style>"], "mappings": "AA+BA,OAAAA,eAAA;AACA,OAAAC,cAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAN,iBAAA;IACAF,eAAA;IACAC,cAAA;IACAE,cAAA;IACAC,UAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,SAAA;QACAC,SAAA;QACAC,IAAA;MAEA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,UAAA;QACAL,SAAA;QACAC,IAAA;MACA;MACAK,eAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAb,QAAA;IACAc,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,eAAA;IACA;EACA;EACAC,KAAA;IACAJ,SAAAK,EAAA;MACA,IAAAA,EAAA,EAAAC,QAAA;QACA;QACA;QACA;QACA,KAAAL,MAAA,CAAAM,MAAA;QACA;QACA,KAAAN,MAAA,CAAAM,MAAA;QACA,KAAAV,eAAA,GAAAQ,EAAA;QACA;MACA;IACA;IACAG,UAAA;MACAC,QAAAJ,EAAA,GACA;MACAK,SAAA;IACA;EAEA;EACAC,QAAA,GAEA;EACAC,cAAA;IACA,KAAAC,SAAA,CAAAC,KAAA;EAEA;EACAC,OAAA;IACAC,YAAAC,QAAA;MACA,KAAAxB,QAAA;QAAA,QAAAA,QAAA;QAAA,GAAAwB;MAAA;IACA;IACA;IACAC,UAAA;MACA,KAAAjB,MAAA,CAAAM,MAAA;IACA;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}