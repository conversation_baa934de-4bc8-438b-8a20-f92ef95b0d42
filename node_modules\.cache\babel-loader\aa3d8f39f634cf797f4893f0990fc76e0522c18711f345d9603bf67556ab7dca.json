{"ast": null, "code": "// import { toRaw } from 'vue';\nconst _RSLPLAYERMESSAGE = {\n  0: \"视频流获取成功\",\n  1: \"视频第一帧解析成功\",\n  2: \"视频图片保存成功\"\n};\nfunction RslPlayer(e) {\n  this._option = e;\n  this._isPlay = false;\n  this._isFullScreen = false;\n  this._wasmInit = false;\n  this._callback = e.callback || function () {};\n  this._canvas = document.getElementById(e.canvasId);\n  if (this._canvas && this._canvas.getContext(\"2d\")) {\n    this._ctx = this._canvas.getContext(\"2d\");\n  } else {\n    this._ctx = null;\n  }\n  this._openLoopThreadTime = e.openLoopThreadTime || 1e3 * 60 * 60;\n  this._openLoopThreadTimer = null;\n  this._fetchOption = e.fetchOption || {};\n  this._fetchRquestOption = {\n    ...{\n      method: \"get\",\n      cors: true,\n      cache: \"no-cache\"\n    },\n    ...this._fetchOption\n  };\n  this._watermark = Object.assign({\n    text: this._option.watermarkText\n  }, {\n    font: \"14px 微软雅黑\",\n    fillStyle: \"rgba(255,255,255,.4)\",\n    rotate: -15 * Math.PI / 180,\n    num: 5\n  }, this._option.watermarkStyle ? this._option.watermarkStyle : {});\n  this.clip_data = [];\n  if (this._option.autoPlay) {\n    this.play();\n  }\n}\nRslPlayer.prototype = {\n  constructor: RslPlayer,\n  _initWorker: function () {\n    if (typeof this._work == \"undefined\") {\n      this._work = new Worker(`/rslPlayer/work.js`);\n      this._work.onmessage = this._workMsgHandler.bind(this);\n    }\n    if (typeof this._render == \"undefined\") {\n      this._render = new Worker(`/rslPlayer/render.js`);\n      this._render.onmessage = this._renderMsgHandler.bind(this);\n    }\n  },\n  _renderMsgHandler: function ({\n    data: {\n      type: e,\n      result: t\n    }\n  }) {\n    let s = this;\n    if (e === \"wasmInit\") {\n      let e = this._canvas.width;\n      let t = this._canvas.height;\n      s._wasmInit = true;\n      s._render.postMessage({\n        type: \"setWH\",\n        params: {\n          width: e,\n          height: t\n        }\n      });\n      if (this._isPlay) {\n        s._work.postMessage({\n          type: \"fetchStart\",\n          params: {\n            url: s._option.url,\n            fetchOption: {\n              ...s._fetchRquestOption\n            }\n          }\n        });\n      }\n    } else if (e === \"renderSuccess\") {\n      let {\n        imgData: e\n      } = t;\n      if (s._canvas && s._ctx) {\n        s._clearCanvas();\n        // 判断是否有裁剪数据\n        if (this.clip_data.length === 0) {\n          // 判断每一帧的宽度是否和canvas宽高一致\n          if (this._canvas.width === e.width) {\n            s._ctx.putImageData(e, 0, 0);\n          } else {\n            (async () => {\n              const bitmap = await createImageBitmap(e);\n              s._ctx.drawImage(bitmap, 0, 0, this._canvas.width, this._canvas.height);\n              bitmap.close();\n            })();\n          }\n        } else {\n          // 裁剪规则定义\n          const clip_rules = this.getClipRules(s._ctx);\n          (async () => {\n            const bitmap = await createImageBitmap(e);\n            s._ctx.drawImage(bitmap, 0, 0, this._canvas.width, this._canvas.height);\n            bitmap.close();\n          })();\n        }\n        s._renderWidget();\n        s._callback && s._callback(\"1\", _RSLPLAYERMESSAGE[\"1\"]);\n      }\n    }\n  },\n  _workMsgHandler: function ({\n    data: {\n      type: e,\n      result: t\n    }\n  }) {\n    if (e === \"fetchSuccess\") {\n      this._render.postMessage({\n        type: \"renderStart\",\n        params: {\n          value: t.value\n        }\n      }, [t.value.buffer]);\n    }\n  },\n  play: function () {\n    if (!this._isPlay) {\n      this._isPlay = true;\n      this._initWorker();\n      this.openLoopThread();\n    }\n  },\n  setUrl: function (e) {\n    this._option.url = e;\n  },\n  setClipData: function (clip_data = []) {\n    // 裁剪数据必须为数组，否则处理会报错\n    if (clip_data && clip_data instanceof Array) {\n      this.clip_data = clip_data;\n    } else {\n      this.clip_data = [];\n    }\n  },\n  getClipRules: function (context) {\n    //从起始点开始绘制\n    if (this.clip_data.length === 0) {\n      return false;\n    }\n    const points = this.clip_data;\n    context.beginPath();\n    for (let i = 0; i < points.length; i++) {\n      context.moveTo(points[0][0].x, points[0][0].y);\n      context.lineWidth = 4;\n      // 如果某项是null，则中止\n      context.strokeStyle = \"rgba(18, 239, 255, 1)\";\n      if (!points[i]) {\n        return false;\n      }\n      for (let j = 0; j < points[i].length; j++) {\n        context.lineTo(points[i][j].x, points[i][j].y);\n      }\n      context.strokeStyle = \"rgba(18, 239, 255, 0)\";\n      context.lineTo(points[i][0].x, points[i][0].y);\n    }\n    // context.stroke();\n    context.clip(); //次方法下面的部分为待剪切区域，上面的部分为剪切区域\n  },\n  setWaterText: function (e) {\n    this._watermark.text = e;\n  },\n  openLoopThread() {\n    let e = this;\n    if (this._openLoopThreadTime) {\n      if (this._openLoopThreadTimer) {\n        clearInterval(e._openLoopThreadTimer);\n        e._openLoopThreadTimer = null;\n      }\n      e._openLoopThreadTimer = setInterval(function () {\n        if (typeof e._work != \"undefined\") {\n          e._work.postMessage({\n            type: \"fetchStop\"\n          });\n          e._work.terminate();\n          e._work = undefined;\n        }\n        if (typeof e._render != \"undefined\") {\n          e._render.terminate();\n          e._render = undefined;\n        }\n        e._initWorker();\n      }, e._openLoopThreadTime);\n    }\n  },\n  _getTimes: function () {\n    function e(e) {\n      return e < 10 ? \"0\" + e : e;\n    }\n    let t = new Date();\n    let s = t.getFullYear();\n    let n = e(t.getMonth() + 1);\n    let i = e(t.getDay());\n    let a = e(t.getHours());\n    let r = e(t.getMinutes());\n    let l = e(t.getSeconds());\n    return `${s}${n}${i}_${a}${r}${l}`;\n  },\n  _renderWidget: function () {\n    if (this._watermark.text) {\n      this._renderWaterMarkText();\n    }\n  },\n  _renderWaterMarkText() {\n    let e = this._canvas.width;\n    let t = this._canvas.height;\n    let s = this._watermark.text.split(\"###\");\n    let n = s[0] ? s[0] : \"\";\n    let i = s[1] ? s[1] : \"\";\n    let a = parseInt(this._watermark.font);\n    let r = [{\n      x: e / 4,\n      y: t / 4\n    }, {\n      x: 3 * e / 4,\n      y: t / 4\n    }, {\n      x: e / 4,\n      y: 3 * t / 4\n    }, {\n      x: 3 * e / 4,\n      y: 3 * t / 4\n    }, {\n      x: e / 2,\n      y: t / 2\n    }];\n    for (let s = 0; s < r.length; s++) {\n      let {\n        x: e,\n        y: t\n      } = r[s];\n      this._renderCanvasText(n, e, t, \"fix\");\n      this._renderCanvasText(i, e, t);\n    }\n  },\n  _renderCanvasText: function (i, a, r, l) {\n    if (this._ctx) {\n      let e = this._ctx.measureText(i);\n      let t = e.width;\n      let s = e.actualBoundingBoxAscent + e.actualBoundingBoxDescent;\n      this._ctx.save();\n      this._ctx.font = this._watermark.font;\n      this._ctx.fillStyle = this._watermark.fillStyle;\n      let n = l ? r + t / 2 - s : r + t / 2;\n      this._ctx.rotate(this._watermark.rotate);\n      this._ctx.fillText(i, a - t / 2 - t / 2, n);\n      this._ctx.restore();\n    }\n  },\n  stop: function () {\n    let e = this;\n    if (this._isPlay) {\n      this._work.postMessage({\n        type: \"fetchStop\"\n      });\n      this._isPlay = false;\n      this._wasmInit = false;\n      if (this._openLoopThreadTimer) {\n        clearInterval(e._openLoopThreadTimer);\n        e._openLoopThreadTimer = null;\n      }\n      this._work.terminate();\n      this._render.terminate();\n      this._work = undefined;\n      this._render = undefined;\n    }\n  },\n  _clearCanvas: function () {\n    let e = this;\n    e._canvas.height = e._canvas.height;\n    e._ctx && e._ctx.clearRect(0, 0, e._canvas.width, e._canvas.height);\n  },\n  _downLoad: function (n) {\n    let i = this;\n    return new Promise(function (t, s) {\n      try {\n        let e = document.createElement(\"a\");\n        e.download = i._getTimes();\n        e.href = n;\n        document.body.appendChild(e);\n        e.click();\n        e.remove();\n        t();\n      } catch (e) {\n        s(e);\n      }\n    });\n  },\n  resize: function () {\n    let e = this;\n    let t = e._canvas.width;\n    let s = e._canvas.height;\n    if (e._render && e._render.postMessage) {\n      e._render.postMessage({\n        type: \"setWH\",\n        params: {\n          width: t,\n          height: s\n        }\n      });\n      e._clearCanvas();\n    }\n  },\n  hide: function () {\n    this._canvas.style.display = \"none\";\n  },\n  show: function () {\n    this._canvas.style.display = \"block\";\n  },\n  cancel: function () {\n    this._clearCanvas();\n    this.stop();\n  },\n  screenshot: function () {\n    let e = this;\n    this.stop();\n    let t = this._canvas.toDataURL(\"image/png\");\n    this._downLoad(t).then(() => {\n      e._callback && e._callback(\"2\", _RSLPLAYERMESSAGE[\"2\"]);\n      e.play();\n    });\n  },\n  fullScreen: function () {\n    let e = this;\n    if (e._canvas) {\n      if (e._canvas.requestFullscreen) {\n        e._canvas.requestFullscreen();\n      } else if (e._canvas.msRequestFullscreen) {\n        e._canvas.msRequestFullscreen();\n      } else if (e._canvas.mozRequestFullScreen) {\n        e._canvas.mozRequestFullScreen();\n      } else if (e._canvas.webkitRequestFullScreen) {\n        e._canvas.webkitRequestFullScreen();\n      }\n    }\n  },\n  exitFullScreen: function () {\n    if (this._canvas) {\n      if (this._canvas.exitFullscreen) {\n        this._canvas.exitFullscreen();\n      } else if (document.msExitFullscreen) {\n        this._canvas.msExitFullscreen();\n      } else if (document.mozCancelFullScreen) {\n        this._canvas.mozCancelFullScreen();\n      } else if (document.webkitCancelFullScreen) {\n        this._canvas.webkitCancelFullScreen();\n      }\n    }\n  }\n};\nexport default RslPlayer;", "map": {"version": 3, "names": ["_RSLPLAYERMESSAGE", "RslPlayer", "e", "_option", "_isPlay", "_isFullScreen", "_wasmInit", "_callback", "callback", "_canvas", "document", "getElementById", "canvasId", "getContext", "_ctx", "_openLoopThreadTime", "openLoopThreadTime", "_openLoopThreadTimer", "_fetchOption", "fetchOption", "_fetchRquestOption", "method", "cors", "cache", "_watermark", "Object", "assign", "text", "watermarkText", "font", "fillStyle", "rotate", "Math", "PI", "num", "watermarkStyle", "clip_data", "autoPlay", "play", "prototype", "constructor", "_initWorker", "_work", "Worker", "onmessage", "_workMsgHandler", "bind", "_render", "_renderMsgHandler", "data", "type", "result", "t", "s", "width", "height", "postMessage", "params", "url", "imgData", "_clearCanvas", "length", "putImageData", "bitmap", "createImageBitmap", "drawImage", "close", "clip_rules", "getClipRules", "_renderWidget", "value", "buffer", "openLoopThread", "setUrl", "setClipData", "Array", "context", "points", "beginPath", "i", "moveTo", "x", "y", "lineWidth", "strokeStyle", "j", "lineTo", "clip", "setWaterText", "clearInterval", "setInterval", "terminate", "undefined", "_getTimes", "Date", "getFullYear", "n", "getMonth", "getDay", "a", "getHours", "r", "getMinutes", "l", "getSeconds", "_renderWaterMarkText", "split", "parseInt", "_renderCanvasText", "measureText", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "save", "fillText", "restore", "stop", "clearRect", "_downLoad", "Promise", "createElement", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "resize", "hide", "style", "display", "show", "cancel", "screenshot", "toDataURL", "then", "fullScreen", "requestFullscreen", "msRequestFullscreen", "mozRequestFullScreen", "webkitRequestFullScreen", "exitFullScreen", "exitFullscreen", "msExitFullscreen", "mozCancelFullScreen", "webkitCancelFullScreen"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/rslPlayer/rslPlayer_v1.0.all.js"], "sourcesContent": ["// import { toRaw } from 'vue';\r\nconst _RSLPLAYERMESSAGE = {\r\n\t0: \"视频流获取成功\",\r\n\t1: \"视频第一帧解析成功\",\r\n\t2: \"视频图片保存成功\"\r\n};\r\n\r\nfunction RslPlayer(e) {\r\n\tthis._option = e;\r\n\tthis._isPlay = false;\r\n\tthis._isFullScreen = false;\r\n\tthis._wasmInit = false;\r\n\tthis._callback = e.callback || function () { };\r\n\tthis._canvas = document.getElementById(e.canvasId);\r\n\tif (this._canvas && this._canvas.getContext(\"2d\")) {\r\n\t\tthis._ctx = this._canvas.getContext(\"2d\")\r\n\t} else {\r\n\t\tthis._ctx = null\r\n\t}\r\n\tthis._openLoopThreadTime = e.openLoopThreadTime || 1e3 * 60 * 60;\r\n\tthis._openLoopThreadTimer = null;\r\n\tthis._fetchOption = e.fetchOption || {};\r\n\tthis._fetchRquestOption = {\r\n\t\t...{\r\n\t\t\tmethod: \"get\",\r\n\t\t\tcors: true,\r\n\t\t\tcache: \"no-cache\"\r\n\t\t},\r\n\t\t...this._fetchOption\r\n\t};\r\n\tthis._watermark = Object.assign({\r\n\t\ttext: this._option.watermarkText\r\n\t}, {\r\n\t\tfont: \"14px 微软雅黑\",\r\n\t\tfillStyle: \"rgba(255,255,255,.4)\",\r\n\t\trotate: -15 * Math.PI / 180,\r\n\t\tnum: 5\r\n\t}, this._option.watermarkStyle ? this._option.watermarkStyle : {});\r\n\tthis.clip_data = []\r\n\tif (this._option.autoPlay) {\r\n\t\tthis.play()\r\n\t}\r\n}\r\nRslPlayer.prototype = {\r\n\tconstructor: RslPlayer,\r\n\t_initWorker: function () {\r\n\t\tif (typeof this._work == \"undefined\") {\r\n\t\t\tthis._work = new Worker(`/rslPlayer/work.js`);\r\n\t\t\tthis._work.onmessage = this._workMsgHandler.bind(this)\r\n\t\t}\r\n\t\tif (typeof this._render == \"undefined\") {\r\n\t\t\tthis._render = new Worker(`/rslPlayer/render.js`);\r\n\t\t\tthis._render.onmessage = this._renderMsgHandler.bind(this)\r\n\t\t}\r\n\t},\r\n\t_renderMsgHandler: function ({\r\n\t\tdata: {\r\n\t\t\ttype: e,\r\n\t\t\tresult: t\r\n\t\t}\r\n\t}) {\r\n\t\tlet s = this;\r\n\t\tif (e === \"wasmInit\") {\r\n\t\t\tlet e = this._canvas.width;\r\n\t\t\tlet t = this._canvas.height;\r\n\t\t\ts._wasmInit = true;\r\n\t\t\ts._render.postMessage({\r\n\t\t\t\ttype: \"setWH\",\r\n\t\t\t\tparams: {\r\n\t\t\t\t\twidth: e,\r\n\t\t\t\t\theight: t\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tif (this._isPlay) {\r\n\t\t\t\ts._work.postMessage({\r\n\t\t\t\t\ttype: \"fetchStart\",\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\turl: s._option.url,\r\n\t\t\t\t\t\tfetchOption: {...s._fetchRquestOption}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t} else if (e === \"renderSuccess\") {\r\n\t\t\tlet {\r\n\t\t\t\timgData: e\r\n\t\t\t} = t;\r\n\t\t\tif (s._canvas && s._ctx) {\r\n\t\t\t\ts._clearCanvas();\r\n\t\t\t\t// 判断是否有裁剪数据\r\n\t\t\t\tif (this.clip_data.length === 0) {\r\n\t\t\t\t\t// 判断每一帧的宽度是否和canvas宽高一致\r\n\t\t\t\t\tif (this._canvas.width === e.width) {\r\n\t\t\t\t\t\ts._ctx.putImageData(e, 0, 0)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t(async () => {\r\n\t\t\t\t\t\t\tconst bitmap = await createImageBitmap(e);\r\n\t\t\t\t\t\t\ts._ctx.drawImage(bitmap, 0, 0, this._canvas.width, this._canvas.height);\r\n\t\t\t\t\t\t\tbitmap.close()\r\n\t\t\t\t\t\t})()\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 裁剪规则定义\r\n\t\t\t\t\tconst clip_rules = this.getClipRules(s._ctx);\r\n\t\t\t\t\t(async () => {\r\n\t\t\t\t\t\tconst bitmap = await createImageBitmap(e);\r\n\t\t\t\t\t\ts._ctx.drawImage(bitmap, 0, 0, this._canvas.width, this._canvas.height);\r\n\t\t\t\t\t\tbitmap.close()\r\n\t\t\t\t\t})()\r\n\t\t\t\t}\r\n\t\t\t\ts._renderWidget();\r\n\t\t\t\ts._callback && s._callback(\"1\", _RSLPLAYERMESSAGE[\"1\"])\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t_workMsgHandler: function ({\r\n\t\tdata: {\r\n\t\t\ttype: e,\r\n\t\t\tresult: t\r\n\t\t}\r\n\t}) {\r\n\t\tif (e === \"fetchSuccess\") {\r\n\t\t\tthis._render.postMessage({\r\n\t\t\t\ttype: \"renderStart\",\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tvalue: t.value\r\n\t\t\t\t}\r\n\t\t\t}, [t.value.buffer])\r\n\t\t}\r\n\t},\r\n\tplay: function () {\r\n\t\tif (!this._isPlay) {\r\n\t\t\tthis._isPlay = true;\r\n\t\t\tthis._initWorker();\r\n\t\t\tthis.openLoopThread()\r\n\t\t}\r\n\t},\r\n\tsetUrl: function (e) {\r\n\t\tthis._option.url = e\r\n\t},\r\n\tsetClipData: function (clip_data = []) {\r\n\t\t// 裁剪数据必须为数组，否则处理会报错\r\n\t\tif (clip_data && clip_data instanceof Array) {\r\n\t\t\tthis.clip_data = clip_data\r\n\t\t} else {\r\n\t\t\tthis.clip_data = []\r\n\t\t}\r\n\t},\r\n\tgetClipRules: function (context) {\r\n\t\t//从起始点开始绘制\r\n\t\tif (this.clip_data.length === 0) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\tconst points = this.clip_data\r\n\t\tcontext.beginPath();\r\n\t\tfor (let i = 0; i < points.length; i++) {\r\n\t\t\tcontext.moveTo(points[0][0].x, points[0][0].y);\r\n\t\t\tcontext.lineWidth = 4;\r\n\t\t\t// 如果某项是null，则中止\r\n\t\t\tcontext.strokeStyle = \"rgba(18, 239, 255, 1)\";\r\n\t\t\tif (!points[i]) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tfor (let j = 0; j < points[i].length; j++) {\r\n\t\t\t\tcontext.lineTo(points[i][j].x, points[i][j].y);\r\n\t\t\t}\r\n\t\t\tcontext.strokeStyle = \"rgba(18, 239, 255, 0)\";\r\n\t\t\tcontext.lineTo(points[i][0].x, points[i][0].y);\r\n\t\t}\r\n\t\t// context.stroke();\r\n\t\tcontext.clip();//次方法下面的部分为待剪切区域，上面的部分为剪切区域\r\n\t},\r\n\tsetWaterText: function (e) {\r\n\t\tthis._watermark.text = e\r\n\t},\r\n\topenLoopThread() {\r\n\t\tlet e = this;\r\n\t\tif (this._openLoopThreadTime) {\r\n\t\t\tif (this._openLoopThreadTimer) {\r\n\t\t\t\tclearInterval(e._openLoopThreadTimer);\r\n\t\t\t\te._openLoopThreadTimer = null\r\n\t\t\t}\r\n\t\t\te._openLoopThreadTimer = setInterval(function () {\r\n\t\t\t\tif (typeof e._work != \"undefined\") {\r\n\t\t\t\t\te._work.postMessage({\r\n\t\t\t\t\t\ttype: \"fetchStop\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\te._work.terminate();\r\n\t\t\t\t\te._work = undefined\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof e._render != \"undefined\") {\r\n\t\t\t\t\te._render.terminate();\r\n\t\t\t\t\te._render = undefined\r\n\t\t\t\t}\r\n\t\t\t\te._initWorker()\r\n\t\t\t}, e._openLoopThreadTime)\r\n\t\t}\r\n\t},\r\n\t_getTimes: function () {\r\n\t\tfunction e(e) {\r\n\t\t\treturn e < 10 ? \"0\" + e : e\r\n\t\t}\r\n\t\tlet t = new Date;\r\n\t\tlet s = t.getFullYear();\r\n\t\tlet n = e(t.getMonth() + 1);\r\n\t\tlet i = e(t.getDay());\r\n\t\tlet a = e(t.getHours());\r\n\t\tlet r = e(t.getMinutes());\r\n\t\tlet l = e(t.getSeconds());\r\n\t\treturn `${s}${n}${i}_${a}${r}${l}`\r\n\t},\r\n\t_renderWidget: function () {\r\n\t\tif (this._watermark.text) {\r\n\t\t\tthis._renderWaterMarkText()\r\n\t\t}\r\n\t},\r\n\t_renderWaterMarkText() {\r\n\t\tlet e = this._canvas.width;\r\n\t\tlet t = this._canvas.height;\r\n\t\tlet s = this._watermark.text.split(\"###\");\r\n\t\tlet n = s[0] ? s[0] : \"\";\r\n\t\tlet i = s[1] ? s[1] : \"\";\r\n\t\tlet a = parseInt(this._watermark.font);\r\n\t\tlet r = [{\r\n\t\t\tx: e / 4,\r\n\t\t\ty: t / 4\r\n\t\t}, {\r\n\t\t\tx: 3 * e / 4,\r\n\t\t\ty: t / 4\r\n\t\t}, {\r\n\t\t\tx: e / 4,\r\n\t\t\ty: 3 * t / 4\r\n\t\t}, {\r\n\t\t\tx: 3 * e / 4,\r\n\t\t\ty: 3 * t / 4\r\n\t\t}, {\r\n\t\t\tx: e / 2,\r\n\t\t\ty: t / 2\r\n\t\t}];\r\n\t\tfor (let s = 0; s < r.length; s++) {\r\n\t\t\tlet {\r\n\t\t\t\tx: e,\r\n\t\t\t\ty: t\r\n\t\t\t} = r[s];\r\n\t\t\tthis._renderCanvasText(n, e, t, \"fix\");\r\n\t\t\tthis._renderCanvasText(i, e, t)\r\n\t\t}\r\n\t},\r\n\t_renderCanvasText: function (i, a, r, l) {\r\n\t\tif (this._ctx) {\r\n\t\t\tlet e = this._ctx.measureText(i);\r\n\t\t\tlet t = e.width;\r\n\t\t\tlet s = e.actualBoundingBoxAscent + e.actualBoundingBoxDescent;\r\n\t\t\tthis._ctx.save();\r\n\t\t\tthis._ctx.font = this._watermark.font;\r\n\t\t\tthis._ctx.fillStyle = this._watermark.fillStyle;\r\n\t\t\tlet n = l ? r + t / 2 - s : r + t / 2;\r\n\t\t\tthis._ctx.rotate(this._watermark.rotate);\r\n\t\t\tthis._ctx.fillText(i, a - t / 2 - t / 2, n);\r\n\t\t\tthis._ctx.restore()\r\n\t\t}\r\n\t},\r\n\tstop: function () {\r\n\t\tlet e = this;\r\n\t\tif (this._isPlay) {\r\n\t\t\tthis._work.postMessage({\r\n\t\t\t\ttype: \"fetchStop\"\r\n\t\t\t});\r\n\t\t\tthis._isPlay = false;\r\n\t\t\tthis._wasmInit = false;\r\n\t\t\tif (this._openLoopThreadTimer) {\r\n\t\t\t\tclearInterval(e._openLoopThreadTimer);\r\n\t\t\t\te._openLoopThreadTimer = null\r\n\t\t\t}\r\n\t\t\tthis._work.terminate();\r\n\t\t\tthis._render.terminate();\r\n\t\t\tthis._work = undefined;\r\n\t\t\tthis._render = undefined\r\n\t\t}\r\n\t},\r\n\t_clearCanvas: function () {\r\n\t\tlet e = this;\r\n\t\te._canvas.height = e._canvas.height;\r\n\t\te._ctx && e._ctx.clearRect(0, 0, e._canvas.width, e._canvas.height)\r\n\t},\r\n\t_downLoad: function (n) {\r\n\t\tlet i = this;\r\n\t\treturn new Promise(function (t, s) {\r\n\t\t\ttry {\r\n\t\t\t\tlet e = document.createElement(\"a\");\r\n\t\t\t\te.download = i._getTimes();\r\n\t\t\t\te.href = n;\r\n\t\t\t\tdocument.body.appendChild(e);\r\n\t\t\t\te.click();\r\n\t\t\t\te.remove();\r\n\t\t\t\tt()\r\n\t\t\t} catch (e) {\r\n\t\t\t\ts(e)\r\n\t\t\t}\r\n\t\t})\r\n\t},\r\n\tresize: function () {\r\n\t\tlet e = this;\r\n\t\tlet t = e._canvas.width;\r\n\t\tlet s = e._canvas.height;\r\n\t\tif (e._render && e._render.postMessage) {\r\n\t\t\te._render.postMessage({\r\n\t\t\t\ttype: \"setWH\",\r\n\t\t\t\tparams: {\r\n\t\t\t\t\twidth: t,\r\n\t\t\t\t\theight: s\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\te._clearCanvas()\r\n\t\t}\r\n\t},\r\n\thide: function () {\r\n\t\tthis._canvas.style.display = \"none\"\r\n\t},\r\n\tshow: function () {\r\n\t\tthis._canvas.style.display = \"block\"\r\n\t},\r\n\tcancel: function () {\r\n\t\tthis._clearCanvas();\r\n\t\tthis.stop()\r\n\t},\r\n\tscreenshot: function () {\r\n\t\tlet e = this;\r\n\t\tthis.stop();\r\n\t\tlet t = this._canvas.toDataURL(\"image/png\");\r\n\t\tthis._downLoad(t).then(() => {\r\n\t\t\te._callback && e._callback(\"2\", _RSLPLAYERMESSAGE[\"2\"]);\r\n\t\t\te.play()\r\n\t\t})\r\n\t},\r\n\tfullScreen: function () {\r\n\t\tlet e = this;\r\n\t\tif (e._canvas) {\r\n\t\t\tif (e._canvas.requestFullscreen) {\r\n\t\t\t\te._canvas.requestFullscreen()\r\n\t\t\t} else if (e._canvas.msRequestFullscreen) {\r\n\t\t\t\te._canvas.msRequestFullscreen()\r\n\t\t\t} else if (e._canvas.mozRequestFullScreen) {\r\n\t\t\t\te._canvas.mozRequestFullScreen()\r\n\t\t\t} else if (e._canvas.webkitRequestFullScreen) {\r\n\t\t\t\te._canvas.webkitRequestFullScreen()\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\texitFullScreen: function () {\r\n\t\tif (this._canvas) {\r\n\t\t\tif (this._canvas.exitFullscreen) {\r\n\t\t\t\tthis._canvas.exitFullscreen()\r\n\t\t\t} else if (document.msExitFullscreen) {\r\n\t\t\t\tthis._canvas.msExitFullscreen()\r\n\t\t\t} else if (document.mozCancelFullScreen) {\r\n\t\t\t\tthis._canvas.mozCancelFullScreen()\r\n\t\t\t} else if (document.webkitCancelFullScreen) {\r\n\t\t\t\tthis._canvas.webkitCancelFullScreen()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\nexport default RslPlayer;"], "mappings": "AAAA;AACA,MAAMA,iBAAiB,GAAG;EACzB,CAAC,EAAE,SAAS;EACZ,CAAC,EAAE,WAAW;EACd,CAAC,EAAE;AACJ,CAAC;AAED,SAASC,SAASA,CAACC,CAAC,EAAE;EACrB,IAAI,CAACC,OAAO,GAAGD,CAAC;EAChB,IAAI,CAACE,OAAO,GAAG,KAAK;EACpB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;EACtB,IAAI,CAACC,SAAS,GAAGL,CAAC,CAACM,QAAQ,IAAI,YAAY,CAAE,CAAC;EAC9C,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACT,CAAC,CAACU,QAAQ,CAAC;EAClD,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACA,OAAO,CAACI,UAAU,CAAC,IAAI,CAAC,EAAE;IAClD,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,OAAO,CAACI,UAAU,CAAC,IAAI,CAAC;EAC1C,CAAC,MAAM;IACN,IAAI,CAACC,IAAI,GAAG,IAAI;EACjB;EACA,IAAI,CAACC,mBAAmB,GAAGb,CAAC,CAACc,kBAAkB,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE;EAChE,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAChC,IAAI,CAACC,YAAY,GAAGhB,CAAC,CAACiB,WAAW,IAAI,CAAC,CAAC;EACvC,IAAI,CAACC,kBAAkB,GAAG;IACzB,GAAG;MACFC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE;IACR,CAAC;IACD,GAAG,IAAI,CAACL;EACT,CAAC;EACD,IAAI,CAACM,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC;IAC/BC,IAAI,EAAE,IAAI,CAACxB,OAAO,CAACyB;EACpB,CAAC,EAAE;IACFC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,sBAAsB;IACjCC,MAAM,EAAE,CAAC,EAAE,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC3BC,GAAG,EAAE;EACN,CAAC,EAAE,IAAI,CAAC/B,OAAO,CAACgC,cAAc,GAAG,IAAI,CAAChC,OAAO,CAACgC,cAAc,GAAG,CAAC,CAAC,CAAC;EAClE,IAAI,CAACC,SAAS,GAAG,EAAE;EACnB,IAAI,IAAI,CAACjC,OAAO,CAACkC,QAAQ,EAAE;IAC1B,IAAI,CAACC,IAAI,CAAC,CAAC;EACZ;AACD;AACArC,SAAS,CAACsC,SAAS,GAAG;EACrBC,WAAW,EAAEvC,SAAS;EACtBwC,WAAW,EAAE,SAAAA,CAAA,EAAY;IACxB,IAAI,OAAO,IAAI,CAACC,KAAK,IAAI,WAAW,EAAE;MACrC,IAAI,CAACA,KAAK,GAAG,IAAIC,MAAM,CAAC,oBAAoB,CAAC;MAC7C,IAAI,CAACD,KAAK,CAACE,SAAS,GAAG,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;IACvD;IACA,IAAI,OAAO,IAAI,CAACC,OAAO,IAAI,WAAW,EAAE;MACvC,IAAI,CAACA,OAAO,GAAG,IAAIJ,MAAM,CAAC,sBAAsB,CAAC;MACjD,IAAI,CAACI,OAAO,CAACH,SAAS,GAAG,IAAI,CAACI,iBAAiB,CAACF,IAAI,CAAC,IAAI,CAAC;IAC3D;EACD,CAAC;EACDE,iBAAiB,EAAE,SAAAA,CAAU;IAC5BC,IAAI,EAAE;MACLC,IAAI,EAAEhD,CAAC;MACPiD,MAAM,EAAEC;IACT;EACD,CAAC,EAAE;IACF,IAAIC,CAAC,GAAG,IAAI;IACZ,IAAInD,CAAC,KAAK,UAAU,EAAE;MACrB,IAAIA,CAAC,GAAG,IAAI,CAACO,OAAO,CAAC6C,KAAK;MAC1B,IAAIF,CAAC,GAAG,IAAI,CAAC3C,OAAO,CAAC8C,MAAM;MAC3BF,CAAC,CAAC/C,SAAS,GAAG,IAAI;MAClB+C,CAAC,CAACN,OAAO,CAACS,WAAW,CAAC;QACrBN,IAAI,EAAE,OAAO;QACbO,MAAM,EAAE;UACPH,KAAK,EAAEpD,CAAC;UACRqD,MAAM,EAAEH;QACT;MACD,CAAC,CAAC;MACF,IAAI,IAAI,CAAChD,OAAO,EAAE;QACjBiD,CAAC,CAACX,KAAK,CAACc,WAAW,CAAC;UACnBN,IAAI,EAAE,YAAY;UAClBO,MAAM,EAAE;YACPC,GAAG,EAAEL,CAAC,CAAClD,OAAO,CAACuD,GAAG;YAClBvC,WAAW,EAAE;cAAC,GAAGkC,CAAC,CAACjC;YAAkB;UACtC;QACD,CAAC,CAAC;MACH;IACD,CAAC,MAAM,IAAIlB,CAAC,KAAK,eAAe,EAAE;MACjC,IAAI;QACHyD,OAAO,EAAEzD;MACV,CAAC,GAAGkD,CAAC;MACL,IAAIC,CAAC,CAAC5C,OAAO,IAAI4C,CAAC,CAACvC,IAAI,EAAE;QACxBuC,CAAC,CAACO,YAAY,CAAC,CAAC;QAChB;QACA,IAAI,IAAI,CAACxB,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;UAChC;UACA,IAAI,IAAI,CAACpD,OAAO,CAAC6C,KAAK,KAAKpD,CAAC,CAACoD,KAAK,EAAE;YACnCD,CAAC,CAACvC,IAAI,CAACgD,YAAY,CAAC5D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7B,CAAC,MAAM;YACN,CAAC,YAAY;cACZ,MAAM6D,MAAM,GAAG,MAAMC,iBAAiB,CAAC9D,CAAC,CAAC;cACzCmD,CAAC,CAACvC,IAAI,CAACmD,SAAS,CAACF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACtD,OAAO,CAAC6C,KAAK,EAAE,IAAI,CAAC7C,OAAO,CAAC8C,MAAM,CAAC;cACvEQ,MAAM,CAACG,KAAK,CAAC,CAAC;YACf,CAAC,EAAE,CAAC;UACL;QACD,CAAC,MAAM;UACN;UACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACf,CAAC,CAACvC,IAAI,CAAC;UAC5C,CAAC,YAAY;YACZ,MAAMiD,MAAM,GAAG,MAAMC,iBAAiB,CAAC9D,CAAC,CAAC;YACzCmD,CAAC,CAACvC,IAAI,CAACmD,SAAS,CAACF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACtD,OAAO,CAAC6C,KAAK,EAAE,IAAI,CAAC7C,OAAO,CAAC8C,MAAM,CAAC;YACvEQ,MAAM,CAACG,KAAK,CAAC,CAAC;UACf,CAAC,EAAE,CAAC;QACL;QACAb,CAAC,CAACgB,aAAa,CAAC,CAAC;QACjBhB,CAAC,CAAC9C,SAAS,IAAI8C,CAAC,CAAC9C,SAAS,CAAC,GAAG,EAAEP,iBAAiB,CAAC,GAAG,CAAC,CAAC;MACxD;IACD;EACD,CAAC;EACD6C,eAAe,EAAE,SAAAA,CAAU;IAC1BI,IAAI,EAAE;MACLC,IAAI,EAAEhD,CAAC;MACPiD,MAAM,EAAEC;IACT;EACD,CAAC,EAAE;IACF,IAAIlD,CAAC,KAAK,cAAc,EAAE;MACzB,IAAI,CAAC6C,OAAO,CAACS,WAAW,CAAC;QACxBN,IAAI,EAAE,aAAa;QACnBO,MAAM,EAAE;UACPa,KAAK,EAAElB,CAAC,CAACkB;QACV;MACD,CAAC,EAAE,CAAClB,CAAC,CAACkB,KAAK,CAACC,MAAM,CAAC,CAAC;IACrB;EACD,CAAC;EACDjC,IAAI,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAI,CAAC,IAAI,CAAClC,OAAO,EAAE;MAClB,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACqC,WAAW,CAAC,CAAC;MAClB,IAAI,CAAC+B,cAAc,CAAC,CAAC;IACtB;EACD,CAAC;EACDC,MAAM,EAAE,SAAAA,CAAUvE,CAAC,EAAE;IACpB,IAAI,CAACC,OAAO,CAACuD,GAAG,GAAGxD,CAAC;EACrB,CAAC;EACDwE,WAAW,EAAE,SAAAA,CAAUtC,SAAS,GAAG,EAAE,EAAE;IACtC;IACA,IAAIA,SAAS,IAAIA,SAAS,YAAYuC,KAAK,EAAE;MAC5C,IAAI,CAACvC,SAAS,GAAGA,SAAS;IAC3B,CAAC,MAAM;MACN,IAAI,CAACA,SAAS,GAAG,EAAE;IACpB;EACD,CAAC;EACDgC,YAAY,EAAE,SAAAA,CAAUQ,OAAO,EAAE;IAChC;IACA,IAAI,IAAI,CAACxC,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,KAAK;IACb;IACA,MAAMgB,MAAM,GAAG,IAAI,CAACzC,SAAS;IAC7BwC,OAAO,CAACE,SAAS,CAAC,CAAC;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAChB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MACvCH,OAAO,CAACI,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,CAAC,EAAEJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,CAAC,CAAC;MAC9CN,OAAO,CAACO,SAAS,GAAG,CAAC;MACrB;MACAP,OAAO,CAACQ,WAAW,GAAG,uBAAuB;MAC7C,IAAI,CAACP,MAAM,CAACE,CAAC,CAAC,EAAE;QACf,OAAO,KAAK;MACb;MACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACE,CAAC,CAAC,CAAClB,MAAM,EAAEwB,CAAC,EAAE,EAAE;QAC1CT,OAAO,CAACU,MAAM,CAACT,MAAM,CAACE,CAAC,CAAC,CAACM,CAAC,CAAC,CAACJ,CAAC,EAAEJ,MAAM,CAACE,CAAC,CAAC,CAACM,CAAC,CAAC,CAACH,CAAC,CAAC;MAC/C;MACAN,OAAO,CAACQ,WAAW,GAAG,uBAAuB;MAC7CR,OAAO,CAACU,MAAM,CAACT,MAAM,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,CAAC,EAAEJ,MAAM,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,CAAC,CAAC;IAC/C;IACA;IACAN,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC;EACDC,YAAY,EAAE,SAAAA,CAAUtF,CAAC,EAAE;IAC1B,IAAI,CAACsB,UAAU,CAACG,IAAI,GAAGzB,CAAC;EACzB,CAAC;EACDsE,cAAcA,CAAA,EAAG;IAChB,IAAItE,CAAC,GAAG,IAAI;IACZ,IAAI,IAAI,CAACa,mBAAmB,EAAE;MAC7B,IAAI,IAAI,CAACE,oBAAoB,EAAE;QAC9BwE,aAAa,CAACvF,CAAC,CAACe,oBAAoB,CAAC;QACrCf,CAAC,CAACe,oBAAoB,GAAG,IAAI;MAC9B;MACAf,CAAC,CAACe,oBAAoB,GAAGyE,WAAW,CAAC,YAAY;QAChD,IAAI,OAAOxF,CAAC,CAACwC,KAAK,IAAI,WAAW,EAAE;UAClCxC,CAAC,CAACwC,KAAK,CAACc,WAAW,CAAC;YACnBN,IAAI,EAAE;UACP,CAAC,CAAC;UACFhD,CAAC,CAACwC,KAAK,CAACiD,SAAS,CAAC,CAAC;UACnBzF,CAAC,CAACwC,KAAK,GAAGkD,SAAS;QACpB;QACA,IAAI,OAAO1F,CAAC,CAAC6C,OAAO,IAAI,WAAW,EAAE;UACpC7C,CAAC,CAAC6C,OAAO,CAAC4C,SAAS,CAAC,CAAC;UACrBzF,CAAC,CAAC6C,OAAO,GAAG6C,SAAS;QACtB;QACA1F,CAAC,CAACuC,WAAW,CAAC,CAAC;MAChB,CAAC,EAAEvC,CAAC,CAACa,mBAAmB,CAAC;IAC1B;EACD,CAAC;EACD8E,SAAS,EAAE,SAAAA,CAAA,EAAY;IACtB,SAAS3F,CAACA,CAACA,CAAC,EAAE;MACb,OAAOA,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,CAAC,GAAGA,CAAC;IAC5B;IACA,IAAIkD,CAAC,GAAG,IAAI0C,IAAI,CAAD,CAAC;IAChB,IAAIzC,CAAC,GAAGD,CAAC,CAAC2C,WAAW,CAAC,CAAC;IACvB,IAAIC,CAAC,GAAG9F,CAAC,CAACkD,CAAC,CAAC6C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAIlB,CAAC,GAAG7E,CAAC,CAACkD,CAAC,CAAC8C,MAAM,CAAC,CAAC,CAAC;IACrB,IAAIC,CAAC,GAAGjG,CAAC,CAACkD,CAAC,CAACgD,QAAQ,CAAC,CAAC,CAAC;IACvB,IAAIC,CAAC,GAAGnG,CAAC,CAACkD,CAAC,CAACkD,UAAU,CAAC,CAAC,CAAC;IACzB,IAAIC,CAAC,GAAGrG,CAAC,CAACkD,CAAC,CAACoD,UAAU,CAAC,CAAC,CAAC;IACzB,OAAO,GAAGnD,CAAC,GAAG2C,CAAC,GAAGjB,CAAC,IAAIoB,CAAC,GAAGE,CAAC,GAAGE,CAAC,EAAE;EACnC,CAAC;EACDlC,aAAa,EAAE,SAAAA,CAAA,EAAY;IAC1B,IAAI,IAAI,CAAC7C,UAAU,CAACG,IAAI,EAAE;MACzB,IAAI,CAAC8E,oBAAoB,CAAC,CAAC;IAC5B;EACD,CAAC;EACDA,oBAAoBA,CAAA,EAAG;IACtB,IAAIvG,CAAC,GAAG,IAAI,CAACO,OAAO,CAAC6C,KAAK;IAC1B,IAAIF,CAAC,GAAG,IAAI,CAAC3C,OAAO,CAAC8C,MAAM;IAC3B,IAAIF,CAAC,GAAG,IAAI,CAAC7B,UAAU,CAACG,IAAI,CAAC+E,KAAK,CAAC,KAAK,CAAC;IACzC,IAAIV,CAAC,GAAG3C,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;IACxB,IAAI0B,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;IACxB,IAAI8C,CAAC,GAAGQ,QAAQ,CAAC,IAAI,CAACnF,UAAU,CAACK,IAAI,CAAC;IACtC,IAAIwE,CAAC,GAAG,CAAC;MACRpB,CAAC,EAAE/E,CAAC,GAAG,CAAC;MACRgF,CAAC,EAAE9B,CAAC,GAAG;IACR,CAAC,EAAE;MACF6B,CAAC,EAAE,CAAC,GAAG/E,CAAC,GAAG,CAAC;MACZgF,CAAC,EAAE9B,CAAC,GAAG;IACR,CAAC,EAAE;MACF6B,CAAC,EAAE/E,CAAC,GAAG,CAAC;MACRgF,CAAC,EAAE,CAAC,GAAG9B,CAAC,GAAG;IACZ,CAAC,EAAE;MACF6B,CAAC,EAAE,CAAC,GAAG/E,CAAC,GAAG,CAAC;MACZgF,CAAC,EAAE,CAAC,GAAG9B,CAAC,GAAG;IACZ,CAAC,EAAE;MACF6B,CAAC,EAAE/E,CAAC,GAAG,CAAC;MACRgF,CAAC,EAAE9B,CAAC,GAAG;IACR,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,CAAC,CAACxC,MAAM,EAAER,CAAC,EAAE,EAAE;MAClC,IAAI;QACH4B,CAAC,EAAE/E,CAAC;QACJgF,CAAC,EAAE9B;MACJ,CAAC,GAAGiD,CAAC,CAAChD,CAAC,CAAC;MACR,IAAI,CAACuD,iBAAiB,CAACZ,CAAC,EAAE9F,CAAC,EAAEkD,CAAC,EAAE,KAAK,CAAC;MACtC,IAAI,CAACwD,iBAAiB,CAAC7B,CAAC,EAAE7E,CAAC,EAAEkD,CAAC,CAAC;IAChC;EACD,CAAC;EACDwD,iBAAiB,EAAE,SAAAA,CAAU7B,CAAC,EAAEoB,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAE;IACxC,IAAI,IAAI,CAACzF,IAAI,EAAE;MACd,IAAIZ,CAAC,GAAG,IAAI,CAACY,IAAI,CAAC+F,WAAW,CAAC9B,CAAC,CAAC;MAChC,IAAI3B,CAAC,GAAGlD,CAAC,CAACoD,KAAK;MACf,IAAID,CAAC,GAAGnD,CAAC,CAAC4G,uBAAuB,GAAG5G,CAAC,CAAC6G,wBAAwB;MAC9D,IAAI,CAACjG,IAAI,CAACkG,IAAI,CAAC,CAAC;MAChB,IAAI,CAAClG,IAAI,CAACe,IAAI,GAAG,IAAI,CAACL,UAAU,CAACK,IAAI;MACrC,IAAI,CAACf,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACN,UAAU,CAACM,SAAS;MAC/C,IAAIkE,CAAC,GAAGO,CAAC,GAAGF,CAAC,GAAGjD,CAAC,GAAG,CAAC,GAAGC,CAAC,GAAGgD,CAAC,GAAGjD,CAAC,GAAG,CAAC;MACrC,IAAI,CAACtC,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACP,UAAU,CAACO,MAAM,CAAC;MACxC,IAAI,CAACjB,IAAI,CAACmG,QAAQ,CAAClC,CAAC,EAAEoB,CAAC,GAAG/C,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE4C,CAAC,CAAC;MAC3C,IAAI,CAAClF,IAAI,CAACoG,OAAO,CAAC,CAAC;IACpB;EACD,CAAC;EACDC,IAAI,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAIjH,CAAC,GAAG,IAAI;IACZ,IAAI,IAAI,CAACE,OAAO,EAAE;MACjB,IAAI,CAACsC,KAAK,CAACc,WAAW,CAAC;QACtBN,IAAI,EAAE;MACP,CAAC,CAAC;MACF,IAAI,CAAC9C,OAAO,GAAG,KAAK;MACpB,IAAI,CAACE,SAAS,GAAG,KAAK;MACtB,IAAI,IAAI,CAACW,oBAAoB,EAAE;QAC9BwE,aAAa,CAACvF,CAAC,CAACe,oBAAoB,CAAC;QACrCf,CAAC,CAACe,oBAAoB,GAAG,IAAI;MAC9B;MACA,IAAI,CAACyB,KAAK,CAACiD,SAAS,CAAC,CAAC;MACtB,IAAI,CAAC5C,OAAO,CAAC4C,SAAS,CAAC,CAAC;MACxB,IAAI,CAACjD,KAAK,GAAGkD,SAAS;MACtB,IAAI,CAAC7C,OAAO,GAAG6C,SAAS;IACzB;EACD,CAAC;EACDhC,YAAY,EAAE,SAAAA,CAAA,EAAY;IACzB,IAAI1D,CAAC,GAAG,IAAI;IACZA,CAAC,CAACO,OAAO,CAAC8C,MAAM,GAAGrD,CAAC,CAACO,OAAO,CAAC8C,MAAM;IACnCrD,CAAC,CAACY,IAAI,IAAIZ,CAAC,CAACY,IAAI,CAACsG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAElH,CAAC,CAACO,OAAO,CAAC6C,KAAK,EAAEpD,CAAC,CAACO,OAAO,CAAC8C,MAAM,CAAC;EACpE,CAAC;EACD8D,SAAS,EAAE,SAAAA,CAAUrB,CAAC,EAAE;IACvB,IAAIjB,CAAC,GAAG,IAAI;IACZ,OAAO,IAAIuC,OAAO,CAAC,UAAUlE,CAAC,EAAEC,CAAC,EAAE;MAClC,IAAI;QACH,IAAInD,CAAC,GAAGQ,QAAQ,CAAC6G,aAAa,CAAC,GAAG,CAAC;QACnCrH,CAAC,CAACsH,QAAQ,GAAGzC,CAAC,CAACc,SAAS,CAAC,CAAC;QAC1B3F,CAAC,CAACuH,IAAI,GAAGzB,CAAC;QACVtF,QAAQ,CAACgH,IAAI,CAACC,WAAW,CAACzH,CAAC,CAAC;QAC5BA,CAAC,CAAC0H,KAAK,CAAC,CAAC;QACT1H,CAAC,CAAC2H,MAAM,CAAC,CAAC;QACVzE,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOlD,CAAC,EAAE;QACXmD,CAAC,CAACnD,CAAC,CAAC;MACL;IACD,CAAC,CAAC;EACH,CAAC;EACD4H,MAAM,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI5H,CAAC,GAAG,IAAI;IACZ,IAAIkD,CAAC,GAAGlD,CAAC,CAACO,OAAO,CAAC6C,KAAK;IACvB,IAAID,CAAC,GAAGnD,CAAC,CAACO,OAAO,CAAC8C,MAAM;IACxB,IAAIrD,CAAC,CAAC6C,OAAO,IAAI7C,CAAC,CAAC6C,OAAO,CAACS,WAAW,EAAE;MACvCtD,CAAC,CAAC6C,OAAO,CAACS,WAAW,CAAC;QACrBN,IAAI,EAAE,OAAO;QACbO,MAAM,EAAE;UACPH,KAAK,EAAEF,CAAC;UACRG,MAAM,EAAEF;QACT;MACD,CAAC,CAAC;MACFnD,CAAC,CAAC0D,YAAY,CAAC,CAAC;IACjB;EACD,CAAC;EACDmE,IAAI,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAI,CAACtH,OAAO,CAACuH,KAAK,CAACC,OAAO,GAAG,MAAM;EACpC,CAAC;EACDC,IAAI,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAI,CAACzH,OAAO,CAACuH,KAAK,CAACC,OAAO,GAAG,OAAO;EACrC,CAAC;EACDE,MAAM,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,CAACvE,YAAY,CAAC,CAAC;IACnB,IAAI,CAACuD,IAAI,CAAC,CAAC;EACZ,CAAC;EACDiB,UAAU,EAAE,SAAAA,CAAA,EAAY;IACvB,IAAIlI,CAAC,GAAG,IAAI;IACZ,IAAI,CAACiH,IAAI,CAAC,CAAC;IACX,IAAI/D,CAAC,GAAG,IAAI,CAAC3C,OAAO,CAAC4H,SAAS,CAAC,WAAW,CAAC;IAC3C,IAAI,CAAChB,SAAS,CAACjE,CAAC,CAAC,CAACkF,IAAI,CAAC,MAAM;MAC5BpI,CAAC,CAACK,SAAS,IAAIL,CAAC,CAACK,SAAS,CAAC,GAAG,EAAEP,iBAAiB,CAAC,GAAG,CAAC,CAAC;MACvDE,CAAC,CAACoC,IAAI,CAAC,CAAC;IACT,CAAC,CAAC;EACH,CAAC;EACDiG,UAAU,EAAE,SAAAA,CAAA,EAAY;IACvB,IAAIrI,CAAC,GAAG,IAAI;IACZ,IAAIA,CAAC,CAACO,OAAO,EAAE;MACd,IAAIP,CAAC,CAACO,OAAO,CAAC+H,iBAAiB,EAAE;QAChCtI,CAAC,CAACO,OAAO,CAAC+H,iBAAiB,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAItI,CAAC,CAACO,OAAO,CAACgI,mBAAmB,EAAE;QACzCvI,CAAC,CAACO,OAAO,CAACgI,mBAAmB,CAAC,CAAC;MAChC,CAAC,MAAM,IAAIvI,CAAC,CAACO,OAAO,CAACiI,oBAAoB,EAAE;QAC1CxI,CAAC,CAACO,OAAO,CAACiI,oBAAoB,CAAC,CAAC;MACjC,CAAC,MAAM,IAAIxI,CAAC,CAACO,OAAO,CAACkI,uBAAuB,EAAE;QAC7CzI,CAAC,CAACO,OAAO,CAACkI,uBAAuB,CAAC,CAAC;MACpC;IACD;EACD,CAAC;EACDC,cAAc,EAAE,SAAAA,CAAA,EAAY;IAC3B,IAAI,IAAI,CAACnI,OAAO,EAAE;MACjB,IAAI,IAAI,CAACA,OAAO,CAACoI,cAAc,EAAE;QAChC,IAAI,CAACpI,OAAO,CAACoI,cAAc,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAInI,QAAQ,CAACoI,gBAAgB,EAAE;QACrC,IAAI,CAACrI,OAAO,CAACqI,gBAAgB,CAAC,CAAC;MAChC,CAAC,MAAM,IAAIpI,QAAQ,CAACqI,mBAAmB,EAAE;QACxC,IAAI,CAACtI,OAAO,CAACsI,mBAAmB,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIrI,QAAQ,CAACsI,sBAAsB,EAAE;QAC3C,IAAI,CAACvI,OAAO,CAACuI,sBAAsB,CAAC,CAAC;MACtC;IACD;EACD;AACD,CAAC;AACD,eAAe/I,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}