{"ast": null, "code": "import { getWaterLevelWarning } from '@/api/bigScreen.js';\nexport default {\n  name: 'EquipmentTrend',\n  data() {\n    return {\n      equipmentTrend: null,\n      dataX: [],\n      data: [],\n      data2: []\n    };\n  },\n  beforeDestroy() {\n    if (this.equipmentTrend) {\n      this.equipmentTrend.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.equipmentTrend != undefined && this.equipmentTrend != null && this.equipmentTrend != '') {\n          this.equipmentTrend.dispose();\n        }\n        ;\n        this.equipmentTrend = this.$eCharts.init(this.$refs.equipmentTrend);\n        this.equipmentTrend.clear();\n        getWaterLevelWarning().then(res => {\n          let dataList = res.data.data;\n          for (var i of dataList) {\n            // let time = i.record_tm.split('T')[0];\n            let v = i.down_name;\n            this.data.push({\n              name: v,\n              value: i.down_value * 1,\n              ...i\n            });\n            this.data2.push(i.up_value);\n            this.dataX.push(v);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        title: {\n          show: true,\n          text: \"管网液位预警\",\n          textStyle: {\n            fontSize: 25,\n            color: \"rgba(207, 249, 247,1)\",\n            //设置文字颜色\n            boxShadow: \"0 0 8px rgba(4, 222, 223,1)\" //阴影阴影 宽度和位置 0为不阴影 可以使用CSS\n          }\n        },\n        grid: {\n          top: \"22%\",\n          right: '10%',\n          left: '12%',\n          bottom: '35%'\n        },\n        dataZoom: [{\n          type: 'inside',\n          start: 0,\n          end: 50\n        }, {}],\n        tooltip: {\n          // show: false,\n          trigger: 'axis',\n          formatter: function (params) {\n            let level_status = ['没报警', '液位差', '液位需校核', '水位正常'];\n            let deal_status = ['正常', '正在处理', '已处理', '超时处理'];\n            return params[0].marker + params[0].name + '<br>' + '液位状态 : ' + level_status[params[0].data.level_status * 1] + '<br>' + '处理状态 : ' + deal_status[params[0].data.deal_status * 1] + '<br>' + '上游名称 : ' + params[0].data.up_name + '<br>' + '上游液位值 : ' + params[0].data.up_value + '<br>' + '上游采集时间 : ' + params[0].data.up_tm.split('T')[0] + '<br>' + '下游名称 : ' + params[0].data.down_name + '<br>' + '下游液位值 : ' + params[0].data.down_value + '<br>' + '下游采集时间 : ' + params[0].data.down_tm.split('T')[0];\n          },\n          backgroundColor: \"rgba(13, 63, 103, .8)\",\n          borderWidth: \"1\",\n          //边框宽度设置1\n          borderColor: \"rgba(15, 124, 164, 1)\",\n          //设置边框颜色\n          textStyle: {\n            fontSize: 21,\n            color: \"#fff\"\n          }\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: this.dataX,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(72, 109, 114,.7)',\n              // 颜色\n              width: 2 // 粗细\n            }\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 20,\n            margin: 20\n          }\n        },\n        yAxis: {\n          type: 'value',\n          interval: 5,\n          margin: 20,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: false,\n            lineStyle: {\n              color: \"rgba(142, 238, 255, .15)\"\n            }\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 20\n          }\n        },\n        series: [{\n          type: 'line',\n          smooth: true,\n          symbol: 'circle',\n          symbolSize: [10, 10],\n          showSymbol: false,\n          sampling: 'lttb',\n          // stack: \"总量\",\n\n          itemStyle: {\n            color: new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: 'rgba(29, 214, 130, 1)'\n            }, {\n              offset: 1,\n              color: 'rgba(0, 252, 255, 1)'\n            }])\n            // color: \"rgba(33, 180, 144, 1)\",\n          },\n          areaStyle: {\n            color: new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: 'rgba(2, 39, 55, .3)'\n            }, {\n              offset: 1,\n              color: 'rgba(0, 255, 222, 0.1)'\n            }])\n          },\n          data: this.data\n        }, {\n          type: 'line',\n          smooth: true,\n          symbol: 'circle',\n          symbolSize: [10, 10],\n          showSymbol: false,\n          sampling: 'lttb',\n          // stack: \"总量\",\n\n          lineStyle: {\n            color: '#009FEA'\n          },\n          areaStyle: {\n            color: new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: '#00F4FD'\n            }, {\n              offset: 1,\n              color: \"rgba(0,0, 0, .2)\"\n            }])\n          },\n          data: this.data2\n        }]\n      };\n      if (option && typeof option == 'object') {\n        this.equipmentTrend.setOption(option);\n      }\n      window.onresize = this.equipmentTrend.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["getWaterLevelWarning", "name", "data", "equipmentTrend", "dataX", "data2", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "$eCharts", "init", "$refs", "res", "dataList", "i", "v", "down_name", "push", "value", "down_value", "up_value", "setOption", "option", "title", "show", "text", "textStyle", "fontSize", "color", "boxShadow", "grid", "top", "right", "left", "bottom", "dataZoom", "type", "start", "end", "tooltip", "trigger", "formatter", "params", "level_status", "deal_status", "marker", "up_name", "up_tm", "split", "down_tm", "backgroundColor", "borderWidth", "borderColor", "xAxis", "boundaryGap", "axisTick", "axisLine", "lineStyle", "width", "axisLabel", "margin", "yAxis", "interval", "splitLine", "series", "smooth", "symbol", "symbolSize", "showSymbol", "sampling", "itemStyle", "graphic", "LinearGradient", "offset", "areaStyle", "window", "onresize", "resize"], "sources": ["src/components/smartWater/EquipmentTrend.vue"], "sourcesContent": ["<template>\r\n    <div ref=\"equipmentTrend\" id=\"equipmentTrend\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {getWaterLevelWarning} from '@/api/bigScreen.js'\r\n    export default {\r\n        name: 'EquipmentTrend',\r\n\r\n        data() {\r\n            return {\r\n                equipmentTrend: null,\r\n                dataX: [],\r\n                data: [],\r\n                data2:[]\r\n            };\r\n        },\r\n        beforeDestroy() {\r\n        if (this.equipmentTrend) {\r\n            this.equipmentTrend.clear()\r\n        }\r\n    },\r\n        mounted() {\r\n            this.$nextTick(() => {\r\n                this.myChart()\r\n            })\r\n        },\r\n\r\n        methods: {\r\n            myChart() {\r\n                new Promise((resolve) => {\r\n                    resolve()\r\n                }).then(() => {\r\n                    if (this.equipmentTrend != undefined && this.equipmentTrend != null && this.equipmentTrend != '') {\r\n                        this.equipmentTrend.dispose()\r\n                    };\r\n                    this.equipmentTrend = this.$eCharts.init(this.$refs.equipmentTrend);\r\n                    this.equipmentTrend.clear();\r\n                    \r\n                    getWaterLevelWarning().then(res=>{\r\n                        let dataList = res.data.data;\r\n                        for(var i of dataList){\r\n                            // let time = i.record_tm.split('T')[0];\r\n                            let v = i.down_name\r\n                            this.data.push({\r\n                                name:v, \r\n                                value:i.down_value*1,\r\n                                ...i\r\n                            })\r\n                            this.data2.push(i.up_value)\r\n                            \r\n                            this.dataX.push(v)\r\n                        }\r\n                        this.setOption()\r\n                    })\r\n\r\n                })\r\n            },\r\n            setOption() {\r\n\r\n                let option = {\r\n                    title: {\r\n                        show: true,\r\n                        text: \"管网液位预警\",\r\n                        textStyle: {\r\n                            fontSize: 25,\r\n                            color: \"rgba(207, 249, 247,1)\", //设置文字颜色\r\n                            boxShadow: \"0 0 8px rgba(4, 222, 223,1)\" //阴影阴影 宽度和位置 0为不阴影 可以使用CSS\r\n\r\n                        }\r\n                    },\r\n                    grid: {\r\n                        top: \"22%\",\r\n                        right: '10%',\r\n                        left: '12%',\r\n                        bottom: '35%',\r\n\r\n                    },\r\n                    dataZoom: [\r\n                        {\r\n                            type: 'inside',\r\n                            start: 0,\r\n                            end: 50\r\n                        },{}\r\n                    ],\r\n                    tooltip: {\r\n                        // show: false,\r\n                        trigger: 'axis',\r\n                        formatter: function (params) {\r\n                            let level_status = ['没报警','液位差','液位需校核','水位正常'];\r\n                            let deal_status = ['正常','正在处理','已处理','超时处理']\r\n                            return params[0].marker + params[0].name+'<br>'+'液位状态 : '+level_status[params[0].data.level_status*1]+'<br>'+'处理状态 : '+deal_status[params[0].data.deal_status*1]+'<br>'+'上游名称 : '+params[0].data.up_name + '<br>' + '上游液位值 : ' + params[0].data.up_value+'<br>'+'上游采集时间 : '+params[0].data.up_tm.split('T')[0]+'<br>'+'下游名称 : '+params[0].data.down_name+'<br>'+'下游液位值 : '+params[0].data.down_value+'<br>'+'下游采集时间 : '+params[0].data.down_tm.split('T')[0];\r\n\r\n                        },\r\n                        backgroundColor: \"rgba(13, 63, 103, .8)\",\r\n                        borderWidth: \"1\", //边框宽度设置1\r\n                        borderColor: \"rgba(15, 124, 164, 1)\", //设置边框颜色\r\n                        textStyle: {\r\n                            fontSize: 21,\r\n                            color: \"#fff\"\r\n\r\n                        },\r\n                    },\r\n\r\n\r\n                    xAxis: {\r\n                        type: 'category',\r\n                        boundaryGap: false,\r\n                        data: this.dataX,\r\n                        axisTick: {\r\n                            show: false,\r\n                        },\r\n                        axisLine: {\r\n                            show: true,\r\n                            lineStyle: {\r\n                                color: 'rgba(72, 109, 114,.7)', // 颜色\r\n                                width: 2// 粗细\r\n                            }\r\n                        },\r\n                        axisLabel: {\r\n                            color: \"#fff\",\r\n                            fontSize: 20,\r\n                            margin: 20,\r\n\r\n                        },\r\n\r\n                    },\r\n                    yAxis: {\r\n                        type: 'value',\r\n                        interval: 5,\r\n                        margin: 20,\r\n                        axisTick: {\r\n                            show: false,\r\n                        },\r\n                        axisLine: {\r\n                            show: false,\r\n                        },\r\n                        splitLine: {\r\n                            show: false,\r\n                            lineStyle: {\r\n                                color: \"rgba(142, 238, 255, .15)\"\r\n                            }\r\n                        },\r\n                        axisLabel: {\r\n                            color: \"#fff\",\r\n                            fontSize: 20,\r\n                        },\r\n\r\n                    },\r\n\r\n                    series: [\r\n\r\n                        {\r\n                            type: 'line',\r\n                            smooth: true,\r\n                            symbol: 'circle',\r\n                            symbolSize: [10, 10],\r\n                            showSymbol: false,\r\n                            sampling: 'lttb',\r\n                            // stack: \"总量\",\r\n\r\n                            itemStyle: {\r\n                                color: new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: 'rgba(29, 214, 130, 1)'\r\n                                    },\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: 'rgba(0, 252, 255, 1)'\r\n                                    },\r\n                                ])\r\n                                // color: \"rgba(33, 180, 144, 1)\",\r\n                            },\r\n                            areaStyle: {\r\n\r\n                                color: new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: 'rgba(2, 39, 55, .3)'\r\n                                    },\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: 'rgba(0, 255, 222, 0.1)'\r\n                                    }\r\n                                ])\r\n                            },\r\n                            data: this.data,\r\n                        }, \r\n                        {\r\n                            type: 'line',\r\n                            smooth: true,\r\n                            symbol: 'circle',\r\n                            symbolSize: [10, 10],\r\n                            showSymbol: false,\r\n                            sampling: 'lttb',\r\n                            // stack: \"总量\",\r\n\r\n                            lineStyle: {\r\n                                color: '#009FEA'\r\n                            },\r\n                            areaStyle: {\r\n\r\n                                color:new this.$eCharts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: '#00F4FD',\r\n                                    },\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: \"rgba(0,0, 0, .2)\",\r\n                                    }\r\n                                ])\r\n                            },\r\n                            data: this.data2,\r\n                        }]\r\n                }\r\n\r\n                if (option && typeof option == 'object') {\r\n                    this.equipmentTrend.setOption(option)\r\n                }\r\n\r\n                window.onresize = this.equipmentTrend.resize;\r\n            }\r\n        },\r\n    };\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n    #equipmentTrend {\r\n        height: 22rem;\r\n        width: 100%;\r\n    }\r\n</style>"], "mappings": "AAOA,SAAAA,oBAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,cAAA;MACAC,KAAA;MACAF,IAAA;MACAG,KAAA;IACA;EACA;EACAC,cAAA;IACA,SAAAH,cAAA;MACA,KAAAA,cAAA,CAAAI,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IACA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAX,cAAA,IAAAY,SAAA,SAAAZ,cAAA,iBAAAA,cAAA;UACA,KAAAA,cAAA,CAAAa,OAAA;QACA;QAAA;QACA,KAAAb,cAAA,QAAAc,QAAA,CAAAC,IAAA,MAAAC,KAAA,CAAAhB,cAAA;QACA,KAAAA,cAAA,CAAAI,KAAA;QAEAP,oBAAA,GAAAc,IAAA,CAAAM,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAlB,IAAA,CAAAA,IAAA;UACA,SAAAoB,CAAA,IAAAD,QAAA;YACA;YACA,IAAAE,CAAA,GAAAD,CAAA,CAAAE,SAAA;YACA,KAAAtB,IAAA,CAAAuB,IAAA;cACAxB,IAAA,EAAAsB,CAAA;cACAG,KAAA,EAAAJ,CAAA,CAAAK,UAAA;cACA,GAAAL;YACA;YACA,KAAAjB,KAAA,CAAAoB,IAAA,CAAAH,CAAA,CAAAM,QAAA;YAEA,KAAAxB,KAAA,CAAAqB,IAAA,CAAAF,CAAA;UACA;UACA,KAAAM,SAAA;QACA;MAEA;IACA;IACAA,UAAA;MAEA,IAAAC,MAAA;QACAC,KAAA;UACAC,IAAA;UACAC,IAAA;UACAC,SAAA;YACAC,QAAA;YACAC,KAAA;YAAA;YACAC,SAAA;UAEA;QACA;QACAC,IAAA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,MAAA;QAEA;QACAC,QAAA,GACA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA,MACA;QACAC,OAAA;UACA;UACAC,OAAA;UACAC,SAAA,WAAAA,CAAAC,MAAA;YACA,IAAAC,YAAA;YACA,IAAAC,WAAA;YACA,OAAAF,MAAA,IAAAG,MAAA,GAAAH,MAAA,IAAAjD,IAAA,wBAAAkD,YAAA,CAAAD,MAAA,IAAAhD,IAAA,CAAAiD,YAAA,6BAAAC,WAAA,CAAAF,MAAA,IAAAhD,IAAA,CAAAkD,WAAA,6BAAAF,MAAA,IAAAhD,IAAA,CAAAoD,OAAA,yBAAAJ,MAAA,IAAAhD,IAAA,CAAA0B,QAAA,0BAAAsB,MAAA,IAAAhD,IAAA,CAAAqD,KAAA,CAAAC,KAAA,gCAAAN,MAAA,IAAAhD,IAAA,CAAAsB,SAAA,yBAAA0B,MAAA,IAAAhD,IAAA,CAAAyB,UAAA,0BAAAuB,MAAA,IAAAhD,IAAA,CAAAuD,OAAA,CAAAD,KAAA;UAEA;UACAE,eAAA;UACAC,WAAA;UAAA;UACAC,WAAA;UAAA;UACA1B,SAAA;YACAC,QAAA;YACAC,KAAA;UAEA;QACA;QAGAyB,KAAA;UACAjB,IAAA;UACAkB,WAAA;UACA5D,IAAA,OAAAE,KAAA;UACA2D,QAAA;YACA/B,IAAA;UACA;UACAgC,QAAA;YACAhC,IAAA;YACAiC,SAAA;cACA7B,KAAA;cAAA;cACA8B,KAAA;YACA;UACA;UACAC,SAAA;YACA/B,KAAA;YACAD,QAAA;YACAiC,MAAA;UAEA;QAEA;QACAC,KAAA;UACAzB,IAAA;UACA0B,QAAA;UACAF,MAAA;UACAL,QAAA;YACA/B,IAAA;UACA;UACAgC,QAAA;YACAhC,IAAA;UACA;UACAuC,SAAA;YACAvC,IAAA;YACAiC,SAAA;cACA7B,KAAA;YACA;UACA;UACA+B,SAAA;YACA/B,KAAA;YACAD,QAAA;UACA;QAEA;QAEAqC,MAAA,GAEA;UACA5B,IAAA;UACA6B,MAAA;UACAC,MAAA;UACAC,UAAA;UACAC,UAAA;UACAC,QAAA;UACA;;UAEAC,SAAA;YACA1C,KAAA,WAAAnB,QAAA,CAAA8D,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACA7C,KAAA;YACA,GACA;cACA6C,MAAA;cACA7C,KAAA;YACA,EACA;YACA;UACA;UACA8C,SAAA;YAEA9C,KAAA,WAAAnB,QAAA,CAAA8D,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACA7C,KAAA;YACA,GACA;cACA6C,MAAA;cACA7C,KAAA;YACA,EACA;UACA;UACAlC,IAAA,OAAAA;QACA,GACA;UACA0C,IAAA;UACA6B,MAAA;UACAC,MAAA;UACAC,UAAA;UACAC,UAAA;UACAC,QAAA;UACA;;UAEAZ,SAAA;YACA7B,KAAA;UACA;UACA8C,SAAA;YAEA9C,KAAA,WAAAnB,QAAA,CAAA8D,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACA7C,KAAA;YACA,GACA;cACA6C,MAAA;cACA7C,KAAA;YACA,EACA;UACA;UACAlC,IAAA,OAAAG;QACA;MACA;MAEA,IAAAyB,MAAA,WAAAA,MAAA;QACA,KAAA3B,cAAA,CAAA0B,SAAA,CAAAC,MAAA;MACA;MAEAqD,MAAA,CAAAC,QAAA,QAAAjF,cAAA,CAAAkF,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}