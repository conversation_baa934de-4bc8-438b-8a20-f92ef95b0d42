{"ast": null, "code": "//水务感知\nimport waterAwarenessLeft from \"./components/water-awareness/left-view.vue\";\nimport waterAwarenessRight from \"./components/water-awareness/right-view.vue\";\n//水务决策\nimport waterDecisionLeft from \"./components/water-decision/left-view.vue\";\nimport waterDecisionRight from \"./components/water-decision/right-view.vue\";\nimport { mapState } from 'vuex';\nexport default {\n  name: 'smartWater',\n  data() {\n    return {\n      leftPanel: true,\n      rightPanel: false,\n      rightText: '预警统计',\n      leftView: waterAwarenessLeft,\n      rightView: waterAwarenessRight,\n      width: '383px'\n    };\n  },\n  components: {},\n  watch: {\n    // 监听tabs切换组件\n    footerTbsItem: {\n      handler(nv) {\n        // this.close();\n        // this.deleteAllPoint();\n        this.leftPanel = true;\n        this.rightPanel = false;\n        switch (nv.item) {\n          case \"智慧水务\":\n            this.leftView = waterAwarenessLeft;\n            this.rightView = waterAwarenessRight;\n            this.rightText = \"预警统计\";\n            this.width = '383px';\n            break;\n          case \"水务感知\":\n            this.leftView = waterAwarenessLeft;\n            this.rightView = waterAwarenessRight;\n            this.rightText = \"预警统计\";\n            this.width = '383px';\n            break;\n          case \"水务决策\":\n            this.leftView = waterDecisionLeft;\n            this.rightView = null;\n            this.rightText = \"等级分类\";\n            this.width = '750px';\n            break;\n        }\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState({\n      footerTbsItem: state => state.footerTbsItem\n    })\n  },\n  mounted() {},\n  updated() {},\n  methods: {\n    close() {\n      // this.clearPoint(this.arrName)\n      this.$store.commit('warningType/getCurrentIndex', -1);\n      this.$store.commit('warningType/getIsShowAccumulationArr', false);\n      this.$store.commit('action/getIsScreenShow', true);\n      this.$store.commit('warningType/getAccumulationData', {\n        name: '事件列表',\n        data: ''\n      });\n      // this.$store.commit('warningType/getIsShowAccumulationArr', false)\n    },\n    deleteAllPoint() {\n      let params = {\n        name: \"\",\n        flag: false\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    isShowView(type) {\n      if (type == 1) {\n        this.leftPanel = !this.leftPanel;\n      } else {\n        this.rightPanel = !this.rightPanel;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["waterAwarenessLeft", "waterAwarenessRight", "waterDecisionLeft", "waterDecisionRight", "mapState", "name", "data", "leftPanel", "rightPanel", "rightText", "leftView", "<PERSON><PERSON><PERSON><PERSON>", "width", "components", "watch", "footerTbsItem", "handler", "nv", "item", "immediate", "computed", "state", "mounted", "updated", "methods", "close", "$store", "commit", "deleteAllPoint", "params", "flag", "$eventBus", "$emit", "isShowView", "type"], "sources": ["src/views/smartWater/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <div class=\"left-box\" :style=\"{ left: leftPanel ? '20px' : '-750px' }\">\r\n            <component :is=\"leftView\"></component>\r\n            <img v-if=\"leftPanel\" @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/left-btn.png\"\r\n                alt=\"\">\r\n            <img v-else @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n        </div>\r\n        <div class=\"right-box\" v-if=\"rightView\" :style=\"{\r\n            right: rightPanel ? '20px' : '-' + width,\r\n            width: width,\r\n            background: width == '383px' ? 'none' : 'rgba(18, 76, 111, .7)'\r\n        }\">\r\n            <component :is=\"rightView\"></component>\r\n            <p v-if=\"rightPanel\" @click=\"isShowView(2)\" :style=\"{ right: width }\">\r\n                <!-- <span>{{ rightText }}</span> -->\r\n                <img src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n            </p>\r\n            <p v-else @click=\"isShowView(2)\" :style=\"{ right: width }\">\r\n                <span>{{ rightText }}</span>\r\n                <img src=\"@/assets/images/comprehensiveSituation/long-left-btn.png\" alt=\"\">\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n//水务感知\r\nimport waterAwarenessLeft from \"./components/water-awareness/left-view.vue\"\r\nimport waterAwarenessRight from \"./components/water-awareness/right-view.vue\"\r\n//水务决策\r\nimport waterDecisionLeft from \"./components/water-decision/left-view.vue\"\r\nimport waterDecisionRight from \"./components/water-decision/right-view.vue\"\r\n\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'smartWater',\r\n    data() {\r\n        return {\r\n            leftPanel: true,\r\n            rightPanel: false,\r\n            rightText: '预警统计',\r\n            leftView: waterAwarenessLeft,\r\n            rightView: waterAwarenessRight,\r\n            width: '383px'\r\n        };\r\n    },\r\n\r\n    components: {\r\n    },\r\n    watch: {\r\n        // 监听tabs切换组件\r\n        footerTbsItem: {\r\n            handler(nv) {\r\n                // this.close();\r\n                // this.deleteAllPoint();\r\n                this.leftPanel = true;\r\n                this.rightPanel = false;\r\n                switch (nv.item) {\r\n                    case \"智慧水务\":\r\n                        this.leftView = waterAwarenessLeft;\r\n                        this.rightView = waterAwarenessRight;\r\n                        this.rightText = \"预警统计\";\r\n                        this.width = '383px'\r\n                        break;\r\n                    case \"水务感知\":\r\n                        this.leftView = waterAwarenessLeft;\r\n                        this.rightView = waterAwarenessRight;\r\n                        this.rightText = \"预警统计\";\r\n                        this.width = '383px'\r\n                        break;\r\n                    case \"水务决策\":\r\n                        this.leftView = waterDecisionLeft;\r\n                        this.rightView = null;\r\n                        this.rightText = \"等级分类\";\r\n                        this.width = '750px'\r\n                        break;\r\n                }\r\n            },\r\n            immediate: true\r\n\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            footerTbsItem: state => state.footerTbsItem,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        close() {\r\n            // this.clearPoint(this.arrName)\r\n            this.$store.commit('warningType/getCurrentIndex', -1)\r\n            this.$store.commit('warningType/getIsShowAccumulationArr', false)\r\n            this.$store.commit('action/getIsScreenShow', true)\r\n            this.$store.commit('warningType/getAccumulationData', { name: '事件列表', data: '' })\r\n            // this.$store.commit('warningType/getIsShowAccumulationArr', false)\r\n        },\r\n        deleteAllPoint() {\r\n            let params = {\r\n                name: \"\",\r\n                flag: false\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        isShowView(type) {\r\n            if (type == 1) {\r\n                this.leftPanel = !this.leftPanel;\r\n            } else {\r\n                this.rightPanel = !this.rightPanel;\r\n\r\n            }\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.container {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    .left-box {\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        width: 750px;\r\n        height: 1960px;\r\n        position: fixed;\r\n        background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        left: 20px;\r\n\r\n        >img {\r\n            cursor: pointer;\r\n            width: 103px;\r\n            height: 93px;\r\n            position: absolute;\r\n            left: 750px;\r\n            top: calc(50% - 46.5px);\r\n\r\n        }\r\n    }\r\n\r\n    .right-box {\r\n        // width: 750px;\r\n        height: 1960px;\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        position: fixed;\r\n        // background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        right: 20px;\r\n\r\n        >p {\r\n            width: 103px;\r\n            height: 338px;\r\n            position: absolute;\r\n            // right: 750px;\r\n            top: calc(50% - 169px);\r\n            cursor: pointer;\r\n\r\n            >span {\r\n                width: 40px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 48px;\r\n                color: #FFFFFF;\r\n                line-height: 56px;\r\n                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\r\n                position: absolute;\r\n                top: 96px;\r\n                left: 28px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AA4BA;AACA,OAAAA,kBAAA;AACA,OAAAC,mBAAA;AACA;AACA,OAAAC,iBAAA;AACA,OAAAC,kBAAA;AAEA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA,EAAAV,kBAAA;MACAW,SAAA,EAAAV,mBAAA;MACAW,KAAA;IACA;EACA;EAEAC,UAAA,GACA;EACAC,KAAA;IACA;IACAC,aAAA;MACAC,QAAAC,EAAA;QACA;QACA;QACA,KAAAV,SAAA;QACA,KAAAC,UAAA;QACA,QAAAS,EAAA,CAAAC,IAAA;UACA;YACA,KAAAR,QAAA,GAAAV,kBAAA;YACA,KAAAW,SAAA,GAAAV,mBAAA;YACA,KAAAQ,SAAA;YACA,KAAAG,KAAA;YACA;UACA;YACA,KAAAF,QAAA,GAAAV,kBAAA;YACA,KAAAW,SAAA,GAAAV,mBAAA;YACA,KAAAQ,SAAA;YACA,KAAAG,KAAA;YACA;UACA;YACA,KAAAF,QAAA,GAAAR,iBAAA;YACA,KAAAS,SAAA;YACA,KAAAF,SAAA;YACA,KAAAG,KAAA;YACA;QACA;MACA;MACAO,SAAA;IAEA;EACA;EACAC,QAAA;IACA,GAAAhB,QAAA;MACAW,aAAA,EAAAM,KAAA,IAAAA,KAAA,CAAAN;IACA;EACA;EAEAO,QAAA,GACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,MAAA;MACA;MACA,KAAAC,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;QAAAtB,IAAA;QAAAC,IAAA;MAAA;MACA;IACA;IACAsB,eAAA;MACA,IAAAC,MAAA;QACAxB,IAAA;QACAyB,IAAA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAH,MAAA;IACA;IACAI,WAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA3B,SAAA,SAAAA,SAAA;MACA;QACA,KAAAC,UAAA,SAAAA,UAAA;MAEA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}