{"ast": null, "code": "import * as echarts from 'echarts';\nimport { AdminApprovalAfter } from '@/api/index.js';\nexport default {\n  name: 'DiffAge',\n  data() {\n    return {\n      diffAge: null,\n      xAxisData: [],\n      data: []\n    };\n  },\n  beforeDestroy() {\n    if (this.diffAge) {\n      this.diffAge.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      // 1. 实例化对象\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.diffAge != null && this.diffAge != \"\" && this.diffAge != undefined) {\n          this.diffAge.dispose();\n        }\n        this.diffAge = echarts.init(this.$refs.diffAge);\n        this.diffAge.clear();\n        AdminApprovalAfter().then(res => {\n          for (let v of res.data.extra) {\n            this.xAxisData.push(v.name);\n            this.data.push({\n              value: (v.value / 10000).toFixed(2),\n              name: v.name\n            });\n          }\n          this.$nextTick(() => {\n            this.setOption();\n          });\n        });\n      });\n    },\n    setOption() {\n      var data2 = this.data;\n      let option = {\n        grid: {\n          left: '0%',\n          right: '0%',\n          bottom: '0%',\n          top: '32%',\n          containLabel: true\n        },\n        tooltip: {\n          show: false,\n          //显示鼠标移动条\n          trigger: 'item'\n        },\n        title: [{\n          // text: \"各年龄阶段人数预览\",\n          text: '审批取号人群',\n          textStyle: {\n            color: '#fff',\n            fontWeight: 500,\n            fontSize: 23\n          }\n        }, {\n          subtext: \"单位: 万人\",\n          //副标题\n          // itemGap: 6, //主副标题间距\n          right: '0',\n          //标题的位置 默认是left，其余还有center、right属性\n          top: '-2%',\n          subtextStyle: {\n            color: \"#FFFFFF\",\n            fontSize: 18,\n            fontWeight: '600',\n            textAlign: 'center'\n          }\n        }],\n        xAxis: {\n          data: this.xAxisData,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          axisLabel: {\n            interval: 0,\n            color: '#eee',\n            fontSize: 19,\n            fontWeight: 700,\n            margin: 30,\n            rotate: 12\n          }\n        },\n        yAxis: {\n          interval: 10,\n          splitLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          axisLabel: {\n            color: '#fff',\n            fontSize: 20\n          }\n        },\n        series: [{\n          //三个最低下的圆片\n          name: '',\n          type: 'pictorialBar',\n          symbolSize: [30, 15],\n          symbolOffset: [0, 10],\n          z: 12,\n          itemStyle: {\n            opacity: 1,\n            color: 'rgba(0, 171, 255, 1)'\n          },\n          data: [0, 0, 0, 0]\n        }, {\n          name: '',\n          // 头部\n          type: 'pictorialBar',\n          symbolSize: [30, 10],\n          symbolOffset: [0, -7],\n          z: 12,\n          symbolPosition: 'end',\n          itemStyle: {\n            color: 'rgba(42, 118, 255, 1)'\n          },\n          data: data2\n        }, {\n          name: '2021',\n          type: 'bar',\n          barWidth: 30,\n          barGap: '-100%',\n          z: 0,\n          itemStyle: {\n            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{\n              offset: 0,\n              color: 'rgba(0, 171, 255, 1)'\n            },\n            // 0 起始颜色\n            {\n              offset: 1,\n              color: \"rgba(15, 212, 205, 1)\"\n            } // 1 结束颜色\n            ])\n          },\n          label: {\n            show: true,\n            position: 'top',\n            offset: [0, -5],\n            backgroundColor: \"rgba(15, 212, 205, .2)\",\n            color: '#fff',\n            fontWeight: 'bold',\n            fontSize: 20,\n            padding: [5, 2]\n          },\n          data: this.data\n        }]\n      };\n      if (option && typeof option === 'object') {\n        this.diffAge.setOption(option);\n      }\n      window.onresize = this.diffAge.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "AdminApprovalAfter", "name", "data", "diffAge", "xAxisData", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "v", "extra", "push", "value", "toFixed", "setOption", "data2", "option", "grid", "left", "right", "bottom", "top", "containLabel", "tooltip", "show", "trigger", "title", "text", "textStyle", "color", "fontWeight", "fontSize", "subtext", "subtextStyle", "textAlign", "xAxis", "axisTick", "axisLine", "axisLabel", "interval", "margin", "rotate", "yAxis", "splitLine", "series", "type", "symbolSize", "symbolOffset", "z", "itemStyle", "opacity", "symbolPosition", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "graphic", "LinearGradient", "offset", "label", "position", "backgroundColor", "padding", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/DiffAge.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"diffAge\" ref=\"diffAge\">\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { AdminApprovalAfter } from '@/api/index.js'\r\nexport default {\r\n    name: 'DiffAge',\r\n\r\n    data() {\r\n        return {\r\n            diffAge: null,\r\n            xAxisData: [],\r\n            data: []\r\n        };\r\n    },\r\n    beforeDestroy(){\r\n        if(this.diffAge){\r\n            this.diffAge.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.myChart()\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            // 1. 实例化对象\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.diffAge != null && this.diffAge != \"\" && this.diffAge != undefined) {\r\n                    this.diffAge.dispose();\r\n                }\r\n                this.diffAge = echarts.init(this.$refs.diffAge);\r\n                this.diffAge.clear();\r\n                AdminApprovalAfter().then(res => {\r\n                    for (let v of res.data.extra) {\r\n                        this.xAxisData.push(v.name);\r\n                        this.data.push({\r\n                            value: (v.value / 10000).toFixed(2),\r\n                            name: v.name\r\n                        });\r\n                    }\r\n\r\n                    this.$nextTick(() => {\r\n                        this.setOption()\r\n                    });\r\n\r\n                })\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            var data2 = this.data;\r\n            let option = {\r\n                grid: {\r\n                    left: '0%',\r\n                    right: '0%',\r\n                    bottom: '0%',\r\n                    top: '32%',\r\n                    containLabel: true\r\n                },\r\n\r\n                tooltip: {\r\n                    show: false, //显示鼠标移动条\r\n                    trigger: 'item'\r\n                },\r\n                title: [{\r\n                    // text: \"各年龄阶段人数预览\",\r\n                    text: '审批取号人群',\r\n                    textStyle: {\r\n                        color: '#fff',\r\n                        fontWeight: 500,\r\n                        fontSize: 23,\r\n                    },\r\n                },\r\n                {\r\n                    subtext: \"单位: 万人\", //副标题\r\n                    // itemGap: 6, //主副标题间距\r\n                    right: '0', //标题的位置 默认是left，其余还有center、right属性\r\n                    top: '-2%',\r\n                    subtextStyle: {\r\n                        color: \"#FFFFFF\",\r\n                        fontSize: 18,\r\n                        fontWeight: '600',\r\n                        textAlign: 'center'\r\n                    }\r\n                }\r\n                ],\r\n\r\n                xAxis: {\r\n\r\n                    data: this.xAxisData,\r\n                    axisTick: {\r\n                        show: false\r\n                    },\r\n                    axisLine: {\r\n                        show: false\r\n                    },\r\n                    axisLabel: {\r\n                        interval: 0,\r\n                        color: '#eee',\r\n                        fontSize: 19,\r\n                        fontWeight: 700,\r\n                        margin: 30,\r\n                        rotate: 12,\r\n                    }\r\n                },\r\n                yAxis: {\r\n                    interval: 10,\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                    axisTick: {\r\n                        show: false,\r\n\r\n                    },\r\n                    axisLine: {\r\n                        show: false\r\n                    },\r\n                    axisLabel: {\r\n                        color: '#fff',\r\n                        fontSize: 20\r\n                    }\r\n                },\r\n                series: [\r\n\r\n                    {\r\n                        //三个最低下的圆片\r\n                        name: '',\r\n                        type: 'pictorialBar',\r\n                        symbolSize: [30, 15],\r\n                        symbolOffset: [0, 10],\r\n                        z: 12,\r\n                        itemStyle: {\r\n                            opacity: 1,\r\n                            color: 'rgba(0, 171, 255, 1)'\r\n                        },\r\n                        data: [0, 0, 0, 0]\r\n                    },\r\n\r\n\r\n                    {\r\n                        name: '', // 头部\r\n                        type: 'pictorialBar',\r\n                        symbolSize: [30, 10],\r\n                        symbolOffset: [0, -7],\r\n                        z: 12,\r\n                        symbolPosition: 'end',\r\n                        itemStyle: {\r\n                            color: 'rgba(42, 118, 255, 1)',\r\n                        },\r\n                        data: data2\r\n                    },\r\n\r\n                    {\r\n                        name: '2021',\r\n                        type: 'bar',\r\n                        barWidth: 30,\r\n                        barGap: '-100%',\r\n                        z: 0,\r\n\r\n                        itemStyle: {\r\n                            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [\r\n                                { offset: 0, color: 'rgba(0, 171, 255, 1)' }, // 0 起始颜色\r\n                                { offset: 1, color: \"rgba(15, 212, 205, 1)\" } // 1 结束颜色\r\n                            ]),\r\n                        },\r\n                        label: {\r\n                            show: true,\r\n                            position: 'top',\r\n                            offset: [0, -5],\r\n                            backgroundColor: \"rgba(15, 212, 205, .2)\",\r\n                            color: '#fff',\r\n                            fontWeight: 'bold',\r\n                            fontSize: 20,\r\n                            padding: [5, 2]\r\n                        },\r\n                        data: this.data\r\n                    },\r\n                ]\r\n            };\r\n\r\n\r\n            if (option && typeof option === 'object') {\r\n                this.diffAge.setOption(option);\r\n            }\r\n            window.onresize = this.diffAge.resize;\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.diffAge {\r\n    height: 300px;\r\n    width: 94%;\r\n}\r\n</style>"], "mappings": "AASA,YAAAA,OAAA;AACA,SAAAC,kBAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAF,IAAA;IACA;EACA;EACAG,cAAA;IACA,SAAAF,OAAA;MACA,KAAAA,OAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IAEA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAV,OAAA,iBAAAA,OAAA,eAAAA,OAAA,IAAAW,SAAA;UACA,KAAAX,OAAA,CAAAY,OAAA;QACA;QACA,KAAAZ,OAAA,GAAAJ,OAAA,CAAAiB,IAAA,MAAAC,KAAA,CAAAd,OAAA;QACA,KAAAA,OAAA,CAAAG,KAAA;QACAN,kBAAA,GAAAa,IAAA,CAAAK,GAAA;UACA,SAAAC,CAAA,IAAAD,GAAA,CAAAhB,IAAA,CAAAkB,KAAA;YACA,KAAAhB,SAAA,CAAAiB,IAAA,CAAAF,CAAA,CAAAlB,IAAA;YACA,KAAAC,IAAA,CAAAmB,IAAA;cACAC,KAAA,GAAAH,CAAA,CAAAG,KAAA,UAAAC,OAAA;cACAtB,IAAA,EAAAkB,CAAA,CAAAlB;YACA;UACA;UAEA,KAAAO,SAAA;YACA,KAAAgB,SAAA;UACA;QAEA;MAEA;IACA;IACAA,UAAA;MACA,IAAAC,KAAA,QAAAvB,IAAA;MACA,IAAAwB,MAAA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QAEAC,OAAA;UACAC,IAAA;UAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACA;UACAC,IAAA;UACAC,SAAA;YACAC,KAAA;YACAC,UAAA;YACAC,QAAA;UACA;QACA,GACA;UACAC,OAAA;UAAA;UACA;UACAb,KAAA;UAAA;UACAE,GAAA;UACAY,YAAA;YACAJ,KAAA;YACAE,QAAA;YACAD,UAAA;YACAI,SAAA;UACA;QACA,EACA;QAEAC,KAAA;UAEA3C,IAAA,OAAAE,SAAA;UACA0C,QAAA;YACAZ,IAAA;UACA;UACAa,QAAA;YACAb,IAAA;UACA;UACAc,SAAA;YACAC,QAAA;YACAV,KAAA;YACAE,QAAA;YACAD,UAAA;YACAU,MAAA;YACAC,MAAA;UACA;QACA;QACAC,KAAA;UACAH,QAAA;UACAI,SAAA;YACAnB,IAAA;UACA;UACAY,QAAA;YACAZ,IAAA;UAEA;UACAa,QAAA;YACAb,IAAA;UACA;UACAc,SAAA;YACAT,KAAA;YACAE,QAAA;UACA;QACA;QACAa,MAAA,GAEA;UACA;UACArD,IAAA;UACAsD,IAAA;UACAC,UAAA;UACAC,YAAA;UACAC,CAAA;UACAC,SAAA;YACAC,OAAA;YACArB,KAAA;UACA;UACArC,IAAA;QACA,GAGA;UACAD,IAAA;UAAA;UACAsD,IAAA;UACAC,UAAA;UACAC,YAAA;UACAC,CAAA;UACAG,cAAA;UACAF,SAAA;YACApB,KAAA;UACA;UACArC,IAAA,EAAAuB;QACA,GAEA;UACAxB,IAAA;UACAsD,IAAA;UACAO,QAAA;UACAC,MAAA;UACAL,CAAA;UAEAC,SAAA;YACApB,KAAA,MAAAxC,OAAA,CAAAiE,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAA3B,KAAA;YAAA;YAAA;YACA;cAAA2B,MAAA;cAAA3B,KAAA;YAAA;YAAA,CACA;UACA;UACA4B,KAAA;YACAjC,IAAA;YACAkC,QAAA;YACAF,MAAA;YACAG,eAAA;YACA9B,KAAA;YACAC,UAAA;YACAC,QAAA;YACA6B,OAAA;UACA;UACApE,IAAA,OAAAA;QACA;MAEA;MAGA,IAAAwB,MAAA,WAAAA,MAAA;QACA,KAAAvB,OAAA,CAAAqB,SAAA,CAAAE,MAAA;MACA;MACA6C,MAAA,CAAAC,QAAA,QAAArE,OAAA,CAAAsE,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}