/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import t from"../support/FeatureSet.js";import r from"../support/IdSet.js";import{defaultMaxRecords as s,FeatureServiceDatabaseType as i,IdState as a}from"../support/shared.js";import{toWhereClause as l}from"../support/sqlUtils.js";import{create as n,resolve as o,reject as u}from"../../../core/promiseUtils.js";import h from"../../../geometry/Geometry.js";import c from"../../../layers/FeatureLayer.js";import d from"../../../layers/support/FeatureType.js";import f from"../../../layers/support/Field.js";import p from"../../../rest/support/Query.js";class y extends t{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerMemory",this._removeGeometry=!1,this._overrideFields=null,this._forceIsTable=!1,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,!0===e.isTable&&(this._forceIsTable=!0),void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return s}end(){return this._layer}optimisePagingFeatureQueries(){}load(){return null===this._loadPromise&&(this._loadPromise=n(((e,t)=>{if(!0===this._layer.loaded)return this._initialiseFeatureSet(),void e(this);this._layer.when().then((()=>{try{this._initialiseFeatureSet(),e(this)}catch(r){t(r)}}),t),this._layer.load()}))),this._loadPromise}get gdbVersion(){return""}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields)if(1===this._layer.outFields.length&&"*"===this._layer.outFields[0]);else{const e=[];for(const t of this.fields)if("oid"===t.type)e.push(t);else for(const r of this._layer.outFields)if(r.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}else;if(null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const s of this._overrideFields)if(s.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this._databaseType=i.Standardised,this.typeIdField=this._layer.typeIdField,this.types=this._layer.types}isTable(){return this._forceIsTable||this._layer.isTable||"table"===this._layer.type||!this._layer.geometryType}_isInFeatureSet(){return a.InFeatureSet}_candidateIdTransform(e){return e}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._getFilteredSet("",null,null,null,e))).then((e=>(this._wset=e,e))):o(this._wset)}_changeFeature(t){const r={};for(const e of this.fields)r[e.name]=t.attributes[e.name];return new e({geometry:!0===this._removeGeometry?null:t.geometry,attributes:r})}_getFilteredSet(e,t,s,a,n){let u="",h=!1;if(null!==a&&(u=a.constructClause(),h=!0),this.isTable()&&t&&null!==e&&""!==e){const e=new r([],[],!0,null);return o(e)}const c=new p;return c.where=null===s?null===t?"1=1":"":l(s,i.Standardised),c.spatialRelationship=this._makeRelationshipEnum(e),c.outSpatialReference=this.spatialReference,c.orderByFields=""!==u?u.split(","):null,c.geometry=null===t?null:t,c.returnGeometry=!0,c.relationParameter=this._makeRelationshipParam(e),this._layer.queryFeatures(c).then((e=>{if(null===e)return new r([],[],h,null);this._checkCancelled(n);const t=[];e.features.forEach((e=>{const r=e.attributes[this._layer.objectIdField];t.push(r),this._featureCache[r]=this._changeFeature(e)}));return new r([],t,h,null)}))}_makeRelationshipEnum(e){if(e.indexOf("esriSpatialRelRelation")>=0)return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.indexOf("esriSpatialRelRelation")>=0?e.split(":")[1]:""}_queryAllFeatures(){if(this._wset)return o(this._wset);const e=new p;return e.where="1=1",this._ensureLoaded().then((()=>{if(this._layer.source&&this._layer.source.items){const e=[];return this._layer.source.items.forEach((t=>{const r=t.attributes[this._layer.objectIdField];e.push(r),this._featureCache[r]=this._changeFeature(t)})),this._wset=new r([],e,!1,null),this._wset}return this._layer.queryFeatures(e).then((e=>{const t=[];return e.features.forEach((e=>{const r=e.attributes[this._layer.objectIdField];t.push(r),this._featureCache[r]=this._changeFeature(e)})),this._wset=new r([],t,!1,null),this._wset}))}))}_getFeatures(e,t,r){const s=[];-1!==t&&void 0===this._featureCache[t]&&s.push(t);for(let i=e._lastFetchedIndex;i<e._known.length&&(e._lastFetchedIndex+=1,!(void 0===this._featureCache[e._known[i]]&&(e._known[i]!==t&&s.push(e._known[i]),s.length>r)));i++);return 0===s.length?o("success"):u(new Error("Unaccounted for Features. Not in Feature Collection"))}_refineSetBlock(e){return o(e)}_stat(){return o({calculated:!1})}_canDoAggregates(){return o(!1)}relationshipMetaData(){return[]}static _cloneAttr(e){const t={};for(const r in e)t[r]=e[r];return t}nativeCapabilities(){return{title:this._layer.title,canQueryRelated:!1,source:this,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:!0}}static create(e,t){let r=e.layerDefinition.objectIdField;const s=e.layerDefinition.typeIdField?e.layerDefinition.typeIdField:"",i=[];if(e.layerDefinition.types)for(const h of e.layerDefinition.types)i.push(d.fromJSON(h));let a=e.layerDefinition.geometryType;void 0===a&&(a=e.featureSet.geometryType||"");let l=e.featureSet.features;const n=t.toJSON();if(""===r||void 0===r){let t=!1;for(const s of e.layerDefinition.fields)if("oid"===s.type||"esriFieldTypeOID"===s.type){r=s.name,t=!0;break}if(!1===t){let t="FID",s=!0,i=0;for(;s;){let r=!0;for(const s of e.layerDefinition.fields)if(s.name===t){r=!1;break}!0===r?s=!1:(i++,t="FID"+i.toString())}e.layerDefinition.fields.push({type:"esriFieldTypeOID",name:t,alias:t});const a=[];for(let r=0;r<l.length;r++)a.push({geometry:e.featureSet.features[r].geometry,attributes:e.featureSet.features[r].attributes?this._cloneAttr(e.featureSet.features[r].attributes):{}}),a[r].attributes[t]=r;l=a,r=t}}const o=[];for(const h of e.layerDefinition.fields)h instanceof f?o.push(h):o.push(f.fromJSON(h));let u=a;switch(u){case"esriGeometryPoint":u="point";break;case"esriGeometryPolyline":u="polyline";break;case"esriGeometryPolygon":u="polygon";break;case"esriGeometryExtent":u="extent";break;case"esriGeometryMultipoint":u="multipoint"}for(const c of l)c.geometry&&c.geometry instanceof h==!1&&(c.geometry.type=u,void 0===c.geometry.spatialReference&&(c.geometry.spatialReference=n));const p={outFields:["*"],source:l,fields:o,types:i,typeIdField:s,objectIdField:r,spatialReference:t};p.geometryType=u||"point";const _=new c(p);return new y({layer:_,spatialReference:t,isTable:null===u||""===u})}queryAttachments(){return o([])}getFeatureByObjectId(e){const t=new p;return t.where=this.objectIdField+"="+e.toString(),this._layer.queryFeatures(t).then((e=>1===e.features.length?e.features[0]:null))}getOwningSystemUrl(){return o("")}getIdentityUser(){return o("")}}export{y as default};
