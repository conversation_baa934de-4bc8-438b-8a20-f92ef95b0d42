{"ast": null, "code": "// 指定元素全屏\nexport const openFullscreen = id => {\n  const ele = document.getElementById(id);\n  if (ele.RequestFullScreen) {\n    ele.RequestFullScreen();\n    // 兼容Firefox\n  } else if (ele.mozRequestFullScreen) {\n    ele.mozRequestFullScreen();\n    // 兼容Chrome, Safari and Opera等\n  } else if (ele.webkitRequestFullScreen) {\n    ele.webkitRequestFullScreen();\n    // 兼容IE/Edge\n  } else if (ele.msRequestFullscreen) {\n    ele.msRequestFullscreen();\n  }\n};\n// 退出全屏\nexport const exitFullscreen = () => {\n  if (document.exitFullScreen) {\n    document.exitFullScreen();\n    // 兼容Firefox\n  } else if (document.mozCancelFullScreen) {\n    document.mozCancelFullScreen();\n    // 兼容Chrome, Safari and Opera等\n  } else if (document.webkitExitFullscreen) {\n    document.webkitExitFullscreen();\n    // 兼容IE/Edge\n  }\n};\nexport const exitFullscreenItem = id => {\n  const ele = document.getElementById(id);\n  if (ele.exitFullScreen) {\n    ele.exitFullScreen();\n    // 兼容Firefox\n  } else if (ele.mozCancelFullScreen) {\n    ele.mozCancelFullScreen();\n    // 兼容Chrome, Safari and Opera等\n  } else if (ele.webkitExitFullscreen) {\n    ele.webkitExitFullscreen();\n    // 兼容IE/Edge\n  } else if (ele.msExitFullscreen) {\n    ele.msExitFullscreen();\n  }\n};\n// 判断是否是全屏状态\nexport const isFullscreen = () => {\n  return document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen || document.msFullscreenElement || false;\n};\n// 获取当前浏览器的缩放比\nexport const getWindowRatio = () => {\n  var ratio = 0;\n  var screen = window.screen;\n  var ua = navigator.userAgent.toLowerCase();\n  if (window.devicePixelRatio !== undefined) {\n    ratio = window.devicePixelRatio;\n  } else if (~ua.indexOf('msie')) {\n    if (screen.deviceXDPI && screen.logicalXDPI) {\n      ratio = screen.deviceXDPI / screen.logicalXDPI;\n    }\n  } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {\n    ratio = window.outerWidth / window.innerWidth;\n  }\n  if (ratio) {\n    ratio = Math.round(ratio * 100);\n  }\n  return ratio;\n};", "map": {"version": 3, "names": ["openFullscreen", "id", "ele", "document", "getElementById", "RequestFullScreen", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "exitFullscreen", "exitFullScreen", "mozCancelFullScreen", "webkitExitFullscreen", "exitFullscreenItem", "msExitFullscreen", "isFullscreen", "fullScreen", "mozFullScreen", "webkitIsFullScreen", "msFullscreenElement", "getWindowRatio", "ratio", "screen", "window", "ua", "navigator", "userAgent", "toLowerCase", "devicePixelRatio", "undefined", "indexOf", "deviceXDPI", "logicalXDPI", "outerWidth", "innerWidth", "Math", "round"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/fullScreen.js"], "sourcesContent": ["\n// 指定元素全屏\nexport const openFullscreen = (id) => {\n  const ele = document.getElementById(id)\n  if (ele.RequestFullScreen) {\n    ele.RequestFullScreen()\n    // 兼容Firefox\n  } else if (ele.mozRequestFullScreen) {\n    ele.mozRequestFullScreen()\n    // 兼容Chrome, Safari and Opera等\n  } else if (ele.webkitRequestFullScreen) {\n    ele.webkitRequestFullScreen()\n    // 兼容IE/Edge\n  } else if (ele.msRequestFullscreen) {\n    ele.msRequestFullscreen()\n  }\n}\n// 退出全屏\nexport const exitFullscreen = () => {\n  if (document.exitFullScreen) {\n    document.exitFullScreen()\n    // 兼容Firefox\n  } else if (document.mozCancelFullScreen) {\n    document.mozCancelFullScreen()\n    // 兼容Chrome, Safari and Opera等\n  } else if (document.webkitExitFullscreen) {\n    document.webkitExitFullscreen()\n    // 兼容IE/Edge\n  }\n}\nexport const exitFullscreenItem = (id) => {\n  const ele = document.getElementById(id)\n  if (ele.exitFullScreen) {\n    ele.exitFullScreen()\n    // 兼容Firefox\n  } else if (ele.mozCancelFullScreen) {\n    ele.mozCancelFullScreen()\n    // 兼容Chrome, Safari and Opera等\n  } else if (ele.webkitExitFullscreen) {\n    ele.webkitExitFullscreen()\n    // 兼容IE/Edge\n  } else if (ele.msExitFullscreen) {\n    ele.msExitFullscreen()\n  }\n}\n// 判断是否是全屏状态\nexport const isFullscreen = () => {\n  return document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen || document.msFullscreenElement || false\n}\n// 获取当前浏览器的缩放比\nexport const getWindowRatio = () => {\n  var ratio = 0\n  var screen = window.screen\n  var ua = navigator.userAgent.toLowerCase()\n  if (window.devicePixelRatio !== undefined) {\n    ratio = window.devicePixelRatio\n  } else if (~ua.indexOf('msie')) {\n    if (screen.deviceXDPI && screen.logicalXDPI) {\n      ratio = screen.deviceXDPI / screen.logicalXDPI\n    }\n  } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {\n    ratio = window.outerWidth / window.innerWidth\n  }\n\n  if (ratio) {\n    ratio = Math.round(ratio * 100)\n  }\n  return ratio\n}\n"], "mappings": "AACA;AACA,OAAO,MAAMA,cAAc,GAAIC,EAAE,IAAK;EACpC,MAAMC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAACH,EAAE,CAAC;EACvC,IAAIC,GAAG,CAACG,iBAAiB,EAAE;IACzBH,GAAG,CAACG,iBAAiB,CAAC,CAAC;IACvB;EACF,CAAC,MAAM,IAAIH,GAAG,CAACI,oBAAoB,EAAE;IACnCJ,GAAG,CAACI,oBAAoB,CAAC,CAAC;IAC1B;EACF,CAAC,MAAM,IAAIJ,GAAG,CAACK,uBAAuB,EAAE;IACtCL,GAAG,CAACK,uBAAuB,CAAC,CAAC;IAC7B;EACF,CAAC,MAAM,IAAIL,GAAG,CAACM,mBAAmB,EAAE;IAClCN,GAAG,CAACM,mBAAmB,CAAC,CAAC;EAC3B;AACF,CAAC;AACD;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAIN,QAAQ,CAACO,cAAc,EAAE;IAC3BP,QAAQ,CAACO,cAAc,CAAC,CAAC;IACzB;EACF,CAAC,MAAM,IAAIP,QAAQ,CAACQ,mBAAmB,EAAE;IACvCR,QAAQ,CAACQ,mBAAmB,CAAC,CAAC;IAC9B;EACF,CAAC,MAAM,IAAIR,QAAQ,CAACS,oBAAoB,EAAE;IACxCT,QAAQ,CAACS,oBAAoB,CAAC,CAAC;IAC/B;EACF;AACF,CAAC;AACD,OAAO,MAAMC,kBAAkB,GAAIZ,EAAE,IAAK;EACxC,MAAMC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAACH,EAAE,CAAC;EACvC,IAAIC,GAAG,CAACQ,cAAc,EAAE;IACtBR,GAAG,CAACQ,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,MAAM,IAAIR,GAAG,CAACS,mBAAmB,EAAE;IAClCT,GAAG,CAACS,mBAAmB,CAAC,CAAC;IACzB;EACF,CAAC,MAAM,IAAIT,GAAG,CAACU,oBAAoB,EAAE;IACnCV,GAAG,CAACU,oBAAoB,CAAC,CAAC;IAC1B;EACF,CAAC,MAAM,IAAIV,GAAG,CAACY,gBAAgB,EAAE;IAC/BZ,GAAG,CAACY,gBAAgB,CAAC,CAAC;EACxB;AACF,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAChC,OAAOZ,QAAQ,CAACa,UAAU,IAAIb,QAAQ,CAACc,aAAa,IAAId,QAAQ,CAACe,kBAAkB,IAAIf,QAAQ,CAACgB,mBAAmB,IAAI,KAAK;AAC9H,CAAC;AACD;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAGC,MAAM,CAACD,MAAM;EAC1B,IAAIE,EAAE,GAAGC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;EAC1C,IAAIJ,MAAM,CAACK,gBAAgB,KAAKC,SAAS,EAAE;IACzCR,KAAK,GAAGE,MAAM,CAACK,gBAAgB;EACjC,CAAC,MAAM,IAAI,CAACJ,EAAE,CAACM,OAAO,CAAC,MAAM,CAAC,EAAE;IAC9B,IAAIR,MAAM,CAACS,UAAU,IAAIT,MAAM,CAACU,WAAW,EAAE;MAC3CX,KAAK,GAAGC,MAAM,CAACS,UAAU,GAAGT,MAAM,CAACU,WAAW;IAChD;EACF,CAAC,MAAM,IAAIT,MAAM,CAACU,UAAU,KAAKJ,SAAS,IAAIN,MAAM,CAACW,UAAU,KAAKL,SAAS,EAAE;IAC7ER,KAAK,GAAGE,MAAM,CAACU,UAAU,GAAGV,MAAM,CAACW,UAAU;EAC/C;EAEA,IAAIb,KAAK,EAAE;IACTA,KAAK,GAAGc,IAAI,CAACC,KAAK,CAACf,KAAK,GAAG,GAAG,CAAC;EACjC;EACA,OAAOA,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}