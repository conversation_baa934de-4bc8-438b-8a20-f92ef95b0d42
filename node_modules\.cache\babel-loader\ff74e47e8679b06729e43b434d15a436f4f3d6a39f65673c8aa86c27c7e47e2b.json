{"ast": null, "code": "import * as echarts from 'echarts';\nexport default {\n  name: 'dealComNumChart',\n  data() {\n    return {\n      schoolChart: '',\n      uid: null,\n      data: [86.6],\n      dataList: {\n        id: 1,\n        style: {\n          width: '100%',\n          height: '84px'\n        },\n        yData: ['正常结束'],\n        data: [86.6]\n      }\n    };\n  },\n  beforeDestroy() {\n    if (this.schoolChart) {\n      this.schoolChart.clear();\n    }\n  },\n  mounted() {\n    this.uid = Math.floor(Math.random() * 100);\n    setTimeout(() => {\n      this.myChart();\n    }, 50);\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.schoolChart != null && this.schoolChart != \"\" && this.schoolChart != undefined) {\n          this.schoolChart.dispose();\n        }\n        this.schoolChart = echarts.init(document.getElementById(this.uid));\n        this.schoolChart.clear();\n        this.setOption();\n      });\n    },\n    setOption() {\n      let option = {\n        color: \"rgba(30, 149, 156, 1)\",\n        title: {\n          show: true,\n          text: this.dataList.yData,\n          left: '0',\n          top: '20',\n          textStyle: {\n            color: '#fff',\n            fontSize: 28,\n            fontWeight: '500'\n          }\n        },\n        tooltip: {\n          trigger: 'false'\n        },\n        grid: {\n          left: '0%',\n          right: '10%',\n          bottom: '-50%',\n          top: '0'\n        },\n        xAxis: {\n          type: 'value',\n          max: 100,\n          // boundaryGap: [0, 0.01],\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: false\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: this.dataList.yData,\n          inverse: false,\n          axisLabel: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: false\n          }\n        },\n        series: [{\n          type: 'bar',\n          data: this.data,\n          barWidth: '8px',\n          // barGap: 'px',\n          itemStyle: {\n            color: value => {\n              let color = ['rgba(8, 218, 228, 1)', 'rgba(58, 209, 253, 1)', 'rgba(0, 140, 255, 1)', 'rgba(84, 46, 218, 1)'];\n              return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{\n                offset: 0,\n                color: value.color\n              }, {\n                offset: 1,\n                color: color[value.dataIndex]\n              }]);\n            },\n            label: {\n              show: false,\n              position: \"right\",\n              offset: [0, 0],\n              color: \"#fff\",\n              fontSize: 20,\n              padding: [0, 0, 0, 10],\n              formatter: function (params) {\n                return params.data + \"所\";\n              }\n            }\n          }\n        }, {\n          type: 'bar',\n          data: [100],\n          barWidth: '8px',\n          barGap: '-1px',\n          color: \"rgba(119,143,165,0.43)\",\n          label: {\n            show: true,\n            position: \"top\",\n            color: '#fff',\n            fontSize: 27,\n            offset: [200, 0],\n            formatter: params => {\n              return this.data[params.dataIndex] + \"%\";\n            }\n          }\n        }]\n      };\n      if (option && typeof option === 'object') {\n        this.schoolChart.setOption(option);\n      }\n      window.onresize = this.schoolChart.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "name", "data", "schoolChart", "uid", "dataList", "id", "style", "width", "height", "yData", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "Math", "floor", "random", "setTimeout", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "document", "getElementById", "setOption", "option", "color", "title", "show", "text", "left", "top", "textStyle", "fontSize", "fontWeight", "tooltip", "trigger", "grid", "right", "bottom", "xAxis", "type", "max", "axisTick", "axisLine", "splitLine", "yAxis", "inverse", "axisLabel", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "value", "graphic", "LinearGradient", "offset", "dataIndex", "label", "position", "padding", "formatter", "params", "barGap", "window", "onresize", "resize"], "sources": ["src/views/chargingPilesScreen/echarts/process.vue"], "sourcesContent": ["<template>\r\n    <div class=\"box\">\r\n        <div :id=\"uid\" ref=\"schoolChart\" :style=\"dataList.style\">\r\n\r\n    </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n    name: 'dealComNumChart',\r\n    data() {\r\n        return {\r\n            schoolChart: '',\r\n            uid: null,\r\n            data: [86.6],\r\n            dataList: {\r\n                id: 1,\r\n                style: {\r\n                    width: '100%',\r\n                    height: '84px',\r\n                },\r\n                yData: ['正常结束'],\r\n                data: [86.6]\r\n\r\n            },\r\n\r\n        };\r\n    },\r\n    beforeDestroy(){\r\n        if(this.schoolChart){\r\n            this.schoolChart.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n            this.uid = Math.floor(Math.random() * 100);\r\n            setTimeout(() => {\r\n\r\n                this.myChart()\r\n            },50)\r\n\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.schoolChart != null && this.schoolChart != \"\" && this.schoolChart != undefined) {\r\n                    this.schoolChart.dispose();\r\n                }\r\n                this.schoolChart = echarts.init(document.getElementById(this.uid));\r\n                this.schoolChart.clear()\r\n\r\n               \r\n                this.setOption()\r\n\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            let option = {\r\n                color: \"rgba(30, 149, 156, 1)\",\r\n                title: {\r\n                    show: true,\r\n                    text: this.dataList.yData,\r\n                    left: '0',\r\n                    top: '20',\r\n                    textStyle: {\r\n                        color: '#fff',\r\n                        fontSize: 28,\r\n                        fontWeight: '500'\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    trigger: 'false',\r\n\r\n                },\r\n\r\n                grid: {\r\n                    left: '0%',\r\n                    right: '10%',\r\n                    bottom: '-50%',\r\n                    top: '0',\r\n                },\r\n\r\n                xAxis: {\r\n                    type: 'value',\r\n                    max: 100,\r\n                    // boundaryGap: [0, 0.01],\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n\r\n                    },\r\n\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                },\r\n                yAxis: {\r\n                    type: 'category',\r\n                    data: this.dataList.yData,\r\n                    inverse: false,\r\n                    axisLabel:{\r\n                        show:false\r\n                    },\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n\r\n                    },\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                },\r\n                series: [\r\n                {\r\n                        type: 'bar',\r\n                        data: this.data,\r\n                        barWidth: '8px',\r\n                        // barGap: 'px',\r\n                        itemStyle: {\r\n\r\n                            color: (value) => {\r\n                                let color = ['rgba(8, 218, 228, 1)', 'rgba(58, 209, 253, 1)', 'rgba(0, 140, 255, 1)', 'rgba(84, 46, 218, 1)']\r\n                                return new echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: value.color,\r\n                                    },\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: color[value.dataIndex],\r\n                                    }\r\n                                ])\r\n                            },\r\n                            label: {\r\n                                show: false,\r\n                                position: \"right\",\r\n                                offset: [0, 0],\r\n                                color: \"#fff\",\r\n                                fontSize: 20,\r\n                                padding: [0, 0, 0, 10],\r\n                                formatter: function (params) {\r\n                                    return params.data + \"所\"\r\n                                }\r\n\r\n                            },\r\n\r\n                        }\r\n\r\n                    },\r\n                    {\r\n                        type: 'bar',\r\n                        data: [100],\r\n                        barWidth: '8px',\r\n                        barGap: '-1px',\r\n                        color: \"rgba(119,143,165,0.43)\",\r\n                        label: {\r\n                            show: true,\r\n                            position: \"top\",\r\n                            color: '#fff',\r\n                            fontSize: 27,\r\n                            offset: [200, 0],\r\n                            formatter:  (params)=> {\r\n                                    return this.data[params.dataIndex] +\"%\"\r\n                                }\r\n                        }\r\n\r\n                    },\r\n                   \r\n                ]\r\n            };\r\n\r\n\r\n            if (option && typeof option === 'object') {\r\n                this.schoolChart.setOption(option);\r\n            }\r\n            window.onresize = this.schoolChart.resize;\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped></style>"], "mappings": "AAUA,YAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,GAAA;MACAF,IAAA;MACAG,QAAA;QACAC,EAAA;QACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;QACAR,IAAA;MAEA;IAEA;EACA;EACAS,cAAA;IACA,SAAAR,WAAA;MACA,KAAAA,WAAA,CAAAS,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAT,GAAA,GAAAU,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;IACAC,UAAA;MAEA,KAAAC,OAAA;IACA;EAEA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAnB,WAAA,iBAAAA,WAAA,eAAAA,WAAA,IAAAoB,SAAA;UACA,KAAApB,WAAA,CAAAqB,OAAA;QACA;QACA,KAAArB,WAAA,GAAAH,OAAA,CAAAyB,IAAA,CAAAC,QAAA,CAAAC,cAAA,MAAAvB,GAAA;QACA,KAAAD,WAAA,CAAAS,KAAA;QAGA,KAAAgB,SAAA;MAIA;IACA;IACAA,UAAA;MACA,IAAAC,MAAA;QACAC,KAAA;QACAC,KAAA;UACAC,IAAA;UACAC,IAAA,OAAA5B,QAAA,CAAAK,KAAA;UACAwB,IAAA;UACAC,GAAA;UACAC,SAAA;YACAN,KAAA;YACAO,QAAA;YACAC,UAAA;UACA;QACA;QACAC,OAAA;UACAC,OAAA;QAEA;QAEAC,IAAA;UACAP,IAAA;UACAQ,KAAA;UACAC,MAAA;UACAR,GAAA;QACA;QAEAS,KAAA;UACAC,IAAA;UACAC,GAAA;UACA;UACAC,QAAA;YACAf,IAAA;UACA;UACAgB,QAAA;YACAhB,IAAA;UAEA;UAEAiB,SAAA;YACAjB,IAAA;UACA;QACA;QACAkB,KAAA;UACAL,IAAA;UACA3C,IAAA,OAAAG,QAAA,CAAAK,KAAA;UACAyC,OAAA;UACAC,SAAA;YACApB,IAAA;UACA;UACAe,QAAA;YACAf,IAAA;UACA;UACAgB,QAAA;YACAhB,IAAA;UAEA;UACAiB,SAAA;YACAjB,IAAA;UACA;QACA;QACAqB,MAAA,GACA;UACAR,IAAA;UACA3C,IAAA,OAAAA,IAAA;UACAoD,QAAA;UACA;UACAC,SAAA;YAEAzB,KAAA,EAAA0B,KAAA;cACA,IAAA1B,KAAA;cACA,WAAA9B,OAAA,CAAAyD,OAAA,CAAAC,cAAA,cACA;gBACAC,MAAA;gBACA7B,KAAA,EAAA0B,KAAA,CAAA1B;cACA,GACA;gBACA6B,MAAA;gBACA7B,KAAA,EAAAA,KAAA,CAAA0B,KAAA,CAAAI,SAAA;cACA,EACA;YACA;YACAC,KAAA;cACA7B,IAAA;cACA8B,QAAA;cACAH,MAAA;cACA7B,KAAA;cACAO,QAAA;cACA0B,OAAA;cACAC,SAAA,WAAAA,CAAAC,MAAA;gBACA,OAAAA,MAAA,CAAA/D,IAAA;cACA;YAEA;UAEA;QAEA,GACA;UACA2C,IAAA;UACA3C,IAAA;UACAoD,QAAA;UACAY,MAAA;UACApC,KAAA;UACA+B,KAAA;YACA7B,IAAA;YACA8B,QAAA;YACAhC,KAAA;YACAO,QAAA;YACAsB,MAAA;YACAK,SAAA,EAAAC,MAAA;cACA,YAAA/D,IAAA,CAAA+D,MAAA,CAAAL,SAAA;YACA;UACA;QAEA;MAGA;MAGA,IAAA/B,MAAA,WAAAA,MAAA;QACA,KAAA1B,WAAA,CAAAyB,SAAA,CAAAC,MAAA;MACA;MACAsC,MAAA,CAAAC,QAAA,QAAAjE,WAAA,CAAAkE,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}