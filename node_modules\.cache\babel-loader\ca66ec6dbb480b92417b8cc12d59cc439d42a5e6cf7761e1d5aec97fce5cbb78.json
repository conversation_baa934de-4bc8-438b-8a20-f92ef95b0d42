{"ast": null, "code": "import { Lark<PERSON> } from \"larksr_websdk\";\nimport { mapState } from 'vuex';\n\n// import PxyCommonUI from 'pxy_webcommonui';\n// const {\n//   Joystick,\n//   Capabilities,\n//   Keyboard,\n// KJoystickEvents,\n// KJoystickSubTypes\n// } = PxyCommonUI;\n// const JoystickBottomImage = require('@/assets/joy_stick_bottom.png');\n// const JoystickTopImage = require('@/assets/joy_stick_top.png');\nexport default {\n  name: \"play\",\n  components: {},\n  props: {\n    screen: {\n      type: String,\n      default: 'main'\n    }\n  },\n  data() {\n    return {\n      larksr: null,\n      joystick: null,\n      appContainer: null,\n      keyboard: null,\n      appliId: '',\n      serverAddress: '',\n      sdkCode: '',\n      videoList: null,\n      isShowPlay: false,\n      num: 0,\n      nodeData: [{\n        appliId: '1174745537333690368',\n        serverAddress: \"http://***********:8181/\",\n        sdkCode: \"350e8457e3b74dac97221fc6c05ba36b\"\n      }, {\n        appliId: '1348705191460864000',\n        serverAddress: \"http://***********:8181/\",\n        sdkCode: \"af07500490da46eab1002a4c28ff6129\"\n      }]\n    };\n  },\n  mounted() {\n    this.appliId = this.nodeData[0].appliId;\n    this.serverAddress = this.nodeData[0].serverAddress;\n    this.sdkCode = this.nodeData[0].sdkCode;\n    this.$nextTick(() => {\n      this.init();\n    });\n  },\n  watch: {\n    nodeIndex: {\n      handler(nv) {\n        if (this.larksr) {\n          this.larksr.close();\n          this.larksr = null;\n          // this.$store.commit('getLarksrInfo', null);\n          this.$store.dispatch('getUeLarksr', null);\n        }\n        this.appliId = this.nodeData[nv].appliId;\n        this.serverAddress = this.nodeData[nv].serverAddress;\n        this.sdkCode = this.nodeData[nv].sdkCode;\n        this.init();\n      }\n    },\n    ueTxt(nv) {\n      if (nv && nv.State == 'OnLeftClick') {\n        nv.POIType == '视频监控' ? this.openPlay(nv.POIcode) : '';\n      } else if (nv && nv.State == 'OnRightClick') {\n        let item = nv;\n        this.$store.commit('action/getBufferShow', {\n          flag: true,\n          bufferGeometry: {\n            Lng: item.Lng,\n            Lat: item.Lat\n          }\n        });\n      }\n    }\n  },\n  computed: {\n    ...mapState(['nodeIndex']),\n    ...mapState({\n      ueTxt: state => state.dataChannelText\n    })\n  },\n  beforeDestroy() {\n    this.larksr.close();\n    this.larksr = null;\n    // this.$store.commit('getLarksrInfo', null);\n    this.$store.dispatch('getUeLarksr', null);\n  },\n  methods: {\n    // 打开监控\n    openPlay(id) {\n      // this.getLatLon(, 'play')\n      this.$store.commit(\"action/getVideoPlayerList\", {\n        channalCode: id\n      });\n      this.$store.commit(\"action/getIsShowNumInitChange\", true);\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n    },\n    init() {\n      let larksr = new LarkSR({\n        rootElement: this.$refs[\"appContainer\"],\n        serverAddress: this.serverAddress\n        //  serverAddress: \"http://***************:8181\",  //修改1\n        // scaleMode: \"fill_stretch\",\n        // 测试载入背景图\n        // loadingBgUrl: \"https://home-obs.pingxingyun.com/homePage_4_0/bg.jpg\",\n        // handleRootElementSize: false,\n      });\n      larksr.initSDKAuthCode(this.sdkCode)\n      //  larksr.initSDKAuthCode(\"5509f14bcd8e4c7387d3bed94e73a9c7\") //修改2\n      .then(() => {\n        /*  */\n        // start connect;/*  */\n        larksr.connect({\n          appliId: this.appliId\n          //  appliId: '1288097115364392960'  //修改3\n        }).then(() => {\n          // console.log('enter success');\n        }).catch(e => {\n          console.log(e);\n          alert(JSON.stringify(e));\n        });\n      }).catch(e => {\n        console.error(e);\n        alert(JSON.stringify(e));\n      });\n\n      // 监听连接成功事件\n      larksr.on('connect', e => {\n        // console.log(\"LarkSRClientEvent CONNECT\", e);\n      });\n      larksr.on('gotremotesteam', e => {\n        // console.log(\"LarkSRClientEvent gotremotesteam\", e);\n      });\n      // 视频加载成功，等待播放 \n      larksr.on('meidaloaded', e => {\n        // 加载总大小\n\n        // console.log(\"LarkSRClientEvent meidaloaded\", e);\n      });\n      // 视频自动播放成功 \n      larksr.on('mediaplaysuccess', e => {\n        // console.log(\"LarkSRClientEvent mediaplaysuccess\", e);\n        try {\n          this.$eventBus.$on('senTxtToUe', nv => {\n            let msg = JSON.stringify(nv);\n            console.log(msg, 'msg');\n            larksr.sendTextToDataChannel(msg); //发送ue\n          });\n        } finally {\n          this.$store.dispatch('getUeLarksr', larksr);\n          this.$eventBus.$emit('senTxtToUe', '清除');\n          let params = {\n            \"mode\": \"delete\"\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n          this.$eventBus.$emit(\"senTxtToUe\", \"首页\");\n        }\n        // if (this.joystick) {\n        //   this.joystick.show();\n\n        // }\n      });\n      // 视频自动播放失败\n      larksr.on('mediaplayfailed', e => {\n        // console.log(\"LarkSRClientEvent mediaplayfailed\", e);\n      });\n\n      // console.log(\"load appli success\", larksr);\n      larksr.on('error', e => {\n        // console.error(\"LarkSRClientEvent error\", e);\n        alert(JSON.stringify(e.message));\n      });\n      // 一般信息提示 \n      larksr.on('info', e => {\n        // console.log(\"datachanneltext info\", e);\n      });\n      larksr.on('taskcreatesuccess', e => {\n        // console.log(\"LarkSRClientEvent TASK_CREATE_SUCCESS\", e);\n      });\n      // 数据通道打开\n      larksr.on('datachannelopen', e => {\n        this.isShowPlay = true;\n      });\n      // 接受ue信息\n      larksr.on('datachanneltext', e => {\n        if (e.data != '') {\n          let params = JSON.parse(e.data);\n          console.log(params, '接收');\n          this.$store.commit('getLarksrInfo', params);\n        }\n      });\n      // larksr.on('apprequestinput', (e) => {\n      //   console.log(\"apprequestinput\", e);\n\n      //   if (e.data === true) {\n      //     if (Capabilities.isMobile) {\n      //       this.keyboard.show();\n      //     }\n      //   } else {\n      //     if (Capabilities.isMobile) {\n      //       this.keyboard.hide();\n      //     }\n      //     if (this.mobileScreenLandscapState) {\n      //       this.larksr?.screenState.setMobileForceLandScape(true);\n      //       this.mobileScreenLandscapState = false;\n      //     }\n      //   }\n      // });\n\n      larksr.on('resourcenotenough', e => {\n        console.log(\"LarkSRClientEvent resourcenotenough\", e);\n        if (e.type === 0) {\n          alert(\"当前系统繁忙，请稍后再试！\");\n        }\n      });\n      this.larksr = larksr;\n    },\n    closeBtn() {\n      this.larksr.close();\n      this.isShowPlay = false;\n    },\n    openBtn() {\n      this.init();\n    }\n  }\n};", "map": {"version": 3, "names": ["LarkSR", "mapState", "name", "components", "props", "screen", "type", "String", "default", "data", "larksr", "joystick", "appContainer", "keyboard", "appliId", "serverAddress", "sdkCode", "videoList", "isShowPlay", "num", "nodeData", "mounted", "$nextTick", "init", "watch", "nodeIndex", "handler", "nv", "close", "$store", "dispatch", "ueTxt", "State", "POIType", "openPlay", "POIcode", "item", "commit", "flag", "bufferGeometry", "Lng", "Lat", "computed", "state", "dataChannelText", "<PERSON><PERSON><PERSON><PERSON>", "methods", "id", "channalCode", "rootElement", "$refs", "initSDKAuthCode", "then", "connect", "catch", "e", "console", "log", "alert", "JSON", "stringify", "error", "on", "$eventBus", "$on", "msg", "sendTextToDataChannel", "$emit", "params", "message", "parse", "closeBtn", "openBtn"], "sources": ["src/components/play.vue"], "sourcesContent": ["<template>\r\n  <div id=\"playCon\" ref=\"appContainer\">\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { LarkSR } from \"larksr_websdk\";\r\nimport { mapState } from 'vuex';\r\n\r\n\r\n// import PxyCommonUI from 'pxy_webcommonui';\r\n// const {\r\n//   Joystick,\r\n//   Capabilities,\r\n//   Keyboard,\r\n// KJoystickEvents,\r\n// KJoystickSubTypes\r\n// } = PxyCommonUI;\r\n// const JoystickBottomImage = require('@/assets/joy_stick_bottom.png');\r\n// const JoystickTopImage = require('@/assets/joy_stick_top.png');\r\nexport default {\r\n  name: \"play\",\r\n  components: {\r\n\r\n  },\r\n  props: {\r\n    screen: {\r\n      type: String,\r\n      default: 'main'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      larksr: null,\r\n      joystick: null,\r\n      appContainer: null,\r\n      keyboard: null,\r\n      appliId: '',\r\n      serverAddress: '',\r\n      sdkCode: '',\r\n      videoList: null,\r\n      isShowPlay: false,\r\n      num: 0,\r\n      nodeData: [\r\n        {\r\n          appliId: '1174745537333690368',\r\n          serverAddress: \"http://***********:8181/\",\r\n          sdkCode: \"350e8457e3b74dac97221fc6c05ba36b\"\r\n        },\r\n        {\r\n          appliId: '1348705191460864000',\r\n          serverAddress: \"http://***********:8181/\",\r\n          sdkCode: \"af07500490da46eab1002a4c28ff6129\"\r\n        },\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    this.appliId = this.nodeData[0].appliId;\r\n    this.serverAddress = this.nodeData[0].serverAddress;\r\n    this.sdkCode = this.nodeData[0].sdkCode;\r\n    this.$nextTick(() => {\r\n      this.init();\r\n    })\r\n  },\r\n  watch: {\r\n    nodeIndex: {\r\n      handler(nv) {\r\n        if (this.larksr) {\r\n          this.larksr.close();\r\n          this.larksr = null;\r\n          // this.$store.commit('getLarksrInfo', null);\r\n          this.$store.dispatch('getUeLarksr', null);\r\n        }\r\n        this.appliId = this.nodeData[nv].appliId;\r\n        this.serverAddress = this.nodeData[nv].serverAddress;\r\n        this.sdkCode = this.nodeData[nv].sdkCode;\r\n        this.init();\r\n      }\r\n    },\r\n    ueTxt(nv) {\r\n      if (nv && nv.State == 'OnLeftClick') {\r\n        nv.POIType == '视频监控' ? this.openPlay(nv.POIcode) : ''\r\n      }\r\n      else if (nv && nv.State == 'OnRightClick') {\r\n        let item = nv;\r\n        this.$store.commit('action/getBufferShow', { flag: true, bufferGeometry: { Lng: item.Lng, Lat: item.Lat } })\r\n\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapState(['nodeIndex']),\r\n    ...mapState({\r\n      ueTxt: state => state.dataChannelText,\r\n    }),\r\n  },\r\n  beforeDestroy() {\r\n\r\n    this.larksr.close();\r\n    this.larksr = null;\r\n    // this.$store.commit('getLarksrInfo', null);\r\n    this.$store.dispatch('getUeLarksr', null);\r\n\r\n\r\n  },\r\n  methods: {\r\n\r\n    // 打开监控\r\n    openPlay(id) {\r\n      // this.getLatLon(, 'play')\r\n      this.$store.commit(\"action/getVideoPlayerList\", { channalCode: id })\r\n      this.$store.commit(\"action/getIsShowNumInitChange\", true)\r\n      this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n\r\n    },\r\n    init() {\r\n\r\n      let larksr = new LarkSR({\r\n        rootElement: this.$refs[\"appContainer\"],\r\n        serverAddress: this.serverAddress,\r\n        //  serverAddress: \"http://***************:8181\",  //修改1\r\n        // scaleMode: \"fill_stretch\",\r\n        // 测试载入背景图\r\n        // loadingBgUrl: \"https://home-obs.pingxingyun.com/homePage_4_0/bg.jpg\",\r\n        // handleRootElementSize: false,\r\n      });\r\n\r\n      larksr.initSDKAuthCode(this.sdkCode)\r\n        //  larksr.initSDKAuthCode(\"5509f14bcd8e4c7387d3bed94e73a9c7\") //修改2\r\n        .then(() => {/*  */\r\n          // start connect;/*  */\r\n          larksr.connect({\r\n            appliId: this.appliId\r\n            //  appliId: '1288097115364392960'  //修改3\r\n          })\r\n            .then(() => {\r\n              // console.log('enter success');\r\n            })\r\n            .catch((e) => {\r\n              console.log(e);\r\n              alert(JSON.stringify(e));\r\n            });\r\n        })\r\n        .catch((e) => {\r\n          console.error(e);\r\n          alert(JSON.stringify(e));\r\n        });\r\n\r\n      // 监听连接成功事件\r\n      larksr.on('connect', (e) => {\r\n        // console.log(\"LarkSRClientEvent CONNECT\", e);\r\n\r\n      });\r\n      larksr.on('gotremotesteam', (e) => {\r\n        // console.log(\"LarkSRClientEvent gotremotesteam\", e);\r\n      });\r\n      // 视频加载成功，等待播放 \r\n      larksr.on('meidaloaded', (e) => {\r\n        // 加载总大小\r\n\r\n        // console.log(\"LarkSRClientEvent meidaloaded\", e);\r\n      });\r\n      // 视频自动播放成功 \r\n      larksr.on('mediaplaysuccess', (e) => {\r\n        // console.log(\"LarkSRClientEvent mediaplaysuccess\", e);\r\n        try {\r\n          this.$eventBus.$on('senTxtToUe', (nv) => {\r\n            let msg = JSON.stringify(nv)\r\n            console.log(msg, 'msg')\r\n            larksr.sendTextToDataChannel(msg);//发送ue\r\n          })\r\n        } finally {\r\n          this.$store.dispatch('getUeLarksr', larksr)\r\n          this.$eventBus.$emit('senTxtToUe', '清除')\r\n          let params = {\r\n            \"mode\": \"delete\",\r\n          }\r\n          this.$eventBus.$emit('senTxtToUe', params)\r\n          this.$eventBus.$emit(\"senTxtToUe\", \"首页\")\r\n        }\r\n        // if (this.joystick) {\r\n        //   this.joystick.show();\r\n\r\n        // }\r\n      });\r\n      // 视频自动播放失败\r\n      larksr.on('mediaplayfailed', (e) => {\r\n        // console.log(\"LarkSRClientEvent mediaplayfailed\", e);\r\n      });\r\n\r\n      // console.log(\"load appli success\", larksr);\r\n      larksr.on('error', (e) => {\r\n        // console.error(\"LarkSRClientEvent error\", e);\r\n        alert(JSON.stringify(e.message));\r\n      });\r\n      // 一般信息提示 \r\n      larksr.on('info', (e) => {\r\n        // console.log(\"datachanneltext info\", e);\r\n\r\n      });\r\n      larksr.on('taskcreatesuccess', (e) => {\r\n        // console.log(\"LarkSRClientEvent TASK_CREATE_SUCCESS\", e);\r\n      })\r\n      // 数据通道打开\r\n      larksr.on('datachannelopen', (e) => {\r\n        this.isShowPlay = true;\r\n      })\r\n      // 接受ue信息\r\n      larksr.on('datachanneltext', (e) => {\r\n        if (e.data != '') {\r\n          let params = JSON.parse(e.data);\r\n          console.log(params, '接收')\r\n          this.$store.commit('getLarksrInfo', params)\r\n        }\r\n\r\n      })\r\n      // larksr.on('apprequestinput', (e) => {\r\n      //   console.log(\"apprequestinput\", e);\r\n\r\n      //   if (e.data === true) {\r\n      //     if (Capabilities.isMobile) {\r\n      //       this.keyboard.show();\r\n      //     }\r\n      //   } else {\r\n      //     if (Capabilities.isMobile) {\r\n      //       this.keyboard.hide();\r\n      //     }\r\n      //     if (this.mobileScreenLandscapState) {\r\n      //       this.larksr?.screenState.setMobileForceLandScape(true);\r\n      //       this.mobileScreenLandscapState = false;\r\n      //     }\r\n      //   }\r\n      // });\r\n\r\n      larksr.on('resourcenotenough', (e) => {\r\n        console.log(\"LarkSRClientEvent resourcenotenough\", e);\r\n        if (e.type === 0) {\r\n          alert(\"当前系统繁忙，请稍后再试！\");\r\n        }\r\n      });\r\n\r\n      this.larksr = larksr;\r\n    },\r\n\r\n    closeBtn() {\r\n      this.larksr.close();\r\n      this.isShowPlay = false;\r\n\r\n    },\r\n    openBtn() {\r\n      this.init()\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n#playCon {\r\n  width: 100% !important;\r\n  height: 100% !important;\r\n  border: 0px solid #000;\r\n}\r\n\r\n.pxy-container {\r\n  width: 100% !important;\r\n  height: 100% !important;\r\n}\r\n/deep/ .showVideo{\r\n  height: 100vh!important;\r\n}\r\n</style>"], "mappings": "AAOA,SAAAA,MAAA;AACA,SAAAC,QAAA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC,IAAA;EACAC,UAAA,GAEA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,QAAA;MACAC,YAAA;MACAC,QAAA;MACAC,OAAA;MACAC,aAAA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,GAAA;MACAC,QAAA,GACA;QACAN,OAAA;QACAC,aAAA;QACAC,OAAA;MACA,GACA;QACAF,OAAA;QACAC,aAAA;QACAC,OAAA;MACA;IAEA;EACA;EACAK,QAAA;IACA,KAAAP,OAAA,QAAAM,QAAA,IAAAN,OAAA;IACA,KAAAC,aAAA,QAAAK,QAAA,IAAAL,aAAA;IACA,KAAAC,OAAA,QAAAI,QAAA,IAAAJ,OAAA;IACA,KAAAM,SAAA;MACA,KAAAC,IAAA;IACA;EACA;EACAC,KAAA;IACAC,SAAA;MACAC,QAAAC,EAAA;QACA,SAAAjB,MAAA;UACA,KAAAA,MAAA,CAAAkB,KAAA;UACA,KAAAlB,MAAA;UACA;UACA,KAAAmB,MAAA,CAAAC,QAAA;QACA;QACA,KAAAhB,OAAA,QAAAM,QAAA,CAAAO,EAAA,EAAAb,OAAA;QACA,KAAAC,aAAA,QAAAK,QAAA,CAAAO,EAAA,EAAAZ,aAAA;QACA,KAAAC,OAAA,QAAAI,QAAA,CAAAO,EAAA,EAAAX,OAAA;QACA,KAAAO,IAAA;MACA;IACA;IACAQ,MAAAJ,EAAA;MACA,IAAAA,EAAA,IAAAA,EAAA,CAAAK,KAAA;QACAL,EAAA,CAAAM,OAAA,kBAAAC,QAAA,CAAAP,EAAA,CAAAQ,OAAA;MACA,OACA,IAAAR,EAAA,IAAAA,EAAA,CAAAK,KAAA;QACA,IAAAI,IAAA,GAAAT,EAAA;QACA,KAAAE,MAAA,CAAAQ,MAAA;UAAAC,IAAA;UAAAC,cAAA;YAAAC,GAAA,EAAAJ,IAAA,CAAAI,GAAA;YAAAC,GAAA,EAAAL,IAAA,CAAAK;UAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACA,GAAAzC,QAAA;IACA,GAAAA,QAAA;MACA8B,KAAA,EAAAY,KAAA,IAAAA,KAAA,CAAAC;IACA;EACA;EACAC,cAAA;IAEA,KAAAnC,MAAA,CAAAkB,KAAA;IACA,KAAAlB,MAAA;IACA;IACA,KAAAmB,MAAA,CAAAC,QAAA;EAGA;EACAgB,OAAA;IAEA;IACAZ,SAAAa,EAAA;MACA;MACA,KAAAlB,MAAA,CAAAQ,MAAA;QAAAW,WAAA,EAAAD;MAAA;MACA,KAAAlB,MAAA,CAAAQ,MAAA;MACA,KAAAR,MAAA,CAAAQ,MAAA;IAEA;IACAd,KAAA;MAEA,IAAAb,MAAA,OAAAV,MAAA;QACAiD,WAAA,OAAAC,KAAA;QACAnC,aAAA,OAAAA;QACA;QACA;QACA;QACA;QACA;MACA;MAEAL,MAAA,CAAAyC,eAAA,MAAAnC,OAAA;MACA;MAAA,CACAoC,IAAA;QAAA;QACA;QACA1C,MAAA,CAAA2C,OAAA;UACAvC,OAAA,OAAAA;UACA;QACA,GACAsC,IAAA;UACA;QAAA,CACA,EACAE,KAAA,CAAAC,CAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,CAAA;UACAG,KAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAL,CAAA;QACA;MACA,GACAD,KAAA,CAAAC,CAAA;QACAC,OAAA,CAAAK,KAAA,CAAAN,CAAA;QACAG,KAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAL,CAAA;MACA;;MAEA;MACA7C,MAAA,CAAAoD,EAAA,YAAAP,CAAA;QACA;MAAA,CAEA;MACA7C,MAAA,CAAAoD,EAAA,mBAAAP,CAAA;QACA;MAAA,CACA;MACA;MACA7C,MAAA,CAAAoD,EAAA,gBAAAP,CAAA;QACA;;QAEA;MAAA,CACA;MACA;MACA7C,MAAA,CAAAoD,EAAA,qBAAAP,CAAA;QACA;QACA;UACA,KAAAQ,SAAA,CAAAC,GAAA,eAAArC,EAAA;YACA,IAAAsC,GAAA,GAAAN,IAAA,CAAAC,SAAA,CAAAjC,EAAA;YACA6B,OAAA,CAAAC,GAAA,CAAAQ,GAAA;YACAvD,MAAA,CAAAwD,qBAAA,CAAAD,GAAA;UACA;QACA;UACA,KAAApC,MAAA,CAAAC,QAAA,gBAAApB,MAAA;UACA,KAAAqD,SAAA,CAAAI,KAAA;UACA,IAAAC,MAAA;YACA;UACA;UACA,KAAAL,SAAA,CAAAI,KAAA,eAAAC,MAAA;UACA,KAAAL,SAAA,CAAAI,KAAA;QACA;QACA;QACA;;QAEA;MACA;MACA;MACAzD,MAAA,CAAAoD,EAAA,oBAAAP,CAAA;QACA;MAAA,CACA;;MAEA;MACA7C,MAAA,CAAAoD,EAAA,UAAAP,CAAA;QACA;QACAG,KAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAL,CAAA,CAAAc,OAAA;MACA;MACA;MACA3D,MAAA,CAAAoD,EAAA,SAAAP,CAAA;QACA;MAAA,CAEA;MACA7C,MAAA,CAAAoD,EAAA,sBAAAP,CAAA;QACA;MAAA,CACA;MACA;MACA7C,MAAA,CAAAoD,EAAA,oBAAAP,CAAA;QACA,KAAArC,UAAA;MACA;MACA;MACAR,MAAA,CAAAoD,EAAA,oBAAAP,CAAA;QACA,IAAAA,CAAA,CAAA9C,IAAA;UACA,IAAA2D,MAAA,GAAAT,IAAA,CAAAW,KAAA,CAAAf,CAAA,CAAA9C,IAAA;UACA+C,OAAA,CAAAC,GAAA,CAAAW,MAAA;UACA,KAAAvC,MAAA,CAAAQ,MAAA,kBAAA+B,MAAA;QACA;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA1D,MAAA,CAAAoD,EAAA,sBAAAP,CAAA;QACAC,OAAA,CAAAC,GAAA,wCAAAF,CAAA;QACA,IAAAA,CAAA,CAAAjD,IAAA;UACAoD,KAAA;QACA;MACA;MAEA,KAAAhD,MAAA,GAAAA,MAAA;IACA;IAEA6D,SAAA;MACA,KAAA7D,MAAA,CAAAkB,KAAA;MACA,KAAAV,UAAA;IAEA;IACAsD,QAAA;MACA,KAAAjD,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}