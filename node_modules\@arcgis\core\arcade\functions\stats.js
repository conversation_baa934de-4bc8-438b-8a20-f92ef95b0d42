/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{p as n,a as t,f as r,b as u}from"../../chunks/languageUtils.js";import{calculateStat as e}from"./fieldStats.js";function i(n,r,i,o){if(1===o.length){if(t(o[0]))return e(n,o[0],-1);if(u(o[0]))return e(n,o[0].toArray(),-1)}return e(n,o,-1)}function o(e,o){e.stdev=function(n,t){return o(n,t,(function(n,t,r){return i("stdev",n,t,r)}))},e.variance=function(n,t){return o(n,t,(function(n,t,r){return i("variance",n,t,r)}))},e.average=function(n,t){return o(n,t,(function(n,t,r){return i("mean",n,t,r)}))},e.mean=function(n,t){return o(n,t,(function(n,t,r){return i("mean",n,t,r)}))},e.sum=function(n,t){return o(n,t,(function(n,t,r){return i("sum",n,t,r)}))},e.min=function(n,t){return o(n,t,(function(n,t,r){return i("min",n,t,r)}))},e.max=function(n,t){return o(n,t,(function(n,t,r){return i("max",n,t,r)}))},e.distinct=function(n,t){return o(n,t,(function(n,t,r){return i("distinct",n,t,r)}))},e.count=function(e,i){return o(e,i,(function(e,i,o){if(n(o,1,1),t(o[0])||r(o[0]))return o[0].length;if(u(o[0]))return o[0].length();throw new Error("Invalid Parameters for Count")}))}}export{o as registerFunctions};
