{"ast": null, "code": "import { mapState } from 'vuex';\nexport default {\n  name: \"informationType\",\n  data() {\n    return {\n      videoInfo: null\n    };\n  },\n  props: {\n    sizeStyle: {\n      type: Object,\n      default: () => {\n        return {\n          left: \"calc(25%)\"\n        };\n      }\n    }\n  },\n  computed: {\n    ...mapState(\"action\", [\"playerList\", \"TypeVideoAll\", \"videoDetailsExpand\"])\n  },\n  watch: {\n    videoDetailsExpand: {\n      handler(nv) {\n        if (nv) {\n          this.videoInfo = nv;\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    closeInfo() {\n      this.$store.commit(\"action/getVideoInfoFlag\", false);\n    }\n  },\n  mounted() {\n    // this.getInfo()\n  }\n};", "map": {"version": 3, "names": ["mapState", "name", "data", "videoInfo", "props", "sizeStyle", "type", "Object", "default", "left", "computed", "watch", "videoDetailsExpand", "handler", "nv", "immediate", "methods", "closeInfo", "$store", "commit", "mounted"], "sources": ["src/components/videoTypeOption/getVideoInfo.vue"], "sourcesContent": ["<template>\r\n    <div class=\"inform boxBgStyle\" :style=\"sizeStyle\">\r\n        <div class=\"topMain\">\r\n            <div class=\"title\">\r\n                <div class=\"txt fz30\">监控详情</div>\r\n            </div>\r\n            <div class=\"delete\" @click=\"closeInfo\">\r\n                <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\" width=\"30px\">\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"mainCon\">\r\n            <ul>\r\n                <li>\r\n                   \r\n                        <div class=\"name\">视频名称：</div>\r\n                    <div class=\"txt\">{{ videoInfo?.channalName }}</div>\r\n                     \r\n                </li>\r\n                <li>\r\n                 \r\n                        <div class=\"name\">所属组织：</div>\r\n                    <div class=\"txt\">{{ videoInfo?.organization }}</div>\r\n                 \r\n                </li>\r\n                <li>\r\n                    <div class=\"name\">摄像头类型：</div>\r\n                    <div class=\"txt\">{{ videoInfo?.cameraType }}</div>\r\n                </li>\r\n                <li>\r\n                    <div class=\"name\">视频连接：</div>\r\n                    <div class=\"txt\">{{ videoInfo?.connectWay }}</div>\r\n                </li>\r\n                 <li>\r\n                    <div class=\"name\">设备类型：</div>\r\n                    <div class=\"txt\">{{ videoInfo?.deviceType }}</div>\r\n                 </li>\r\n            </ul>\r\n             \r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: \"informationType\",\r\n    data() {\r\n        return {\r\n           videoInfo:null\r\n        }\r\n    },\r\n    props: {\r\n         sizeStyle: {\r\n            type: Object,\r\n            default: () => {\r\n                return {\r\n                   \r\n                    left: \"calc(25%)\"\r\n                }\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"playerList\",\r\n            \"TypeVideoAll\",\r\n            \"videoDetailsExpand\"\r\n        ]),\r\n        \r\n    },\r\n    watch: {\r\n        videoDetailsExpand:{\r\n            handler(nv){\r\n                if(nv){\r\n                    this.videoInfo=nv\r\n\r\n                }\r\n            },\r\n            immediate:true\r\n        }\r\n\r\n    },\r\n\r\n    methods: {\r\n        closeInfo(){\r\n            this.$store.commit(\"action/getVideoInfoFlag\", false)\r\n        }\r\n\r\n    },\r\n    mounted() {\r\n        // this.getInfo()\r\n    }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.inform {\r\n    pointer-events: stroke;\r\n    z-index: 22;\r\n    position: fixed;\r\n    top: calc(10%);\r\n    // width: fit-content;\r\n    width: 550px;\r\n    padding-bottom: 20px;\r\n    transform-origin: 0 0;\r\n    transform: scale(var(--scaleX), var(--scaleY));\r\n    transition: 0.3s;\r\n\r\n    .title {\r\n        padding: 8px 15px 8px 40px;\r\n\r\n    }\r\n\r\n    .mainCon {\r\n        padding-left: 30px;\r\n        max-height: 500px;\r\n        overflow-y: auto;\r\n        li{\r\n            padding: .4rem 0;\r\n            display:flex;\r\n            font-size: 25px;\r\n            line-height:50px;\r\n            .name{\r\n                width: calc(65% - 50px)\r\n            }\r\n            .txt{\r\n                width:calc(100% - 50px)\r\n            }\r\n        }\r\n     \r\n    }\r\n\r\n     \r\n}\r\n</style>"], "mappings": "AA2CA,SAAAA,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;UAEAC,IAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAV,QAAA,YACA,cACA,gBACA,qBACA;EAEA;EACAW,KAAA;IACAC,kBAAA;MACAC,QAAAC,EAAA;QACA,IAAAA,EAAA;UACA,KAAAX,SAAA,GAAAW,EAAA;QAEA;MACA;MACAC,SAAA;IACA;EAEA;EAEAC,OAAA;IACAC,UAAA;MACA,KAAAC,MAAA,CAAAC,MAAA;IACA;EAEA;EACAC,QAAA;IACA;EAAA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}