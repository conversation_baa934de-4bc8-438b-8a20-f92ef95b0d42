{"ast": null, "code": "import topTitle from './header/topTitle.vue';\nimport ScreenView from '@/components/ScreenView.vue';\nimport mainScreen from './mainScreen/mainScreen.vue';\nimport SiteAlarmList from './SiteAlarmList/SiteAlarmList.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'chargingPilesScreen',\n  components: {\n    topTitle,\n    ScreenView,\n    mainScreen,\n    SiteAlarmList\n  },\n  data() {\n    return {\n      typeNumInfo: [],\n      AlarmList: {}\n    };\n  },\n  computed: {\n    ...mapState(['dataChannelText', 'larksr'])\n  },\n  watch: {\n    dataChannelText(nv) {\n      if (nv.typename == 'chargingPiles') {\n        this.AlarmList = nv.object;\n      }\n    },\n    larksr: {\n      handler(nv) {\n        if (nv) {\n          this.initPointData();\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.$store.dispatch('getUser');\n  },\n  mounted() {},\n  beforeDestroy() {\n    this.$eventBus.$emit('senTxtToUe', '关闭充电桩');\n  },\n  methods: {\n    initPointData() {\n      this.$eventBus.$emit('senTxtToUe', '充电桩');\n    }\n  }\n};", "map": {"version": 3, "names": ["topTitle", "ScreenView", "mainScreen", "SiteAlarmList", "mapState", "name", "components", "data", "typeNumInfo", "AlarmList", "computed", "watch", "dataChannelText", "nv", "typename", "object", "larksr", "handler", "initPointData", "immediate", "created", "$store", "dispatch", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "$eventBus", "$emit", "methods"], "sources": ["src/views/chargingPilesScreen/chargingPilesScreen.vue"], "sourcesContent": ["<template>\r\n    <div class=\"chargingPilesScreen\">\r\n        <v-scale-screen width=\"3840\" height=\"2160\" :fullScreen=\"true\">\r\n\r\n            <div class=\"container\">\r\n                <topTitle></topTitle>\r\n                <mainScreen></mainScreen>\r\n                <SiteAlarmList ref=\"SiteAlarmList\" :AlarmList=\"AlarmList\"></SiteAlarmList>\r\n            </div>\r\n        </v-scale-screen>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport topTitle from './header/topTitle.vue'\r\nimport ScreenView from '@/components/ScreenView.vue'\r\nimport mainScreen from './mainScreen/mainScreen.vue'\r\nimport SiteAlarmList from './SiteAlarmList/SiteAlarmList.vue'\r\n\r\nimport { mapState } from 'vuex'\r\n\r\nexport default {\r\n    name: 'chargingPilesScreen',\r\n    components: {\r\n        topTitle,\r\n        ScreenView,\r\n        mainScreen,\r\n        SiteAlarmList\r\n    },\r\n    data() {\r\n        return {\r\n            typeNumInfo: [],\r\n            AlarmList: {}\r\n\r\n        };\r\n    },\r\n    computed: {\r\n        ...mapState(['dataChannelText', 'larksr'])\r\n    },\r\n    \r\n    watch: {\r\n        \r\n        dataChannelText(nv) {\r\n            if (nv.typename == 'chargingPiles') { \r\n                this.AlarmList = nv.object\r\n\r\n            }\r\n        },\r\n        larksr: {\r\n            handler(nv) {\r\n                if (nv) {\r\n                    this.initPointData();\r\n                }\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n\r\n    created() {\r\n        this.$store.dispatch('getUser');\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    beforeDestroy() {\r\n        this.$eventBus.$emit('senTxtToUe', '关闭充电桩')\r\n    },\r\n    methods: {\r\n        initPointData() {\r\n            this.$eventBus.$emit('senTxtToUe', '充电桩')\r\n\r\n        },\r\n\r\n    },\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.chargingPilesScreen {\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: auto;\r\n\r\n    .screen-box {\r\n        pointer-events: none;\r\n    }\r\n\r\n    .container {\r\n\r\n        .middle {\r\n            pointer-events: stroke;\r\n            width: 1883px;\r\n            height: 102px;\r\n            position: absolute;\r\n            top: 192px;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            background: rgba(39, 81, 112, 0.5);\r\n            border-radius: 0px 0px 0px 0px;\r\n            z-index: 1;\r\n            padding: 10px 0;\r\n            box-sizing: border-box;\r\n\r\n            .item {\r\n                padding-left: 10px;\r\n                width: 269px;\r\n                border-right: 4px solid rgba(168, 197, 220, 0.6);\r\n\r\n                .img {\r\n                    width: 80px;\r\n\r\n                    margin-right: 15px;\r\n\r\n                }\r\n            }\r\n\r\n            .item:nth-of-type(1) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon1.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-of-type(2) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon2.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-child(3) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon3.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-child(4) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon4.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-child(5) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon5.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-child(6) {\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon6.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .item:nth-child(7) {\r\n                border-right: 0px solid rgba(168, 197, 220, 0.6);\r\n\r\n                .img {\r\n                    background: url('@/assets/images/chongdian/top_icon7.png') no-repeat center;\r\n                    background-size: 100% 100%;\r\n                }\r\n            }\r\n\r\n            .name {\r\n                line-height: 37px;\r\n            }\r\n\r\n            .num {\r\n                font-size: 46px;\r\n                line-height: 54px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAcA,OAAAA,QAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,aAAA;AAEA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,QAAA;IACAC,UAAA;IACAC,UAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;IAEA;EACA;EACAC,QAAA;IACA,GAAAN,QAAA;EACA;EAEAO,KAAA;IAEAC,gBAAAC,EAAA;MACA,IAAAA,EAAA,CAAAC,QAAA;QACA,KAAAL,SAAA,GAAAI,EAAA,CAAAE,MAAA;MAEA;IACA;IACAC,MAAA;MACAC,QAAAJ,EAAA;QACA,IAAAA,EAAA;UACA,KAAAK,aAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,MAAA,CAAAC,QAAA;EAEA;EACAC,QAAA,GAEA;EACAC,cAAA;IACA,KAAAC,SAAA,CAAAC,KAAA;EACA;EACAC,OAAA;IACAT,cAAA;MACA,KAAAO,SAAA,CAAAC,KAAA;IAEA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}