{"ast": null, "code": "import { groupByYwStatus } from '@/api/index.js';\nexport default {\n  name: 'WaterHistory',\n  data() {\n    return {\n      myChartWater: null,\n      legend: [],\n      data: []\n    };\n  },\n  beforeDestroy() {\n    if (this.myChartWater) {\n      this.myChartWater.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.myChartWater != null && this.myChartWater != \"\" && this.myChart != undefined) {\n          this.myChartWater.dispose();\n        }\n        this.myChartWater = this.$eCharts.init(this.$refs.myChartWater);\n        this.myChartWater.clear();\n\n        // 液位差实时报警\n\n        groupByYwStatus().then(res => {\n          let dataList = res.data.extra;\n          let max = 0;\n          for (let v of dataList) {\n            max += v.ywStatusCount;\n          }\n          for (let v of dataList) {\n            this.legend.push(v.ywStatus + '-' + v.ywStatusCount);\n            let value = (v.ywStatusCount / max).toFixed(2) * 100;\n            this.data.push({\n              value: value,\n              name: v.ywStatus + '-' + v.ywStatusCount,\n              perName: value + \"%\" + '-' + v.ywStatus\n            });\n          }\n          this.$nextTick(() => {\n            this.setOption();\n          });\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        // title: {\n        //     text: '液位差实时报警',\n        //     top: '10%',\n        //     left: '30%',\n        //     textStyle: {\n        //         fontSize: 25,\n        //         fontWeight: '',\n        //         color: '#fff'\n        //     },\n        // },//标题\n\n        // tooltip: {\n        //     trigger: 'item',\n        //     formatter: \"{a} <br/>{b}: {c} ({d}%)\",\n        //     /*formatter:function(val){   //让series 中的文字进行换行\n        //          console.log(val);//查看val属性，可根据里边属性自定义内容\n        //          var content = var['name'];\n        //          return content;//返回可以含有html中标签\n        //      },*/ //自定义鼠标悬浮交互信息提示，鼠标放在饼状图上时触发事件\n        // },//提示框，鼠标悬浮交互时的信息提示\n        legend: {\n          show: true,\n          // orient: 'vertical',\n          x: 'center',\n          y: \"0%\",\n          itemGap: 15,\n          data: this.legend,\n          textStyle: {\n            color: '#fff',\n            fontSize: 18,\n            padding: [0, 10, 0, 0]\n          }\n        },\n        //图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配\n        // graphic: {\n        //     type: 'text',\n        //     left: 'center',\n        //     top: 'center',\n        //     style: {\n        //         text: '用户统计\\n' + '100', //使用“+”可以使每行文字居中\n        //         textAlign: 'center',\n        //         font: 'italic bolder 16px cursive',\n        //         fill: '#000',\n        //         width: 30,\n        //         height: 30\n        //     }\n        // },//此例饼状图为圆环中心文字显示属性，这是一个原生图形元素组件，功能很多\n        series: [{\n          name: '',\n          type: 'pie',\n          radius: ['25%', '60%'],\n          center: ['50%', '60%'],\n          label: {\n            show: true,\n            color: '#fff',\n            fontSize: \"21\",\n            formatter: function (val) {\n              return val.data.perName.split(\"-\").join(\"\\n\");\n            }\n          },\n          emphasis: {\n            labelLine: {\n              show: true,\n              length: 30,\n              // length2: 40,\n              lineStyle: {\n                color: '#fff'\n              }\n            },\n            //基本样式\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.2)',\n              //鼠标放在区域边框颜色\n              textColor: '#000'\n            } //鼠标放在各个区域的样式\n          },\n          data: this.data,\n          //数据，数据中其他属性，查阅文档\n          color: ['#51CEC6', '#FFB703', '#5FA0FA'] //各个区域颜色\n        } //数组中一个{}元素，一个图，以此可以做出环形图\n        ] //系列列表\n      };\n      this.myChartWater.setOption(option);\n    }\n  }\n};", "map": {"version": 3, "names": ["groupByYwStatus", "name", "data", "myChartWater", "legend", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "$eCharts", "init", "$refs", "res", "dataList", "extra", "max", "v", "ywStatusCount", "push", "ywStatus", "value", "toFixed", "perName", "setOption", "option", "show", "x", "y", "itemGap", "textStyle", "color", "fontSize", "padding", "series", "type", "radius", "center", "label", "formatter", "val", "split", "join", "emphasis", "labelLine", "length", "lineStyle", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "textColor"], "sources": ["src/components/smartWater/WaterHistory.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div ref=\"myChartWater\" id=\"myChartWater\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { groupByYwStatus } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'WaterHistory',\r\n\r\n    data() {\r\n        return {\r\n            myChartWater: null,\r\n            legend: [],\r\n            data: [],\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.myChartWater) {\r\n            this.myChartWater.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.myChart()\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.myChartWater != null && this.myChartWater != \"\" && this.myChart != undefined) {\r\n                    this.myChartWater.dispose();\r\n                }\r\n                this.myChartWater = this.$eCharts.init(this.$refs.myChartWater);\r\n                this.myChartWater.clear();\r\n\r\n                // 液位差实时报警\r\n\r\n                groupByYwStatus().then(res => {\r\n                    let dataList = res.data.extra;\r\n                    let max = 0;\r\n                    for (let v of dataList) {\r\n                        max += v.ywStatusCount\r\n                    }\r\n                    for (let v of dataList) {\r\n                        this.legend.push(v.ywStatus + '-' + v.ywStatusCount);\r\n                        let value = (v.ywStatusCount / max).toFixed(2) * 100;\r\n                        this.data.push({\r\n                            value: value,\r\n                            name: v.ywStatus + '-' + v.ywStatusCount,\r\n                            perName: value + \"%\" + '-' + v.ywStatus\r\n                        })\r\n                    }\r\n                    this.$nextTick(() => {\r\n                        this.setOption()\r\n\r\n                    })\r\n                })\r\n\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            let option = {\r\n\r\n                // title: {\r\n                //     text: '液位差实时报警',\r\n                //     top: '10%',\r\n                //     left: '30%',\r\n                //     textStyle: {\r\n                //         fontSize: 25,\r\n                //         fontWeight: '',\r\n                //         color: '#fff'\r\n                //     },\r\n                // },//标题\r\n\r\n                // tooltip: {\r\n                //     trigger: 'item',\r\n                //     formatter: \"{a} <br/>{b}: {c} ({d}%)\",\r\n                //     /*formatter:function(val){   //让series 中的文字进行换行\r\n                //          console.log(val);//查看val属性，可根据里边属性自定义内容\r\n                //          var content = var['name'];\r\n                //          return content;//返回可以含有html中标签\r\n                //      },*/ //自定义鼠标悬浮交互信息提示，鼠标放在饼状图上时触发事件\r\n                // },//提示框，鼠标悬浮交互时的信息提示\r\n                legend: {\r\n                    show: true,\r\n                    // orient: 'vertical',\r\n                    x: 'center',\r\n                    y: \"0%\",\r\n                    itemGap: 15,\r\n                    data: this.legend,\r\n                    textStyle: {\r\n                        color: '#fff',\r\n                        fontSize: 18,\r\n                        padding: [0, 10, 0, 0]\r\n                    },\r\n                },//图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配\r\n                // graphic: {\r\n                //     type: 'text',\r\n                //     left: 'center',\r\n                //     top: 'center',\r\n                //     style: {\r\n                //         text: '用户统计\\n' + '100', //使用“+”可以使每行文字居中\r\n                //         textAlign: 'center',\r\n                //         font: 'italic bolder 16px cursive',\r\n                //         fill: '#000',\r\n                //         width: 30,\r\n                //         height: 30\r\n                //     }\r\n                // },//此例饼状图为圆环中心文字显示属性，这是一个原生图形元素组件，功能很多\r\n                series: [\r\n                    {\r\n                        name: '',\r\n                        type: 'pie',\r\n                        radius: ['25%', '60%'],\r\n                        center: ['50%', '60%'],\r\n\r\n                        label: {\r\n                            show: true,\r\n                           color: '#fff',\r\n                            fontSize: \"21\" ,\r\n                            formatter: function (val) {\r\n                                return val.data.perName.split(\"-\").join(\"\\n\");\r\n                            }\r\n                        },\r\n                        emphasis: {\r\n                            labelLine: {\r\n                                show: true,\r\n                                length: 30,\r\n                                // length2: 40,\r\n                                lineStyle: { color: '#fff' }\r\n\r\n\r\n                            },//基本样式\r\n                            itemStyle: {\r\n                                shadowBlur: 10,\r\n                                shadowOffsetX: 0,\r\n                                shadowColor: 'rgba(0, 0, 0, 0.2)',//鼠标放在区域边框颜色\r\n                                textColor: '#000'\r\n                            }//鼠标放在各个区域的样式\r\n                        },\r\n                        data: this.data,//数据，数据中其他属性，查阅文档\r\n                        color: ['#51CEC6', '#FFB703', '#5FA0FA'],//各个区域颜色\r\n                    },//数组中一个{}元素，一个图，以此可以做出环形图\r\n                ],//系列列表\r\n            };\r\n            this.myChartWater.setOption(option);\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#myChartWater {\r\n    width: 100%;\r\n    height: 500px;\r\n    padding-top: 20px;\r\n}\r\n</style>"], "mappings": "AAOA,SAAAA,eAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,YAAA;MACAC,MAAA;MACAF,IAAA;IACA;EACA;EACAG,cAAA;IACA,SAAAF,YAAA;MACA,KAAAA,YAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IAEA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAV,YAAA,iBAAAA,YAAA,eAAAM,OAAA,IAAAK,SAAA;UACA,KAAAX,YAAA,CAAAY,OAAA;QACA;QACA,KAAAZ,YAAA,QAAAa,QAAA,CAAAC,IAAA,MAAAC,KAAA,CAAAf,YAAA;QACA,KAAAA,YAAA,CAAAG,KAAA;;QAEA;;QAEAN,eAAA,GAAAa,IAAA,CAAAM,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAjB,IAAA,CAAAmB,KAAA;UACA,IAAAC,GAAA;UACA,SAAAC,CAAA,IAAAH,QAAA;YACAE,GAAA,IAAAC,CAAA,CAAAC,aAAA;UACA;UACA,SAAAD,CAAA,IAAAH,QAAA;YACA,KAAAhB,MAAA,CAAAqB,IAAA,CAAAF,CAAA,CAAAG,QAAA,SAAAH,CAAA,CAAAC,aAAA;YACA,IAAAG,KAAA,IAAAJ,CAAA,CAAAC,aAAA,GAAAF,GAAA,EAAAM,OAAA;YACA,KAAA1B,IAAA,CAAAuB,IAAA;cACAE,KAAA,EAAAA,KAAA;cACA1B,IAAA,EAAAsB,CAAA,CAAAG,QAAA,SAAAH,CAAA,CAAAC,aAAA;cACAK,OAAA,EAAAF,KAAA,eAAAJ,CAAA,CAAAG;YACA;UACA;UACA,KAAAlB,SAAA;YACA,KAAAsB,SAAA;UAEA;QACA;MAIA;IACA;IACAA,UAAA;MACA,IAAAC,MAAA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA3B,MAAA;UACA4B,IAAA;UACA;UACAC,CAAA;UACAC,CAAA;UACAC,OAAA;UACAjC,IAAA,OAAAE,MAAA;UACAgC,SAAA;YACAC,KAAA;YACAC,QAAA;YACAC,OAAA;UACA;QACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,MAAA,GACA;UACAvC,IAAA;UACAwC,IAAA;UACAC,MAAA;UACAC,MAAA;UAEAC,KAAA;YACAZ,IAAA;YACAK,KAAA;YACAC,QAAA;YACAO,SAAA,WAAAA,CAAAC,GAAA;cACA,OAAAA,GAAA,CAAA5C,IAAA,CAAA2B,OAAA,CAAAkB,KAAA,MAAAC,IAAA;YACA;UACA;UACAC,QAAA;YACAC,SAAA;cACAlB,IAAA;cACAmB,MAAA;cACA;cACAC,SAAA;gBAAAf,KAAA;cAAA;YAGA;YAAA;YACAgB,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,WAAA;cAAA;cACAC,SAAA;YACA;UACA;UACAvD,IAAA,OAAAA,IAAA;UAAA;UACAmC,KAAA;QACA;QAAA,CACA;MACA;MACA,KAAAlC,YAAA,CAAA2B,SAAA,CAAAC,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}