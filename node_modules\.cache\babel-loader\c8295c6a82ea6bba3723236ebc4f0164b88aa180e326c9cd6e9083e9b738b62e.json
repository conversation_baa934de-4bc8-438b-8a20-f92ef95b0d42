{"ast": null, "code": "import GDPTrend from '@/components/comprehensive/GDPTrend.vue';\nimport RotationData from '@/components/RotationData/RotationData.vue';\nimport { getWrkPrjInfo } from '@/api/userMenu.js';\nimport { IndustrialChangeTrend, EconomicDevelopment } from '@/api/index.js';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      scrollList: [],\n      headerList: [\"项目名称\", \"项目主要负责人\", \"上级主管党组织名称\", \"立项时间\"],\n      styleAll: {\n        width: '94%',\n        height: '310px'\n      },\n      yearAllGdp: {},\n      seasonData: [{\n        name: \"第一季度\",\n        num: 90.7\n      }, {\n        name: \"第二季度\",\n        num: 110.3\n      }, {\n        name: \"第三季度\",\n        num: 103.8\n      }, {\n        name: \"第四季度\",\n        num: 88.4\n      }],\n      // 产业结构\n      IndustrialPer: {}\n    };\n  },\n  components: {\n    RotationData,\n    GDPTrend\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.getWrkPrjInfoList();\n    this.getEconomicDevelopment();\n    this.IndustrialTrend();\n  },\n  updated() {},\n  methods: {\n    // 产业变化情况趋势\n    async IndustrialTrend() {\n      await IndustrialChangeTrend().then(res => {\n        let {\n          extra\n        } = res.data;\n        let max = 0;\n        let data = {};\n        extra.map(res => {\n          if (res.yearTime > max) {\n            max = res.yearTime;\n            data = res;\n          }\n        });\n        this.IndustrialPer = data;\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n    getEconomicDevelopment() {\n      EconomicDevelopment().then(res => {\n        // this.economicDevData = res.data.extra;\n        for (let v of res.data.extra) {\n          if (v.edName == '地区生产总值') {\n            this.yearAllGdp = v;\n          }\n        }\n      }).catch(e => {\n        console.log(e);\n      });\n    },\n    getWrkPrjInfoList() {\n      getWrkPrjInfo({}).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra.records;\n          list.forEach(item => {\n            let {\n              wrkProjectName,\n              wkrProjectLeader,\n              parentPartyOrgName,\n              wrkProjectDate\n            } = item;\n            this.scrollList.push([wrkProjectName, wkrProjectLeader, parentPartyOrgName, wrkProjectDate]);\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["GDPTrend", "RotationData", "getWrkPrjInfo", "IndustrialChangeTrend", "EconomicDevelopment", "name", "data", "scrollList", "headerList", "styleAll", "width", "height", "yearAllGdp", "seasonData", "num", "IndustrialPer", "components", "watch", "computed", "mounted", "getWrkPrjInfoList", "getEconomicDevelopment", "IndustrialTrend", "updated", "methods", "then", "res", "extra", "max", "map", "yearTime", "catch", "err", "console", "log", "v", "ed<PERSON>ame", "e", "code", "list", "records", "for<PERSON>ach", "item", "wrkProjectName", "wkrProjectLeader", "parentPartyOrgName", "wrkProjectDate", "push"], "sources": ["src/views/comprehensiveSituation/components/decision-analysis/left-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leftView\">\r\n        <div class=\"title-box\">\r\n            <span>决策分析</span>\r\n        </div>\r\n        <div class=\"two-title\">综合实力</div>\r\n        <img style=\"width: 96%; height: 650px; margin-top: 10px;\" src=\"@/assets/zhsl.png\" alt=\"\">\r\n        <!-- <RotationData :data=\"scrollList\" :header=\"headerList\" :styleAll=\"styleAll\" class=\"liebiao\">\r\n        </RotationData> -->\r\n        <div class=\"two-title\">地区生产总值走势</div>\r\n        <GDPTrend></GDPTrend>\r\n        <!-- <div class=\"dataInfo\">\r\n            <div class=\"sum\">\r\n                <p class=\"sumNum\">{{ yearAllGdp.gdp }}</p>\r\n                <p>{{ yearAllGdp.unit }}</p>\r\n                <div class=\"year\">\r\n                    {{ yearAllGdp.year }}年\r\n                </div>\r\n            </div>\r\n            <div class=\"seasonData\">\r\n                <div class=\"seasonItems\" v-for=\"(item, index) of seasonData\" :key=\"index\">\r\n                    <div class=\"imgs\">\r\n                        <img src=\"@/assets/images/left_slices/point1.png\" alt=\"\">\r\n                        <img src=\"@/assets/images/left_slices/point2.png\" alt=\"\">\r\n                    </div>\r\n                    <p>{{ item.name }}</p>\r\n                    <p class=\"num\">{{ item.num }}</p>\r\n                </div>\r\n\r\n            </div>\r\n        </div> -->\r\n        <!-- <div class=\"two-title\">本年GDP走势</div>\r\n        <GDPTrend></GDPTrend> -->\r\n        <div class=\"two-title\">产业结构占比</div>\r\n        <div class=\"IndustrialItems\">\r\n            <div>\r\n                <p>{{ (IndustrialPer.priIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                <img src=\"@/assets/images/left_slices/proIcon1.png\" alt=\"\">\r\n                <div class=\"txt\">第一产业</div>\r\n            </div>\r\n            <div>\r\n                <p>{{ (IndustrialPer.secIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                <img src=\"@/assets/images/left_slices/proIcon2.png\" alt=\"\">\r\n                <div class=\"txt\">第二产业</div>\r\n            </div>\r\n            <div>\r\n                <p>{{ (IndustrialPer.terIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                <img src=\"@/assets/images/left_slices/proIcon3.png\" alt=\"\">\r\n                <div class=\"txt\">第三产业</div>\r\n            </div>\r\n\r\n        </div>\r\n        <div class=\"two-title\">产业分布</div>\r\n        <div class=\"proDistributionInfo\">\r\n            <div class=\"leftPic\">\r\n                <img src=\"@/assets/images/left_slices/map.png\" alt=\"\">\r\n                <img class=\"btm\" src=\"@/assets/images/left_slices/bgBtm2.png\" alt=\"\">\r\n            </div>\r\n            <div class=\"areaName\">\r\n                <div class=\"dashed\"></div>\r\n                <div class=\"areaTitle\">\r\n                    <div class=\"circle\">\r\n                        <div></div>\r\n\r\n                    </div>\r\n                    区域名称\r\n                </div>\r\n                <div class=\"areaItem\">\r\n\r\n                    <div>\r\n                        <div class=\"littleBox\">\r\n                            <div class=\"circle\">\r\n                                <div></div>\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                第一产业\r\n                            </div>\r\n\r\n                        </div>\r\n                        <div class=\"numInfo\">\r\n                            <div class=\"sum\">\r\n                                {{ IndustrialPer.priIndustyGdp }}\r\n                            </div>\r\n                            <div class=\"per\">\r\n                                {{ (IndustrialPer.priIndustyGdpRate * 100).toFixed(2) }}%\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div>\r\n                        <div class=\"littleBox\">\r\n                            <div class=\"circle\">\r\n                                <div></div>\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                第二产业\r\n                            </div>\r\n\r\n                        </div>\r\n                        <div class=\"numInfo\">\r\n                            <div class=\"sum\">\r\n                                {{ IndustrialPer.secIndustyGdp }}\r\n\r\n                            </div>\r\n                            <div class=\"per\">\r\n                                {{ (IndustrialPer.secIndustyGdpRate * 100).toFixed(2) }}%\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div>\r\n                        <div class=\"littleBox\">\r\n                            <div class=\"circle\">\r\n                                <div></div>\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                第三产业\r\n                            </div>\r\n\r\n                        </div>\r\n                        <div class=\"numInfo\">\r\n                            <div class=\"sum\">\r\n                                {{ IndustrialPer.terIndustyGdp }}\r\n\r\n                            </div>\r\n                            <div class=\"per\">\r\n                                {{ (IndustrialPer.terIndustyGdpRate * 100).toFixed(2) }}%\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport GDPTrend from '@/components/comprehensive/GDPTrend.vue'\r\nimport RotationData from '@/components/RotationData/RotationData.vue'\r\nimport { getWrkPrjInfo } from '@/api/userMenu.js'\r\nimport { IndustrialChangeTrend, EconomicDevelopment } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'leftView',\r\n    data() {\r\n        return {\r\n            scrollList: [],\r\n            headerList: [\"项目名称\", \"项目主要负责人\", \"上级主管党组织名称\", \"立项时间\"],\r\n            styleAll: {\r\n                width: '94%',\r\n                height: '310px'\r\n            },\r\n            yearAllGdp: {},\r\n            seasonData: [\r\n                {\r\n                    name: \"第一季度\",\r\n                    num: 90.7\r\n                },\r\n                {\r\n                    name: \"第二季度\",\r\n                    num: 110.3\r\n                },\r\n                {\r\n                    name: \"第三季度\",\r\n                    num: 103.8\r\n                },\r\n                {\r\n                    name: \"第四季度\",\r\n                    num: 88.4\r\n                }\r\n            ],\r\n            // 产业结构\r\n            IndustrialPer: {},\r\n        };\r\n    },\r\n    components: {\r\n        RotationData,\r\n        GDPTrend\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.getWrkPrjInfoList();\r\n        this.getEconomicDevelopment();\r\n        this.IndustrialTrend();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 产业变化情况趋势\r\n        async IndustrialTrend() {\r\n            await IndustrialChangeTrend().then(res => {\r\n\r\n                let { extra } = res.data\r\n                let max = 0;\r\n                let data = {}\r\n                extra.map(res => {\r\n                    if (res.yearTime > max) {\r\n                        max = res.yearTime;\r\n                        data = res\r\n                    }\r\n                })\r\n                this.IndustrialPer = data\r\n\r\n            }).catch(err => {\r\n                console.log(err);\r\n            })\r\n        },\r\n        getEconomicDevelopment() {\r\n            EconomicDevelopment().then(res => {\r\n                // this.economicDevData = res.data.extra;\r\n                for (let v of res.data.extra) {\r\n                    if (v.edName == '地区生产总值') {\r\n                        this.yearAllGdp = v\r\n                    }\r\n\r\n                }\r\n            }).catch(e => {\r\n                console.log(e)\r\n            });\r\n        },\r\n\r\n        getWrkPrjInfoList() {\r\n            getWrkPrjInfo({}).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra.records\r\n                    list.forEach((item) => {\r\n                        let { wrkProjectName, wkrProjectLeader, parentPartyOrgName, wrkProjectDate } = item\r\n                        this.scrollList.push([wrkProjectName, wkrProjectLeader, parentPartyOrgName, wrkProjectDate])\r\n                    })\r\n                }\r\n            })\r\n        }\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    width: 700px;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    margin-top: 20px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n.leftView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .dataInfo {\r\n        margin-top: 15px;\r\n        display: flex;\r\n\r\n        .sum {\r\n            display: flex;\r\n            flex-direction: column;\r\n            margin-right: 37px;\r\n            align-items: center;\r\n            width: 254px;\r\n            height: 210px;\r\n            background: url('@/assets/images/left_slices/GDP.png') no-repeat center;\r\n            background-size: 100%;\r\n            color: rgba(166, 174, 184, 1);\r\n\r\n            .sumNum {\r\n                color: rgba(233, 173, 88, 1);\r\n                font-size: 2.7rem;\r\n                font-weight: bold;\r\n                margin-top: -.5rem;\r\n            }\r\n\r\n            .year {\r\n                color: #fff;\r\n                margin-top: 4.8rem;\r\n                font-size: 2rem;\r\n            }\r\n        }\r\n\r\n        .seasonData {\r\n            width: 400px;\r\n            height: 240px;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-around;\r\n\r\n            .seasonItems {\r\n                display: flex;\r\n                font-size: 1.5rem;\r\n                border-bottom: 5px solid;\r\n                /* border-image-source: url(@/assets/images/left_slices/border.png); */\r\n                border-image: url('@/assets/images/left_slices/border.png') 0 0 100% 0 round;\r\n                height: 2.3rem;\r\n\r\n                p {\r\n                    line-height: 2rem;\r\n                    margin-left: 0.5rem;\r\n                }\r\n\r\n                .num {\r\n                    width: 40%;\r\n                    text-align: right;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .IndustrialItems {\r\n        margin-top: 53px;\r\n        width: 94%;\r\n        display: flex;\r\n        justify-content: space-around;\r\n        height: 190px;\r\n        background: url('@/assets/images/left_slices/per.png') no-repeat center;\r\n        background-size: 100%;\r\n\r\n        >div {\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            text-align: center;\r\n            width: 30%;\r\n            margin-right: 1%;\r\n\r\n            p {\r\n                transform: translateY(40%);\r\n                font-size: 1.6rem;\r\n                color: rgba(82, 164, 170);\r\n                font-weight: bold;\r\n                background: linear-gradient(0deg, #68D5DF 30%, #FFFFFF 70%);\r\n                -webkit-background-clip: text;\r\n                -webkit-text-fill-color: transparent;\r\n            }\r\n\r\n            .txt {\r\n                font-size: 1.5rem;\r\n                position: absolute;\r\n                bottom: -0.5rem;\r\n                left: 50%;\r\n                transform: translateX(-50%);\r\n                width: 10rem;\r\n                padding: 2px 0;\r\n                border: 3px solid #0AE5FF;\r\n                background: rgba(10, 229, 255, 0.11);\r\n                border-radius: 1.7rem;\r\n            }\r\n\r\n            img {\r\n                margin-bottom: 6rem;\r\n            }\r\n\r\n            span {\r\n                position: absolute;\r\n                left: 50%;\r\n                top: 0.6rem;\r\n                transform: translateX(-50%);\r\n            }\r\n        }\r\n    }\r\n\r\n    .proDistributionInfo {\r\n        margin-top: 2rem;\r\n        display: flex;\r\n        // justify-content: space-around;\r\n        // height: 18rem;\r\n\r\n        .leftPic {\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            width: 300px;\r\n            height: 300px;\r\n\r\n            img {\r\n                width: 100%;\r\n            }\r\n\r\n            .btm {\r\n                position: absolute;\r\n                width: 140%;\r\n                bottom: 0rem;\r\n                left: -4rem;\r\n            }\r\n        }\r\n\r\n        .areaName {\r\n            width: 300px;\r\n            height: 300px;\r\n            margin-left: 40px;\r\n\r\n            .circle {\r\n                position: relative;\r\n                width: 1rem;\r\n                height: 1rem;\r\n                background: rgba(10, 229, 255, 0.5);\r\n                border-radius: 50%;\r\n                margin-right: 1rem;\r\n\r\n                >div {\r\n                    position: absolute;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 40%;\r\n                    height: 40%;\r\n                    background: #1AF3FD;\r\n                    border-radius: 50%;\r\n                }\r\n            }\r\n\r\n            .areaTitle {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 1.6rem;\r\n                color: #03FFFF;\r\n                margin-bottom: 1.56rem;\r\n                ;\r\n            }\r\n\r\n            .areaItem {\r\n                height: 14rem;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: space-between;\r\n                font-size: 1.5rem;\r\n\r\n                .littleBox {\r\n                    display: flex;\r\n                    align-items: center;\r\n                }\r\n\r\n                >div {\r\n                    width: 100%;\r\n                    display: flex;\r\n                    align-items: center;\r\n\r\n                    .numInfo {\r\n                        margin-left: .5rem;\r\n\r\n                    }\r\n\r\n\r\n                    .littleBox {\r\n                        padding: 0 0.3rem;\r\n                    }\r\n                }\r\n\r\n                >div:nth-child(2) {\r\n                    .txt {\r\n                        background-color: rgba(4, 252, 255, .3);\r\n                    }\r\n\r\n                    .numInfo {\r\n                        background: rgba(117, 165, 222, .3);\r\n                    }\r\n                }\r\n\r\n                >div:nth-child(2) .littleBox {\r\n                    border: 3px solid #0AE5FF;\r\n                    border-radius: 1rem;\r\n                }\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAuIA,OAAAA,QAAA;AACA,OAAAC,YAAA;AACA,SAAAC,aAAA;AACA,SAAAC,qBAAA,EAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,UAAA;MACAC,UAAA,GACA;QACAR,IAAA;QACAS,GAAA;MACA,GACA;QACAT,IAAA;QACAS,GAAA;MACA,GACA;QACAT,IAAA;QACAS,GAAA;MACA,GACA;QACAT,IAAA;QACAS,GAAA;MACA,EACA;MACA;MACAC,aAAA;IACA;EACA;EACAC,UAAA;IACAf,YAAA;IACAD;EACA;EACAiB,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,sBAAA;IACA,KAAAC,eAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACA;IACA,MAAAF,gBAAA;MACA,MAAAnB,qBAAA,GAAAsB,IAAA,CAAAC,GAAA;QAEA;UAAAC;QAAA,IAAAD,GAAA,CAAApB,IAAA;QACA,IAAAsB,GAAA;QACA,IAAAtB,IAAA;QACAqB,KAAA,CAAAE,GAAA,CAAAH,GAAA;UACA,IAAAA,GAAA,CAAAI,QAAA,GAAAF,GAAA;YACAA,GAAA,GAAAF,GAAA,CAAAI,QAAA;YACAxB,IAAA,GAAAoB,GAAA;UACA;QACA;QACA,KAAAX,aAAA,GAAAT,IAAA;MAEA,GAAAyB,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;IACAX,uBAAA;MACAjB,mBAAA,GAAAqB,IAAA,CAAAC,GAAA;QACA;QACA,SAAAS,CAAA,IAAAT,GAAA,CAAApB,IAAA,CAAAqB,KAAA;UACA,IAAAQ,CAAA,CAAAC,MAAA;YACA,KAAAxB,UAAA,GAAAuB,CAAA;UACA;QAEA;MACA,GAAAJ,KAAA,CAAAM,CAAA;QACAJ,OAAA,CAAAC,GAAA,CAAAG,CAAA;MACA;IACA;IAEAjB,kBAAA;MACAlB,aAAA,KAAAuB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAApB,IAAA,CAAAgC,IAAA;UACA,IAAAC,IAAA,GAAAb,GAAA,CAAApB,IAAA,CAAAqB,KAAA,CAAAa,OAAA;UACAD,IAAA,CAAAE,OAAA,CAAAC,IAAA;YACA;cAAAC,cAAA;cAAAC,gBAAA;cAAAC,kBAAA;cAAAC;YAAA,IAAAJ,IAAA;YACA,KAAAnC,UAAA,CAAAwC,IAAA,EAAAJ,cAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,cAAA;UACA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}