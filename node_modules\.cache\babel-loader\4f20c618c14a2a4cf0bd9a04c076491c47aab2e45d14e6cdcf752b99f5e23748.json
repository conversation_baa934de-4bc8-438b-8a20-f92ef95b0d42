{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport routes from './routes.js';\nimport { Message } from 'element-ui';\nVue.use(VueRouter);\nconst router = new VueRouter({\n  mode: 'hash',\n  routes\n});\nrouter.beforeEach((to, from, next) => {\n  let userId = localStorage.getItem('userId');\n  if (to.path == '/AuthorityManagement') {\n    if (userId == 6) {\n      next();\n    } else {\n      if (from.name == null) {\n        next('/');\n      } else {\n        next(from.path);\n      }\n      Message.error({\n        message: '暂无权限进入权限管理'\n      });\n    }\n  } else {\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "Message", "use", "router", "mode", "beforeEach", "to", "from", "next", "userId", "localStorage", "getItem", "path", "name", "error", "message"], "sources": ["D:/Project/HuaQiaoSanQi/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport routes from './routes.js'\nimport { Message } from 'element-ui';\n\nVue.use(VueRouter)\n\nconst router = new VueRouter({\n  mode: 'hash',\n  routes\n})\n\nrouter.beforeEach(\n  (to, from, next) => {\n    let userId = localStorage.getItem('userId')\n    if(to.path=='/AuthorityManagement'){\n      if(userId == 6){\n        next()\n      }else{\n        if(from.name == null){\n          next('/')\n        }else{\n          next(from.path)\n        }\n        Message.error({message:'暂无权限进入权限管理'})\n      }\n    }else{\n      next()\n\n    }\n\n  }\n)\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,OAAO,QAAQ,YAAY;AAEpCH,GAAG,CAACI,GAAG,CAACH,SAAS,CAAC;AAElB,MAAMI,MAAM,GAAG,IAAIJ,SAAS,CAAC;EAC3BK,IAAI,EAAE,MAAM;EACZJ;AACF,CAAC,CAAC;AAEFG,MAAM,CAACE,UAAU,CACf,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAClB,IAAIC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3C,IAAGL,EAAE,CAACM,IAAI,IAAE,sBAAsB,EAAC;IACjC,IAAGH,MAAM,IAAI,CAAC,EAAC;MACbD,IAAI,CAAC,CAAC;IACR,CAAC,MAAI;MACH,IAAGD,IAAI,CAACM,IAAI,IAAI,IAAI,EAAC;QACnBL,IAAI,CAAC,GAAG,CAAC;MACX,CAAC,MAAI;QACHA,IAAI,CAACD,IAAI,CAACK,IAAI,CAAC;MACjB;MACAX,OAAO,CAACa,KAAK,CAAC;QAACC,OAAO,EAAC;MAAY,CAAC,CAAC;IACvC;EACF,CAAC,MAAI;IACHP,IAAI,CAAC,CAAC;EAER;AAEF,CACF,CAAC;AACD,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}