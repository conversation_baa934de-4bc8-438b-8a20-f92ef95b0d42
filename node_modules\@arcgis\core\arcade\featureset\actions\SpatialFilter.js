/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../sources/Empty.js";import t from"../support/FeatureSet.js";import r from"../support/IdSet.js";import{IdState as i,shapeExtent as n}from"../support/shared.js";import{resolve as a,all as s,reject as l}from"../../../core/promiseUtils.js";import{relate as o,crosses as u,touches as h,within as _,overlaps as c,contains as p,intersects as f}from"../../../geometry/geometryEngineAsync.js";class g extends t{constructor(e){super(e),this._relation="",this._relationGeom=null,this._relationString="",this.declaredClass="esri.arcade.featureset.actions.SpatialFilter",this._relationString=e.relationString,this._parent=e.parentfeatureset,this._maxProcessing=40,this._relation=e.relation,this._relationGeom=e.relationGeom}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,null,null,e))).then((t=>(this._checkCancelled(e),this._wset=new r(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset))):a(this._wset)}_isInFeatureSet(e){let t=this._parent._isInFeatureSet(e);return t===i.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?i.Unknown:t)}_getFeature(e,t,r){return this._parent._getFeature(e,t,r)}_getFeatures(e,t,r,i){return this._parent._getFeatures(e,t,r,i)}_featureFromCache(e){return this._parent._featureFromCache(e)}executeSpatialRelationTest(e){if(null===e.geometry)return a(!1);switch(this._relation){case"esriSpatialRelEnvelopeIntersects":{const t=n(this._relationGeom),r=n(e.geometry);return f(t,r)}case"esriSpatialRelIntersects":return f(this._relationGeom,e.geometry);case"esriSpatialRelContains":return p(this._relationGeom,e.geometry);case"esriSpatialRelOverlaps":return c(this._relationGeom,e.geometry);case"esriSpatialRelWithin":return _(this._relationGeom,e.geometry);case"esriSpatialRelTouches":return h(this._relationGeom,e.geometry);case"esriSpatialRelCrosses":return u(this._relationGeom,e.geometry);case"esriSpatialRelRelation":return o(this._relationGeom,e.geometry,this._relationString)}}_fetchAndRefineFeatures(e,t,n){const a=new r([],e,!1,null),l=Math.min(t,e.length);return this._parent._getFeatures(a,-1,l,n).then((()=>{this._checkCancelled(n);const t=[];for(let r=0;r<l;r++){const i=this._parent._featureFromCache(e[r]);t.push(this.executeSpatialRelationTest(i))}return s(t)})).then((r=>{for(let n=0;n<t;n++)!0===r[n]?this._idstates[e[n]]=i.InFeatureSet:this._idstates[e[n]]=i.NotInFeatureSet;return"success"}))}_getFilteredSet(e,t,i,n,a){return this._ensureLoaded().then((()=>this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,i,n,a))).then((e=>{let i;return this._checkCancelled(a),i=null!==t?new r(e._candidates.slice(0).concat(e._known.slice(0)),[],e._ordered,this._clonePageDefinition(e.pagesDefinition)):new r(e._candidates.slice(0),e._known.slice(0),e._ordered,this._clonePageDefinition(e.pagesDefinition)),i}))}_stat(e,t,r,i,n,s,l){return""!==r?a({calculated:!1}):this._parent._stat(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n,s,l).then((a=>!1===a.calculated?null===n&&""===r&&null===i?this._manualStat(e,t,s,l):{calculated:!1}:a))}_canDoAggregates(e,t,r,i,n){return""!==r||null!==i||null===this._parent?a(!1):this._parent._canDoAggregates(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n)}_getAggregatePagesDataSourceDefinition(e,t,r,i,n,a,s){return null===this._parent?l(new Error("Should never be called")):this._parent._getAggregatePagesDataSourceDefinition(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n,a,s)}static registerAction(){t._featuresetFunctions.intersects=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelIntersects",relationGeom:t})},t._featuresetFunctions.envelopeIntersects=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelEnvelopeIntersects",relationGeom:t})},t._featuresetFunctions.contains=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelContains",relationGeom:t})},t._featuresetFunctions.overlaps=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelOverlaps",relationGeom:t})},t._featuresetFunctions.within=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelWithin",relationGeom:t})},t._featuresetFunctions.touches=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelTouches",relationGeom:t})},t._featuresetFunctions.crosses=function(t){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelCrosses",relationGeom:t})},t._featuresetFunctions.relate=function(t,r){return null==t?new e({parentfeatureset:this}):new g({parentfeatureset:this,relation:"esriSpatialRelRelation",relationGeom:t,relationString:r})}}}export{g as default};
