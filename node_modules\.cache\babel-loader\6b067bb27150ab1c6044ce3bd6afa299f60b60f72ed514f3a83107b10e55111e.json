{"ast": null, "code": "import { getWaterManagementList } from '@/api/index.js';\nimport WaterEventInfo from '@/views/dataScreen/fileType/WaterEventInfo/WaterEventInfo.vue';\nimport ReportEvent from '@/components/smartWater/ReportEvent.vue';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      WaterEventData: {},\n      //点击对应的事件详情\n      WaterEventFlag: false,\n      //事件处理情况详情\n      picInfoList: [],\n      //事件处理情况\n      dialogVisible: false,\n      tableData: [],\n      gridList: [{\n        name: '重点区域雨水',\n        people: '闫来山',\n        iphone: '13405187120'\n      }, {\n        name: '道路南标',\n        people: '季益民',\n        iphone: '18912619696'\n      }, {\n        name: '污水A',\n        people: '孙苹',\n        iphone: '15062637815'\n      }, {\n        name: '道路突击标',\n        people: '卢国平',\n        iphone: '13914977373'\n      }, {\n        name: '污水B',\n        people: '徐根福',\n        iphone: '13451799119'\n      }, {\n        name: '雨水西标',\n        people: '姚志良',\n        iphone: '13912696250'\n      }, {\n        name: '道路西标',\n        people: '季长松',\n        iphone: '15050113122'\n      }, {\n        name: '道路东标',\n        people: '陈敏',\n        iphone: '13862628333'\n      }, {\n        name: '雨水东标',\n        people: '王亮',\n        iphone: '13915757042'\n      }, {\n        name: '污水C',\n        people: '张伟',\n        iphone: '13806260218'\n      }],\n      gridType: {\n        name: '网格',\n        data: [{\n          prop: 'name',\n          label: '网格名称'\n        }, {\n          prop: 'people',\n          label: '负责人'\n        }, {\n          prop: 'iphone',\n          label: '电话'\n        }]\n      },\n      dialogType: {},\n      riverEventDealData: [{\n        name: \"河道垦种\",\n        num: 8\n      }, {\n        name: \"垃圾漂浮\",\n        num: 26\n      }, {\n        name: '河水污染',\n        num: 17\n      }, {\n        name: \"河岸垃圾\",\n        num: 14\n      }],\n      riverData: [{\n        name: '巡河总次数',\n        num: '4402'\n      }, {\n        name: \"巡河总时长\",\n        num: '353'\n      }, {\n        name: \"巡河总长度(km)\",\n        num: '9259.84'\n      }],\n      // 网格员巡查\n      diffAreaPeopleNum: [{\n        name: \"河道网格人员\",\n        num: 59\n      }, {\n        name: \"河长网格人员\",\n        num: 137\n      }, {\n        name: \"积水防汛人员\",\n        num: 9\n      }, {\n        name: \"积水地势人员\",\n        num: 10\n      }, {\n        name: '历史积水点人员',\n        num: 12\n      }]\n    };\n  },\n  components: {\n    ReportEvent,\n    WaterEventInfo\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.getWaterDealInfo();\n  },\n  updated() {},\n  methods: {\n    // 网格数展示\n    showPop() {\n      this.dialogVisible = true;\n      this.tableData = this.gridList;\n      this.dialogType = this.gridType;\n    },\n    // 巡河事件处理\n    async getWaterDealInfo() {\n      await getWaterManagementList().then(res => {\n        this.picInfoList = res.data.extra.data.splice(0, 6);\n      });\n    },\n    getWaterEventFlag(val) {\n      this.WaterEventFlag = val;\n    },\n    // 巡河事件处理详情\n    getWaterEventInfo(val) {\n      this.WaterEventFlag = true;\n      this.WaterEventData = val; //点击对应的事件详情\n    }\n  }\n};", "map": {"version": 3, "names": ["getWaterManagementList", "WaterEventInfo", "ReportEvent", "name", "data", "WaterEventData", "WaterEventFlag", "picInfoList", "dialogVisible", "tableData", "gridList", "people", "iphone", "gridType", "prop", "label", "dialogType", "riverEventDealData", "num", "riverData", "diffAreaPeopleNum", "components", "watch", "computed", "mounted", "getWaterDealInfo", "updated", "methods", "showPop", "then", "res", "extra", "splice", "getWaterEventFlag", "val", "getWaterEventInfo"], "sources": ["src/views/smartWater/components/water-decision/left-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leftView\">\r\n        <div class=\"title-box\">\r\n            <span>智慧水务</span>\r\n        </div>\r\n        <div class=\"two-title\">网格员巡查</div>\r\n        <div class=\"wg-box\">\r\n            <div class=\"sum\" @click=\"showPop\">\r\n                <span>网格数</span>\r\n                <span>10</span>\r\n            </div>\r\n            <div class=\"type-box\">\r\n                <div v-for=\"item in diffAreaPeopleNum\">\r\n                    <span>{{ item.name }}</span>\r\n                    <span>{{ item.num }}</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"two-title\">巡河数据管理</div>\r\n        <div class=\"riverMainData\">\r\n            <ul>\r\n                <li v-for=\"(item, index) of riverData\" :key=\"index\">\r\n                    <div class=\"tit\">\r\n                        {{ item.name }}\r\n                    </div>\r\n                    <div class=\"num\">\r\n                        <div>\r\n                            {{ item.num }}\r\n                        </div>\r\n                        <div v-if=\"index == 0\">\r\n                            次\r\n                        </div>\r\n                        <div v-if=\"index == 1\">\r\n                            h\r\n                        </div>\r\n                        <div v-if=\"index == 2\">\r\n                            km\r\n                        </div>\r\n                    </div>\r\n                </li>\r\n\r\n            </ul>\r\n        </div>\r\n        <div class=\"eventSumCharts\">\r\n            <ReportEvent></ReportEvent>\r\n        </div>\r\n        <div class=\"two-title\">事件处理情况</div>\r\n        <div class=\"handingRes\">\r\n            <div class=\"leftNumData\">\r\n                <ul>\r\n                    <li v-for=\"(item, index) of riverEventDealData\" :key=\"index\">\r\n                        <div class=\"num\">\r\n                            {{ item.num }}\r\n                        </div>\r\n                        <div class=\"tit\">{{ item.name }}\r\n                        </div>\r\n                    </li>\r\n\r\n                </ul>\r\n            </div>\r\n            <div class=\"rightSumPro\">\r\n                <div class=\"picAnimation\">\r\n                    <section>\r\n                        <div v-for=\"item of picInfoList\" :key=\"item.id\" @click=\"getWaterEventInfo(item)\">\r\n                            <img :src=\"item.dealImage.split('，')[0]\" width=\"100%\" height=\"100%\">\r\n                        </div>\r\n\r\n                    </section>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <!-- 事件处理详情 -->\r\n        <div class=\"dalogWaterInfo\">\r\n            <WaterEventInfo :WaterEventFlag=\"WaterEventFlag\" :WaterEventData=\"WaterEventData\"\r\n                @sendWaterEventFlag=\"getWaterEventFlag\"></WaterEventInfo>\r\n\r\n        </div>\r\n        <!-- 水质监测弹出框 -->\r\n        <el-dialog class=\"waterDaLog\" :title=\"dialogType.name\" :visible.sync=\"dialogVisible\" width=\"30%\"\r\n            :fullscreen=\"false\" :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\"\r\n            :before-close=\"closeDialog\" append-to-body>\r\n\r\n            <el-table :data=\"tableData\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"300\" :header-cell-style=\"{\r\n                color: '#fff',\r\n                fontWeight: '700',\r\n                backgroundColor: 'rgba(18, 76, 111, .45)',\r\n\r\n            }\">\r\n                <el-table-column v-for=\"(item, index) of dialogType.data\" :key=\"index\" :prop=\"item.prop\"\r\n                    :label=\"item.label\" :show-overflow-tooltip=\"true\">\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWaterManagementList } from '@/api/index.js'\r\n\r\nimport WaterEventInfo from '@/views/dataScreen/fileType/WaterEventInfo/WaterEventInfo.vue'\r\nimport ReportEvent from '@/components/smartWater/ReportEvent.vue'\r\nexport default {\r\n    name: 'leftView',\r\n    data() {\r\n        return {\r\n            WaterEventData: {}, //点击对应的事件详情\r\n            WaterEventFlag: false,//事件处理情况详情\r\n            picInfoList: [],//事件处理情况\r\n            dialogVisible: false,\r\n            tableData: [],\r\n            gridList: [\r\n                {\r\n                    name: '重点区域雨水',\r\n                    people: '闫来山',\r\n                    iphone: '13405187120'\r\n                },\r\n                {\r\n                    name: '道路南标',\r\n                    people: '季益民',\r\n                    iphone: '18912619696'\r\n                },\r\n                {\r\n                    name: '污水A',\r\n                    people: '孙苹',\r\n                    iphone: '15062637815'\r\n                },\r\n                {\r\n                    name: '道路突击标',\r\n                    people: '卢国平',\r\n                    iphone: '13914977373'\r\n                },\r\n                {\r\n                    name: '污水B',\r\n                    people: '徐根福',\r\n                    iphone: '13451799119'\r\n                },\r\n                {\r\n                    name: '雨水西标',\r\n                    people: '姚志良',\r\n                    iphone: '13912696250'\r\n                },\r\n\r\n                {\r\n                    name: '道路西标',\r\n                    people: '季长松',\r\n                    iphone: '15050113122'\r\n                },\r\n                {\r\n                    name: '道路东标',\r\n                    people: '陈敏',\r\n                    iphone: '13862628333'\r\n                },\r\n                {\r\n                    name: '雨水东标',\r\n                    people: '王亮',\r\n                    iphone: '13915757042'\r\n                },\r\n                {\r\n                    name: '污水C',\r\n                    people: '张伟',\r\n                    iphone: '13806260218'\r\n                },\r\n            ],\r\n            gridType: {\r\n                name: '网格',\r\n                data: [\r\n                    { prop: 'name', label: '网格名称' },\r\n                    { prop: 'people', label: '负责人' },\r\n                    { prop: 'iphone', label: '电话' },\r\n                ]\r\n            },\r\n            dialogType: {},\r\n            riverEventDealData: [\r\n                {\r\n                    name: \"河道垦种\",\r\n                    num: 8\r\n                },\r\n                {\r\n                    name: \"垃圾漂浮\",\r\n                    num: 26\r\n                },\r\n                {\r\n                    name: '河水污染',\r\n                    num: 17\r\n                },\r\n                {\r\n                    name: \"河岸垃圾\",\r\n                    num: 14\r\n                }\r\n            ],\r\n            riverData: [\r\n                {\r\n                    name: '巡河总次数',\r\n                    num: '4402'\r\n                },\r\n                {\r\n                    name: \"巡河总时长\",\r\n                    num: '353'\r\n                },\r\n                {\r\n                    name: \"巡河总长度(km)\",\r\n                    num: '9259.84'\r\n                }\r\n            ],\r\n            // 网格员巡查\r\n            diffAreaPeopleNum: [\r\n                {\r\n                    name: \"河道网格人员\",\r\n                    num: 59\r\n                },\r\n                {\r\n                    name: \"河长网格人员\",\r\n                    num: 137\r\n                },\r\n                {\r\n                    name: \"积水防汛人员\",\r\n                    num: 9\r\n                },\r\n                {\r\n                    name: \"积水地势人员\",\r\n                    num: 10\r\n                },\r\n                {\r\n                    name: '历史积水点人员',\r\n                    num: 12\r\n                }\r\n            ],\r\n        };\r\n    },\r\n    components: {\r\n        ReportEvent,\r\n        WaterEventInfo\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.getWaterDealInfo()\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 网格数展示\r\n        showPop() {\r\n            this.dialogVisible = true\r\n            this.tableData = this.gridList\r\n            this.dialogType = this.gridType\r\n        },\r\n        // 巡河事件处理\r\n        async getWaterDealInfo() {\r\n            await getWaterManagementList().then(res => {\r\n                this.picInfoList = res.data.extra.data.splice(0, 6);\r\n\r\n            })\r\n        },\r\n        getWaterEventFlag(val) {\r\n            this.WaterEventFlag = val\r\n\r\n        },\r\n        // 巡河事件处理详情\r\n        getWaterEventInfo(val) {\r\n            this.WaterEventFlag = true\r\n            this.WaterEventData = val; //点击对应的事件详情\r\n\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.waterDaLog {\r\n\r\n    /deep/.el-table,\r\n    /deep/ .el-table tr,\r\n    /deep/ .el-table td,\r\n    /deep/ .el-table th {\r\n        color: #fff;\r\n        background-color: transparent !important;\r\n        border: 0;\r\n    }\r\n\r\n    /deep/ .el-table__cell.is-leaf {\r\n        border: 0;\r\n\r\n    }\r\n\r\n    /deep/ .el-table::before {\r\n        height: 0; // 将高度修改为0\r\n    }\r\n\r\n    ::v-deep .el-dialog {\r\n        /* background-color: transparent; */\r\n        background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n        background-size: 100% 100%;\r\n\r\n        .el-dialog__header {\r\n            padding: 3rem 4rem 0;\r\n\r\n            .el-dialog__title {\r\n                color: #fff;\r\n                font-size: 1.4rem;\r\n\r\n            }\r\n\r\n            .el-dialog__close {\r\n                color: #fff;\r\n                padding: 1.6rem 1rem 0;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n\r\n    ::v-deep .el-table {\r\n\r\n        .el-table__body {\r\n            height: 100%;\r\n\r\n        }\r\n\r\n    }\r\n\r\n}\r\n\r\n.two-title {\r\n    width: 700px;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    margin-top: 20px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n.leftView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .wg-box {\r\n        width: 700px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .sum {\r\n            margin-left: 49px;\r\n            width: 634px;\r\n            height: 160px;\r\n            background-image: url(\"@/assets/images/smartWater/wg-sum.png\");\r\n            background-size: 100% 100%;\r\n            position: relative;\r\n            cursor: pointer;\r\n            >span:nth-child(1) {\r\n                position: absolute;\r\n                left: 52px;\r\n                top: 63px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 32px;\r\n                color: #DFF2FF;\r\n                letter-spacing: 1px;\r\n                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3);\r\n            }\r\n\r\n            >span:nth-child(2) {\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 36px;\r\n                color: #00F8F4;\r\n                position: absolute;\r\n                top: 60px;\r\n                right: 239px;\r\n            }\r\n        }\r\n\r\n        .type-box {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            justify-content: space-around;\r\n\r\n            >div {\r\n                display: flex;\r\n                flex-direction: column;\r\n                margin-bottom: 37px;\r\n\r\n                >span:nth-child(1) {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 500;\r\n                    font-size: 32px;\r\n                    color: #E4F3FF;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    width: 204px;\r\n                    height: 77px;\r\n                    background-size: 100% 100%;\r\n                    background-image: url(\"@/assets/images/smartWater/wg-type.png\");\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: bold;\r\n                    font-size: 36px;\r\n                    color: #00F8F4;\r\n                    line-height: 77px;\r\n                    text-align: center;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .riverMainData {\r\n        margin-top: 54px;\r\n        width: 700px;\r\n\r\n        >ul {\r\n            display: flex;\r\n            height: 5rem;\r\n            font-size: 32px;\r\n\r\n            >li {\r\n                width: 33%;\r\n\r\n                &:nth-of-type(2) {\r\n                    margin: 0 1.4rem;\r\n                }\r\n\r\n                .tit {\r\n                    text-align: center;\r\n                    white-space: nowrap;\r\n                }\r\n\r\n                .num {\r\n                    height: 4rem;\r\n                    line-height: 4rem;\r\n                    display: flex;\r\n                    align-items: flex-end;\r\n                    justify-content: center;\r\n                    color: #66D6FF;\r\n\r\n                    >div:nth-of-type(1) {\r\n                        font-weight: bold;\r\n                        font-size: 36px;\r\n                        text-shadow: -5px 5px 5px #1d457c;\r\n                    }\r\n\r\n                    >div:nth-of-type(2) {\r\n                        font-size: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .handingRes {\r\n\r\n        display: flex;\r\n        margin-top: 1.3rem;\r\n\r\n        .leftNumData {\r\n\r\n            >ul>li {\r\n                width: 173px;\r\n                height: 95px;\r\n                margin-top: 5px;\r\n                background: url('@/assets/images/left_slices/water/river_bg.png') no-repeat center 100%;\r\n                background-size: 100% 100%;\r\n                text-align: center;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: space-between;\r\n                padding: 20px 0;\r\n\r\n                .num {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 500;\r\n                    font-size: 32px;\r\n                    color: #6DFFFF;\r\n                }\r\n\r\n                .tit {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 500;\r\n                    font-size: 30px;\r\n                    color: #E2F3F5;\r\n\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        .rightSumPro {\r\n            width: 450px;\r\n            height: 553px;\r\n\r\n            .pic {\r\n                width: 14rem;\r\n                margin: 0 auto;\r\n                border: 2px dashed #3bc8eb;\r\n                margin-bottom: 1.3rem;\r\n                background-color: rgba(0, 140, 255, .1);\r\n            }\r\n\r\n            .picAnimation section {\r\n                margin-top: 80px;\r\n                margin-left: 53px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAkGA,SAAAA,sBAAA;AAEA,OAAAC,cAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,cAAA;MAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,aAAA;MACAC,SAAA;MACAC,QAAA,GACA;QACAP,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GAEA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,GACA;QACAT,IAAA;QACAQ,MAAA;QACAC,MAAA;MACA,EACA;MACAC,QAAA;QACAV,IAAA;QACAC,IAAA,GACA;UAAAU,IAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,KAAA;QAAA;MAEA;MACAC,UAAA;MACAC,kBAAA,GACA;QACAd,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,EACA;MACAC,SAAA,GACA;QACAhB,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,EACA;MACA;MACAE,iBAAA,GACA;QACAjB,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA;IAEA;EACA;EACAG,UAAA;IACAnB,WAAA;IACAD;EACA;EACAqB,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,gBAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACA;IACAC,QAAA;MACA,KAAApB,aAAA;MACA,KAAAC,SAAA,QAAAC,QAAA;MACA,KAAAM,UAAA,QAAAH,QAAA;IACA;IACA;IACA,MAAAY,iBAAA;MACA,MAAAzB,sBAAA,GAAA6B,IAAA,CAAAC,GAAA;QACA,KAAAvB,WAAA,GAAAuB,GAAA,CAAA1B,IAAA,CAAA2B,KAAA,CAAA3B,IAAA,CAAA4B,MAAA;MAEA;IACA;IACAC,kBAAAC,GAAA;MACA,KAAA5B,cAAA,GAAA4B,GAAA;IAEA;IACA;IACAC,kBAAAD,GAAA;MACA,KAAA5B,cAAA;MACA,KAAAD,cAAA,GAAA6B,GAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}