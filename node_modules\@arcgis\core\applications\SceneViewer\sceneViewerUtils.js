/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../core/Logger.js";import{isHostedAgolService as r}from"../../layers/support/arcgisLayerUrl.js";import{renderSVG as t}from"../../symbols/support/svgUtils.js";import{viewingModeFromString as n}from"../../views/ViewingMode.js";import{isSpatialReferenceSupported as o}from"../../views/support/spatialReferenceSupport.js";async function a(e,r,t="awaiting-feature-creation-info"){await e.startCreateFeaturesWorkflow(r,t)}function i(e){return e.numberOfFeatureTemplates}function u(e){const r=e.activeWorkflow;return"create-features"===(null==r?void 0:r.type)?r.numPendingFeatures:0}function s(e){return e.state}function f(e){var r;return null==(r=e.activeWorkflow)?void 0:r.type}const c=e.getLogger("sceneViewer.appState.saveState");function p(e,r){return c.warn(e,r)}function l(e){return r(e)}function m(e,r,n,o){return t(e,r,n,o)}function g(e){return!e.canNotSaveAs()}function v(e,r){return o(e,n(r))}function w(e,r,t){return e.getOrCreateCompatible(r,t)}export{g as canSaveAs,u as createFeaturesWorkflowGetNumPendingFeatures,s as getActiveWorkflowState,f as getActiveWorkflowType,w as getOrCreateCompatibleTileInfo,l as isHostedAgolServiceUrl,v as isSpatialReferenceSupported,i as numberOfFeatureTemplates,m as renderSVG,p as saveStateWarning,a as startCreateFeaturesWorkflow};
