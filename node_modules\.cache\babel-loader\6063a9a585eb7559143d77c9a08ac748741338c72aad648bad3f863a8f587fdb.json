{"ast": null, "code": "const genID = () => {\n  let uid = Math.floor(Math.random() * 100) + 0.12;\n  return uid;\n};\nimport \"echarts-gl\";\nexport default {\n  name: 'WaterQualityDetection',\n  props: {\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '13rem'\n    },\n    fontSize: {\n      type: Number,\n      default: 22\n    },\n    peopleCount: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  watch: {\n    peopleCount: {\n      handler(nv) {\n        this.uid = genID();\n        this.option = '';\n        this.waterQuality = '';\n        if (nv.peopleCount) {\n          let params = [{\n            name: '男',\n            value: nv.maleCount\n          }, {\n            name: '女',\n            value: nv.femaleCount\n          }];\n          setTimeout(() => {\n            this.myChart(params);\n          }, 10);\n        }\n      }\n    }\n  },\n  data() {\n    return {\n      waterQuality: null,\n      option: {},\n      uid: null,\n      data: [{\n        name: \"已达标\",\n        value: 925\n      }, {\n        name: \"未达标\",\n        value: 286\n      }],\n      color: ['rgba(5, 169, 252,1)', 'rgba(0, 215, 233,1)']\n    };\n  },\n  beforeDestroy() {\n    if (this.waterQuality) {\n      this.waterQuality.clear();\n    }\n  },\n  mounted() {\n    this.uid = genID();\n    this.$nextTick(() => {\n      if (this.height == '13rem') {\n        this.myChart(this.data);\n      }\n    });\n  },\n  created() {},\n  methods: {\n    myChart(data) {\n      // 构建3d饼状图\n\n      // 传入数据生成 option\n      this.option = this.getPie3D(data, 0.8);\n      let dom = document.getElementById(this.uid);\n      this.waterQuality = this.$eCharts.init(dom);\n      this.setOptions(data);\n    },\n    setOptions(data) {\n      // 是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption\n      this.option.series.push({\n        name: 'pie2d',\n        type: 'pie',\n        startAngle: 70,\n        // 起始角度，支持范围[0, 360]。\n        clockwise: false,\n        // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式\n        radius: ['20%', '50%'],\n        center: ['50%', '50%'],\n        data: data,\n        itemStyle: {\n          color: 'rgba(255,255,255,0)',\n          opacity: 1\n        },\n        label: {\n          alignTo: 'edge',\n          formatter: ele => {\n            let bfb = ((ele.data.endRatio - ele.data.startRatio) * 100).toFixed(2);\n            let str = `${ele.value}/${bfb}%\\n\\n\\t${ele.name}`;\n            return str;\n          },\n          color: \"#fff\",\n          fontSize: this.fontSize,\n          minMargin: 5,\n          edgeDistance: 10,\n          lineHeight: 10,\n          rich: {\n            name: {\n              fontSize: this.fontSize,\n              color: '#fff'\n            },\n            value: {\n              fontSize: this.fontSize,\n              color: '#fff',\n              fontWeight: 'bold'\n            }\n          }\n        },\n        labelLine: {\n          length: 25,\n          length2: 15,\n          maxSurfaceAngle: 80,\n          lineStyle: {\n            color: \"#fff\",\n            // 改变标示线的颜色\n            width: 1,\n            type: 'solid' // 线的类型\n          }\n        }\n      });\n      if (this.waterQuality) {\n        this.waterQuality.setOption(this.option);\n        this.bindListen();\n      }\n    },\n    // 生成模拟 3D 饼图的配置项\n    getPie3D(pieData, internalDiameterRatio) {\n      const series = [];\n      // 总和\n      let sumValue = 0;\n      let startValue = 0;\n      let endValue = 0;\n      const legendData = [];\n      // const k = typeof internalDiameterRatio !== 'undefined'\n      //   ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)\n      //   : 1 / 3;\n      const k = 1;\n      // 为每一个饼图数据，生成一个 series-surface 配置\n      for (let i = 0; i < pieData.length; i += 1) {\n        sumValue += pieData[i].value;\n        const seriesItem = {\n          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,\n          type: 'surface',\n          parametric: true,\n          wireframe: {\n            show: false\n          },\n          itemStyle: {\n            color: this.color[i],\n            opacity: 1\n          },\n          pieData: pieData[i],\n          pieStatus: {\n            selected: false,\n            hovered: false,\n            k\n          }\n        };\n        if (typeof pieData[i].itemStyle !== 'undefined') {\n          const {\n            itemStyle\n          } = pieData[i];\n\n          // eslint-disable-next-line no-unused-expressions\n          typeof pieData[i].itemStyle.color !== 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;\n          // eslint-disable-next-line no-unused-expressions\n          typeof pieData[i].itemStyle.opacity !== 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;\n          seriesItem.itemStyle = itemStyle;\n        }\n        series.push(seriesItem);\n      }\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\n      for (let i = 0; i < series.length; i += 1) {\n        endValue = startValue + series[i].pieData.value;\n        series[i].pieData.startRatio = startValue / sumValue;\n        series[i].pieData.endRatio = endValue / sumValue;\n        series[i].parametricEquation = this.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, series[i].pieData.value);\n        startValue = endValue;\n        legendData.push(series[i].name);\n      }\n      const boxHeight = this.getHeight3D(series, 15);\n\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\n      const option = {\n        animation: true,\n        // x,y,z调整大小的\n        xAxis3D: {\n          min: -1,\n          max: 1\n        },\n        yAxis3D: {\n          min: -1,\n          max: 1\n        },\n        zAxis3D: {\n          min: -1,\n          max: 1\n        },\n        grid3D: {\n          show: false,\n          boxHeight: boxHeight,\n          // 饼环的高度\n          top: '-10%',\n          left: '0%',\n          bottom: '10%',\n          viewControl: {\n            // 3d效果可以放大、旋转等，请自己去查看官方配置\n            alpha: 30,\n            //z轴旋转角度\n            beta: -20,\n            //x轴旋转角度\n            rotateSensitivity: 0,\n            // 旋转\n            zoomSensitivity: 0,\n            // 缩放\n            panSensitivity: 0,\n            // 平移\n            autoRotate: false,\n            // 旋转\n            distance: 205 // 整视角到主体的距离，类似调整zoom\n          },\n          // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。\n          postEffect: {\n            // 配置这项会出现锯齿，请自己去查看官方配置有办法解决\n            enable: false,\n            bloom: {\n              enable: true,\n              bloomIntensity: 0.1\n            },\n            SSAO: {\n              enable: true,\n              quality: 'medium',\n              radius: 1\n            }\n            // temporalSuperSampling: {\n            //   enable: true,\n            // },\n          }\n        },\n        series\n      };\n      return option;\n    },\n    // 获取3d丙图的最高扇区的高度\n    getHeight3D(series, height) {\n      series.sort((a, b) => b.pieData.value - a.pieData.value);\n      return height * 25 / series[0].pieData.value;\n    },\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\n    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {\n      // 计算\n      const midRatio = (startRatio + endRatio) / 2;\n      const startRadian = startRatio * Math.PI * 2;\n      const endRadian = endRatio * Math.PI * 2;\n      const midRadian = midRatio * Math.PI * 2;\n      // 如果只有一个扇形，则不实现选中效果。\n      if (startRatio === 0 && endRatio === 1) {\n        // eslint-disable-next-line no-param-reassign\n        isSelected = false;\n      }\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\n      // eslint-disable-next-line no-param-reassign\n      k = typeof k !== 'undefined' ? k : 1 / 3;\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\n      // const offsetX = isSelected ? 0 : Math.cos(midRadian) * 0.2 ;\n      // const offsetY = isSelected ? 0 : Math.sin(midRadian) * 0.2;\n      const offsetX = isHovered ? Math.cos(midRadian) * 0.2 : Math.cos(midRadian) * 0.1;\n      const offsetY = isHovered ? Math.sin(midRadian) * 0.2 : Math.sin(midRadian) * 0.1;\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\n      const hoverRate = isHovered ? 1.01 : 1;\n      // 返回曲面参数方程\n      return {\n        u: {\n          min: -Math.PI,\n          max: Math.PI * 3,\n          step: Math.PI / 32\n        },\n        v: {\n          min: 0,\n          max: Math.PI * 2,\n          step: Math.PI / 20\n        },\n        x(u, v) {\n          if (u < startRadian) {\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\n          }\n          if (u > endRadian) {\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\n          }\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;\n        },\n        y(u, v) {\n          if (u < startRadian) {\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\n          }\n          if (u > endRadian) {\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\n          }\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;\n        },\n        z(u, v) {\n          if (u < -Math.PI * 0.5) {\n            return Math.sin(u);\n          }\n          if (u > Math.PI * 2.5) {\n            return Math.sin(u) * h * 0.1;\n          }\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;\n        }\n      };\n    },\n    fomatFloat(num, n) {\n      let f = parseFloat(num);\n      // eslint-disable-next-line no-restricted-globals\n      if (isNaN(f)) {\n        return false;\n      }\n      // eslint-disable-next-line no-restricted-properties\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂\n      let s = f.toString();\n      let rs = s.indexOf('.');\n      // 判定如果是整数，增加小数点再补0\n      if (rs < 0) {\n        rs = s.length;\n        s += '.';\n      }\n      while (s.length <= rs + n) {\n        s += '0';\n      }\n      return s;\n    },\n    bindListen() {\n      // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。\n      const that = this;\n      let selectedIndex = '';\n      let hoveredIndex = '';\n      // 监听点击事件，实现选中效果（单选）\n\n      // 修正取消高亮失败的 bug\n      this.waterQuality.on('globalout', () => {\n        // 准备重新渲染扇形所需的参数\n        let isSelected;\n        let isHovered;\n        let startRatio;\n        let endRatio;\n        let k;\n        if (hoveredIndex !== '') {\n          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。\n          isSelected = that.option.series[hoveredIndex].pieStatus.selected;\n          isHovered = false;\n          // eslint-disable-next-line prefer-destructuring\n          k = that.option.series[hoveredIndex].pieStatus.k;\n          // eslint-disable-next-line prefer-destructuring\n          startRatio = that.option.series[hoveredIndex].pieData.startRatio;\n          // eslint-disable-next-line prefer-destructuring\n          endRatio = that.option.series[hoveredIndex].pieData.endRatio;\n          // 对当前点击的扇形，执行取消高亮操作（对 option 更新）\n          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, 35); // 取消高亮时扇形的高度\n          that.option.series[hoveredIndex].pieStatus.hovered = isHovered;\n          // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空\n          hoveredIndex = '';\n        }\n        // 使用更新后的 option，渲染图表\n        this.waterQuality.setOption(that.option);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["genID", "uid", "Math", "floor", "random", "name", "props", "width", "type", "String", "default", "height", "fontSize", "Number", "peopleCount", "Object", "watch", "handler", "nv", "option", "waterQuality", "params", "value", "maleCount", "femaleCount", "setTimeout", "myChart", "data", "color", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "created", "methods", "getPie3D", "dom", "document", "getElementById", "$eCharts", "init", "setOptions", "series", "push", "startAngle", "clockwise", "radius", "center", "itemStyle", "opacity", "label", "alignTo", "formatter", "ele", "bfb", "endRatio", "startRatio", "toFixed", "str", "<PERSON><PERSON><PERSON><PERSON>", "edgeDistance", "lineHeight", "rich", "fontWeight", "labelLine", "length", "length2", "maxSurfaceAngle", "lineStyle", "setOption", "bindListen", "pieData", "internalDiameterRatio", "sumValue", "startValue", "endValue", "legendData", "k", "i", "seriesItem", "parametric", "wireframe", "show", "pieStatus", "selected", "hovered", "parametricEquation", "getParametricEquation", "boxHeight", "getHeight3D", "animation", "xAxis3D", "min", "max", "yAxis3D", "zAxis3D", "grid3D", "top", "left", "bottom", "viewControl", "alpha", "beta", "rotateSensitivity", "zoomSensitivity", "panSensitivity", "autoRotate", "distance", "postEffect", "enable", "bloom", "bloomIntensity", "SSAO", "quality", "sort", "a", "b", "isSelected", "isHovered", "h", "midRatio", "startRadian", "PI", "endRadian", "midRadian", "offsetX", "cos", "offsetY", "sin", "hoverRate", "u", "step", "v", "x", "y", "z", "fomatFloat", "num", "n", "f", "parseFloat", "isNaN", "round", "pow", "s", "toString", "rs", "indexOf", "that", "selectedIndex", "hoveredIndex", "on"], "sources": ["src/components/smartWater/WaterQualityDetection.vue"], "sourcesContent": ["<template>\r\n    <div class=\"box\">\r\n        <div :id=\"uid\" ref=\"waterQuality\" :style=\"{ 'width': width, 'height': height }\">\r\n\r\n        </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nconst genID = () => {\r\n    let uid = Math.floor(Math.random() * 100) + 0.12;\r\n    return uid;\r\n}\r\nimport \"echarts-gl\";\r\nexport default {\r\n    name: 'WaterQualityDetection',\r\n    props: {\r\n        width: {\r\n            type: String,\r\n            default: '100%'\r\n        },\r\n        height: {\r\n            type: String,\r\n            default: '13rem'\r\n        },\r\n        fontSize: {\r\n            type: Number,\r\n            default: 22\r\n        },\r\n        peopleCount: {\r\n            type: Object,\r\n            default: () => {\r\n            }\r\n        },\r\n    },\r\n    watch: {\r\n        peopleCount: {\r\n            handler(nv) {\r\n                this.uid = genID();\r\n                this.option = '';\r\n                this.waterQuality = ''\r\n                if (nv.peopleCount) {\r\n                    let params = [\r\n                        { name: '男', value: nv.maleCount },\r\n                        { name: '女', value: nv.femaleCount }\r\n                    ]\r\n                    setTimeout(() => {\r\n                        this.myChart(params);\r\n                    }, 10)\r\n\r\n                }\r\n            }\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            waterQuality: null,\r\n            option: {},\r\n            uid: null,\r\n            data: [\r\n                { name: \"已达标\", value: 925 },\r\n                { name: \"未达标\", value: 286 },\r\n            ],\r\n            color: ['rgba(5, 169, 252,1)', 'rgba(0, 215, 233,1)']\r\n\r\n        }\r\n    },\r\n    beforeDestroy() {\r\n        if (this.waterQuality) {\r\n            this.waterQuality.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.uid = genID();\r\n\r\n        this.$nextTick(() => {\r\n            if (this.height == '13rem') {\r\n                this.myChart(this.data);\r\n            }\r\n        });\r\n\r\n    },\r\n    created() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        myChart(data) {\r\n            // 构建3d饼状图\r\n\r\n            // 传入数据生成 option\r\n            this.option = this.getPie3D(data, 0.8);\r\n            let dom = document.getElementById(this.uid);\r\n            this.waterQuality = this.$eCharts.init(dom)\r\n            this.setOptions(data)\r\n        },\r\n        setOptions(data) {\r\n\r\n\r\n            // 是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption\r\n            this.option.series.push({\r\n                name: 'pie2d',\r\n                type: 'pie',\r\n\r\n                startAngle: 70, // 起始角度，支持范围[0, 360]。\r\n                clockwise: false, // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式\r\n                radius: ['20%', '50%'],\r\n                center: ['50%', '50%'],\r\n                data: data,\r\n                itemStyle: {\r\n                    color: 'rgba(255,255,255,0)',\r\n                    opacity: 1,\r\n\r\n                },\r\n\r\n\r\n                label: {\r\n                    alignTo: 'edge',\r\n                    formatter: (ele) => {\r\n                        let bfb = (\r\n                            (ele.data.endRatio - ele.data.startRatio) *\r\n                            100\r\n                        ).toFixed(2);\r\n                        let str = `${ele.value}/${bfb}%\\n\\n\\t${ele.name}`;\r\n                        return str\r\n                    },\r\n\r\n                    color: \"#fff\",\r\n                    fontSize: this.fontSize,\r\n\r\n                    minMargin: 5, \r\n                    edgeDistance: 10,\r\n                    lineHeight: 10,\r\n                    rich: {\r\n                        name: {\r\n                            fontSize: this.fontSize,\r\n                            color: '#fff',\r\n\r\n                        },\r\n                        value: {\r\n                            fontSize: this.fontSize,\r\n                            color: '#fff',\r\n                            fontWeight: 'bold',\r\n                        }\r\n                    },\r\n                },\r\n\r\n\r\n                labelLine: {\r\n                    length: 25,\r\n                    length2: 15,\r\n                    maxSurfaceAngle: 80,\r\n\r\n                    lineStyle: {\r\n                        color: \"#fff\", // 改变标示线的颜色\r\n                        width: 1,\r\n                        type: 'solid', // 线的类型\r\n                    },\r\n\r\n                },\r\n\r\n            });\r\n            if(this.waterQuality){\r\n                this.waterQuality.setOption(this.option);\r\n                this.bindListen();\r\n\r\n            }\r\n        },\r\n        // 生成模拟 3D 饼图的配置项\r\n        getPie3D(pieData, internalDiameterRatio) {\r\n            const series = [];\r\n            // 总和\r\n            let sumValue = 0;\r\n            let startValue = 0;\r\n            let endValue = 0;\r\n            const legendData = [];\r\n            // const k = typeof internalDiameterRatio !== 'undefined'\r\n            //   ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)\r\n            //   : 1 / 3;\r\n            const k = 1\r\n            // 为每一个饼图数据，生成一个 series-surface 配置\r\n            for (let i = 0; i < pieData.length; i += 1) {\r\n                sumValue += pieData[i].value;\r\n                const seriesItem = {\r\n                    name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,\r\n                    type: 'surface',\r\n                    parametric: true,\r\n                    wireframe: {\r\n                        show: false,\r\n                    },\r\n                    itemStyle: {\r\n                        color: this.color[i],\r\n                        opacity: 1,\r\n\r\n                    },\r\n                    pieData: pieData[i],\r\n                    pieStatus: {\r\n                        selected: false,\r\n                        hovered: false,\r\n                        k,\r\n                    },\r\n                };\r\n\r\n                if (typeof pieData[i].itemStyle !== 'undefined') {\r\n                    const { itemStyle } = pieData[i];\r\n\r\n                    // eslint-disable-next-line no-unused-expressions\r\n                    typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;\r\n                    // eslint-disable-next-line no-unused-expressions\r\n                    typeof pieData[i].itemStyle.opacity !== 'undefined'\r\n                        ? (itemStyle.opacity = pieData[i].itemStyle.opacity)\r\n                        : null;\r\n\r\n                    seriesItem.itemStyle = itemStyle;\r\n                }\r\n                series.push(seriesItem);\r\n            }\r\n            // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\r\n            // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\r\n            for (let i = 0; i < series.length; i += 1) {\r\n                endValue = startValue + series[i].pieData.value;\r\n\r\n                series[i].pieData.startRatio = startValue / sumValue;\r\n                series[i].pieData.endRatio = endValue / sumValue;\r\n                series[i].parametricEquation = this.getParametricEquation(\r\n                    series[i].pieData.startRatio,\r\n                    series[i].pieData.endRatio,\r\n                    false,\r\n                    false,\r\n                    k,\r\n\r\n                    series[i].pieData.value,\r\n                );\r\n\r\n                startValue = endValue;\r\n\r\n                legendData.push(series[i].name);\r\n            }\r\n\r\n            const boxHeight = this.getHeight3D(series, 15)\r\n\r\n            // 准备待返回的配置项，把准备好的 legendData、series 传入。\r\n            const option = {\r\n                animation: true,\r\n                // x,y,z调整大小的\r\n                xAxis3D: {\r\n                    min: -1,\r\n                    max: 1,\r\n                },\r\n                yAxis3D: {\r\n                    min: -1,\r\n                    max: 1,\r\n                },\r\n                zAxis3D: {\r\n                    min: -1,\r\n                    max: 1,\r\n                },\r\n\r\n                grid3D: {\r\n                    show: false,\r\n                    boxHeight: boxHeight, // 饼环的高度\r\n                    top: '-10%',\r\n                    left: '0%',\r\n                    bottom: '10%',\r\n                    viewControl: {\r\n                        // 3d效果可以放大、旋转等，请自己去查看官方配置\r\n                        alpha: 30,//z轴旋转角度\r\n                        beta: -20,//x轴旋转角度\r\n                        rotateSensitivity: 0, // 旋转\r\n                        zoomSensitivity: 0, // 缩放\r\n                        panSensitivity: 0, // 平移\r\n                        autoRotate: false, // 旋转\r\n                        distance: 205, // 整视角到主体的距离，类似调整zoom\r\n                    },\r\n                    // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。\r\n                    postEffect: {\r\n                        // 配置这项会出现锯齿，请自己去查看官方配置有办法解决\r\n                        enable: false,\r\n                        bloom: {\r\n                            enable: true,\r\n                            bloomIntensity: 0.1,\r\n                        },\r\n                        SSAO: {\r\n                            enable: true,\r\n                            quality: 'medium',\r\n                            radius: 1,\r\n                        },\r\n                        // temporalSuperSampling: {\r\n                        //   enable: true,\r\n                        // },\r\n                    },\r\n                },\r\n                series,\r\n            };\r\n            return option;\r\n        },\r\n\r\n        // 获取3d丙图的最高扇区的高度\r\n        getHeight3D(series, height) {\r\n            series.sort((a, b) => (b.pieData.value - a.pieData.value));\r\n            return height * 25 / series[0].pieData.value;\r\n        },\r\n\r\n        // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\r\n        getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {\r\n            // 计算\r\n            const midRatio = (startRatio + endRatio) / 2;\r\n            const startRadian = startRatio * Math.PI * 2;\r\n            const endRadian = endRatio * Math.PI * 2;\r\n            const midRadian = midRatio * Math.PI * 2;\r\n            // 如果只有一个扇形，则不实现选中效果。\r\n            if (startRatio === 0 && endRatio === 1) {\r\n                // eslint-disable-next-line no-param-reassign\r\n                isSelected = false;\r\n            }\r\n            // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\r\n            // eslint-disable-next-line no-param-reassign\r\n            k = typeof k !== 'undefined' ? k : 1 / 3;\r\n            // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\r\n            // const offsetX = isSelected ? 0 : Math.cos(midRadian) * 0.2 ;\r\n            // const offsetY = isSelected ? 0 : Math.sin(midRadian) * 0.2;\r\n            const offsetX = isHovered ? Math.cos(midRadian) * 0.2 : Math.cos(midRadian) * 0.1;\r\n            const offsetY = isHovered ? Math.sin(midRadian) * 0.2 : Math.sin(midRadian) * 0.1;\r\n            // 计算高亮效果的放大比例（未高亮，则比例为 1）\r\n            const hoverRate = isHovered ? 1.01 : 1;\r\n            // 返回曲面参数方程\r\n            return {\r\n                u: {\r\n                    min: -Math.PI,\r\n                    max: Math.PI * 3,\r\n                    step: Math.PI / 32,\r\n                },\r\n                v: {\r\n                    min: 0,\r\n                    max: Math.PI * 2,\r\n                    step: Math.PI / 20,\r\n                },\r\n                x(u, v) {\r\n                    if (u < startRadian) {\r\n                        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n                    }\r\n                    if (u > endRadian) {\r\n                        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n                    }\r\n                    return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n                },\r\n                y(u, v) {\r\n                    if (u < startRadian) {\r\n                        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n                    }\r\n                    if (u > endRadian) {\r\n                        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n                    }\r\n                    return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n                },\r\n                z(u, v) {\r\n                    if (u < -Math.PI * 0.5) {\r\n                        return Math.sin(u);\r\n                    }\r\n                    if (u > Math.PI * 2.5) {\r\n                        return Math.sin(u) * h * 0.1;\r\n                    }\r\n                    return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;\r\n                },\r\n            };\r\n        },\r\n\r\n        fomatFloat(num, n) {\r\n            let f = parseFloat(num);\r\n            // eslint-disable-next-line no-restricted-globals\r\n            if (isNaN(f)) {\r\n                return false;\r\n            }\r\n            // eslint-disable-next-line no-restricted-properties\r\n            f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂\r\n            let s = f.toString();\r\n            let rs = s.indexOf('.');\r\n            // 判定如果是整数，增加小数点再补0\r\n            if (rs < 0) {\r\n                rs = s.length;\r\n                s += '.';\r\n            }\r\n            while (s.length <= rs + n) {\r\n                s += '0';\r\n            }\r\n            return s;\r\n        },\r\n\r\n        bindListen() {\r\n            // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。\r\n            const that = this;\r\n            let selectedIndex = '';\r\n            let hoveredIndex = '';\r\n            // 监听点击事件，实现选中效果（单选）\r\n\r\n            // 修正取消高亮失败的 bug\r\n            this.waterQuality.on('globalout', () => {\r\n                // 准备重新渲染扇形所需的参数\r\n                let isSelected;\r\n                let isHovered;\r\n                let startRatio;\r\n                let endRatio;\r\n                let k;\r\n                if (hoveredIndex !== '') {\r\n                    // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。\r\n                    isSelected = that.option.series[hoveredIndex].pieStatus.selected;\r\n                    isHovered = false;\r\n                    // eslint-disable-next-line prefer-destructuring\r\n                    k = that.option.series[hoveredIndex].pieStatus.k;\r\n                    // eslint-disable-next-line prefer-destructuring\r\n                    startRatio = that.option.series[hoveredIndex].pieData.startRatio;\r\n                    // eslint-disable-next-line prefer-destructuring\r\n                    endRatio = that.option.series[hoveredIndex].pieData.endRatio;\r\n                    // 对当前点击的扇形，执行取消高亮操作（对 option 更新）\r\n                    that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,\r\n                        isSelected,\r\n                        isHovered, k, 35); // 取消高亮时扇形的高度\r\n                    that.option.series[hoveredIndex].pieStatus.hovered = isHovered;\r\n                    // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空\r\n                    hoveredIndex = '';\r\n                }\r\n                // 使用更新后的 option，渲染图表\r\n                this.waterQuality.setOption(that.option);\r\n            });\r\n        }\r\n\r\n\r\n\r\n    },\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped></style>"], "mappings": "AAUA,MAAAA,KAAA,GAAAA,CAAA;EACA,IAAAC,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;EACA,OAAAH,GAAA;AACA;AACA;AACA;EACAI,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA;IACA;IACAI,WAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA,EAAAA,CAAA,MACA;IACA;EACA;EACAM,KAAA;IACAF,WAAA;MACAG,QAAAC,EAAA;QACA,KAAAjB,GAAA,GAAAD,KAAA;QACA,KAAAmB,MAAA;QACA,KAAAC,YAAA;QACA,IAAAF,EAAA,CAAAJ,WAAA;UACA,IAAAO,MAAA,IACA;YAAAhB,IAAA;YAAAiB,KAAA,EAAAJ,EAAA,CAAAK;UAAA,GACA;YAAAlB,IAAA;YAAAiB,KAAA,EAAAJ,EAAA,CAAAM;UAAA,EACA;UACAC,UAAA;YACA,KAAAC,OAAA,CAAAL,MAAA;UACA;QAEA;MACA;IACA;EACA;EACAM,KAAA;IACA;MACAP,YAAA;MACAD,MAAA;MACAlB,GAAA;MACA0B,IAAA,GACA;QAAAtB,IAAA;QAAAiB,KAAA;MAAA,GACA;QAAAjB,IAAA;QAAAiB,KAAA;MAAA,EACA;MACAM,KAAA;IAEA;EACA;EACAC,cAAA;IACA,SAAAT,YAAA;MACA,KAAAA,YAAA,CAAAU,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAA9B,GAAA,GAAAD,KAAA;IAEA,KAAAgC,SAAA;MACA,SAAArB,MAAA;QACA,KAAAe,OAAA,MAAAC,IAAA;MACA;IACA;EAEA;EACAM,QAAA,GAEA;EAEAC,OAAA;IACAR,QAAAC,IAAA;MACA;;MAEA;MACA,KAAAR,MAAA,QAAAgB,QAAA,CAAAR,IAAA;MACA,IAAAS,GAAA,GAAAC,QAAA,CAAAC,cAAA,MAAArC,GAAA;MACA,KAAAmB,YAAA,QAAAmB,QAAA,CAAAC,IAAA,CAAAJ,GAAA;MACA,KAAAK,UAAA,CAAAd,IAAA;IACA;IACAc,WAAAd,IAAA;MAGA;MACA,KAAAR,MAAA,CAAAuB,MAAA,CAAAC,IAAA;QACAtC,IAAA;QACAG,IAAA;QAEAoC,UAAA;QAAA;QACAC,SAAA;QAAA;QACAC,MAAA;QACAC,MAAA;QACApB,IAAA,EAAAA,IAAA;QACAqB,SAAA;UACApB,KAAA;UACAqB,OAAA;QAEA;QAGAC,KAAA;UACAC,OAAA;UACAC,SAAA,EAAAC,GAAA;YACA,IAAAC,GAAA,IACA,CAAAD,GAAA,CAAA1B,IAAA,CAAA4B,QAAA,GAAAF,GAAA,CAAA1B,IAAA,CAAA6B,UAAA,IACA,KACAC,OAAA;YACA,IAAAC,GAAA,MAAAL,GAAA,CAAA/B,KAAA,IAAAgC,GAAA,UAAAD,GAAA,CAAAhD,IAAA;YACA,OAAAqD,GAAA;UACA;UAEA9B,KAAA;UACAhB,QAAA,OAAAA,QAAA;UAEA+C,SAAA;UACAC,YAAA;UACAC,UAAA;UACAC,IAAA;YACAzD,IAAA;cACAO,QAAA,OAAAA,QAAA;cACAgB,KAAA;YAEA;YACAN,KAAA;cACAV,QAAA,OAAAA,QAAA;cACAgB,KAAA;cACAmC,UAAA;YACA;UACA;QACA;QAGAC,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,eAAA;UAEAC,SAAA;YACAxC,KAAA;YAAA;YACArB,KAAA;YACAC,IAAA;UACA;QAEA;MAEA;MACA,SAAAY,YAAA;QACA,KAAAA,YAAA,CAAAiD,SAAA,MAAAlD,MAAA;QACA,KAAAmD,UAAA;MAEA;IACA;IACA;IACAnC,SAAAoC,OAAA,EAAAC,qBAAA;MACA,MAAA9B,MAAA;MACA;MACA,IAAA+B,QAAA;MACA,IAAAC,UAAA;MACA,IAAAC,QAAA;MACA,MAAAC,UAAA;MACA;MACA;MACA;MACA,MAAAC,CAAA;MACA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAP,OAAA,CAAAN,MAAA,EAAAa,CAAA;QACAL,QAAA,IAAAF,OAAA,CAAAO,CAAA,EAAAxD,KAAA;QACA,MAAAyD,UAAA;UACA1E,IAAA,SAAAkE,OAAA,CAAAO,CAAA,EAAAzE,IAAA,4BAAAyE,CAAA,KAAAP,OAAA,CAAAO,CAAA,EAAAzE,IAAA;UACAG,IAAA;UACAwE,UAAA;UACAC,SAAA;YACAC,IAAA;UACA;UACAlC,SAAA;YACApB,KAAA,OAAAA,KAAA,CAAAkD,CAAA;YACA7B,OAAA;UAEA;UACAsB,OAAA,EAAAA,OAAA,CAAAO,CAAA;UACAK,SAAA;YACAC,QAAA;YACAC,OAAA;YACAR;UACA;QACA;QAEA,WAAAN,OAAA,CAAAO,CAAA,EAAA9B,SAAA;UACA;YAAAA;UAAA,IAAAuB,OAAA,CAAAO,CAAA;;UAEA;UACA,OAAAP,OAAA,CAAAO,CAAA,EAAA9B,SAAA,CAAApB,KAAA,mBAAAoB,SAAA,CAAApB,KAAA,GAAA2C,OAAA,CAAAO,CAAA,EAAA9B,SAAA,CAAApB,KAAA;UACA;UACA,OAAA2C,OAAA,CAAAO,CAAA,EAAA9B,SAAA,CAAAC,OAAA,mBACAD,SAAA,CAAAC,OAAA,GAAAsB,OAAA,CAAAO,CAAA,EAAA9B,SAAA,CAAAC,OAAA,GACA;UAEA8B,UAAA,CAAA/B,SAAA,GAAAA,SAAA;QACA;QACAN,MAAA,CAAAC,IAAA,CAAAoC,UAAA;MACA;MACA;MACA;MACA,SAAAD,CAAA,MAAAA,CAAA,GAAApC,MAAA,CAAAuB,MAAA,EAAAa,CAAA;QACAH,QAAA,GAAAD,UAAA,GAAAhC,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAjD,KAAA;QAEAoB,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAf,UAAA,GAAAkB,UAAA,GAAAD,QAAA;QACA/B,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAhB,QAAA,GAAAoB,QAAA,GAAAF,QAAA;QACA/B,MAAA,CAAAoC,CAAA,EAAAQ,kBAAA,QAAAC,qBAAA,CACA7C,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAf,UAAA,EACAd,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAhB,QAAA,EACA,OACA,OACAsB,CAAA,EAEAnC,MAAA,CAAAoC,CAAA,EAAAP,OAAA,CAAAjD,KACA;QAEAoD,UAAA,GAAAC,QAAA;QAEAC,UAAA,CAAAjC,IAAA,CAAAD,MAAA,CAAAoC,CAAA,EAAAzE,IAAA;MACA;MAEA,MAAAmF,SAAA,QAAAC,WAAA,CAAA/C,MAAA;;MAEA;MACA,MAAAvB,MAAA;QACAuE,SAAA;QACA;QACAC,OAAA;UACAC,GAAA;UACAC,GAAA;QACA;QACAC,OAAA;UACAF,GAAA;UACAC,GAAA;QACA;QACAE,OAAA;UACAH,GAAA;UACAC,GAAA;QACA;QAEAG,MAAA;UACAd,IAAA;UACAM,SAAA,EAAAA,SAAA;UAAA;UACAS,GAAA;UACAC,IAAA;UACAC,MAAA;UACAC,WAAA;YACA;YACAC,KAAA;YAAA;YACAC,IAAA;YAAA;YACAC,iBAAA;YAAA;YACAC,eAAA;YAAA;YACAC,cAAA;YAAA;YACAC,UAAA;YAAA;YACAC,QAAA;UACA;UACA;UACAC,UAAA;YACA;YACAC,MAAA;YACAC,KAAA;cACAD,MAAA;cACAE,cAAA;YACA;YACAC,IAAA;cACAH,MAAA;cACAI,OAAA;cACAnE,MAAA;YACA;YACA;YACA;YACA;UACA;QACA;QACAJ;MACA;MACA,OAAAvB,MAAA;IACA;IAEA;IACAsE,YAAA/C,MAAA,EAAA/B,MAAA;MACA+B,MAAA,CAAAwE,IAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAA,CAAA,CAAA7C,OAAA,CAAAjD,KAAA,GAAA6F,CAAA,CAAA5C,OAAA,CAAAjD,KAAA;MACA,OAAAX,MAAA,QAAA+B,MAAA,IAAA6B,OAAA,CAAAjD,KAAA;IACA;IAEA;IACAiE,sBAAA/B,UAAA,EAAAD,QAAA,EAAA8D,UAAA,EAAAC,SAAA,EAAAzC,CAAA,EAAA0C,CAAA;MACA;MACA,MAAAC,QAAA,IAAAhE,UAAA,GAAAD,QAAA;MACA,MAAAkE,WAAA,GAAAjE,UAAA,GAAAtD,IAAA,CAAAwH,EAAA;MACA,MAAAC,SAAA,GAAApE,QAAA,GAAArD,IAAA,CAAAwH,EAAA;MACA,MAAAE,SAAA,GAAAJ,QAAA,GAAAtH,IAAA,CAAAwH,EAAA;MACA;MACA,IAAAlE,UAAA,UAAAD,QAAA;QACA;QACA8D,UAAA;MACA;MACA;MACA;MACAxC,CAAA,UAAAA,CAAA,mBAAAA,CAAA;MACA;MACA;MACA;MACA,MAAAgD,OAAA,GAAAP,SAAA,GAAApH,IAAA,CAAA4H,GAAA,CAAAF,SAAA,UAAA1H,IAAA,CAAA4H,GAAA,CAAAF,SAAA;MACA,MAAAG,OAAA,GAAAT,SAAA,GAAApH,IAAA,CAAA8H,GAAA,CAAAJ,SAAA,UAAA1H,IAAA,CAAA8H,GAAA,CAAAJ,SAAA;MACA;MACA,MAAAK,SAAA,GAAAX,SAAA;MACA;MACA;QACAY,CAAA;UACAtC,GAAA,GAAA1F,IAAA,CAAAwH,EAAA;UACA7B,GAAA,EAAA3F,IAAA,CAAAwH,EAAA;UACAS,IAAA,EAAAjI,IAAA,CAAAwH,EAAA;QACA;QACAU,CAAA;UACAxC,GAAA;UACAC,GAAA,EAAA3F,IAAA,CAAAwH,EAAA;UACAS,IAAA,EAAAjI,IAAA,CAAAwH,EAAA;QACA;QACAW,EAAAH,CAAA,EAAAE,CAAA;UACA,IAAAF,CAAA,GAAAT,WAAA;YACA,OAAAI,OAAA,GAAA3H,IAAA,CAAA4H,GAAA,CAAAL,WAAA,SAAAvH,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;UACA;UACA,IAAAC,CAAA,GAAAP,SAAA;YACA,OAAAE,OAAA,GAAA3H,IAAA,CAAA4H,GAAA,CAAAH,SAAA,SAAAzH,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;UACA;UACA,OAAAJ,OAAA,GAAA3H,IAAA,CAAA4H,GAAA,CAAAI,CAAA,SAAAhI,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;QACA;QACAK,EAAAJ,CAAA,EAAAE,CAAA;UACA,IAAAF,CAAA,GAAAT,WAAA;YACA,OAAAM,OAAA,GAAA7H,IAAA,CAAA8H,GAAA,CAAAP,WAAA,SAAAvH,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;UACA;UACA,IAAAC,CAAA,GAAAP,SAAA;YACA,OAAAI,OAAA,GAAA7H,IAAA,CAAA8H,GAAA,CAAAL,SAAA,SAAAzH,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;UACA;UACA,OAAAF,OAAA,GAAA7H,IAAA,CAAA8H,GAAA,CAAAE,CAAA,SAAAhI,IAAA,CAAA4H,GAAA,CAAAM,CAAA,IAAAvD,CAAA,IAAAoD,SAAA;QACA;QACAM,EAAAL,CAAA,EAAAE,CAAA;UACA,IAAAF,CAAA,IAAAhI,IAAA,CAAAwH,EAAA;YACA,OAAAxH,IAAA,CAAA8H,GAAA,CAAAE,CAAA;UACA;UACA,IAAAA,CAAA,GAAAhI,IAAA,CAAAwH,EAAA;YACA,OAAAxH,IAAA,CAAA8H,GAAA,CAAAE,CAAA,IAAAX,CAAA;UACA;UACA,OAAArH,IAAA,CAAA8H,GAAA,CAAAI,CAAA,YAAAb,CAAA;QACA;MACA;IACA;IAEAiB,WAAAC,GAAA,EAAAC,CAAA;MACA,IAAAC,CAAA,GAAAC,UAAA,CAAAH,GAAA;MACA;MACA,IAAAI,KAAA,CAAAF,CAAA;QACA;MACA;MACA;MACAA,CAAA,GAAAzI,IAAA,CAAA4I,KAAA,CAAAL,GAAA,GAAAvI,IAAA,CAAA6I,GAAA,KAAAL,CAAA,KAAAxI,IAAA,CAAA6I,GAAA,KAAAL,CAAA;MACA,IAAAM,CAAA,GAAAL,CAAA,CAAAM,QAAA;MACA,IAAAC,EAAA,GAAAF,CAAA,CAAAG,OAAA;MACA;MACA,IAAAD,EAAA;QACAA,EAAA,GAAAF,CAAA,CAAA/E,MAAA;QACA+E,CAAA;MACA;MACA,OAAAA,CAAA,CAAA/E,MAAA,IAAAiF,EAAA,GAAAR,CAAA;QACAM,CAAA;MACA;MACA,OAAAA,CAAA;IACA;IAEA1E,WAAA;MACA;MACA,MAAA8E,IAAA;MACA,IAAAC,aAAA;MACA,IAAAC,YAAA;MACA;;MAEA;MACA,KAAAlI,YAAA,CAAAmI,EAAA;QACA;QACA,IAAAlC,UAAA;QACA,IAAAC,SAAA;QACA,IAAA9D,UAAA;QACA,IAAAD,QAAA;QACA,IAAAsB,CAAA;QACA,IAAAyE,YAAA;UACA;UACAjC,UAAA,GAAA+B,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAAnE,SAAA,CAAAC,QAAA;UACAkC,SAAA;UACA;UACAzC,CAAA,GAAAuE,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAAnE,SAAA,CAAAN,CAAA;UACA;UACArB,UAAA,GAAA4F,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAA/E,OAAA,CAAAf,UAAA;UACA;UACAD,QAAA,GAAA6F,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAA/E,OAAA,CAAAhB,QAAA;UACA;UACA6F,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAAhE,kBAAA,GAAA8D,IAAA,CAAA7D,qBAAA,CAAA/B,UAAA,EAAAD,QAAA,EACA8D,UAAA,EACAC,SAAA,EAAAzC,CAAA;UACAuE,IAAA,CAAAjI,MAAA,CAAAuB,MAAA,CAAA4G,YAAA,EAAAnE,SAAA,CAAAE,OAAA,GAAAiC,SAAA;UACA;UACAgC,YAAA;QACA;QACA;QACA,KAAAlI,YAAA,CAAAiD,SAAA,CAAA+E,IAAA,CAAAjI,MAAA;MACA;IACA;EAIA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}