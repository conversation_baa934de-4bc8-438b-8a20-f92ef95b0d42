/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import t from"../support/FeatureSet.js";import r from"../support/IdSet.js";import{defaultMaxRecords as i,IdState as s}from"../support/shared.js";import{create as a,all as n,resolve as l,reject as d}from"../../../core/promiseUtils.js";import o from"../../../rest/support/RelationshipQuery.js";class u extends t{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerRelated",this._findObjectId=-1,this._requestStandardised=!1,this._removeGeometry=!1,this._overrideFields=null,this.featureObjectId=null,this.relatedLayer=null,this.relationship=null,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,this._findObjectId=e.objectId,this.featureObjectId=e.objectId,this.relationship=e.relationship,this.relatedLayer=e.relatedLayer,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return i}end(){return this._layer}optimisePagingFeatureQueries(){}load(){return null===this._loadPromise&&(this._loadPromise=a(((e,t)=>{n([this._layer.load(),this.relatedLayer.load()]).then((()=>{this._initialiseFeatureSet(),e(this)}),t)}))),this._loadPromise}nativeCapabilities(){return this.relatedLayer.nativeCapabilities()}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this.relatedLayer.geometryType,this.fields=this.relatedLayer.fields.slice(0),null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const i of this._overrideFields)if(i.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}const e=this._layer.nativeCapabilities();e&&(this._databaseType=e.databaseType,this._requestStandardised=e.requestStandardised),this.objectIdField=this.relatedLayer.objectIdField,this.globalIdField=this.relatedLayer.globalIdField,this.hasM=this.relatedLayer.supportsM,this.hasZ=this.relatedLayer.supportsZ,this.typeIdField=this.relatedLayer.typeIdField,this.types=this.relatedLayer.types}databaseType(){return this.relatedLayer.databaseType().then((()=>(this._databaseType=this.relatedLayer._databaseType,this._databaseType)))}isTable(){return this.relatedLayer.isTable()}_isInFeatureSet(){return s.InFeatureSet}_candidateIdTransform(e){return e}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._getFilteredSet("",null,null,null,e))).then((e=>(this._wset=e,e))):l(this._wset)}_changeFeature(t){const r={};for(const e of this.fields)r[e.name]=t.attributes[e.name];return new e({geometry:!0===this._removeGeometry?null:t.geometry,attributes:r})}_getFilteredSet(e,t,i,s,a){return this.databaseType().then((()=>{if(this.isTable()&&t&&null!==e&&""!==e){return new r([],[],!0,null)}const n=this._layer.nativeCapabilities();if(!1===n.canQueryRelated){return new r([],[],!0,null)}if(n.capabilities.queryRelated&&n.capabilities.queryRelated.supportsPagination)return this._getFilteredSetUsingPaging(e,t,i,s,a);let l="",d=!1;null!==s&&n.capabilities&&n.capabilities.queryRelated&&!0===n.capabilities.queryRelated.supportsOrderBy&&(l=s.constructClause(),d=!0);const u=new o;u.objectIds=[this._findObjectId];const h=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this.relatedLayer.fields?this.relatedLayer.fields.map((e=>e.name)):["*"]);u.outFields=h,u.relationshipId=this.relationship.id,u.where="1=1";let c=!0;return!0===this._removeGeometry&&(c=!1),u.returnGeometry=c,this._requestStandardised&&(u.sqlFormat="standard"),u.outSpatialReference=this.spatialReference,u.orderByFields=""!==l?l.split(","):null,n.source.queryRelatedFeatures(u).then((s=>{this._checkCancelled(a);const n=s[this._findObjectId]?s[this._findObjectId].features:[],l=[];for(let e=0;e<n.length;e++)this._featureCache[n[e].attributes[this.relatedLayer.objectIdField]]=n[e],l.push(n[e].attributes[this.relatedLayer.objectIdField]);const o=t&&null!==e&&""!==e,u=null!=i;return new r(o||u?l:[],o||u?[]:l,d,null)}))}))}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice(0);if(t.indexOf("*")>-1)return t;let r=!1;for(const i of t)if(i.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}_getFilteredSetUsingPaging(e,t,i,s,a){try{let n="",l=!1;const d=this._layer.nativeCapabilities();return null!==s&&d&&d.capabilities.queryRelated&&!0===d.capabilities.queryRelated.supportsOrderBy&&(n=s.constructClause(),l=!0),this.databaseType().then((()=>{const s="1=1";let o=this._maxQueryRate();const u=d.capabilities.query.maxRecordCount;void 0!==u&&u<o&&(o=u);const h=t&&null!==e&&""!==e,c=null!=i;let p=null,y=!0;!0===this._removeGeometry&&(y=!1);const f=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this.relatedLayer.fields?this.relatedLayer.fields.map((e=>e.name)):["*"]);return p=new r(h||c?["GETPAGES"]:[],h||c?[]:["GETPAGES"],l,{outFields:f.join(","),resultRecordCount:o,resultOffset:0,objectIds:[this._findObjectId],where:s,orderByFields:n,returnGeometry:y,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}}),this._expandPagedSet(p,o,0,0,a).then((()=>p))}))}catch(n){return d(n)}}_expandPagedSet(e,t,r,i,s){return this._expandPagedSetFeatureSet(e,t,r,i,s)}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}_getPhysicalPage(e,t,r){try{const t=e.pagesDefinition.internal.lastRetrieved,i=t,s=e.pagesDefinition.internal.lastPage,a=this._layer.nativeCapabilities(),n=new o;return!0===this._requestStandardised&&(n.sqlFormat="standard"),n.relationshipId=this.relationship.id,n.objectIds=e.pagesDefinition.objectIds,n.resultOffset=e.pagesDefinition.internal.lastPage,n.resultRecordCount=e.pagesDefinition.resultRecordCount,n.outFields=e.pagesDefinition.outFields.split(","),n.where=e.pagesDefinition.where,n.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,n.returnGeometry=e.pagesDefinition.returnGeometry,n.outSpatialReference=this.spatialReference,a.source.queryRelatedFeatures(n).then((a=>{if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==s)return 0;const n=a[this._findObjectId]?a[this._findObjectId].features:[];for(let t=0;t<n.length;t++)e.pagesDefinition.internal.set[i+t]=n[t].attributes[this.relatedLayer.objectIdField];for(let e=0;e<n.length;e++)this._featureCache[n[e].attributes[this.relatedLayer.objectIdField]]=n[e];const l=!a[this._findObjectId]||!1===a[this._findObjectId].exceededTransferLimit;return n.length!==e.pagesDefinition.resultRecordCount&&l&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=t+n.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,n.length}))}catch(i){return d(i)}}_getFeatures(e,t,r,i){const s=[];-1!==t&&void 0===this._featureCache[t]&&s.push(t);const a=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(e,a))return this._expandPagedSet(e,a,0,0,i).then((()=>this._getFeatures(e,t,r,i)));let n=0;for(let l=e._lastFetchedIndex;l<e._known.length&&(n++,n<=r&&(e._lastFetchedIndex+=1),!("GETPAGES"!==e._known[l]&&void 0===this._featureCache[e._known[l]]&&(e._known[l]!==t&&s.push(e._known[l]),s.length>r)))&&!(n>=r&&0===s.length);l++);return 0===s.length?l("success"):d(new Error("Unaccounted for Features. Not in Feature Collection"))}_refineSetBlock(e,t,r){return l(e)}_stat(e,t,r,i,s,a,n){return l({calculated:!1})}get gdbVersion(){return this.relatedLayer.gdbVersion}_canDoAggregates(e,t,r,i,s){return l(!1)}relationshipMetaData(){return this.relatedLayer.relationshipMetaData()}serviceUrl(){return this.relatedLayer.serviceUrl()}queryAttachments(e,t,r,i,s){return this.relatedLayer.queryAttachments(e,t,r,i,s)}getFeatureByObjectId(e,t){return this.relatedLayer.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this.relatedLayer.getOwningSystemUrl()}getIdentityUser(){return this.relatedLayer.getIdentityUser()}getDataSourceFeatureSet(){return this.relatedLayer}}export{u as default};
