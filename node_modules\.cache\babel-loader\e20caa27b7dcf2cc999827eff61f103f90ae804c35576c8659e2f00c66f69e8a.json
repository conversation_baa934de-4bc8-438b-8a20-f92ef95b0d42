{"ast": null, "code": "export function timestampToTime(timestamp) {\n  timestamp = timestamp ? timestamp : null;\n  let date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000\n  let Y = date.getFullYear() + '-';\n  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';\n  let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';\n  let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';\n  let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';\n  let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();\n  return Y + M + D + h + m + s;\n}", "map": {"version": 3, "names": ["timestampToTime", "timestamp", "date", "Date", "Y", "getFullYear", "M", "getMonth", "D", "getDate", "h", "getHours", "m", "getMinutes", "s", "getSeconds"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/timeStamp.js"], "sourcesContent": ["export function timestampToTime(timestamp) {\r\n    timestamp = timestamp ? timestamp : null;\r\n    let date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000\r\n    let Y = date.getFullYear() + '-';\r\n    let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';\r\n    let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';\r\n    let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';\r\n    let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';\r\n    let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();\r\n    return Y + M + D + h + m + s;\r\n} "], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,SAAS,EAAE;EACvCA,SAAS,GAAGA,SAAS,GAAGA,SAAS,GAAG,IAAI;EACxC,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC,CAAC;EAC/B,IAAIG,CAAC,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG;EAChC,IAAIC,CAAC,GAAG,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,IAAIL,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGL,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG;EAC5F,IAAIC,CAAC,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGP,IAAI,CAACO,OAAO,CAAC,CAAC,GAAGP,IAAI,CAACO,OAAO,CAAC,CAAC,IAAI,GAAG;EAC3E,IAAIC,CAAC,GAAG,CAACR,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGT,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAGT,IAAI,CAACS,QAAQ,CAAC,CAAC,IAAI,GAAG;EAC9E,IAAIC,CAAC,GAAG,CAACV,IAAI,CAACW,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGX,IAAI,CAACW,UAAU,CAAC,CAAC,GAAGX,IAAI,CAACW,UAAU,CAAC,CAAC,IAAI,GAAG;EACpF,IAAIC,CAAC,GAAGZ,IAAI,CAACa,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGb,IAAI,CAACa,UAAU,CAAC,CAAC,GAAGb,IAAI,CAACa,UAAU,CAAC,CAAC;EAC5E,OAAOX,CAAC,GAAGE,CAAC,GAAGE,CAAC,GAAGE,CAAC,GAAGE,CAAC,GAAGE,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}