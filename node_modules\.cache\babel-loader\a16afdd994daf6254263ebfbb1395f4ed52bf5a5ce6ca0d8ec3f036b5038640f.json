{"ast": null, "code": "import { getInterfaceApi, deleteInterfaceApi } from '@/api/userMenu';\nimport interfaceDialog from './interfaceDialog.vue';\nexport default {\n  name: 'layerManage',\n  data() {\n    return {\n      // 查询参数\n      queryParams: {\n        pageSize: 10,\n        pageNum: 1,\n        name: ''\n      },\n      total: '',\n      tabList: []\n    };\n  },\n  components: {\n    interfaceDialog\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.getInterfaceApiList();\n  },\n  updated() {},\n  methods: {\n    editFn(item) {\n      let data = JSON.parse(JSON.stringify(item));\n      this.$refs.openModal.openAciton(\"edit\", data);\n    },\n    addFn() {\n      this.$refs.openModal.openAciton(\"add\", {});\n    },\n    // 搜索\n    handleQuery() {\n      this.getInterfaceApiList();\n    },\n    //重置\n    resetQuery() {\n      this.queryParams.name = '';\n      this.queryParams.pageNum = 1;\n      this.getInterfaceApiList();\n    },\n    //监听页码变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getInterfaceApiList();\n    },\n    // 获取图层数据\n    getInterfaceApiList() {\n      getInterfaceApi(this.queryParams).then(res => {\n        if (res.data.code == 200) {\n          this.tabList = res.data.extra.data;\n          this.total = res.data.extra.total;\n          this.queryParams.pageNum = res.data.extra.pageNum;\n          this.queryParams.pageSize = res.data.extra.pageSize;\n        }\n      });\n    },\n    // 删除图层数据 \n    deleteFn(id) {\n      this.$confirm('此操作将永久删除, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteInterfaceApi({\n          ids: id\n        }).then(res => {\n          if (res.data.code == 200) {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getInterfaceApiList();\n          }\n        });\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    }\n  },\n  beforeDestroy() {}\n};", "map": {"version": 3, "names": ["getInterfaceApi", "deleteInterfaceApi", "interfaceDialog", "name", "data", "queryParams", "pageSize", "pageNum", "total", "tabList", "components", "watch", "computed", "mounted", "getInterfaceApiList", "updated", "methods", "editFn", "item", "JSON", "parse", "stringify", "$refs", "openModal", "openAciton", "addFn", "handleQuery", "reset<PERSON><PERSON>y", "handleCurrentChange", "val", "then", "res", "code", "extra", "deleteFn", "id", "$confirm", "confirmButtonText", "cancelButtonText", "type", "ids", "$message", "message", "catch", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/mainPage/components/resource-center/components/interfaceManage.vue"], "sourcesContent": ["<template>\r\n    <div class=\"layerManage\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\">\r\n            <el-form-item label=\"接口名称\" prop=\"content\">\r\n                <el-input v-model.trim=\"queryParams.name\" placeholder=\"请输入接口名称\" clearable size=\"small\"\r\n                    style=\"caret-color: #fff;\" />\r\n            </el-form-item>\r\n            <!-- <el-form-item label=\"发布时间\" prop=\"time\">\r\n                <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"time\" type=\"daterange\" range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" style=\"caret-color: #fff;\">\r\n                </el-date-picker>\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"state\">\r\n                <el-select v-model=\"queryParams.state\" placeholder=\"图层状态\" clearable size=\"small\">\r\n                    <el-option key=\"1\" label=\"正常\" value=\"1\" />\r\n                    <el-option key=\"0\" label=\"停用\" value=\"0\" />\r\n                </el-select>\r\n            </el-form-item> -->\r\n            <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n        <el-button class=\"publish\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addFn\">新增</el-button>\r\n        <el-table :data=\"tabList\">\r\n            <el-table-column prop=\"id\" label=\"主键\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"subject\" label=\"分类\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"name\" label=\"接口名称\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"requestType\" label=\"请求方式\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"url\" label=\"请求地址\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\" width=\"165\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-button type=\"text\" size=\"small\" @click=\"editFn(scope.row)\">修改</el-button>\r\n                    <el-button type=\"text\" size=\"small\" @click=\"deleteFn(scope.row.id)\">删除</el-button>\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"queryParams.pageNum\"\r\n            :page-size=\"queryParams.pageSize\" layout=\"total, prev, pager, next, jumper\" :total=\"total\">\r\n        </el-pagination>\r\n        <interfaceDialog ref=\"openModal\" @resetList=\"resetQuery\" />\r\n    </div>\r\n</template>\r\n<script>\r\nimport { getInterfaceApi, deleteInterfaceApi } from '@/api/userMenu'\r\nimport interfaceDialog from './interfaceDialog.vue';\r\nexport default {\r\n    name: 'layerManage',\r\n    data() {\r\n        return {\r\n            // 查询参数\r\n            queryParams: {\r\n                pageSize: 10,\r\n                pageNum: 1,\r\n                name: '',\r\n            },\r\n            total: '',\r\n            tabList: [],\r\n\r\n        };\r\n    },\r\n    components: {\r\n        interfaceDialog,\r\n    },\r\n    watch: {\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.getInterfaceApiList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        editFn(item) {\r\n            let data = JSON.parse(JSON.stringify(item));\r\n            this.$refs.openModal.openAciton(\"edit\", data);\r\n        },\r\n        addFn() {\r\n            this.$refs.openModal.openAciton(\"add\", {});\r\n        },\r\n        // 搜索\r\n        handleQuery() {\r\n            this.getInterfaceApiList();\r\n        },\r\n        //重置\r\n        resetQuery() {\r\n            this.queryParams.name = '';\r\n            this.queryParams.pageNum = 1;\r\n            this.getInterfaceApiList();\r\n        },\r\n        //监听页码变化\r\n        handleCurrentChange(val) {\r\n            this.queryParams.pageNum = val;\r\n            this.getInterfaceApiList();\r\n        },\r\n        // 获取图层数据\r\n        getInterfaceApiList() {\r\n            getInterfaceApi(this.queryParams).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.tabList = res.data.extra.data;\r\n                    this.total = res.data.extra.total;\r\n                    this.queryParams.pageNum = res.data.extra.pageNum;\r\n                    this.queryParams.pageSize = res.data.extra.pageSize;\r\n                }\r\n            })\r\n        },\r\n        // 删除图层数据 \r\n        deleteFn(id) {\r\n            this.$confirm('此操作将永久删除, 是否继续?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                deleteInterfaceApi({ ids: id }).then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.$message({\r\n                            type: 'success',\r\n                            message: '删除成功!'\r\n                        });\r\n                        this.getInterfaceApiList();\r\n                    }\r\n                })\r\n\r\n            }).catch(() => {\r\n                this.$message({\r\n                    type: 'info',\r\n                    message: '已取消删除'\r\n                });\r\n            });\r\n        }\r\n    },\r\n    beforeDestroy() {\r\n    }\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.layerManage {\r\n    width: 100%;\r\n    height: calc(100% - 6vh);\r\n    margin-top: 6vh;\r\n    padding-top: 45px;\r\n    color: #fff;\r\n\r\n    // position: relative;\r\n    :deep(.el-form) {\r\n        display: flex;\r\n        margin-left: 36px;\r\n\r\n        .el-form-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 78px;\r\n\r\n            .el-form-item__label {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 18px;\r\n                color: #D1DEE2;\r\n                line-height: 25px;\r\n            }\r\n\r\n            .el-input__inner {\r\n                background: #103452;\r\n                border-radius: 5px;\r\n                border: 1px solid #6A8096;\r\n                color: #fff;\r\n                width: 300px;\r\n                height: 35px;\r\n            }\r\n\r\n            .el-range-input {\r\n                background: transparent;\r\n            }\r\n\r\n            .el-button {\r\n                color: #fff;\r\n                border: none;\r\n            }\r\n\r\n            .el-button:nth-child(1) {\r\n                background: #0785c9;\r\n            }\r\n\r\n            .el-button:nth-child(2) {\r\n                background: #07c9b2;\r\n            }\r\n        }\r\n\r\n\r\n\r\n\r\n    }\r\n\r\n    :deep(.publish) {\r\n        color: #fff;\r\n        border: none;\r\n        background: #00D0FF;\r\n        margin-left: 36px;\r\n    }\r\n\r\n    :deep(.el-table) {\r\n        background: #1F405C;\r\n        margin: 25px 12px 0 12px;\r\n        width: calc(100% - 24px);\r\n        color: #D1DEE2;\r\n        border: none;\r\n\r\n\r\n        thead {\r\n            border: 2px solid #3B678C;\r\n            height: 73px;\r\n\r\n            tr th {\r\n                background: #124C6F;\r\n\r\n                color: #D1DEE2;\r\n            }\r\n        }\r\n\r\n        .el-table__row {\r\n            background: #1F405C;\r\n        }\r\n\r\n        .el-table__row:hover>td {\r\n            background-color: transparent !important;\r\n        }\r\n\r\n\r\n        .el-table td,\r\n        .el-table th {\r\n            border-bottom: 1px solid #3B678C;\r\n            border-right: none;\r\n            border-left: none;\r\n            border-top: none;\r\n        }\r\n    }\r\n\r\n    :deep(.el-pagination) {\r\n        margin-top: 40px;\r\n        margin-bottom: 30px;\r\n        text-align: center;\r\n\r\n        .el-pagination__total {\r\n            color: #fff;\r\n        }\r\n\r\n        .el-pagination__sizes {\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        >button {\r\n            color: #fff;\r\n            background-color: transparent;\r\n        }\r\n\r\n        ul {\r\n            li {\r\n                background-color: transparent;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        .el-pager .active {\r\n            color: #4BDBFF;\r\n        }\r\n\r\n        .el-pagination__jump {\r\n            color: #fff;\r\n\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #4BDBFF;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAiDA,SAAAA,eAAA,EAAAC,kBAAA;AACA,OAAAC,eAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,OAAA;QACAJ,IAAA;MACA;MACAK,KAAA;MACAC,OAAA;IAEA;EACA;EACAC,UAAA;IACAR;EACA;EACAS,KAAA,GACA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,mBAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,OAAAC,IAAA;MACA,IAAAd,IAAA,GAAAe,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,IAAA;MACA,KAAAI,KAAA,CAAAC,SAAA,CAAAC,UAAA,SAAApB,IAAA;IACA;IACAqB,MAAA;MACA,KAAAH,KAAA,CAAAC,SAAA,CAAAC,UAAA;IACA;IACA;IACAE,YAAA;MACA,KAAAZ,mBAAA;IACA;IACA;IACAa,WAAA;MACA,KAAAtB,WAAA,CAAAF,IAAA;MACA,KAAAE,WAAA,CAAAE,OAAA;MACA,KAAAO,mBAAA;IACA;IACA;IACAc,oBAAAC,GAAA;MACA,KAAAxB,WAAA,CAAAE,OAAA,GAAAsB,GAAA;MACA,KAAAf,mBAAA;IACA;IACA;IACAA,oBAAA;MACAd,eAAA,MAAAK,WAAA,EAAAyB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;UACA,KAAAvB,OAAA,GAAAsB,GAAA,CAAA3B,IAAA,CAAA6B,KAAA,CAAA7B,IAAA;UACA,KAAAI,KAAA,GAAAuB,GAAA,CAAA3B,IAAA,CAAA6B,KAAA,CAAAzB,KAAA;UACA,KAAAH,WAAA,CAAAE,OAAA,GAAAwB,GAAA,CAAA3B,IAAA,CAAA6B,KAAA,CAAA1B,OAAA;UACA,KAAAF,WAAA,CAAAC,QAAA,GAAAyB,GAAA,CAAA3B,IAAA,CAAA6B,KAAA,CAAA3B,QAAA;QACA;MACA;IACA;IACA;IACA4B,SAAAC,EAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAT,IAAA;QACA7B,kBAAA;UAAAuC,GAAA,EAAAL;QAAA,GAAAL,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;YACA,KAAAS,QAAA;cACAF,IAAA;cACAG,OAAA;YACA;YACA,KAAA5B,mBAAA;UACA;QACA;MAEA,GAAA6B,KAAA;QACA,KAAAF,QAAA;UACAF,IAAA;UACAG,OAAA;QACA;MACA;IACA;EACA;EACAE,cAAA,GACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}