/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
class e{constructor(e){const s=this;s._keys=[],s._values=[],s.length=0,e&&e.forEach((e=>{s.set(e[0],e[1])}))}entries(){return[].slice.call(this.keys().map((e=>[e,this.get(e)])))}keys(){return[].slice.call(this._keys)}values(){return[].slice.call(this._values)}has(e){return this._keys.indexOf(e)>-1}get(e){const s=this._keys.indexOf(e);return s>-1?this._values[s]:void 0}deepGet(s){if(!s||!s.length)return null;const t=(s,i)=>null==s?null:i.length?t(s instanceof e?s.get(i[0]):s[i[0]],i.slice(1)):s;return t(this.get(s[0]),s.slice(1))}set(e,s){const t=this,i=this._keys.indexOf(e);return i>-1?t._values[i]=s:(t._keys.push(e),t._values.push(s),t.length=t._values.length),this}sortedSet(e,s,t,i){const h=this,l=this._keys.length,r=t||0,n=void 0!==i?i:l-1;if(0===l)return h._keys.push(e),h._values.push(s),h;if(e===this._keys[r])return this._values.splice(r,0,s),this;if(e===this._keys[n])return this._values.splice(n,0,s),this;if(e>this._keys[n])return this._keys.splice(n+1,0,e),this._values.splice(n+1,0,s),this;if(e<this._keys[r])return this._values.splice(r,0,s),this._keys.splice(r,0,e),this;if(r>=n)return this;const u=r+Math.floor((n-r)/2);return e<this._keys[u]?this.sortedSet(e,s,r,u-1):e>this._keys[u]?this.sortedSet(e,s,u+1,n):this}size(){return this.length}clear(){const e=this;return e._keys.length=e.length=e._values.length=0,this}delete(e){const s=this,t=s._keys.indexOf(e);return t>-1&&(s._keys.splice(t,1),s._values.splice(t,1),s.length=s._keys.length,!0)}forEach(e){this._keys.forEach(((s,t)=>{e(this.get(s),s,t)}))}map(e){return this.keys().map(((s,t)=>e(this.get(s),s,t)))}filter(e){const s=this;return s._keys.forEach(((t,i)=>{!1===e(s.get(t),t,i)&&s.delete(t)})),this}clone(){return new e(this.entries())}}class s{constructor(s=20){this.maxEntries=s,this.values=new e}delete(e){this.values.has(e)&&this.values.delete(e)}get(e){let s=null;return this.values.has(e)&&(s=this.values.get(e),this.values.delete(e),this.values.set(e,s)),s}put(e,s){if(this.values.size()>=this.maxEntries){const e=this.values.keys()[0];this.values.delete(e)}this.values.set(e,s)}}class t{constructor(e=20){this.maxEntries=e,this._cache=new s(this.maxEntries)}clear(){this._cache=new s(this.maxEntries)}addToCache(e,s){this._cache.put(e,s)}removeFromCache(e){this._cache.delete(e)}getFromCache(e){return this._cache.get(e)}}export{t as default};
