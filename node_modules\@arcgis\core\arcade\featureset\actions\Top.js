/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import t from"../support/FeatureSet.js";import e from"../support/IdSet.js";import{IdState as n}from"../support/shared.js";import{resolve as s,reject as i}from"../../../core/promiseUtils.js";class a extends t{constructor(t){super(t),this._topnum=0,this.declaredClass="esri.arcade.featureset.actions.Top",this._countedin=0,this._maxProcessing=100,this._topnum=t.topnum,this._parent=t.parentfeatureset}_getSet(t){return null===this._wset?this._ensureLoaded().then((()=>this._parent._getSet(t))).then((t=>(this._wset=new e(t._candidates.slice(0),t._known.slice(0),!1,this._clonePageDefinition(t.pagesDefinition)),this._setKnownLength(this._wset)>this._topnum&&(this._wset._known=this._wset._known.slice(0,this._topnum)),this._setKnownLength(this._wset)>=this._topnum&&(this._wset._candidates=[]),this._wset))):s(this._wset)}_setKnownLength(t){return t._known.length>0&&"GETPAGES"===t._known[t._known.length-1]?t._known.length-1:t._known.length}_isInFeatureSet(t){const e=this._parent._isInFeatureSet(t);if(e===n.NotInFeatureSet)return e;const s=this._idstates[t];return s===n.InFeatureSet||s===n.NotInFeatureSet?s:e===n.InFeatureSet&&void 0===s?this._countedin<this._topnum?(this._idstates[t]=n.InFeatureSet,this._countedin++,n.InFeatureSet):(this._idstates[t]=n.NotInFeatureSet,n.NotInFeatureSet):n.Unknown}_expandPagedSet(t,e,n,a,r){if(null===this._parent)return i(new Error("Parent Paging not implemented"));if(e>this._topnum&&(e=this._topnum),this._countedin>=this._topnum&&t.pagesDefinition.internal.set.length<=t.pagesDefinition.resultOffset){let e=t._known.length;return e>0&&"GETPAGES"===t._known[e-1]&&(t._known.length=e-1),e=t._candidates.length,e>0&&"GETPAGES"===t._candidates[e-1]&&(t._candidates.length=e-1),s("success")}return this._parent._expandPagedSet(t,e,n,a,r).then((e=>(this._setKnownLength(t)>this._topnum&&(t._known.length=this._topnum),this._setKnownLength(t)>=this._topnum&&(t._candidates.length=0),e)))}_getFeatures(t,n,i,a){const r=[],o=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(t,o))return this._expandPagedSet(t,o,0,0,a).then((()=>this._getFeatures(t,n,i,a)));-1!==n&&void 0===this._featureCache[n]&&r.push(n);let _=0;for(let e=t._lastFetchedIndex;e<t._known.length&&(_++,_<=i&&(t._lastFetchedIndex+=1),!(void 0===this._featureCache[t._known[e]]&&(t._known[e]!==n&&r.push(t._known[e]),r.length>o)));e++);if(0===r.length)return s("success");const h=new e([],r,!1,null),u=Math.min(r.length,i);return this._parent._getFeatures(h,-1,u,a).then((()=>{for(let t=0;t<u;t++){const e=this._parent._featureFromCache(r[t]);void 0!==e&&(this._featureCache[r[t]]=e)}return"success"}))}_getFilteredSet(t,n,s,i,a){return this._ensureLoaded().then((()=>this._getSet(a))).then((t=>new e(t._candidates.slice(0).concat(t._known.slice(0)),[],!1,this._clonePageDefinition(t.pagesDefinition))))}_refineKnowns(t,e){let s=0,i=null;const a=[];for(let r=0;r<t._candidates.length;r++){const o=this._isInFeatureSet(t._candidates[r]);if(o===n.InFeatureSet){if(t._known.push(t._candidates[r]),s+=1,null===i?i={start:r,end:r}:i.end===r-1?i.end=r:(a.push(i),i={start:r,end:r}),t._known.length>=this._topnum)break}else if(o===n.NotInFeatureSet)null===i?i={start:r,end:r}:i.end===r-1?i.end=r:(a.push(i),i={start:r,end:r}),s+=1;else if(o===n.Unknown)break;if(s>=e)break}null!==i&&a.push(i);for(let n=a.length-1;n>=0;n--)t._candidates.splice(a[n].start,a[n].end-a[n].start+1);this._setKnownLength(t)>this._topnum&&(t._known=t._known.slice(0,this._topnum)),this._setKnownLength(t)>=this._topnum&&(t._candidates=[])}_stat(){return s({calculated:!1})}_canDoAggregates(){return s(!1)}static registerAction(){t._featuresetFunctions.top=function(t){return new a({parentfeatureset:this,topnum:t})}}}export{a as default};
