{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getAppealsList } from '@/api/index.js';\nexport default {\n  name: 'EventDealProgress',\n  data() {\n    return {\n      eventDeal: null,\n      data: [],\n      xData: []\n    };\n  },\n  beforeDestroy() {\n    if (this.eventDeal) {\n      this.eventDeal.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.eventDeal != null && this.eventDeal != '' && this.eventDeal != undefined) {\n          this.eventDeal.dispose();\n        }\n        this.eventDeal = echarts.init(this.$refs.eventDeal);\n        this.eventDeal.clear();\n        getAppealsList().then(res => {\n          let dataList = res.data.data.list;\n          for (var i of dataList) {\n            this.data.push(i.processing);\n            this.xData.push(i.communityName);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      const situationColor = {\n        type: 'linear',\n        x: 1,\n        y: 0,\n        x2: 1,\n        y2: 1,\n        colorStops: [{\n          offset: 0,\n          color: 'rgba(0, 112, 119, 0.98)'\n        }, {\n          offset: 1,\n          color: 'rgba(4, 148, 175, 0.01)'\n        }]\n      };\n      let option = {\n        // 多个x轴\n        grid: {\n          top: \"15%\",\n          left: 0,\n          right: 0,\n          bottom: '30%'\n        },\n        xAxis: [{\n          type: 'category',\n          data: this.xData,\n          axisLine: {\n            show: false\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 20,\n            padding: [10, 5]\n          },\n          axisTick: {\n            show: false\n          }\n        }],\n        yAxis: {\n          type: 'value',\n          show: false,\n          axisLine: {\n            lineStyle: {\n              color: '#E6F2FE'\n            }\n          },\n          splitLine: {\n            show: false\n          }\n        },\n        dataZoom: [{\n          type: 'inside',\n          show: true,\n          start: 0,\n          end: 30,\n          bottom: '0%',\n          zoomOnMouseWheel: true\n        }, {}],\n        series: [{\n          name: '地区',\n          type: 'bar',\n          showSymbol: false,\n          hoverAnimation: false,\n          barWidth: 17,\n          data: this.data,\n          itemStyle: {\n            //实体柱子左面\n            // normal: {\n            borderWidth: 0,\n            color: {\n              type: 'linear',\n              x: 1,\n              y: 0,\n              x2: 1,\n              y2: 1,\n              colorStops: [{\n                offset: 0,\n                color: 'rgba(0, 240, 255, 1)'\n              }, {\n                offset: 1,\n                color: 'rgba(0, 246, 255, 0.01)'\n              }]\n              // }\n            }\n          },\n          tooltip: {\n            show: false\n          },\n          z: 3\n        }, {\n          name: '地区',\n          tooltip: {\n            show: true\n          },\n          type: 'bar',\n          barWidth: 18,\n          itemStyle: {\n            //实体柱子右面\n            // normal: {\n            color: situationColor,\n            borderWidth: 0\n            // }\n          },\n          data: this.data,\n          barGap: 0,\n          z: 5\n        }, {\n          //实体柱子顶部\n          z: 10,\n          name: '地区',\n          type: 'pictorialBar',\n          symbolPosition: 'end',\n          data: this.data,\n          symbol: 'diamond',\n          symbolOffset: [-2, -18],\n          symbolRotate: 90,\n          symbolSize: [9, 36],\n          itemStyle: {\n            // normal: {\n            borderWidth: 0,\n            color: {\n              type: 'linear',\n              x: 1,\n              y: 0,\n              x2: 1,\n              y2: 1,\n              colorStops: [{\n                offset: 0,\n                color: 'rgba(173, 248, 253, 1)'\n              }, {\n                offset: 1,\n                color: 'rgba(0, 234, 249, 1)'\n              }]\n            }\n\n            // }\n          },\n          tooltip: {\n            show: false\n          },\n          label: {\n            show: true,\n            position: 'top',\n            offset: [0, 19],\n            fontSize: 23,\n            fontWeight: 700,\n            color: 'rgba(87, 241, 251,1)'\n          }\n        }]\n      };\n      if (typeof option === 'object' && option) {\n        this.eventDeal.setOption(option);\n      }\n      window.onresize = this.eventDeal.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getAppealsList", "name", "data", "eventDeal", "xData", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "list", "i", "push", "processing", "communityName", "setOption", "situationColor", "type", "x", "y", "x2", "y2", "colorStops", "offset", "color", "option", "grid", "top", "left", "right", "bottom", "xAxis", "axisLine", "show", "axisLabel", "fontSize", "padding", "axisTick", "yAxis", "lineStyle", "splitLine", "dataZoom", "start", "end", "zoomOnMouseWheel", "series", "showSymbol", "hoverAnimation", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "borderWidth", "tooltip", "z", "barGap", "symbolPosition", "symbol", "symbolOffset", "symbolRotate", "symbolSize", "label", "position", "fontWeight", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/EventDealProgress.vue"], "sourcesContent": ["<template>\r\n    <div id=\"eventDeal\" ref=\"eventDeal\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { getAppealsList } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'EventDealProgress',\r\n\r\n    data() {\r\n        return {\r\n            eventDeal: null,\r\n            data: [],\r\n            xData: [],\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.eventDeal) {\r\n            this.eventDeal.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.myChart()\r\n        });\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.eventDeal != null && this.eventDeal != '' && this.eventDeal != undefined) {\r\n                    this.eventDeal.dispose();\r\n                }\r\n                this.eventDeal = echarts.init(this.$refs.eventDeal);\r\n                this.eventDeal.clear();\r\n\r\n                getAppealsList().then(res => {\r\n                    let dataList = res.data.data.list;\r\n                    for (var i of dataList) {\r\n                        this.data.push(i.processing);\r\n                        this.xData.push(i.communityName)\r\n                    }\r\n\r\n                    this.setOption()\r\n                })\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n\r\n\r\n            const situationColor = {\r\n                type: 'linear',\r\n                x: 1,\r\n                y: 0,\r\n                x2: 1,\r\n                y2: 1,\r\n                colorStops: [\r\n                    {\r\n                        offset: 0,\r\n                        color: 'rgba(0, 112, 119, 0.98)'\r\n                    },\r\n\r\n\r\n                    {\r\n                        offset: 1,\r\n                        color: 'rgba(4, 148, 175, 0.01)'\r\n                    }\r\n                ]\r\n            };\r\n            let option = {\r\n                // 多个x轴\r\n                grid: {\r\n                    top: \"15%\",\r\n                    left: 0,\r\n                    right: 0,\r\n                    bottom: '30%',\r\n                },\r\n                xAxis: [\r\n                    {\r\n                        type: 'category',\r\n                        data: this.xData,\r\n                        axisLine: {\r\n                            show: false,\r\n\r\n                        },\r\n                        axisLabel: {\r\n                            color: \"#fff\",\r\n                            fontSize: 20,\r\n                            padding: [10, 5]\r\n                        },\r\n                        axisTick: {\r\n                            show: false\r\n                        },\r\n                    },\r\n\r\n                ],\r\n                yAxis: {\r\n                    type: 'value',\r\n                    show: false,\r\n                    axisLine: {\r\n                        lineStyle: {\r\n                            color: '#E6F2FE'\r\n                        }\r\n                    },\r\n                    splitLine: {\r\n                        show: false,\r\n\r\n                    }\r\n                },\r\n                dataZoom: [\r\n                    {\r\n                        type: 'inside',\r\n                        show: true,\r\n                        start: 0,\r\n                        end: 30,\r\n                        bottom: '0%',\r\n                        zoomOnMouseWheel: true,\r\n                    }, {}\r\n                ],\r\n                series: [\r\n                    {\r\n                        name: '地区',\r\n                        type: 'bar',\r\n                        showSymbol: false,\r\n                        hoverAnimation: false,\r\n                        barWidth: 17,\r\n                        data: this.data,\r\n                        itemStyle: {\r\n                            //实体柱子左面\r\n                            // normal: {\r\n                            borderWidth: 0,\r\n                            color: {\r\n                                type: 'linear',\r\n                                x: 1,\r\n                                y: 0,\r\n                                x2: 1,\r\n                                y2: 1,\r\n                                colorStops: [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: 'rgba(0, 240, 255, 1)'\r\n                                    },\r\n\r\n\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: 'rgba(0, 246, 255, 0.01)'\r\n                                    }\r\n                                ]\r\n                                // }\r\n                            }\r\n                        },\r\n                        tooltip: {\r\n                            show: false\r\n                        },\r\n\r\n                        z: 3\r\n                    },\r\n                    {\r\n                        name: '地区',\r\n                        tooltip: {\r\n                            show: true\r\n                        },\r\n                        type: 'bar',\r\n                        barWidth: 18,\r\n                        itemStyle: {\r\n                            //实体柱子右面\r\n                            // normal: {\r\n                            color: situationColor,\r\n                            borderWidth: 0\r\n                            // }\r\n                        },\r\n                        data: this.data,\r\n                        barGap: 0,\r\n                        z: 5,\r\n\r\n                    },\r\n                    {\r\n                        //实体柱子顶部\r\n                        z: 10,\r\n                        name: '地区',\r\n                        type: 'pictorialBar',\r\n                        symbolPosition: 'end',\r\n                        data: this.data,\r\n                        symbol: 'diamond',\r\n                        symbolOffset: [-2, -18],\r\n                        symbolRotate: 90,\r\n                        symbolSize: [9, 36],\r\n                        itemStyle: {\r\n                            // normal: {\r\n                            borderWidth: 0,\r\n                            color: {\r\n                                type: 'linear',\r\n                                x: 1,\r\n                                y: 0,\r\n                                x2: 1,\r\n                                y2: 1,\r\n                                colorStops: [\r\n                                    {\r\n                                        offset: 0,\r\n                                        color: 'rgba(173, 248, 253, 1)'\r\n                                    },\r\n\r\n                                    {\r\n                                        offset: 1,\r\n                                        color: 'rgba(0, 234, 249, 1)'\r\n                                    }\r\n                                ]\r\n                            }\r\n\r\n                            // }\r\n                        },\r\n                        tooltip: {\r\n                            show: false\r\n                        },\r\n                        label: {\r\n                            show: true,\r\n                            position: 'top',\r\n                            offset: [0, 19],\r\n                            fontSize: 23,\r\n                            fontWeight: 700,\r\n                            color: 'rgba(87, 241, 251,1)',\r\n\r\n                        },\r\n\r\n\r\n\r\n                    },\r\n\r\n\r\n                ]\r\n            }\r\n\r\n            if (typeof option === 'object' && option) {\r\n                this.eventDeal.setOption(option);\r\n            }\r\n            window.onresize = this.eventDeal.resize;\r\n\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#eventDeal {\r\n    width: 94%;\r\n    height: 400px;\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,SAAA;MACAD,IAAA;MACAE,KAAA;IACA;EACA;EACAC,cAAA;IACA,SAAAF,SAAA;MACA,KAAAA,SAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IACA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAV,SAAA,iBAAAA,SAAA,eAAAA,SAAA,IAAAW,SAAA;UACA,KAAAX,SAAA,CAAAY,OAAA;QACA;QACA,KAAAZ,SAAA,GAAAJ,OAAA,CAAAiB,IAAA,MAAAC,KAAA,CAAAd,SAAA;QACA,KAAAA,SAAA,CAAAG,KAAA;QAEAN,cAAA,GAAAa,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAhB,IAAA,CAAAA,IAAA,CAAAkB,IAAA;UACA,SAAAC,CAAA,IAAAF,QAAA;YACA,KAAAjB,IAAA,CAAAoB,IAAA,CAAAD,CAAA,CAAAE,UAAA;YACA,KAAAnB,KAAA,CAAAkB,IAAA,CAAAD,CAAA,CAAAG,aAAA;UACA;UAEA,KAAAC,SAAA;QACA;MAGA;IACA;IACAA,UAAA;MAGA,MAAAC,cAAA;QACAC,IAAA;QACAC,CAAA;QACAC,CAAA;QACAC,EAAA;QACAC,EAAA;QACAC,UAAA,GACA;UACAC,MAAA;UACAC,KAAA;QACA,GAGA;UACAD,MAAA;UACAC,KAAA;QACA;MAEA;MACA,IAAAC,MAAA;QACA;QACAC,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA,GACA;UACAd,IAAA;UACAzB,IAAA,OAAAE,KAAA;UACAsC,QAAA;YACAC,IAAA;UAEA;UACAC,SAAA;YACAV,KAAA;YACAW,QAAA;YACAC,OAAA;UACA;UACAC,QAAA;YACAJ,IAAA;UACA;QACA,EAEA;QACAK,KAAA;UACArB,IAAA;UACAgB,IAAA;UACAD,QAAA;YACAO,SAAA;cACAf,KAAA;YACA;UACA;UACAgB,SAAA;YACAP,IAAA;UAEA;QACA;QACAQ,QAAA,GACA;UACAxB,IAAA;UACAgB,IAAA;UACAS,KAAA;UACAC,GAAA;UACAb,MAAA;UACAc,gBAAA;QACA,MACA;QACAC,MAAA,GACA;UACAtD,IAAA;UACA0B,IAAA;UACA6B,UAAA;UACAC,cAAA;UACAC,QAAA;UACAxD,IAAA,OAAAA,IAAA;UACAyD,SAAA;YACA;YACA;YACAC,WAAA;YACA1B,KAAA;cACAP,IAAA;cACAC,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA,GACA;gBACAC,MAAA;gBACAC,KAAA;cACA,GAGA;gBACAD,MAAA;gBACAC,KAAA;cACA;cAEA;YACA;UACA;UACA2B,OAAA;YACAlB,IAAA;UACA;UAEAmB,CAAA;QACA,GACA;UACA7D,IAAA;UACA4D,OAAA;YACAlB,IAAA;UACA;UACAhB,IAAA;UACA+B,QAAA;UACAC,SAAA;YACA;YACA;YACAzB,KAAA,EAAAR,cAAA;YACAkC,WAAA;YACA;UACA;UACA1D,IAAA,OAAAA,IAAA;UACA6D,MAAA;UACAD,CAAA;QAEA,GACA;UACA;UACAA,CAAA;UACA7D,IAAA;UACA0B,IAAA;UACAqC,cAAA;UACA9D,IAAA,OAAAA,IAAA;UACA+D,MAAA;UACAC,YAAA;UACAC,YAAA;UACAC,UAAA;UACAT,SAAA;YACA;YACAC,WAAA;YACA1B,KAAA;cACAP,IAAA;cACAC,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA,GACA;gBACAC,MAAA;gBACAC,KAAA;cACA,GAEA;gBACAD,MAAA;gBACAC,KAAA;cACA;YAEA;;YAEA;UACA;UACA2B,OAAA;YACAlB,IAAA;UACA;UACA0B,KAAA;YACA1B,IAAA;YACA2B,QAAA;YACArC,MAAA;YACAY,QAAA;YACA0B,UAAA;YACArC,KAAA;UAEA;QAIA;MAIA;MAEA,WAAAC,MAAA,iBAAAA,MAAA;QACA,KAAAhC,SAAA,CAAAsB,SAAA,CAAAU,MAAA;MACA;MACAqC,MAAA,CAAAC,QAAA,QAAAtE,SAAA,CAAAuE,MAAA;IAGA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}