{"ast": null, "code": "import request from '@/utils/api/getHZVideo.js';\nexport function getTokenAuthentication() {\n  return request.get('/sso/oauth2.0/accessToken?grant_type=client_credentials&client_id=01&client_secret=admin123&format=json');\n}\nexport function getVideoUrl(channel_code, stream_mode) {\n  return request.get(`/api/vms/v2/webuas/live/stream/url?channel_code=${channel_code}&stream_type=0&stream_mode=${stream_mode}&visit_ip=************`);\n}\nexport function getVideoListInfo(params) {\n  return request.get(`/api/bss/v1/udm/channel/list-with-device`, {\n    params\n  });\n}\n\n// 获取设备基础组织\nexport function getDeviceOrg(params = {}) {\n  return request.get('/api/bss/v1/uuv/users/privilege/device-org/tree', {\n    params\n  });\n}\nexport function getResourcePage(params) {\n  return request.get(`/api/bss/v1/uuv/users/privilege/device-org/resource/page?org_index=${params}`);\n}\n\n//  订阅事件\nexport function getSubScriptions(params) {\n  return request.post('/api/eco/v2/evc/event/subscriptions', params);\n}\n\n//  已订阅\nexport function getSubList() {\n  return request.get('/api/eco/v2/evc/event/subscriptions');\n}\n// 图片路径转换\nexport function getImgUrl(params) {\n  return request.post(`/api/mg/v2/fms/files/url/translate`, params);\n}", "map": {"version": 3, "names": ["request", "getTokenAuthentication", "get", "getVideoUrl", "channel_code", "stream_mode", "getVideoListInfo", "params", "getDeviceOrg", "getResourcePage", "getSubScriptions", "post", "getSubList", "getImgUrl"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/hzVideo.js"], "sourcesContent": ["import request from '@/utils/api/getHZVideo.js'\r\n\r\nexport function getTokenAuthentication() {\r\n    return request.get('/sso/oauth2.0/accessToken?grant_type=client_credentials&client_id=01&client_secret=admin123&format=json')\r\n}\r\nexport function getVideoUrl(channel_code, stream_mode) {\r\n    return request.get(`/api/vms/v2/webuas/live/stream/url?channel_code=${channel_code}&stream_type=0&stream_mode=${stream_mode}&visit_ip=************`)\r\n }\r\n \r\n export function getVideoListInfo(params){\r\n     return request.get(`/api/bss/v1/udm/channel/list-with-device`,{\r\n        params\r\n     })\r\n }\r\n\r\n// 获取设备基础组织\r\nexport function getDeviceOrg(params={}){\r\n    return request.get('/api/bss/v1/uuv/users/privilege/device-org/tree',{\r\n        params\r\n    })\r\n}\r\n\r\nexport function getResourcePage(params){\r\n    return request.get(`/api/bss/v1/uuv/users/privilege/device-org/resource/page?org_index=${params}`)\r\n}\r\n\r\n//  订阅事件\r\n export function getSubScriptions(params){\r\n    return request.post('/api/eco/v2/evc/event/subscriptions',params)\r\n }\r\n\r\n//  已订阅\r\nexport function getSubList(){\r\n    return request.get('/api/eco/v2/evc/event/subscriptions')\r\n}\r\n// 图片路径转换\r\nexport function getImgUrl(params){\r\n    return request.post(`/api/mg/v2/fms/files/url/translate`,params)\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,2BAA2B;AAE/C,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACrC,OAAOD,OAAO,CAACE,GAAG,CAAC,yGAAyG,CAAC;AACjI;AACA,OAAO,SAASC,WAAWA,CAACC,YAAY,EAAEC,WAAW,EAAE;EACnD,OAAOL,OAAO,CAACE,GAAG,CAAC,mDAAmDE,YAAY,8BAA8BC,WAAW,wBAAwB,CAAC;AACvJ;AAEA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAC;EACpC,OAAOP,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAC;IAC3DK;EACH,CAAC,CAAC;AACN;;AAED;AACA,OAAO,SAASC,YAAYA,CAACD,MAAM,GAAC,CAAC,CAAC,EAAC;EACnC,OAAOP,OAAO,CAACE,GAAG,CAAC,iDAAiD,EAAC;IACjEK;EACJ,CAAC,CAAC;AACN;AAEA,OAAO,SAASE,eAAeA,CAACF,MAAM,EAAC;EACnC,OAAOP,OAAO,CAACE,GAAG,CAAC,sEAAsEK,MAAM,EAAE,CAAC;AACtG;;AAEA;AACC,OAAO,SAASG,gBAAgBA,CAACH,MAAM,EAAC;EACrC,OAAOP,OAAO,CAACW,IAAI,CAAC,qCAAqC,EAACJ,MAAM,CAAC;AACpE;;AAED;AACA,OAAO,SAASK,UAAUA,CAAA,EAAE;EACxB,OAAOZ,OAAO,CAACE,GAAG,CAAC,qCAAqC,CAAC;AAC7D;AACA;AACA,OAAO,SAASW,SAASA,CAACN,MAAM,EAAC;EAC7B,OAAOP,OAAO,CAACW,IAAI,CAAC,oCAAoC,EAACJ,MAAM,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}