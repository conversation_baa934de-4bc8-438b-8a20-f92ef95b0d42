{"ast": null, "code": "import canvasPlayerList from '@/components/rslPlayer/components/canvasPlayerList.vue';\nimport { openFullscreen, exitFullscreen, getWindowRatio } from '@/utils/fullScreen';\nexport default {\n  name: 'canvasPlayerSingle',\n  components: {\n    canvasPlayerList\n  },\n  data() {\n    return {\n      // 51视频\n      playerSingleModalStyle: {\n        // position: '',\n        width: \"460px\",\n        height: \"380px\"\n      },\n      playerSingleStyle: {\n        showClose: false,\n        showFull: false,\n        itemBorder: false\n      },\n      headerFlag: false,\n      playerSingleTitle: \"\",\n      playerArray2: [],\n      bottomList: [],\n      orgChildTabs: '',\n      current_height: 0,\n      // 当前高度\n      current_width: 0,\n      // 当前宽度\n      isFull: false,\n      skyCount: 0,\n      // 当前点击标识\n      numSet: 0,\n      currentScreenSize: 0\n    };\n  },\n  props: {\n    list: {\n      type: Array,\n      default: []\n    },\n    currentWin: {\n      type: Number,\n      default: 4\n    }\n  },\n  computed: {\n    canvasPlayListStyle() {\n      return {\n        width: this.bodyWidth,\n        height: this.bodyHeight\n      };\n    },\n    /*\r\n    整个弹窗的高度和宽度\r\n    */\n    modalStyle() {\n      return {\n        ...this.playerSingleModalStyle\n      };\n    },\n    bodyHeight() {\n      // let height = window.screen.height;\n      let height = this.current_height;\n      //   let total = 50 ;\n      let allHeight = this.isFull ? height : parseInt(this.modalStyle.height);\n      return allHeight;\n    },\n    /*\r\n    el-dialog__body的宽度,全屏时窗口的宽度;\r\n    */\n    bodyWidth() {\n      // let width = window.screen.width;\n      let width = this.current_width;\n      return this.isFull ? width : parseInt(this.modalStyle.width);\n    },\n    // 弹出窗的id 属性;\n    modalId() {\n      return `video-container`;\n    }\n  },\n  watch: {\n    currentWin(nv) {\n      this.$refs['videoRsl'].handleChangeWin(nv);\n    }\n  },\n  mounted() {\n    let _that = this;\n    if (document.addEventListener) {\n      document.addEventListener('webkitfullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('mozfullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('fullscreenchange', _that.exitFullscreen, false);\n      document.addEventListener('MSFullscreenChange', _that.exitFullscreen, false);\n      window.addEventListener('resize', _that.resizeChange, false);\n      window.addEventListener('resize', this.changePercentToPx);\n    }\n    this.$nextTick(() => {\n      this.changePercentToPx();\n    });\n  },\n  beforeDestroy() {\n    let _that = this;\n    if (document.removeEventListener) {\n      document.removeEventListener('webkitfullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('mozfullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('fullscreenchange', _that.exitFullscreen);\n      document.removeEventListener('MSFullscreenChange', _that.exitFullscreen);\n      window.removeEventListener('resize', _that.resizeChange);\n    }\n    if (this.playerArray2.length) {\n      this.closeBaseVideo();\n    }\n    window.removeEventListener('resize', this.changePercentToPx);\n  },\n  methods: {\n    //播放内部视频\n    baseVideo(code) {\n      const selectVideo = this.list.find(item => item.cameraCode === code);\n      selectVideo.deviceCode = selectVideo.cameraCode;\n      if (this.playerArray2.length >= this.currentWin) {\n        this.playerArray2.splice(this.skyCount, 1, selectVideo);\n        this.skyCount >= this.currentWin - 1 ? this.skyCount = 0 : this.skyCount++;\n      } else {\n        this.playerArray2.push(selectVideo);\n      }\n      this.$refs.videoRsl.open();\n      // this.playerSingleTitle = selectVideo.cameraName;\n    },\n    // 判断监控数量\n    addBaseVideo(cameraCode) {\n      if (this.playerArray2.length >= this.currentWin) {\n        this.closeBaseVideo(this.playerArray2[this.skyCount].id);\n      }\n      setTimeout(() => {\n        this.baseVideo(cameraCode);\n      }, 100);\n    },\n    //isFull\n    closeBaseVideo(id) {\n      if (id) {\n        this.$refs.videoRsl.stopLoop(id);\n      } else {\n        this.$refs.videoRsl.stop();\n        this.playerArray2 = [];\n      }\n    },\n    // 当前的播放状态;\n    handlePlayType(cameraCode, type) {\n      let index = this.playerArray2.findIndex(item => item['cameraCode'] == cameraCode);\n      if (index != -1) {\n        // this.$set(this.playerArray2[index], 'type', type);\n        this.playerArray2[index]['type'] = type;\n      }\n    },\n    // 自定义按钮的点击事件\n    handleBtnClick(player, index) {\n      this.$emit('handleBtnClick', player, index);\n    },\n    handleCloseItem({\n      cameraCode\n    }) {\n      let cancelIndex = this.list.findIndex(item => item === cameraCode);\n      if (cancelIndex != -1) {\n        let cancelItem = this.playerArray[cancelIndex];\n        this.checkList.splice(cancelIndex, 1, '');\n        this.playerArray.splice(cancelIndex, 1, {\n          cameraCode: '',\n          cameraName: '',\n          id: cancelItem['id'],\n          type: true\n        });\n      }\n    },\n    resizeChange() {\n      if (!this.currentScreenSize) {\n        this.isFull = false;\n      } else {\n        this.isFull = true;\n        this.current_height = document.body.clientHeight;\n        this.current_width = document.body.clientWidth;\n      }\n    },\n    isFullScreen() {\n      let fullFlag = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement ? true : false;\n      if (this.currentScreenSize && !fullFlag) {\n        this.fullScreen();\n      }\n    },\n    fullScreen() {\n      let id = this.modalId;\n      if (this.isFull) {\n        exitFullscreen();\n        this.$set(this, 'isFull', false);\n        this.currentScreenSize = 0;\n      } else {\n        openFullscreen(id);\n        this.$set(this, 'isFull', true);\n        this.currentScreenSize = 1;\n      }\n      this.resizeChange();\n\n      // 切换屏幕显示状态;\n      this.$refs['videoRsl'].resize();\n    },\n    // 换算当前%为px\n    changePercentToPx() {\n      let dom = document.querySelector('.rightVideoShow');\n      let css = window.getComputedStyle(dom);\n      this.playerSingleModalStyle.width = css.width;\n      this.playerSingleModalStyle.height = css.height;\n    },\n    handleDblclick(val) {\n      this.$emit('handleDblclick', val);\n    }\n  }\n};", "map": {"version": 3, "names": ["canvasPlayerList", "openFullscreen", "exitFullscreen", "getWindowRatio", "name", "components", "data", "playerSingleModalStyle", "width", "height", "player<PERSON><PERSON><PERSON><PERSON><PERSON>le", "showClose", "showFull", "itemBorder", "headerFlag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playerArray2", "bottomList", "orgChildTabs", "current_height", "current_width", "isFull", "skyCount", "numSet", "currentScreenSize", "props", "list", "type", "Array", "default", "currentWin", "Number", "computed", "canvasPlayListStyle", "bodyWidth", "bodyHeight", "modalStyle", "allHeight", "parseInt", "modalId", "watch", "nv", "$refs", "handleChangeWin", "mounted", "_that", "document", "addEventListener", "window", "resizeChange", "changePercentToPx", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "length", "closeBaseVideo", "methods", "baseVideo", "code", "selectVideo", "find", "item", "cameraCode", "deviceCode", "splice", "push", "videoRsl", "open", "addBaseVideo", "id", "setTimeout", "stopLoop", "stop", "handlePlayType", "index", "findIndex", "handleBtnClick", "player", "$emit", "handleCloseItem", "cancelIndex", "cancelItem", "<PERSON><PERSON><PERSON><PERSON>", "checkList", "cameraName", "body", "clientHeight", "clientWidth", "isFullScreen", "fullFlag", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "fullScreen", "$set", "resize", "dom", "querySelector", "css", "getComputedStyle", "handleDblclick", "val"], "sources": ["src/components/rslPlayer/components/canvasPlayerSingle.vue"], "sourcesContent": ["<template>\r\n    <div class=\"box\">\r\n        <span :class=\"['fullOrSmall', isFull ? 'isFull' : '']\" @click.stop=\"fullScreen\"> </span>\r\n        <canvas-player-list :player-list-style=\"playerSingleStyle\" :player-array=\"playerArray2\" :init-num=\"currentWin\"\r\n            :height=\"canvasPlayListStyle.height + 'px'\" :width=\"canvasPlayListStyle.width + 'px'\" ref=\"videoRsl\"\r\n            :bottom-list=\"bottomList\" @playType=\"handlePlayType\" @handleBtnClick=\"handleBtnClick\"\r\n            @closeItem=\"handleCloseItem\" @handleDblclick=\"handleDblclick\">\r\n        </canvas-player-list>\r\n    </div>\r\n</template>\r\n<script>\r\nimport canvasPlayerList from '@/components/rslPlayer/components/canvasPlayerList.vue'\r\nimport { openFullscreen, exitFullscreen, getWindowRatio } from '@/utils/fullScreen'\r\n\r\nexport default {\r\n    name: 'canvasPlayerSingle',\r\n    components: { canvasPlayerList },\r\n    data() {\r\n        return {\r\n            // 51视频\r\n            playerSingleModalStyle: {\r\n                // position: '',\r\n                width: \"460px\",\r\n                height: \"380px\",\r\n            },\r\n            playerSingleStyle: {\r\n                showClose: false,\r\n                showFull: false,\r\n                itemBorder: false,\r\n            },\r\n            headerFlag: false,\r\n            playerSingleTitle: \"\",\r\n            playerArray2: [],\r\n            bottomList: [],\r\n            orgChildTabs: '',\r\n            current_height: 0, // 当前高度\r\n            current_width: 0, // 当前宽度\r\n            isFull: false,\r\n            skyCount: 0, // 当前点击标识\r\n            numSet: 0,\r\n            currentScreenSize: 0\r\n\r\n        }\r\n    },\r\n    props: {\r\n        list: {\r\n            type: Array,\r\n            default: []\r\n        },\r\n        currentWin: {\r\n            type: Number,\r\n            default: 4\r\n        }\r\n    },\r\n    computed: {\r\n        canvasPlayListStyle() {\r\n            return {\r\n                width: this.bodyWidth,\r\n                height: this.bodyHeight\r\n            }\r\n        },\r\n        /*\r\n        整个弹窗的高度和宽度\r\n    */\r\n        modalStyle() {\r\n            return {\r\n                ...this.playerSingleModalStyle,\r\n            }\r\n        },\r\n        bodyHeight() {\r\n            // let height = window.screen.height;\r\n            let height = this.current_height\r\n            //   let total = 50 ;\r\n            let allHeight = this.isFull ? height : parseInt(this.modalStyle.height);\r\n            return allHeight;\r\n        },\r\n        /*\r\n        el-dialog__body的宽度,全屏时窗口的宽度;\r\n        */\r\n        bodyWidth() {\r\n            // let width = window.screen.width;\r\n            let width = this.current_width\r\n            return this.isFull ? width : parseInt(this.modalStyle.width);\r\n        },\r\n        // 弹出窗的id 属性;\r\n        modalId() {\r\n            return `video-container`;\r\n        },\r\n    },\r\n    watch: {\r\n        currentWin(nv) {\r\n            this.$refs['videoRsl'].handleChangeWin(nv);\r\n        }\r\n    },\r\n    mounted() {\r\n        let _that = this;\r\n        if (document.addEventListener) {\r\n            document.addEventListener('webkitfullscreenchange', _that.exitFullscreen, false);\r\n            document.addEventListener('mozfullscreenchange', _that.exitFullscreen, false);\r\n            document.addEventListener('fullscreenchange', _that.exitFullscreen, false);\r\n            document.addEventListener('MSFullscreenChange', _that.exitFullscreen, false);\r\n            window.addEventListener('resize', _that.resizeChange, false);\r\n            window.addEventListener('resize', this.changePercentToPx);\r\n\r\n        }\r\n        this.$nextTick(() => {\r\n            this.changePercentToPx()\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n        let _that = this;\r\n        if (document.removeEventListener) {\r\n            document.removeEventListener('webkitfullscreenchange', _that.exitFullscreen);\r\n            document.removeEventListener('mozfullscreenchange', _that.exitFullscreen);\r\n            document.removeEventListener('fullscreenchange', _that.exitFullscreen);\r\n            document.removeEventListener('MSFullscreenChange', _that.exitFullscreen);\r\n            window.removeEventListener('resize', _that.resizeChange);\r\n\r\n        }\r\n        if (this.playerArray2.length) {\r\n            this.closeBaseVideo()\r\n\r\n        }\r\n        window.removeEventListener('resize', this.changePercentToPx);\r\n\r\n    },\r\n    methods: {\r\n        //播放内部视频\r\n        baseVideo(code) {\r\n            const selectVideo = this.list.find(item => item.cameraCode === code);\r\n            selectVideo.deviceCode = selectVideo.cameraCode;\r\n\r\n            if (this.playerArray2.length >= this.currentWin) {\r\n                this.playerArray2.splice(this.skyCount, 1, selectVideo)\r\n                this.skyCount >= this.currentWin - 1 ? this.skyCount = 0 : this.skyCount++;\r\n            } else {\r\n                this.playerArray2.push(selectVideo);\r\n\r\n            }\r\n\r\n            this.$refs.videoRsl.open()\r\n            // this.playerSingleTitle = selectVideo.cameraName;\r\n        },\r\n        // 判断监控数量\r\n        addBaseVideo(cameraCode) {\r\n            if (this.playerArray2.length >= this.currentWin) {\r\n                this.closeBaseVideo(this.playerArray2[this.skyCount].id)\r\n            }\r\n            setTimeout(() => {\r\n                this.baseVideo(cameraCode)\r\n            }, 100)\r\n        },\r\n        //isFull\r\n        closeBaseVideo(id) {\r\n            if (id) {\r\n                this.$refs.videoRsl.stopLoop(id);\r\n            } else {\r\n                this.$refs.videoRsl.stop();\r\n                this.playerArray2 = [];\r\n\r\n            }\r\n\r\n        },\r\n\r\n        // 当前的播放状态;\r\n        handlePlayType(cameraCode, type) {\r\n            let index = this.playerArray2.findIndex(item => item['cameraCode'] == cameraCode);\r\n            if (index != -1) {\r\n                // this.$set(this.playerArray2[index], 'type', type);\r\n                this.playerArray2[index]['type'] = type\r\n            }\r\n        },\r\n        // 自定义按钮的点击事件\r\n        handleBtnClick(player, index) {\r\n            this.$emit('handleBtnClick', player, index);\r\n        },\r\n        handleCloseItem({ cameraCode }) {\r\n            let cancelIndex = this.list.findIndex(item => item === cameraCode);\r\n            if (cancelIndex != -1) {\r\n                let cancelItem = this.playerArray[cancelIndex];\r\n                this.checkList.splice(cancelIndex, 1, '');\r\n                this.playerArray.splice(cancelIndex, 1, {\r\n                    cameraCode: '',\r\n                    cameraName: '',\r\n                    id: cancelItem['id'],\r\n                    type: true\r\n                });\r\n            }\r\n        },\r\n        resizeChange() {\r\n\r\n            if (!this.currentScreenSize) {\r\n                this.isFull = false\r\n            } else {\r\n                this.isFull = true\r\n                this.current_height = document.body.clientHeight\r\n                this.current_width = document.body.clientWidth\r\n            }\r\n        },\r\n        isFullScreen() {\r\n            let fullFlag = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement ? true : false;\r\n            if (this.currentScreenSize && !fullFlag) {\r\n                this.fullScreen()\r\n            }\r\n\r\n\r\n        },\r\n        fullScreen() {\r\n            let id = this.modalId;\r\n            if (this.isFull) {\r\n                exitFullscreen();\r\n                this.$set(this, 'isFull', false);\r\n                this.currentScreenSize = 0\r\n            } else {\r\n                openFullscreen(id);\r\n                this.$set(this, 'isFull', true);\r\n                this.currentScreenSize = 1;\r\n\r\n            }\r\n            this.resizeChange()\r\n\r\n            // 切换屏幕显示状态;\r\n            this.$refs['videoRsl'].resize();\r\n        },\r\n        // 换算当前%为px\r\n        changePercentToPx() {\r\n            let dom = document.querySelector('.rightVideoShow');\r\n            let css = window.getComputedStyle(dom)\r\n            this.playerSingleModalStyle.width = css.width;\r\n            this.playerSingleModalStyle.height = css.height;\r\n        },\r\n        handleDblclick(val) {\r\n\r\n            this.$emit('handleDblclick', val)\r\n\r\n\r\n        }\r\n\r\n    },\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.box {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.fullOrSmall {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: -3px;\r\n    z-index: 200;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    width: 20px;\r\n    height: 20px;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    background-size: 20px 20px;\r\n    background-image: url('@/assets/images/video/fullscreen.png');\r\n\r\n    &.isFull {\r\n        background-image: url('@/assets/images/video/exitfull.png');\r\n    }\r\n}\r\n</style>"], "mappings": "AAWA,OAAAA,gBAAA;AACA,SAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAL;EAAA;EACAM,KAAA;IACA;MACA;MACAC,sBAAA;QACA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,iBAAA;QACAC,SAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAC,UAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,UAAA;MACAC,YAAA;MACAC,cAAA;MAAA;MACAC,aAAA;MAAA;MACAC,MAAA;MACAC,QAAA;MAAA;MACAC,MAAA;MACAC,iBAAA;IAEA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,QAAA;IACAC,oBAAA;MACA;QACAzB,KAAA,OAAA0B,SAAA;QACAzB,MAAA,OAAA0B;MACA;IACA;IACA;AACA;AACA;IACAC,WAAA;MACA;QACA,QAAA7B;MACA;IACA;IACA4B,WAAA;MACA;MACA,IAAA1B,MAAA,QAAAU,cAAA;MACA;MACA,IAAAkB,SAAA,QAAAhB,MAAA,GAAAZ,MAAA,GAAA6B,QAAA,MAAAF,UAAA,CAAA3B,MAAA;MACA,OAAA4B,SAAA;IACA;IACA;AACA;AACA;IACAH,UAAA;MACA;MACA,IAAA1B,KAAA,QAAAY,aAAA;MACA,YAAAC,MAAA,GAAAb,KAAA,GAAA8B,QAAA,MAAAF,UAAA,CAAA5B,KAAA;IACA;IACA;IACA+B,QAAA;MACA;IACA;EACA;EACAC,KAAA;IACAV,WAAAW,EAAA;MACA,KAAAC,KAAA,aAAAC,eAAA,CAAAF,EAAA;IACA;EACA;EACAG,QAAA;IACA,IAAAC,KAAA;IACA,IAAAC,QAAA,CAAAC,gBAAA;MACAD,QAAA,CAAAC,gBAAA,2BAAAF,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAC,gBAAA,wBAAAF,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAC,gBAAA,qBAAAF,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAC,gBAAA,uBAAAF,KAAA,CAAA3C,cAAA;MACA8C,MAAA,CAAAD,gBAAA,WAAAF,KAAA,CAAAI,YAAA;MACAD,MAAA,CAAAD,gBAAA,gBAAAG,iBAAA;IAEA;IACA,KAAAC,SAAA;MACA,KAAAD,iBAAA;IACA;EACA;EACAE,cAAA;IACA,IAAAP,KAAA;IACA,IAAAC,QAAA,CAAAO,mBAAA;MACAP,QAAA,CAAAO,mBAAA,2BAAAR,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAO,mBAAA,wBAAAR,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAO,mBAAA,qBAAAR,KAAA,CAAA3C,cAAA;MACA4C,QAAA,CAAAO,mBAAA,uBAAAR,KAAA,CAAA3C,cAAA;MACA8C,MAAA,CAAAK,mBAAA,WAAAR,KAAA,CAAAI,YAAA;IAEA;IACA,SAAAjC,YAAA,CAAAsC,MAAA;MACA,KAAAC,cAAA;IAEA;IACAP,MAAA,CAAAK,mBAAA,gBAAAH,iBAAA;EAEA;EACAM,OAAA;IACA;IACAC,UAAAC,IAAA;MACA,MAAAC,WAAA,QAAAjC,IAAA,CAAAkC,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,UAAA,KAAAJ,IAAA;MACAC,WAAA,CAAAI,UAAA,GAAAJ,WAAA,CAAAG,UAAA;MAEA,SAAA9C,YAAA,CAAAsC,MAAA,SAAAxB,UAAA;QACA,KAAAd,YAAA,CAAAgD,MAAA,MAAA1C,QAAA,KAAAqC,WAAA;QACA,KAAArC,QAAA,SAAAQ,UAAA,YAAAR,QAAA,YAAAA,QAAA;MACA;QACA,KAAAN,YAAA,CAAAiD,IAAA,CAAAN,WAAA;MAEA;MAEA,KAAAjB,KAAA,CAAAwB,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAC,aAAAN,UAAA;MACA,SAAA9C,YAAA,CAAAsC,MAAA,SAAAxB,UAAA;QACA,KAAAyB,cAAA,MAAAvC,YAAA,MAAAM,QAAA,EAAA+C,EAAA;MACA;MACAC,UAAA;QACA,KAAAb,SAAA,CAAAK,UAAA;MACA;IACA;IACA;IACAP,eAAAc,EAAA;MACA,IAAAA,EAAA;QACA,KAAA3B,KAAA,CAAAwB,QAAA,CAAAK,QAAA,CAAAF,EAAA;MACA;QACA,KAAA3B,KAAA,CAAAwB,QAAA,CAAAM,IAAA;QACA,KAAAxD,YAAA;MAEA;IAEA;IAEA;IACAyD,eAAAX,UAAA,EAAAnC,IAAA;MACA,IAAA+C,KAAA,QAAA1D,YAAA,CAAA2D,SAAA,CAAAd,IAAA,IAAAA,IAAA,kBAAAC,UAAA;MACA,IAAAY,KAAA;QACA;QACA,KAAA1D,YAAA,CAAA0D,KAAA,YAAA/C,IAAA;MACA;IACA;IACA;IACAiD,eAAAC,MAAA,EAAAH,KAAA;MACA,KAAAI,KAAA,mBAAAD,MAAA,EAAAH,KAAA;IACA;IACAK,gBAAA;MAAAjB;IAAA;MACA,IAAAkB,WAAA,QAAAtD,IAAA,CAAAiD,SAAA,CAAAd,IAAA,IAAAA,IAAA,KAAAC,UAAA;MACA,IAAAkB,WAAA;QACA,IAAAC,UAAA,QAAAC,WAAA,CAAAF,WAAA;QACA,KAAAG,SAAA,CAAAnB,MAAA,CAAAgB,WAAA;QACA,KAAAE,WAAA,CAAAlB,MAAA,CAAAgB,WAAA;UACAlB,UAAA;UACAsB,UAAA;UACAf,EAAA,EAAAY,UAAA;UACAtD,IAAA;QACA;MACA;IACA;IACAsB,aAAA;MAEA,UAAAzB,iBAAA;QACA,KAAAH,MAAA;MACA;QACA,KAAAA,MAAA;QACA,KAAAF,cAAA,GAAA2B,QAAA,CAAAuC,IAAA,CAAAC,YAAA;QACA,KAAAlE,aAAA,GAAA0B,QAAA,CAAAuC,IAAA,CAAAE,WAAA;MACA;IACA;IACAC,aAAA;MACA,IAAAC,QAAA,GAAA3C,QAAA,CAAA4C,iBAAA,IAAA5C,QAAA,CAAA6C,uBAAA,IAAA7C,QAAA,CAAA8C,oBAAA,IAAA9C,QAAA,CAAA+C,mBAAA;MACA,SAAArE,iBAAA,KAAAiE,QAAA;QACA,KAAAK,UAAA;MACA;IAGA;IACAA,WAAA;MACA,IAAAzB,EAAA,QAAA9B,OAAA;MACA,SAAAlB,MAAA;QACAnB,cAAA;QACA,KAAA6F,IAAA;QACA,KAAAvE,iBAAA;MACA;QACAvB,cAAA,CAAAoE,EAAA;QACA,KAAA0B,IAAA;QACA,KAAAvE,iBAAA;MAEA;MACA,KAAAyB,YAAA;;MAEA;MACA,KAAAP,KAAA,aAAAsD,MAAA;IACA;IACA;IACA9C,kBAAA;MACA,IAAA+C,GAAA,GAAAnD,QAAA,CAAAoD,aAAA;MACA,IAAAC,GAAA,GAAAnD,MAAA,CAAAoD,gBAAA,CAAAH,GAAA;MACA,KAAA1F,sBAAA,CAAAC,KAAA,GAAA2F,GAAA,CAAA3F,KAAA;MACA,KAAAD,sBAAA,CAAAE,MAAA,GAAA0F,GAAA,CAAA1F,MAAA;IACA;IACA4F,eAAAC,GAAA;MAEA,KAAAxB,KAAA,mBAAAwB,GAAA;IAGA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}