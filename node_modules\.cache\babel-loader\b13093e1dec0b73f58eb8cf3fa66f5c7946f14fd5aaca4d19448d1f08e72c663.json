{"ast": null, "code": "export default {\n  namespaced: true,\n  state: {\n    processFlowInfo: [],\n    //流程图\n    eventTable: false,\n    eventType: false,\n    homeFlag: false\n  },\n  getters: {},\n  mutations: {\n    getProcessFlowInfo(state, payload) {\n      state.processFlowInfo = payload;\n    },\n    getEventTable(state, payload) {\n      state.eventTable = payload;\n      if (!payload) {\n        state.eventType = false;\n      }\n    },\n    getEventType(state, payload) {\n      state.eventType = payload;\n    },\n    getHomeFlag(state, payload) {\n      if (payload == null || payload == undefined || payload == '') {\n        state.homeFlag = !state.homeFlag;\n      } else {\n        state.homeFlag = payload;\n      }\n    }\n  },\n  actions: {}\n};", "map": {"version": 3, "names": ["namespaced", "state", "processFlowInfo", "eventTable", "eventType", "homeFlag", "getters", "mutations", "getProcessFlowInfo", "payload", "getEventTable", "getEventType", "getHomeFlag", "undefined", "actions"], "sources": ["D:/Project/HuaQiaoSanQi/src/store/eventProcessFlow.js"], "sourcesContent": ["export default {\r\n    namespaced: true,\r\n    state: {\r\n        processFlowInfo: [], //流程图\r\n        eventTable: false,\r\n        eventType: false,\r\n        homeFlag: false\r\n    },\r\n    getters: {\r\n\r\n    },\r\n    mutations: {\r\n        getProcessFlowInfo(state, payload) {\r\n            state.processFlowInfo = payload\r\n        },\r\n        getEventTable(state, payload) {\r\n            state.eventTable = payload\r\n            if (!payload) {\r\n                state.eventType = false\r\n            }\r\n        },\r\n        getEventType(state, payload) {\r\n            state.eventType = payload\r\n        },\r\n        getHomeFlag(state, payload) {\r\n            if (payload == null || payload == undefined || payload == '') {\r\n                state.homeFlag = !state.homeFlag\r\n            }else{\r\n                state.homeFlag = payload\r\n\r\n            }\r\n        }\r\n    },\r\n    actions: {\r\n\r\n    }\r\n}"], "mappings": "AAAA,eAAe;EACXA,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE;IACHC,eAAe,EAAE,EAAE;IAAE;IACrBC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;EACd,CAAC;EACDC,OAAO,EAAE,CAET,CAAC;EACDC,SAAS,EAAE;IACPC,kBAAkBA,CAACP,KAAK,EAAEQ,OAAO,EAAE;MAC/BR,KAAK,CAACC,eAAe,GAAGO,OAAO;IACnC,CAAC;IACDC,aAAaA,CAACT,KAAK,EAAEQ,OAAO,EAAE;MAC1BR,KAAK,CAACE,UAAU,GAAGM,OAAO;MAC1B,IAAI,CAACA,OAAO,EAAE;QACVR,KAAK,CAACG,SAAS,GAAG,KAAK;MAC3B;IACJ,CAAC;IACDO,YAAYA,CAACV,KAAK,EAAEQ,OAAO,EAAE;MACzBR,KAAK,CAACG,SAAS,GAAGK,OAAO;IAC7B,CAAC;IACDG,WAAWA,CAACX,KAAK,EAAEQ,OAAO,EAAE;MACxB,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAII,SAAS,IAAIJ,OAAO,IAAI,EAAE,EAAE;QAC1DR,KAAK,CAACI,QAAQ,GAAG,CAACJ,KAAK,CAACI,QAAQ;MACpC,CAAC,MAAI;QACDJ,KAAK,CAACI,QAAQ,GAAGI,OAAO;MAE5B;IACJ;EACJ,CAAC;EACDK,OAAO,EAAE,CAET;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}