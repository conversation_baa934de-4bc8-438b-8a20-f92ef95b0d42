{"ast": null, "code": "import { getLayersList, deleteLayer } from '@/api/userMenu';\nimport layerDialog from \"./layerDialog.vue\";\nexport default {\n  name: 'layerManage',\n  data() {\n    return {\n      // 查询参数\n      queryParams: {\n        state: '',\n        pageSize: 10,\n        pageNum: 1,\n        startDate: '',\n        endDate: '',\n        content: ''\n      },\n      total: '',\n      tabList: [],\n      time: ['', ''],\n      //加载类型\n      loadOptions: [],\n      searchRules: {\n        content: [{\n          pattern: /^[\\u4e00-\\u9fa5_a-zA-Z0-9.·-]+$/,\n          message: \"名称不支持特殊字符\",\n          trigger: \"blur\"\n        }, {\n          max: 30,\n          message: \"长度在 30 个字符内\",\n          trigger: \"blur\"\n        }]\n      }\n    };\n  },\n  components: {\n    layerDialog\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.getLayersManageList();\n  },\n  updated() {},\n  methods: {\n    // 搜索\n    handleQuery() {\n      this.getLayersManageList();\n    },\n    //重置\n    resetQuery() {\n      this.queryParams.content = '';\n      this.time = ['', ''];\n      this.queryParams.startDate = '';\n      this.queryParams.endDate = '';\n      this.queryParams.state = '';\n      this.queryParams.pageNum = 1;\n      this.getLayersManageList();\n    },\n    //监听页码变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getLayersManageList();\n    },\n    // 获取图层数据\n    getLayersManageList() {\n      this.queryParams.startDate = this.time[0];\n      this.queryParams.endDate = this.time[1];\n      getLayersList(this.queryParams).then(res => {\n        if (res.data.code == 200) {\n          this.tabList = res.data.extra.data;\n          this.total = res.data.extra.total;\n          this.queryParams.pageNum = res.data.extra.pageNum;\n          this.queryParams.pageSize = res.data.extra.pageSize;\n        }\n      });\n    },\n    // 删除图层数据 \n    deleteFn(id) {\n      this.$confirm('此操作将永久删除, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteLayer({\n          ids: id\n        }).then(res => {\n          if (res.data.code == 200) {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getLayersManageList();\n          }\n        });\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    },\n    // 编辑图层数据\n    editFn(item) {\n      let data = JSON.parse(JSON.stringify(item));\n      this.$refs.openModal.openAciton(\"edit\", data);\n    },\n    // 新增图层数据\n    addFn() {\n      this.$refs.openModal.openAciton(\"add\", {});\n    }\n  },\n  beforeDestroy() {}\n};", "map": {"version": 3, "names": ["getLayersList", "deleteLayer", "layerDialog", "name", "data", "queryParams", "state", "pageSize", "pageNum", "startDate", "endDate", "content", "total", "tabList", "time", "loadOptions", "searchRules", "pattern", "message", "trigger", "max", "components", "watch", "computed", "mounted", "getLayersManageList", "updated", "methods", "handleQuery", "reset<PERSON><PERSON>y", "handleCurrentChange", "val", "then", "res", "code", "extra", "deleteFn", "id", "$confirm", "confirmButtonText", "cancelButtonText", "type", "ids", "$message", "catch", "editFn", "item", "JSON", "parse", "stringify", "$refs", "openModal", "openAciton", "addFn", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/mainPage/components/resource-center/components/layerManagement.vue"], "sourcesContent": ["<template>\r\n    <div class=\"layerManage\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" :rules=\"searchRules\">\r\n            <el-form-item label=\"资源内容\" prop=\"content\">\r\n                <el-input v-model.trim=\"queryParams.content\" placeholder=\"请输入资源内容\" clearable size=\"small\"\r\n                    style=\"caret-color: #fff;\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"发布时间\" prop=\"time\">\r\n                <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"time\" type=\"daterange\" range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" style=\"caret-color: #fff;\">\r\n                </el-date-picker>\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"state\">\r\n                <el-select v-model=\"queryParams.state\" placeholder=\"图层状态\" clearable size=\"small\">\r\n                    <el-option key=\"1\" label=\"正常\" value=\"1\" />\r\n                    <el-option key=\"0\" label=\"停用\" value=\"0\" />\r\n                </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n        <el-button class=\"publish\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addFn\">新增</el-button>\r\n        <el-table :data=\"tabList\">\r\n            <el-table-column prop=\"id\" label=\"主键\" width=\"302\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"content\" label=\"资源内容\" width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" show-overflow-tooltip>\r\n                <template slot-scope=\"scope\">\r\n                    <span v-if=\"scope.row.state == 1\">正常</span>\r\n                    <span v-if=\"scope.row.state == 0\">停用</span>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"locationExternalUrl\" label=\"地址\" width=\"600\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"name\" label=\"图层名称\" width=\"300\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"description\" label=\"描述\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\" width=\"165\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-button type=\"text\" size=\"small\" @click=\"editFn(scope.row)\">修改</el-button>\r\n                    <el-button type=\"text\" size=\"small\" @click=\"deleteFn(scope.row.id)\">删除</el-button>\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"queryParams.pageNum\"\r\n            :page-size=\"queryParams.pageSize\" layout=\"total, prev, pager, next, jumper\" :total=\"total\">\r\n        </el-pagination>\r\n        <layerDialog ref=\"openModal\" @resetList=\"resetQuery\" />\r\n    </div>\r\n</template>\r\n<script>\r\nimport { getLayersList, deleteLayer } from '@/api/userMenu'\r\nimport layerDialog from \"./layerDialog.vue\"\r\nexport default {\r\n    name: 'layerManage',\r\n    data() {\r\n        return {\r\n            // 查询参数\r\n            queryParams: {\r\n                state: '',\r\n                pageSize: 10,\r\n                pageNum: 1,\r\n                startDate: '',\r\n                endDate: '',\r\n                content: '',\r\n            },\r\n            total: '',\r\n            tabList: [],\r\n            time: ['', ''],\r\n            //加载类型\r\n            loadOptions: [],\r\n            searchRules: {\r\n                content: [\r\n                    {\r\n                        pattern: /^[\\u4e00-\\u9fa5_a-zA-Z0-9.·-]+$/,\r\n                        message: \"名称不支持特殊字符\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    { max: 30, message: \"长度在 30 个字符内\", trigger: \"blur\" },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    components: {\r\n        layerDialog,\r\n    },\r\n    watch: {\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.getLayersManageList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 搜索\r\n        handleQuery() {\r\n            this.getLayersManageList();\r\n        },\r\n        //重置\r\n        resetQuery() {\r\n            this.queryParams.content = '';\r\n            this.time = ['', ''];\r\n            this.queryParams.startDate = '';\r\n            this.queryParams.endDate = '';\r\n            this.queryParams.state = '';\r\n            this.queryParams.pageNum = 1;\r\n            this.getLayersManageList();\r\n        },\r\n        //监听页码变化\r\n        handleCurrentChange(val) {\r\n            this.queryParams.pageNum = val;\r\n            this.getLayersManageList();\r\n        },\r\n        // 获取图层数据\r\n        getLayersManageList() {\r\n            this.queryParams.startDate = this.time[0];\r\n            this.queryParams.endDate = this.time[1];\r\n            getLayersList(this.queryParams).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.tabList = res.data.extra.data;\r\n                    this.total = res.data.extra.total;\r\n                    this.queryParams.pageNum = res.data.extra.pageNum;\r\n                    this.queryParams.pageSize = res.data.extra.pageSize;\r\n                }\r\n            })\r\n        },\r\n        // 删除图层数据 \r\n        deleteFn(id) {\r\n            this.$confirm('此操作将永久删除, 是否继续?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                deleteLayer({ ids: id }).then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.$message({\r\n                            type: 'success',\r\n                            message: '删除成功!'\r\n                        });\r\n                        this.getLayersManageList();\r\n                    }\r\n                })\r\n\r\n            }).catch(() => {\r\n                this.$message({\r\n                    type: 'info',\r\n                    message: '已取消删除'\r\n                });\r\n            });\r\n        },\r\n        // 编辑图层数据\r\n        editFn(item) {\r\n            let data = JSON.parse(JSON.stringify(item));\r\n            this.$refs.openModal.openAciton(\"edit\", data);\r\n        },\r\n        // 新增图层数据\r\n        addFn() {\r\n            this.$refs.openModal.openAciton(\"add\", {});\r\n        }\r\n    },\r\n    beforeDestroy() {\r\n    }\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.layerManage {\r\n    width: 100%;\r\n    height: calc(100% - 6vh);\r\n    margin-top: 6vh;\r\n    padding-top: 45px;\r\n    color: #fff;\r\n\r\n\r\n\r\n    // position: relative;\r\n    :deep(.el-form) {\r\n        display: flex;\r\n        margin-left: 36px;\r\n\r\n        .el-form-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 78px;\r\n\r\n            .el-form-item__label {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 18px;\r\n                color: #D1DEE2;\r\n                line-height: 25px;\r\n            }\r\n\r\n            .el-input__inner {\r\n                background: #103452;\r\n                border-radius: 5px;\r\n                border: 1px solid #6A8096;\r\n                color: #fff;\r\n                height: 35px;\r\n            }\r\n\r\n            .el-range-input {\r\n                background: transparent;\r\n            }\r\n\r\n            .el-button {\r\n                color: #fff;\r\n                border: none;\r\n            }\r\n\r\n            .el-button:nth-child(1) {\r\n                background: #0785c9;\r\n            }\r\n\r\n            .el-button:nth-child(2) {\r\n                background: #07c9b2;\r\n            }\r\n        }\r\n\r\n\r\n\r\n\r\n    }\r\n\r\n    :deep(.publish) {\r\n        color: #fff;\r\n        border: none;\r\n        background: #00D0FF;\r\n        margin-left: 36px;\r\n    }\r\n\r\n    :deep(.el-table) {\r\n        background: #1F405C;\r\n        margin: 25px 12px 0 12px;\r\n        width: calc(100% - 24px);\r\n        color: #D1DEE2;\r\n        border: none;\r\n\r\n\r\n        thead {\r\n            border: 2px solid #3B678C;\r\n            height: 73px;\r\n\r\n            tr th {\r\n                background: #124C6F;\r\n\r\n                color: #D1DEE2;\r\n            }\r\n        }\r\n\r\n        .el-table__row {\r\n            background: #1F405C;\r\n        }\r\n\r\n        .el-table__row:hover>td {\r\n            background-color: transparent !important;\r\n        }\r\n\r\n\r\n        .el-table td,\r\n        .el-table th {\r\n            border-bottom: 1px solid #3B678C;\r\n            border-right: none;\r\n            border-left: none;\r\n            border-top: none;\r\n        }\r\n    }\r\n\r\n    :deep(.el-pagination) {\r\n        margin-top: 40px;\r\n        margin-bottom: 30px;\r\n        text-align: center;\r\n\r\n        .el-pagination__total {\r\n            color: #fff;\r\n        }\r\n\r\n        .el-pagination__sizes {\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        >button {\r\n            color: #fff;\r\n            background-color: transparent;\r\n        }\r\n\r\n        ul {\r\n            li {\r\n                background-color: transparent;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        .el-pager .active {\r\n            color: #4BDBFF;\r\n        }\r\n\r\n        .el-pagination__jump {\r\n            color: #fff;\r\n\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #4BDBFF;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAuDA,SAAAA,aAAA,EAAAC,WAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,IAAA;MACA;MACAC,WAAA;MACAC,WAAA;QACAL,OAAA,GACA;UACAM,OAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,UAAA;IACAnB;EACA;EACAoB,KAAA,GACA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,mBAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACA;IACAC,YAAA;MACA,KAAAH,mBAAA;IACA;IACA;IACAI,WAAA;MACA,KAAAxB,WAAA,CAAAM,OAAA;MACA,KAAAG,IAAA;MACA,KAAAT,WAAA,CAAAI,SAAA;MACA,KAAAJ,WAAA,CAAAK,OAAA;MACA,KAAAL,WAAA,CAAAC,KAAA;MACA,KAAAD,WAAA,CAAAG,OAAA;MACA,KAAAiB,mBAAA;IACA;IACA;IACAK,oBAAAC,GAAA;MACA,KAAA1B,WAAA,CAAAG,OAAA,GAAAuB,GAAA;MACA,KAAAN,mBAAA;IACA;IACA;IACAA,oBAAA;MACA,KAAApB,WAAA,CAAAI,SAAA,QAAAK,IAAA;MACA,KAAAT,WAAA,CAAAK,OAAA,QAAAI,IAAA;MACAd,aAAA,MAAAK,WAAA,EAAA2B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA7B,IAAA,CAAA8B,IAAA;UACA,KAAArB,OAAA,GAAAoB,GAAA,CAAA7B,IAAA,CAAA+B,KAAA,CAAA/B,IAAA;UACA,KAAAQ,KAAA,GAAAqB,GAAA,CAAA7B,IAAA,CAAA+B,KAAA,CAAAvB,KAAA;UACA,KAAAP,WAAA,CAAAG,OAAA,GAAAyB,GAAA,CAAA7B,IAAA,CAAA+B,KAAA,CAAA3B,OAAA;UACA,KAAAH,WAAA,CAAAE,QAAA,GAAA0B,GAAA,CAAA7B,IAAA,CAAA+B,KAAA,CAAA5B,QAAA;QACA;MACA;IACA;IACA;IACA6B,SAAAC,EAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAT,IAAA;QACA/B,WAAA;UAAAyC,GAAA,EAAAL;QAAA,GAAAL,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAA7B,IAAA,CAAA8B,IAAA;YACA,KAAAS,QAAA;cACAF,IAAA;cACAvB,OAAA;YACA;YACA,KAAAO,mBAAA;UACA;QACA;MAEA,GAAAmB,KAAA;QACA,KAAAD,QAAA;UACAF,IAAA;UACAvB,OAAA;QACA;MACA;IACA;IACA;IACA2B,OAAAC,IAAA;MACA,IAAA1C,IAAA,GAAA2C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,IAAA;MACA,KAAAI,KAAA,CAAAC,SAAA,CAAAC,UAAA,SAAAhD,IAAA;IACA;IACA;IACAiD,MAAA;MACA,KAAAH,KAAA,CAAAC,SAAA,CAAAC,UAAA;IACA;EACA;EACAE,cAAA,GACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}