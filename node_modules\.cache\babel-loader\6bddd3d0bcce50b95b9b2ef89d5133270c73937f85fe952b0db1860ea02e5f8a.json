{"ast": null, "code": "import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed';\nexport default {\n  name: 'DiaLogPdf',\n  components: {\n    VuePdfEmbed\n  },\n  props: {\n    url: {\n      type: String,\n      default: ''\n    },\n    dialogVisible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      numPage: null,\n      currentPage: 1\n    };\n  },\n  methods: {\n    closeDialog() {\n      this.$emit('updateFun', false);\n    },\n    getNumPage() {\n      let loadingTask = pdf.createLoadingTask(this.url);\n      loadingTask.promise.then(pdf => {\n        this.numPage = pdf.numPages;\n      }).catch(err => {\n        console.error('pdf加载失败', err);\n      });\n    },\n    prePage() {\n      if (this.currentPage > 1) {\n        this.currentPage--;\n      }\n    },\n    nextPage() {\n      if (this.currentPage < this.numPage) {\n        this.currentPage++;\n      }\n    }\n  },\n  mounted() {\n    this.getNumPage();\n  }\n};", "map": {"version": 3, "names": ["VuePdfEmbed", "name", "components", "props", "url", "type", "String", "default", "dialogVisible", "Boolean", "data", "numPage", "currentPage", "methods", "closeDialog", "$emit", "getNumPage", "loadingTask", "pdf", "createLoadingTask", "promise", "then", "numPages", "catch", "err", "console", "error", "prePage", "nextPage", "mounted"], "sources": ["src/components/DiaLogPdf.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <el-dialog class=\"DialogType_Box\" :visible.sync=\"dialogVisible\" width=\"50%\" :fullscreen=\"false\"\r\n            :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\" :before-close=\"closeDialog\"\r\n            append-to-body>\r\n            <!-- 上下一页按钮 -->\r\n            <div class=\"pdf-preview-btn\">\r\n                <div class=\"txt\">\r\n                    <span>{{ currentPage }}/{{ numPage }}</span>\r\n                </div>\r\n                <div class=\"btns\">\r\n                    <el-button size=\"mini\" @click=\"prePage\">上一页</el-button>\r\n                    <el-button size=\"mini\" @click=\"nextPage\">下一页</el-button>\r\n\r\n                </div>\r\n            </div>\r\n            <vue-pdf-embed\r\n                class=\"pdf-preview\"\r\n                ref=\"pdf\"\r\n                :source=\"url\"\r\n                :page=\"currentPage\"\r\n                @loaded=\"onDocumentLoaded\"\r\n                @loading-failed=\"onLoadingFailed\"\r\n            />\r\n            <!-- <iframe :src=\"url\"> </iframe>-->\r\n\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'\r\nexport default {\r\n    name: 'DiaLogPdf',\r\n    components: {\r\n        VuePdfEmbed\r\n    },\r\n    props: {\r\n        url: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        dialogVisible: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n\r\n    },\r\n    data() {\r\n        return {\r\n            numPage: null,\r\n            currentPage: 1,\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        closeDialog() {\r\n            this.$emit('updateFun', false)\r\n        },\r\n        getNumPage() {\r\n            let loadingTask = pdf.createLoadingTask(this.url);\r\n            loadingTask.promise.then(pdf => {\r\n                this.numPage = pdf.numPages;\r\n            }).catch(err => {\r\n                console.error('pdf加载失败', err)\r\n            })\r\n\r\n        },\r\n        prePage() {\r\n            if (this.currentPage > 1) {\r\n                this.currentPage--\r\n            }\r\n        },\r\n        nextPage() {\r\n            if (this.currentPage < this.numPage) {\r\n                this.currentPage++\r\n            }\r\n        }\r\n    },\r\n    mounted() {\r\n        this.getNumPage()\r\n    }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n::v-deep .el-dialog {\r\n    .el-dialog__header{\r\n        padding: 10px;\r\n    }\r\n}\r\n.DialogType_Box {\r\n    \r\n    .pdf-preview-btn{\r\n        display: flex;\r\n        align-items: center;\r\n        position: absolute;\r\n        top: 15px;\r\n        z-index: 99;\r\n        .btns{\r\n          margin-left: 50px;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.pdf-preview {\r\n    overflow: auto;\r\n    width: 100%;\r\n    height: 72vh;\r\n}\r\n</style>"], "mappings": "AA+BA,OAAAA,WAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,aAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EAEA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;IACA;EACA;EAEAC,OAAA;IACAC,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,WAAA;MACA,IAAAC,WAAA,GAAAC,GAAA,CAAAC,iBAAA,MAAAf,GAAA;MACAa,WAAA,CAAAG,OAAA,CAAAC,IAAA,CAAAH,GAAA;QACA,KAAAP,OAAA,GAAAO,GAAA,CAAAI,QAAA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,KAAA,YAAAF,GAAA;MACA;IAEA;IACAG,QAAA;MACA,SAAAf,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IACAgB,SAAA;MACA,SAAAhB,WAAA,QAAAD,OAAA;QACA,KAAAC,WAAA;MACA;IACA;EACA;EACAiB,QAAA;IACA,KAAAb,UAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}