{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport './assets/css/index.css';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport * as eCharts from 'echarts';\nimport AMapLoader from '@amap/amap-jsapi-loader';\nimport 'video.js/dist/video-js.css';\nimport dataV from '@jiaminghi/data-view';\nimport VScaleScreen from 'v-scale-screen';\nimport EZUIKit from 'ezuikit-js';\nVue.use(VScaleScreen);\nVue.prototype.$eCharts = eCharts;\nconst eventBus = new Vue();\nVue.prototype.$eventBus = eventBus;\nVue.config.productionTip = false;\nVue.use(dataV);\nVue.use(ElementUI);\nAMapLoader.load({\n  key: \"ec627279fb1a4826a0d7dcef697ceb63\",\n  plugins: [\"AMap.ToolBar\", \"AMap.Polygon\", 'AMap.Marker', 'AMap.convertFrom', 'AMap.Pixel', 'AMap.MarkerClusterer', 'AMap.Size', 'AMap.Icon', 'AMap.InfoWindow'] // 需要使用的的插件列表，如比例尺'AMap.Scale'等，更多插件请看官方文档\n}).then(AMap => {\n  new Vue({\n    router,\n    store,\n    render: h => h(App)\n  }).$mount('#app');\n}).catch(e => {\n  console.log(e);\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "<PERSON><PERSON><PERSON><PERSON>", "AMap<PERSON><PERSON><PERSON>", "dataV", "VScaleScreen", "EZUIKit", "use", "prototype", "$eCharts", "eventBus", "$eventBus", "config", "productionTip", "load", "key", "plugins", "then", "AMap", "render", "h", "$mount", "catch", "e", "console", "log"], "sources": ["D:/Project/HuaQiaoSanQi/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport './assets/css/index.css'\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport * as eCharts from 'echarts'\nimport AMapLoader from '@amap/amap-jsapi-loader'\nimport 'video.js/dist/video-js.css' \nimport dataV from '@jiaminghi/data-view' \nimport VScaleScreen from 'v-scale-screen'\nimport EZUIKit from 'ezuikit-js';\n\n\nVue.use(VScaleScreen)\nVue.prototype.$eCharts = eCharts\n\nconst eventBus = new Vue();\nVue.prototype.$eventBus = eventBus;\n\nVue.config.productionTip = false\n \nVue.use(dataV)\nVue.use(ElementUI); \nAMapLoader.load({\n  key: \"ec627279fb1a4826a0d7dcef697ceb63\",\n  plugins: [\n    \"AMap.ToolBar\",\n    \"AMap.Polygon\",\n    'AMap.Marker',\n    'AMap.convertFrom',\n    'AMap.Pixel',\n    'AMap.MarkerClusterer',\n    'AMap.Size',\n    'AMap.Icon',\n    'AMap.InfoWindow',\n  ],         // 需要使用的的插件列表，如比例尺'AMap.Scale'等，更多插件请看官方文档\n}).then((AMap) => {\n  new Vue({\n    router,\n    store,\n    render: h => h(App)\n  }).$mount('#app')\n}).catch(e => {\n  console.log(e)\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,wBAAwB;AAC/B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAO,KAAKC,OAAO,MAAM,SAAS;AAClC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAO,4BAA4B;AACnC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,OAAO,MAAM,YAAY;AAGhCT,GAAG,CAACU,GAAG,CAACF,YAAY,CAAC;AACrBR,GAAG,CAACW,SAAS,CAACC,QAAQ,GAAGP,OAAO;AAEhC,MAAMQ,QAAQ,GAAG,IAAIb,GAAG,CAAC,CAAC;AAC1BA,GAAG,CAACW,SAAS,CAACG,SAAS,GAAGD,QAAQ;AAElCb,GAAG,CAACe,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhChB,GAAG,CAACU,GAAG,CAACH,KAAK,CAAC;AACdP,GAAG,CAACU,GAAG,CAACN,SAAS,CAAC;AAClBE,UAAU,CAACW,IAAI,CAAC;EACdC,GAAG,EAAE,kCAAkC;EACvCC,OAAO,EAAE,CACP,cAAc,EACd,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,sBAAsB,EACtB,WAAW,EACX,WAAW,EACX,iBAAiB,CAClB,CAAU;AACb,CAAC,CAAC,CAACC,IAAI,CAAEC,IAAI,IAAK;EAChB,IAAIrB,GAAG,CAAC;IACNE,MAAM;IACNC,KAAK;IACLmB,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACtB,GAAG;EACpB,CAAC,CAAC,CAACuB,MAAM,CAAC,MAAM,CAAC;AACnB,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAI;EACZC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;AAChB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}