{"ast": null, "code": "import { EconomicDevelopment, IndustrialChangeTrend } from '@/api/index.js';\nimport GDPTrend from '@/components/comprehensive/GDPTrend.vue';\nimport AreaSchoolMembersChart from '@/components/peopleDem/AreaSchoolMembersChart.vue';\nexport default {\n  name: 'mapEcharts',\n  components: {\n    GDPTrend,\n    AreaSchoolMembersChart\n  },\n  data() {\n    return {\n      yearAllGdp: {},\n      seasonData: [{\n        name: \"第一季度\",\n        num: 90.7\n      }, {\n        name: \"第二季度\",\n        num: 110.3\n      }, {\n        name: \"第三季度\",\n        num: 103.8\n      }, {\n        name: \"第四季度\",\n        num: 88.4\n      }],\n      // 产业结构\n      IndustrialPer: {},\n      isShow: false\n    };\n  },\n  mounted() {\n    this.init();\n  },\n  methods: {\n    init() {\n      EconomicDevelopment().then(res => {\n        this.economicDevData = res.data.extra;\n        for (let v of res.data.extra) {\n          if (v.edName == '地区生产总值') {\n            this.yearAllGdp = v;\n          }\n        }\n      });\n\n      // 产业变化情况趋势\n      IndustrialChangeTrend().then(res => {\n        let {\n          extra\n        } = res.data;\n        let max = 0;\n        let data = {};\n        extra.map(res => {\n          if (res.yearTime > max) {\n            max = res.yearTime;\n            data = res;\n          }\n        });\n        this.IndustrialPer = data;\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n    clickBtnShowFlag() {\n      this.isShow = !this.isShow;\n      let dom = document.querySelector('.charts_Con');\n      let boxEle = document.querySelector('.mapBox');\n      if (!this.isShow) {\n        dom.style.left = '-800px';\n        // boxEle.style.height = '0'\n      } else {\n        dom.style.left = '0px';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["EconomicDevelopment", "IndustrialChangeTrend", "GDPTrend", "AreaSchoolMembersChart", "name", "components", "data", "yearAllGdp", "seasonData", "num", "IndustrialPer", "isShow", "mounted", "init", "methods", "then", "res", "economicDevData", "extra", "v", "ed<PERSON>ame", "max", "map", "yearTime", "catch", "err", "console", "log", "clickBtnShowFlag", "dom", "document", "querySelector", "boxEle", "style", "left"], "sources": ["src/views/mainPage/components/mapEcharts.vue"], "sourcesContent": ["<template>\r\n    <div class=\"pic_Com\">\r\n        <div class=\"UnfoldOrPut\" @click=\"clickBtnShowFlag\">\r\n            <span v-if=\"!isShow\">\r\n               图表 <i class=\"el-icon-caret-right\"></i>\r\n            </span>\r\n            <span v-if=\"isShow\">\r\n               图表 <i class=\"el-icon-caret-left\"></i>\r\n            </span>\r\n        </div>\r\n\r\n        <div class=\"mapBox\">\r\n\r\n            <div class=\"charts_Con\">\r\n                <div class=\"left_slices\">\r\n\r\n                    <div class=\"yearGDP\">\r\n                        <div class=\"GDPTop topItems\">\r\n                            年度GDP\r\n                        </div>\r\n                        <div class=\"dataInfo\">\r\n                            <div class=\"sum\">\r\n                                <p class=\"sumNum\">{{ yearAllGdp.gdp }}</p>\r\n                                <p>{{ yearAllGdp.unit }}</p>\r\n                                <div class=\"year\">\r\n                                    {{ yearAllGdp.year }}年\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"seasonData\">\r\n                                <div class=\"seasonItems\" v-for=\"(item, index) of seasonData\" :key=\"index\">\r\n                                    <div class=\"imgs\">\r\n                                        <img src=\"@/assets/images/left_slices/point1.png\" alt=\"\">\r\n                                        <img src=\"@/assets/images/left_slices/point2.png\" alt=\"\">\r\n                                    </div>\r\n                                    <p>{{ item.name }}</p>\r\n                                    <p class=\"num\">{{ item.num }}</p>\r\n                                </div>\r\n\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"trendGDP mt38\">\r\n                            <GDPTrend :fontSize=\"8\" :height=\"'10rem'\"></GDPTrend>\r\n                        </div>\r\n                    </div>\r\n\r\n\r\n                </div>\r\n                <div class=\"right_slices\">\r\n                    <div class=\"IndustrialStrPro mt38\">\r\n                        <div class=\"topItems\">\r\n                            产业结构占比\r\n                        </div>\r\n                        <div class=\"IndustrialItems\">\r\n                            <div>\r\n                                <p>{{ (IndustrialPer.priIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                                <img src=\"@/assets/images/left_slices/proIcon1.png\" alt=\"\">\r\n                                <div class=\"txt\">第一产业</div>\r\n                            </div>\r\n                            <div>\r\n                                <p>{{ (IndustrialPer.secIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                                <img src=\"@/assets/images/left_slices/proIcon2.png\" alt=\"\">\r\n                                <div class=\"txt\">第二产业</div>\r\n                            </div>\r\n                            <div>\r\n                                <p>{{ (IndustrialPer.terIndustyGdpRate * 100).toFixed(2) }} %</p>\r\n                                <img src=\"@/assets/images/left_slices/proIcon3.png\" alt=\"\">\r\n                                <div class=\"txt\">第三产业</div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"proDistribution\">\r\n                        <div class=\"topItems mt38\">\r\n                            产业分布\r\n                        </div>\r\n\r\n                        <div class=\"proDistributionInfo\">\r\n                            <div class=\"leftPic\">\r\n                                <img src=\"@/assets/images/left_slices/map.png\" alt=\"\">\r\n                                <img class=\"btm\" src=\"@/assets/images/left_slices/bgBtm2.png\" alt=\"\">\r\n                            </div>\r\n                            <div class=\"areaName\">\r\n                                <div class=\"dashed\"></div>\r\n\r\n                                <div class=\"areaTitle\">\r\n                                    <div class=\"circle\">\r\n                                        <div></div>\r\n\r\n                                    </div>\r\n                                    区域名称\r\n                                </div>\r\n                                <div class=\"areaItem\">\r\n\r\n                                    <div>\r\n                                        <div class=\"littleBox\">\r\n                                            <div class=\"circle\">\r\n                                                <div></div>\r\n                                            </div>\r\n                                            <div class=\"txt\">\r\n                                                第一产业\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                        <div class=\"numInfo\">\r\n                                            <div class=\"sum\">\r\n                                                {{ IndustrialPer.priIndustyGdp }}\r\n                                            </div>\r\n                                            <div class=\"per\">\r\n                                                {{ (IndustrialPer.priIndustyGdpRate * 100).toFixed(2) }}%\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div>\r\n                                        <div class=\"littleBox\">\r\n                                            <div class=\"circle\">\r\n                                                <div></div>\r\n                                            </div>\r\n                                            <div class=\"txt\">\r\n                                                第二产业\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                        <div class=\"numInfo\">\r\n                                            <div class=\"sum\">\r\n                                                {{ IndustrialPer.secIndustyGdp }}\r\n\r\n                                            </div>\r\n                                            <div class=\"per\">\r\n                                                {{ (IndustrialPer.secIndustyGdpRate * 100).toFixed(2) }}%\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <div>\r\n                                        <div class=\"littleBox\">\r\n                                            <div class=\"circle\">\r\n                                                <div></div>\r\n                                            </div>\r\n                                            <div class=\"txt\">\r\n                                                第三产业\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                        <div class=\"numInfo\">\r\n                                            <div class=\"sum\">\r\n                                                {{ IndustrialPer.terIndustyGdp }}\r\n\r\n                                            </div>\r\n                                            <div class=\"per\">\r\n                                                {{ (IndustrialPer.secIndustyGdpRate * 100).toFixed(2) }}%\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"areaSchoolMembersNum\">\r\n                        <AreaSchoolMembersChart :fontSize=\"10\" :height=\"'7rem'\"></AreaSchoolMembersChart>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { EconomicDevelopment, IndustrialChangeTrend } from '@/api/index.js'\r\nimport GDPTrend from '@/components/comprehensive/GDPTrend.vue'\r\nimport AreaSchoolMembersChart from '@/components/peopleDem/AreaSchoolMembersChart.vue'\r\n\r\nexport default {\r\n    name: 'mapEcharts',\r\n    components: {\r\n        GDPTrend,\r\n        AreaSchoolMembersChart\r\n    },\r\n    data() {\r\n        return {\r\n            yearAllGdp: {},\r\n            seasonData: [\r\n                {\r\n                    name: \"第一季度\",\r\n                    num: 90.7\r\n                },\r\n                {\r\n                    name: \"第二季度\",\r\n                    num: 110.3\r\n                },\r\n                {\r\n                    name: \"第三季度\",\r\n                    num: 103.8\r\n                },\r\n                {\r\n                    name: \"第四季度\",\r\n                    num: 88.4\r\n                }\r\n            ],\r\n            // 产业结构\r\n            IndustrialPer: {},\r\n            isShow: false\r\n        }\r\n    },\r\n    mounted() {\r\n        this.init();\r\n    },\r\n    methods: {\r\n        init() {\r\n            EconomicDevelopment().then(res => {\r\n                this.economicDevData = res.data.extra;\r\n                for (let v of res.data.extra) {\r\n                    if (v.edName == '地区生产总值') {\r\n                        this.yearAllGdp = v\r\n                    }\r\n\r\n                }\r\n            });\r\n\r\n            // 产业变化情况趋势\r\n            IndustrialChangeTrend().then(res => {\r\n                let { extra } = res.data\r\n                let max = 0;\r\n                let data = {}\r\n                extra.map(res => {\r\n                    if (res.yearTime > max) {\r\n                        max = res.yearTime;\r\n                        data = res\r\n                    }\r\n                })\r\n                this.IndustrialPer = data\r\n\r\n            }).catch(err => {\r\n                console.log(err);\r\n            })\r\n\r\n        },\r\n        clickBtnShowFlag() {\r\n            this.isShow = !this.isShow\r\n            let dom = document.querySelector('.charts_Con')\r\n            let boxEle = document.querySelector('.mapBox')\r\n            if (!this.isShow) {\r\n                dom.style.left = '-800px'\r\n                // boxEle.style.height = '0'\r\n            } else {\r\n                dom.style.left = '0px'\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.right_slices {\r\n    >div {\r\n        margin-top: 20px;\r\n    }\r\n}\r\n\r\n.pic_Com {\r\n    margin-left: 3rem;\r\n    .UnfoldOrPut {\r\n        display: inline;\r\n        padding: 2px 8px;\r\n        border-radius: 10px;\r\n        color: #ffffffe7;\r\n        font-size: 15px;\r\n        cursor: pointer;\r\n        margin-left: 2rem;\r\n        background-color: rgba(18, 76, 111, .45);\r\n    }\r\n}\r\n\r\n.mapBox {\r\n    position: relative;\r\n    width: 40rem;\r\n    .charts_Con {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        justify-content: space-between;\r\n        user-select: none;\r\n        position: absolute;\r\n        left:-700px;\r\n        background-color: rgba(18, 76, 111, .45);\r\n        border-radius: 20px;\r\n        padding: 20px 20px 10px;\r\n        transition: all 1s;\r\n\r\n    }\r\n\r\n\r\n\r\n    .topItems {\r\n        width: 15.19rem;\r\n        height: 1.3rem;\r\n        line-height: 1.3rem;\r\n        background: url('@/assets/images/left_slices/bgPic_2.png') no-repeat center;\r\n        background-size: 100% 100%;\r\n        padding-left: 1rem;\r\n        font-size: .7rem;\r\n        color: #fff;\r\n        margin-top: 1rem;\r\n\r\n    }\r\n}\r\n\r\n.yearGDP {\r\n    .dataInfo {\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        >div {\r\n            width: 48%;\r\n            height: 10rem;\r\n        }\r\n\r\n        .sum {\r\n            display: flex;\r\n            flex-direction: column;\r\n            /* justify-content: space-around; */\r\n            align-items: center;\r\n            height: 9rem;\r\n            background: url('@/assets/images/left_slices/GDP.png') no-repeat center;\r\n            background-size: 100%;\r\n            color: rgba(166, 174, 184, 1);\r\n\r\n            .sumNum {\r\n                color: rgba(233, 173, 88, 1);\r\n                font-size: 1rem;\r\n                font-weight: bold;\r\n                margin-top: .8rem;\r\n            }\r\n\r\n            .year {\r\n                color: #fff;\r\n                margin-top: 4rem;\r\n                font-size: 1rem;\r\n            }\r\n        }\r\n\r\n        .seasonData {\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-around;\r\n\r\n            .seasonItems {\r\n                display: flex;\r\n                font-size: .7rem;\r\n                border-bottom: 2px solid;\r\n                /* border-image-source: url(@/assets/images/left_slices/border.png); */\r\n                border-image: url('@/assets/images/left_slices/border.png') 0 0 100% 0 round;\r\n                color: #ffffffc9;\r\n\r\n                p {\r\n                    line-height: 1rem;\r\n                    margin-left: 0.7rem;\r\n                }\r\n\r\n                .num {\r\n                    width: 30%;\r\n                    text-align: right;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .trendGDP {\r\n        margin-top: 2rem;\r\n        width: 100%;\r\n        height: 10rem;\r\n    }\r\n\r\n    .areaGDPdata {\r\n        width: 100%;\r\n        height: 19rem;\r\n\r\n    }\r\n}\r\n\r\n.IndustrialStrPro {\r\n    .IndustrialItems {\r\n        margin-top: 2rem;\r\n        width: 18rem;\r\n        display: flex;\r\n        justify-content: space-around;\r\n        height: 5rem;\r\n        background: url('@/assets/images/left_slices/per.png') no-repeat center;\r\n        background-size: 100%;\r\n\r\n        >div {\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            text-align: center;\r\n            width: 30%;\r\n\r\n            p {\r\n                transform: translateY(40%);\r\n                font-size: .6rem;\r\n                color: rgba(82, 164, 170);\r\n                font-weight: bold;\r\n                background: linear-gradient(0deg, #68D5DF 30%, #FFFFFF 70%);\r\n                -webkit-background-clip: text;\r\n                -webkit-text-fill-color: transparent;\r\n            }\r\n\r\n            .txt {\r\n                font-size: .5rem;\r\n                position: absolute;\r\n                bottom: -0.5rem;\r\n                left: 50%;\r\n                transform: translateX(-50%);\r\n                width: 5rem;\r\n                padding: 2px 0;\r\n                color: #fff;\r\n                border: 1px solid #0AE5FF;\r\n                background: rgba(10, 229, 255, 0.11);\r\n                border-radius: 1.7rem;\r\n            }\r\n\r\n            img {\r\n                margin-bottom: 3rem;\r\n            }\r\n\r\n            span {\r\n                position: absolute;\r\n                left: 50%;\r\n                top: 0.6rem;\r\n                transform: translateX(-50%);\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.proDistribution {\r\n    position: relative;\r\n    margin-top: 1rem;\r\n    // .dashed {\r\n    //     position: absolute;\r\n    //     height: 5rem;\r\n    //     bottom: 2rem;\r\n    //     margin-left: .8rem;\r\n    //     border-left: 1px dashed #fff;\r\n\r\n    // }\r\n\r\n    .proDistributionInfo {\r\n        margin-top: 1rem;\r\n        display: flex;\r\n        justify-content: space-around;\r\n        height: 5rem;\r\n\r\n        >div {\r\n            width: 30%;\r\n        }\r\n\r\n        .leftPic {\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            img {\r\n                width: 100%;\r\n            }\r\n\r\n            .btm {\r\n                position: absolute;\r\n                width: 140%;\r\n                bottom: 0rem;\r\n                left: -1rem;\r\n            }\r\n        }\r\n\r\n        .areaName {\r\n            width: 50%;\r\n\r\n            .circle {\r\n                position: relative;\r\n                width: .4rem;\r\n                height: .4rem;\r\n                background: rgba(10, 229, 255, 0.5);\r\n                border-radius: 50%;\r\n                margin-right: .5rem;\r\n\r\n                >div {\r\n                    position: absolute;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 50%;\r\n                    height: 40%;\r\n                    background: #1AF3FD;\r\n                    border-radius: 50%;\r\n                }\r\n            }\r\n\r\n            .areaTitle {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: .6rem;\r\n                color: #03FFFF;\r\n                margin-bottom: .56rem;\r\n                ;\r\n            }\r\n\r\n            .areaItem {\r\n                height: 4rem;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: space-between;\r\n                font-size: .5rem;\r\n                color: #fff;\r\n\r\n                .littleBox {\r\n                    display: flex;\r\n                    align-items: center;\r\n                }\r\n\r\n                >div {\r\n                    width: 100%;\r\n                    display: flex;\r\n                    align-items: center;\r\n\r\n                    .numInfo {\r\n                        margin-left: .5rem;\r\n\r\n                    }\r\n\r\n\r\n                    .littleBox {\r\n                        padding: 0 0.3rem;\r\n                    }\r\n                }\r\n\r\n                >div:nth-child(2) {\r\n                    .txt {\r\n                        background-color: rgba(4, 252, 255, .3);\r\n                    }\r\n\r\n                    .numInfo {\r\n                        background: rgba(117, 165, 222, .3);\r\n                    }\r\n                }\r\n\r\n\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AA0KA,SAAAA,mBAAA,EAAAC,qBAAA;AACA,OAAAC,QAAA;AACA,OAAAC,sBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,QAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,UAAA;MACAC,UAAA,GACA;QACAJ,IAAA;QACAK,GAAA;MACA,GACA;QACAL,IAAA;QACAK,GAAA;MACA,GACA;QACAL,IAAA;QACAK,GAAA;MACA,GACA;QACAL,IAAA;QACAK,GAAA;MACA,EACA;MACA;MACAC,aAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,KAAA;MACAb,mBAAA,GAAAe,IAAA,CAAAC,GAAA;QACA,KAAAC,eAAA,GAAAD,GAAA,CAAAV,IAAA,CAAAY,KAAA;QACA,SAAAC,CAAA,IAAAH,GAAA,CAAAV,IAAA,CAAAY,KAAA;UACA,IAAAC,CAAA,CAAAC,MAAA;YACA,KAAAb,UAAA,GAAAY,CAAA;UACA;QAEA;MACA;;MAEA;MACAlB,qBAAA,GAAAc,IAAA,CAAAC,GAAA;QACA;UAAAE;QAAA,IAAAF,GAAA,CAAAV,IAAA;QACA,IAAAe,GAAA;QACA,IAAAf,IAAA;QACAY,KAAA,CAAAI,GAAA,CAAAN,GAAA;UACA,IAAAA,GAAA,CAAAO,QAAA,GAAAF,GAAA;YACAA,GAAA,GAAAL,GAAA,CAAAO,QAAA;YACAjB,IAAA,GAAAU,GAAA;UACA;QACA;QACA,KAAAN,aAAA,GAAAJ,IAAA;MAEA,GAAAkB,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IAEA;IACAG,iBAAA;MACA,KAAAjB,MAAA,SAAAA,MAAA;MACA,IAAAkB,GAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAC,MAAA,GAAAF,QAAA,CAAAC,aAAA;MACA,UAAApB,MAAA;QACAkB,GAAA,CAAAI,KAAA,CAAAC,IAAA;QACA;MACA;QACAL,GAAA,CAAAI,KAAA,CAAAC,IAAA;MAEA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}