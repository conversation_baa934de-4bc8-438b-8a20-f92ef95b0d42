{"ast": null, "code": "import axios from 'axios';\nimport { mapState } from 'vuex';\nimport { getLayersList } from '@/api/userMenu';\nimport { getNeighbourCameraList } from '@/api/index.js';\nexport default {\n  name: 'bufferSearch',\n  data() {\n    return {\n      bufferList: [{\n        content: '视频监控'\n      }],\n      bufferIndex: 0,\n      isShow: false,\n      url: '',\n      features: [],\n      handleVal: {}\n    };\n  },\n  components: {},\n  watch: {\n    bufferGeometry: {\n      handler(nv) {\n        this.handleVal = nv;\n        this.getNeighbourCamera(nv.Lng, nv.Lat);\n        setTimeout(() => {\n          this.radarFn(this.handleVal.Lng, this.handleVal.Lat);\n        }, 1000);\n      },\n      immediate: true\n    },\n    bufferIndex: {\n      handler(nv) {\n        this.deletePoi();\n        if (nv != 0) {\n          this.getPoiList(this.handleVal.Lng, this.handleVal.Lat);\n        } else if (nv == 0) {\n          this.getNeighbourCamera(this.handleVal.Lng, this.handleVal.Lat);\n        }\n        setTimeout(() => {\n          this.radarFn(this.handleVal.Lng, this.handleVal.Lat);\n        }, 1000);\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      bufferGeometry: state => state.action.bufferGeometry\n    })\n  },\n  mounted() {\n    this.getLayersManageList();\n  },\n  updated() {},\n  methods: {\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    isShowFn() {\n      this.isShow = !this.isShow;\n    },\n    bufferTabs(item, index) {\n      this.bufferIndex = index;\n      const {\n        hostname,\n        port\n      } = window.location;\n      this.url = `http://${hostname}:${port}${item?.externalUrl}/0/query`;\n      // if (item.content != '视频监控') {\n      //     // this.deletePoi();\n      //     const { hostname, port } = window.location;\n      //     this.url = `http://${hostname}:${port}${item.externalUrl}/0/query`;\n      //     this.getPoiList(this.handleVal.Lng, this.handleVal.Lat)\n      //     // this.radarFn(this.handleVal.Lng, this.handleVal.Lat)\n      // } else {\n      //     this.url = ''\n      //     this.getNeighbourCamera(this.handleVal.Lng, this.handleVal.Lat);\n\n      // }\n    },\n    radarFn(Lng, Lat) {\n      let params = {\n        \"name\": \"雷达图\",\n        \"jd\": Lng,\n        \"wd\": Lat,\n        \"high\": \"320\",\n        \"scale\": \"30\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    // 获取周围摄像头\n    getNeighbourCamera(Lng, Lat) {\n      getNeighbourCameraList({\n        distance: 100,\n        latitude: Lat,\n        longitude: Lng\n      }).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra;\n          list.forEach((item, index) => {\n            item.Name = item.deviceName;\n            item.levelType = 3;\n            item.ImageX = 0.18;\n            item.ImageY = 0.18;\n          });\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": list\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n        }\n      });\n    },\n    // 根据点位及距离查询附近数据并撒点\n    async getPoiList(lng, lat) {\n      try {\n        // 构建请求体参数\n        const params = new URLSearchParams();\n        params.append('f', 'json');\n        params.append('where', '1=1');\n        params.append('outFields', '*');\n        params.append('geometry', JSON.stringify({\n          x: lng,\n          y: lat\n        })); // 将几何信息序列化为JSON字符串\n        params.append('geometryType', 'esriGeometryPoint');\n        params.append('spatialRel', 'esriSpatialRelIntersects');\n        params.append('distance', 100);\n        params.append('units', 'esriSRUnit_Meter');\n        const response = await axios.post(this.url, params, {\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n          }\n        });\n        if (response.data && response.data.features) {\n          this.features = response.data.features;\n          let list = [];\n          this.features.forEach((item, index) => {\n            list[index] = {\n              \"Name\": item.attributes.name,\n              \"channalCode\": \"\",\n              \"Lng\": item.geometry.x,\n              \"Lat\": item.geometry.y,\n              \"Type\": \"空间查询\",\n              \"IconType\": \"3\",\n              \"Height\": \"100\",\n              \"TextX\": \"0\",\n              \"TextY\": \"-42\",\n              \"TextSize\": \"10\",\n              \"ImageX\": \"0.25\",\n              \"ImageY\": \"0.25\"\n            };\n          });\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": list\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n        }\n      } catch (error) {\n        console.error(`请求失败: ${error.message}`);\n      }\n    },\n    setUrl(index) {\n      return require(`@/assets/images/left_slices/radio${this.bufferIndex == index ? '-active' : ''}.png`);\n    },\n    // 获取图层数据\n    getLayersManageList() {\n      getLayersList({\n        key: '缓冲专题',\n        pageSize: 10,\n        pageNum: 1\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.bufferList = [...this.bufferList, ...res.data.extra.data];\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "mapState", "getLayersList", "getNeighbourCameraList", "name", "data", "bufferList", "content", "bufferIndex", "isShow", "url", "features", "handleVal", "components", "watch", "bufferGeometry", "handler", "nv", "getNeighbourCamera", "Lng", "Lat", "setTimeout", "radarFn", "immediate", "deletePoi", "getPoiList", "computed", "state", "action", "mounted", "getLayersManageList", "updated", "methods", "params", "$eventBus", "$emit", "isShowFn", "bufferTabs", "item", "index", "hostname", "port", "window", "location", "externalUrl", "distance", "latitude", "longitude", "then", "res", "code", "list", "extra", "for<PERSON>ach", "Name", "deviceName", "levelType", "ImageX", "ImageY", "lng", "lat", "URLSearchParams", "append", "JSON", "stringify", "x", "y", "response", "post", "headers", "attributes", "geometry", "error", "console", "message", "setUrl", "require", "key", "pageSize", "pageNum"], "sources": ["src/components/bufferSearch.vue"], "sourcesContent": ["<template>\r\n    <div class=\"buffer\">\r\n        <img src=\"@/assets/images/left_slices/leida.png\" @click=\"isShowFn\" alt=\"\">\r\n        <div class=\"search\" v-show=\"isShow\">\r\n            <div class=\"search-top\">\r\n                <span>空间查询</span>\r\n            </div>\r\n            <div class=\"container\">\r\n                <p v-for=\"(item, index) in bufferList\">\r\n                    <img :src=\"setUrl(index)\" alt=\"\" @click=\"bufferTabs(item, index)\">\r\n                    <span :class=\"{ radioActive: bufferIndex == index }\">{{ item.content }}</span>\r\n                </p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios';\r\nimport { mapState } from 'vuex'\r\nimport { getLayersList } from '@/api/userMenu'\r\nimport { getNeighbourCameraList } from '@/api/index.js'\r\nexport default {\r\n    name: 'bufferSearch',\r\n    data() {\r\n        return {\r\n            bufferList: [{ content: '视频监控' }],\r\n            bufferIndex: 0,\r\n            isShow: false,\r\n            url: '',\r\n            features: [],\r\n            handleVal: {}\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n        bufferGeometry: {\r\n            handler(nv) {\r\n                this.handleVal = nv;\r\n                this.getNeighbourCamera(nv.Lng, nv.Lat);\r\n                setTimeout(() => {\r\n                    this.radarFn(this.handleVal.Lng, this.handleVal.Lat)\r\n                }, 1000)\r\n            }, immediate: true,\r\n        },\r\n        bufferIndex: {\r\n            handler(nv) {\r\n                this.deletePoi();\r\n                if (nv != 0) {\r\n                    this.getPoiList(this.handleVal.Lng, this.handleVal.Lat)\r\n                } else if (nv == 0) {\r\n                    this.getNeighbourCamera(this.handleVal.Lng, this.handleVal.Lat);\r\n                }\r\n                setTimeout(() => {\r\n                    this.radarFn(this.handleVal.Lng, this.handleVal.Lat)\r\n                }, 1000)\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            bufferGeometry: state => state.action.bufferGeometry,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n        this.getLayersManageList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        isShowFn() {\r\n            this.isShow = !this.isShow;\r\n        },\r\n        bufferTabs(item, index) {\r\n            this.bufferIndex = index;\r\n            const { hostname, port } = window.location;\r\n            this.url = `http://${hostname}:${port}${item?.externalUrl}/0/query`;\r\n            // if (item.content != '视频监控') {\r\n            //     // this.deletePoi();\r\n            //     const { hostname, port } = window.location;\r\n            //     this.url = `http://${hostname}:${port}${item.externalUrl}/0/query`;\r\n            //     this.getPoiList(this.handleVal.Lng, this.handleVal.Lat)\r\n            //     // this.radarFn(this.handleVal.Lng, this.handleVal.Lat)\r\n            // } else {\r\n            //     this.url = ''\r\n            //     this.getNeighbourCamera(this.handleVal.Lng, this.handleVal.Lat);\r\n\r\n            // }\r\n        },\r\n        radarFn(Lng, Lat) {\r\n            let params = {\r\n                \"name\": \"雷达图\",\r\n                \"jd\": Lng,\r\n                \"wd\": Lat,\r\n                \"high\": \"320\",\r\n                \"scale\": \"30\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params)\r\n        },\r\n        // 获取周围摄像头\r\n        getNeighbourCamera(Lng, Lat) {\r\n            getNeighbourCameraList({ distance: 100, latitude: Lat, longitude: Lng }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra;\r\n                    list.forEach((item, index) => {\r\n                        item.Name = item.deviceName;\r\n                        item.levelType = 3;\r\n                        item.ImageX = 0.18;\r\n                        item.ImageY = 0.18;\r\n                    })\r\n                    let params = {\r\n                        \"mode\": \"add\",\r\n                        \"sources\": list\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params)\r\n\r\n                }\r\n            })\r\n        },\r\n\r\n        // 根据点位及距离查询附近数据并撒点\r\n        async getPoiList(lng, lat) {\r\n            try {\r\n                // 构建请求体参数\r\n                const params = new URLSearchParams();\r\n                params.append('f', 'json');\r\n                params.append('where', '1=1');\r\n                params.append('outFields', '*');\r\n                params.append('geometry', JSON.stringify({ x: lng, y: lat })); // 将几何信息序列化为JSON字符串\r\n                params.append('geometryType', 'esriGeometryPoint');\r\n                params.append('spatialRel', 'esriSpatialRelIntersects');\r\n                params.append('distance', 100);\r\n                params.append('units', 'esriSRUnit_Meter');\r\n                const response = await axios.post(this.url, params,\r\n                    {\r\n                        headers: {\r\n                            'Content-Type': 'application/x-www-form-urlencoded',\r\n                        },\r\n                    }\r\n                );\r\n                if (response.data && response.data.features) {\r\n                    this.features = response.data.features;\r\n                    let list = [];\r\n                    this.features.forEach((item, index) => {\r\n                        list[index] = {\r\n                            \"Name\": item.attributes.name,\r\n                            \"channalCode\": \"\",\r\n                            \"Lng\": item.geometry.x,\r\n                            \"Lat\": item.geometry.y,\r\n                            \"Type\": \"空间查询\",\r\n                            \"IconType\": \"3\",\r\n                            \"Height\": \"100\",\r\n                            \"TextX\": \"0\",\r\n                            \"TextY\": \"-42\",\r\n                            \"TextSize\": \"10\",\r\n                            \"ImageX\": \"0.25\",\r\n                            \"ImageY\": \"0.25\"\r\n                        }\r\n                    })\r\n                    let params = {\r\n                        \"mode\": \"add\",\r\n                        \"sources\": list\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params)\r\n                }\r\n            } catch (error) {\r\n                console.error(`请求失败: ${error.message}`);\r\n            }\r\n        },\r\n        setUrl(index) {\r\n            return require(`@/assets/images/left_slices/radio${this.bufferIndex == index ? '-active' : ''}.png`);\r\n        },\r\n        // 获取图层数据\r\n        getLayersManageList() {\r\n            getLayersList({ key: '缓冲专题', pageSize: 10, pageNum: 1 }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.bufferList = [...this.bufferList, ...res.data.extra.data];\r\n                }\r\n            })\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.buffer {\r\n    pointer-events: none;\r\n\r\n    >img {\r\n        width: 103px;\r\n        height: 93px;\r\n        cursor: pointer;\r\n        position: fixed;\r\n        right: 100px;\r\n        top: 40px;\r\n        transform: scale(0.5, 0.445833);\r\n        pointer-events: stroke;\r\n    }\r\n\r\n    .search {\r\n        position: fixed;\r\n        top: 50px;\r\n        right: 80px;\r\n        transform: scale(0.5, 0.445833);\r\n        display: flex;\r\n        flex-direction: column;\r\n        width: 338px;\r\n        height: 416px;\r\n        background-size: 100% 100%;\r\n        background-image: url(@/assets/images/left_slices/leida-bg.png);\r\n        padding-top: 32px;\r\n\r\n        .search-top {\r\n            width: 314px;\r\n            height: 57px;\r\n            background-size: 100% 100%;\r\n            background-image: url(@/assets/images/left_slices/leida-title.png);\r\n            padding-left: 11px;\r\n\r\n            >span {\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 36px;\r\n                color: #89FFFE;\r\n                line-height: 57px;\r\n                letter-spacing: 3px;\r\n                text-align: left;\r\n                font-style: normal;\r\n                text-transform: none;\r\n            }\r\n        }\r\n\r\n        .container {\r\n            display: flex;\r\n            flex-direction: column;\r\n            height: 300px;\r\n            overflow-y: scroll;\r\n            pointer-events: stroke;\r\n\r\n            >p {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-top: 19px;\r\n                padding: 0 60px;\r\n\r\n                img {\r\n                    width: 26px;\r\n                    height: 26px;\r\n                    cursor: pointer;\r\n                    margin-right: 20px;\r\n                    pointer-events: stroke;\r\n                }\r\n\r\n                >span {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: bold;\r\n                    font-size: 32px;\r\n                    color: #FFFFFF;\r\n                    line-height: 38px;\r\n                    text-align: left;\r\n                    font-style: normal;\r\n                    text-transform: none;\r\n                }\r\n\r\n                .radioActive {\r\n                    color: #FFD900;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAoBA,OAAAA,KAAA;AACA,SAAAC,QAAA;AACA,SAAAC,aAAA;AACA,SAAAC,sBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;QAAAC,OAAA;MAAA;MACAC,WAAA;MACAC,MAAA;MACAC,GAAA;MACAC,QAAA;MACAC,SAAA;IACA;EACA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,cAAA;MACAC,QAAAC,EAAA;QACA,KAAAL,SAAA,GAAAK,EAAA;QACA,KAAAC,kBAAA,CAAAD,EAAA,CAAAE,GAAA,EAAAF,EAAA,CAAAG,GAAA;QACAC,UAAA;UACA,KAAAC,OAAA,MAAAV,SAAA,CAAAO,GAAA,OAAAP,SAAA,CAAAQ,GAAA;QACA;MACA;MAAAG,SAAA;IACA;IACAf,WAAA;MACAQ,QAAAC,EAAA;QACA,KAAAO,SAAA;QACA,IAAAP,EAAA;UACA,KAAAQ,UAAA,MAAAb,SAAA,CAAAO,GAAA,OAAAP,SAAA,CAAAQ,GAAA;QACA,WAAAH,EAAA;UACA,KAAAC,kBAAA,MAAAN,SAAA,CAAAO,GAAA,OAAAP,SAAA,CAAAQ,GAAA;QACA;QACAC,UAAA;UACA,KAAAC,OAAA,MAAAV,SAAA,CAAAO,GAAA,OAAAP,SAAA,CAAAQ,GAAA;QACA;MACA;IACA;EACA;EACAM,QAAA;IACA,GAAAzB,QAAA;MACAc,cAAA,EAAAY,KAAA,IAAAA,KAAA,CAAAC,MAAA,CAAAb;IACA;EACA;EAEAc,QAAA;IACA,KAAAC,mBAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAR,UAAA;MACA,IAAAS,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAG,SAAA;MACA,KAAA3B,MAAA,SAAAA,MAAA;IACA;IACA4B,WAAAC,IAAA,EAAAC,KAAA;MACA,KAAA/B,WAAA,GAAA+B,KAAA;MACA;QAAAC,QAAA;QAAAC;MAAA,IAAAC,MAAA,CAAAC,QAAA;MACA,KAAAjC,GAAA,aAAA8B,QAAA,IAAAC,IAAA,GAAAH,IAAA,EAAAM,WAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;IACA;IACAtB,QAAAH,GAAA,EAAAC,GAAA;MACA,IAAAa,MAAA;QACA;QACA,MAAAd,GAAA;QACA,MAAAC,GAAA;QACA;QACA;MACA;MACA,KAAAc,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACA;IACAf,mBAAAC,GAAA,EAAAC,GAAA;MACAjB,sBAAA;QAAA0C,QAAA;QAAAC,QAAA,EAAA1B,GAAA;QAAA2B,SAAA,EAAA5B;MAAA,GAAA6B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA5C,IAAA,CAAA6C,IAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAA5C,IAAA,CAAA+C,KAAA;UACAD,IAAA,CAAAE,OAAA,EAAAf,IAAA,EAAAC,KAAA;YACAD,IAAA,CAAAgB,IAAA,GAAAhB,IAAA,CAAAiB,UAAA;YACAjB,IAAA,CAAAkB,SAAA;YACAlB,IAAA,CAAAmB,MAAA;YACAnB,IAAA,CAAAoB,MAAA;UACA;UACA,IAAAzB,MAAA;YACA;YACA,WAAAkB;UACA;UACA,KAAAjB,SAAA,CAAAC,KAAA,eAAAF,MAAA;QAEA;MACA;IACA;IAEA;IACA,MAAAR,WAAAkC,GAAA,EAAAC,GAAA;MACA;QACA;QACA,MAAA3B,MAAA,OAAA4B,eAAA;QACA5B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA,aAAAC,IAAA,CAAAC,SAAA;UAAAC,CAAA,EAAAN,GAAA;UAAAO,CAAA,EAAAN;QAAA;QACA3B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA;QACA7B,MAAA,CAAA6B,MAAA;QACA,MAAAK,QAAA,SAAAnE,KAAA,CAAAoE,IAAA,MAAA1D,GAAA,EAAAuB,MAAA,EACA;UACAoC,OAAA;YACA;UACA;QACA,CACA;QACA,IAAAF,QAAA,CAAA9D,IAAA,IAAA8D,QAAA,CAAA9D,IAAA,CAAAM,QAAA;UACA,KAAAA,QAAA,GAAAwD,QAAA,CAAA9D,IAAA,CAAAM,QAAA;UACA,IAAAwC,IAAA;UACA,KAAAxC,QAAA,CAAA0C,OAAA,EAAAf,IAAA,EAAAC,KAAA;YACAY,IAAA,CAAAZ,KAAA;cACA,QAAAD,IAAA,CAAAgC,UAAA,CAAAlE,IAAA;cACA;cACA,OAAAkC,IAAA,CAAAiC,QAAA,CAAAN,CAAA;cACA,OAAA3B,IAAA,CAAAiC,QAAA,CAAAL,CAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;UACA,IAAAjC,MAAA;YACA;YACA,WAAAkB;UACA;UACA,KAAAjB,SAAA,CAAAC,KAAA,eAAAF,MAAA;QACA;MACA,SAAAuC,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA,CAAAE,OAAA;MACA;IACA;IACAC,OAAApC,KAAA;MACA,OAAAqC,OAAA,0CAAApE,WAAA,IAAA+B,KAAA;IACA;IACA;IACAT,oBAAA;MACA5B,aAAA;QAAA2E,GAAA;QAAAC,QAAA;QAAAC,OAAA;MAAA,GAAA/B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA5C,IAAA,CAAA6C,IAAA;UACA,KAAA5C,UAAA,YAAAA,UAAA,KAAA2C,GAAA,CAAA5C,IAAA,CAAA+C,KAAA,CAAA/C,IAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}