{"ast": null, "code": "import axios from \"axios\";\nlet http = axios.create({\n  // 通用请求前缀\n  baseURL: process.env.VUE_APP_BASE_API,\n  timeout: 200000\n});\n// 添加请求拦截器\nhttp.interceptors.request.use(function (config) {\n  let token = JSON.parse(localStorage.getItem(\"token\"));\n  if (token) {\n    config.headers.Authorization = token;\n  }\n  // 设置请求头\n  return config;\n}, function (error) {\n  // 对请求错误做些什么\n  return Promise.reject(error);\n});\n\n// 添加响应拦截器\nhttp.interceptors.response.use(function (res) {\n  const code = res.data.code || 200;\n  const msg = res.data.msg || \"error\";\n  // 2xx 范围内的状态码都会触发该函数。\n  // 对响应数据做点什么\n\n  // 判断后端返回的错误信息\n  if (code === 401) {\n    // 清除token信息并跳转到登录页\n    localStorage.removeItem(\"token\");\n    window.location.href = \"http://2.36.27.133:5002/dataLake/dataService\";\n  } else if (code === 500) {\n    return Promise.reject(new Error(msg));\n  } else if (code === 601) {\n    return Promise.reject(new Error(msg));\n  } else {\n    return Promise.resolve(res);\n  }\n}, function (error) {\n  let {\n    message\n  } = error;\n  if (message == \"Network Error\") {\n    message = \"后端接口连接异常\";\n  } else if (message.includes(\"timeout\")) {\n    message = \"系统接口请求超时\";\n  } else if (message.includes(\"Request failed with status code\")) {\n    const statusCode = message.substr(message.length - 3);\n    if (statusCode === \"500\") {\n      console.warn(\"服务器内部错误，请联系管理员或稍后重试\");\n      // 对于500错误，不显示错误弹窗，只在控制台记录\n    } else {\n      message = \"系统接口\" + statusCode + \"异常\";\n    }\n  }\n  return Promise.reject(error);\n});\nexport default http;", "map": {"version": 3, "names": ["axios", "http", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "token", "JSON", "parse", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "res", "code", "data", "msg", "removeItem", "window", "location", "href", "Error", "resolve", "message", "includes", "statusCode", "substr", "length", "console", "warn"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/api/request.js"], "sourcesContent": ["import axios from \"axios\";\r\nlet http = axios.create({\r\n  // 通用请求前缀\r\n  baseURL: process.env.VUE_APP_BASE_API,\r\n  timeout: 200000,\r\n});\r\n// 添加请求拦截器\r\nhttp.interceptors.request.use(\r\n  function (config) {\r\n    let token = JSON.parse(localStorage.getItem(\"token\"));\r\n    if (token) {\r\n      config.headers.Authorization = token;\r\n    }\r\n    // 设置请求头\r\n    return config;\r\n  },\r\n  function (error) {\r\n    // 对请求错误做些什么\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 添加响应拦截器\r\nhttp.interceptors.response.use(\r\n  function (res) {\r\n    const code = res.data.code || 200;\r\n    const msg = res.data.msg || \"error\";\r\n    // 2xx 范围内的状态码都会触发该函数。\r\n    // 对响应数据做点什么\r\n\r\n    // 判断后端返回的错误信息\r\n    if (code === 401) {\r\n      // 清除token信息并跳转到登录页\r\n      localStorage.removeItem(\"token\");\r\n      window.location.href = \"http://2.36.27.133:5002/dataLake/dataService\";\r\n    } else if (code === 500) {\r\n      return Promise.reject(new Error(msg));\r\n    } else if (code === 601) {\r\n      return Promise.reject(new Error(msg));\r\n    } else {\r\n      return Promise.resolve(res);\r\n    }\r\n  },\r\n  function (error) {\r\n    let { message } = error;\r\n\r\n    if (message == \"Network Error\") {\r\n      message = \"后端接口连接异常\";\r\n    } else if (message.includes(\"timeout\")) {\r\n      message = \"系统接口请求超时\";\r\n    } else if (message.includes(\"Request failed with status code\")) {\r\n      const statusCode = message.substr(message.length - 3);\r\n      if (statusCode === \"500\") {\r\n        console.warn(\"服务器内部错误，请联系管理员或稍后重试\");\r\n        // 对于500错误，不显示错误弹窗，只在控制台记录\r\n      } else {\r\n        message = \"系统接口\" + statusCode + \"异常\";\r\n      }\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default http;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAIC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAAC;EACtB;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrCC,OAAO,EAAE;AACX,CAAC,CAAC;AACF;AACAN,IAAI,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC3B,UAAUC,MAAM,EAAE;EAChB,IAAIC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACrD,IAAIJ,KAAK,EAAE;IACTD,MAAM,CAACM,OAAO,CAACC,aAAa,GAAGN,KAAK;EACtC;EACA;EACA,OAAOD,MAAM;AACf,CAAC,EACD,UAAUQ,KAAK,EAAE;EACf;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlB,IAAI,CAACO,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC5B,UAAUa,GAAG,EAAE;EACb,MAAMC,IAAI,GAAGD,GAAG,CAACE,IAAI,CAACD,IAAI,IAAI,GAAG;EACjC,MAAME,GAAG,GAAGH,GAAG,CAACE,IAAI,CAACC,GAAG,IAAI,OAAO;EACnC;EACA;;EAEA;EACA,IAAIF,IAAI,KAAK,GAAG,EAAE;IAChB;IACAT,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,8CAA8C;EACvE,CAAC,MAAM,IAAIN,IAAI,KAAK,GAAG,EAAE;IACvB,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIU,KAAK,CAACL,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAIF,IAAI,KAAK,GAAG,EAAE;IACvB,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIU,KAAK,CAACL,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM;IACL,OAAON,OAAO,CAACY,OAAO,CAACT,GAAG,CAAC;EAC7B;AACF,CAAC,EACD,UAAUJ,KAAK,EAAE;EACf,IAAI;IAAEc;EAAQ,CAAC,GAAGd,KAAK;EAEvB,IAAIc,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtCD,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9D,MAAMC,UAAU,GAAGF,OAAO,CAACG,MAAM,CAACH,OAAO,CAACI,MAAM,GAAG,CAAC,CAAC;IACrD,IAAIF,UAAU,KAAK,KAAK,EAAE;MACxBG,OAAO,CAACC,IAAI,CAAC,qBAAqB,CAAC;MACnC;IACF,CAAC,MAAM;MACLN,OAAO,GAAG,MAAM,GAAGE,UAAU,GAAG,IAAI;IACtC;EACF;EAEA,OAAOf,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAelB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}