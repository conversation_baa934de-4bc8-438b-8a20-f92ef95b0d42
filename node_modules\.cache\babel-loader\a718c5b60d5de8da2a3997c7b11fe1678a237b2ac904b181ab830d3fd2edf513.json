{"ast": null, "code": "import { mapState } from 'vuex';\nimport { getGongDanList, getInterestPoints } from '@/api/index.js';\nexport default {\n  name: 'rightView',\n  data() {\n    return {\n      tableList: [],\n      total: 0,\n      totalPage: 1\n    };\n  },\n  components: {},\n  watch: {\n    ueTxt(nv) {\n      if (nv && nv.POIType == '视频监控') {\n        this.$emit('showEvent', false);\n        this.$store.commit(\"action/getEventType\", false);\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      ueTxt: state => state.dataChannelText\n    })\n  },\n  mounted() {\n    this.getGongDanList();\n  },\n  updated() {},\n  methods: {\n    // 获取周边兴趣点\n    async getNearbyPoint(params = {}) {\n      // this.$eventBus.$emit('senTxtToUe', '清除')\n      await getInterestPoints(params).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra;\n          if (list.length < 1) {\n            this.$message.warning(\"暂无周边兴趣点\");\n          }\n          let params = {\n            name: '工单周边兴趣点',\n            flag: true,\n            array: [this.SelectList, ...list],\n            number: 4\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n        }\n      });\n    },\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    // 详情列表\n    showDetail(item) {\n      this.deletePoi();\n      let {\n        lat84,\n        lng84,\n        formType\n      } = item;\n      if (lat84 && lng84) {\n        this.$eventBus.$emit('senTxtToUe', '全局视角');\n        // this.getNearbyPoint({\n        //     latitude: lat84,\n        //     longitude: lng84\n        // })\n        let params = {\n          \"mode\": \"add\",\n          \"sources\": [{\n            \"Name\": formType,\n            \"channalCode\": \"\",\n            \"Lng\": lng84,\n            \"Lat\": lat84,\n            \"Type\": \"事件报警\",\n            \"IconType\": \"14\",\n            \"Height\": \"100\",\n            \"TextX\": \"0\",\n            \"TextY\": \"-28\",\n            \"TextSize\": \"10\",\n            \"ImageX\": \"0.25\",\n            \"ImageY\": \"0.25\"\n          }]\n        };\n        this.$eventBus.$emit('senTxtToUe', params);\n      } else {\n        this.$eventBus.$emit('senTxtToUe', '清除');\n        this.$message.warning(\"暂无坐标\");\n      }\n      item.newUrl = '';\n      this.$store.commit(\"action/getProcessFlowInfo\", item);\n      this.$store.commit(\"action/getEventType\", true);\n      this.$store.commit(\"action/getEventRight\", '900px');\n    },\n    // 获取表格数据\n    async getGongDanList(params = {}) {\n      await getGongDanList(params).then(res => {\n        if (res.data.code == 200) {\n          this.tableList = res.data.extra.data;\n          this.total = res.data.extra.total;\n          this.totalPage = Math.ceil(this.total / this.pageSize);\n          // list.forEach((item, index) => {\n          //     let { orderSource, formType, waitingTime, appealAddress, id } = item\n          //     this.scrollList.push([id, formType, orderSource, waitingTime, appealAddress, \"<i style='color:#a7fcff' class='el-icon-location'></i>\"])\n          // })\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "getGongDanList", "getInterestPoints", "name", "data", "tableList", "total", "totalPage", "components", "watch", "ueTxt", "nv", "POIType", "$emit", "$store", "commit", "computed", "state", "dataChannelText", "mounted", "updated", "methods", "getNearbyPoint", "params", "then", "res", "code", "list", "extra", "length", "$message", "warning", "flag", "array", "SelectList", "number", "$eventBus", "deletePoi", "showDetail", "item", "lat84", "lng84", "formType", "newUrl", "Math", "ceil", "pageSize"], "sources": ["src/views/comprehensiveSituation/components/joint-command/right-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"rightView\">\r\n        <div class=\"title-box\">\r\n            <span>事件监控</span>\r\n        </div>\r\n        <div class=\"day-num\">\r\n            <span>事件预警</span>\r\n            <!-- <span>5条</span> -->\r\n        </div>\r\n        <div class=\"jk-list\">\r\n            <div class=\"list\" v-for=\"(item, index) in tableList\">\r\n                <p>{{ item.waitingTime.split(' ')[0] }}</p>\r\n                <p>\r\n                    <img src=\"@/assets/images/comprehensiveSituation/jk-btn1.png\" alt=\"\">\r\n                    <!-- <img src=\"@/assets/images/comprehensiveSituation/jk-btn2.png\" alt=\"\"> -->\r\n                    <span>{{ item.formType }}</span>\r\n                    <span>处理中</span>\r\n                </p>\r\n                <p>事件来源:<span>{{ item.dataSource }}</span> </p>\r\n                <p>发生时间:<span>{{ item.waitingTime }}</span> </p>\r\n                <p>发生位置:<span>{{ item.appealAddress }}</span> </p>\r\n                <img @click=\"showDetail(item)\" src=\"@/assets/images/comprehensiveSituation/jk-detail.png\" alt=\"\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { getGongDanList, getInterestPoints } from '@/api/index.js'\r\nexport default {\r\n    name: 'rightView',\r\n    data() {\r\n        return {\r\n            tableList: [],\r\n            total: 0,\r\n            totalPage: 1\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n        ueTxt(nv) {\r\n            if (nv && nv.POIType == '视频监控') {\r\n                this.$emit('showEvent', false);\r\n                this.$store.commit(\"action/getEventType\", false)\r\n            }\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            ueTxt: state => state.dataChannelText,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n        this.getGongDanList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n    methods: {\r\n        // 获取周边兴趣点\r\n        async getNearbyPoint(params = {}) {\r\n            // this.$eventBus.$emit('senTxtToUe', '清除')\r\n            await getInterestPoints(params).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra\r\n\r\n                    if (list.length < 1) {\r\n                        this.$message.warning(\"暂无周边兴趣点\")\r\n\r\n                    }\r\n                    let params = {\r\n                        name: '工单周边兴趣点',\r\n                        flag: true,\r\n                        array: [this.SelectList, ...list],\r\n                        number: 4\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params)\r\n\r\n                }\r\n            })\r\n        },\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        // 详情列表\r\n        showDetail(item) {\r\n            this.deletePoi();\r\n            let { lat84, lng84, formType } = item\r\n            if (lat84 && lng84) {\r\n                this.$eventBus.$emit('senTxtToUe', '全局视角')\r\n                // this.getNearbyPoint({\r\n                //     latitude: lat84,\r\n                //     longitude: lng84\r\n                // })\r\n                let params = {\r\n                    \"mode\": \"add\",\r\n                    \"sources\": [{\r\n                        \"Name\": formType,\r\n                        \"channalCode\": \"\",\r\n                        \"Lng\": lng84,\r\n                        \"Lat\": lat84,\r\n                        \"Type\": \"事件报警\",\r\n                        \"IconType\": \"14\",\r\n                        \"Height\": \"100\",\r\n                        \"TextX\": \"0\",\r\n                        \"TextY\": \"-28\",\r\n                        \"TextSize\": \"10\",\r\n                        \"ImageX\": \"0.25\",\r\n                        \"ImageY\": \"0.25\"\r\n                    }]\r\n\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params)\r\n\r\n            } else {\r\n                this.$eventBus.$emit('senTxtToUe', '清除')\r\n                this.$message.warning(\"暂无坐标\")\r\n\r\n            }\r\n            item.newUrl = ''\r\n            this.$store.commit(\"action/getProcessFlowInfo\", item)\r\n            this.$store.commit(\"action/getEventType\", true)\r\n            this.$store.commit(\"action/getEventRight\", '900px')\r\n        },\r\n        // 获取表格数据\r\n        async getGongDanList(params = {}) {\r\n            await getGongDanList(params).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.tableList = res.data.extra.data;\r\n                    this.total = res.data.extra.total\r\n                    this.totalPage = Math.ceil(this.total / this.pageSize)\r\n                    // list.forEach((item, index) => {\r\n                    //     let { orderSource, formType, waitingTime, appealAddress, id } = item\r\n                    //     this.scrollList.push([id, formType, orderSource, waitingTime, appealAddress, \"<i style='color:#a7fcff' class='el-icon-location'></i>\"])\r\n                    // })\r\n                }\r\n            })\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.rightView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 50px 0 50px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -50px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .day-num {\r\n        width: 651px;\r\n        height: 140px;\r\n        display: flex;\r\n        align-items: center;\r\n        background-size: 100% 100%;\r\n        background-image: url(\"@/assets/images/comprehensiveSituation/jk-bg.png\");\r\n        margin-top: 41px;\r\n        margin-bottom: 51px;\r\n\r\n        >span:nth-child(1) {\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 36px;\r\n            color: #FFFFFF;\r\n            line-height: 28px;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            margin-left: 158px;\r\n            margin-right: 141px;\r\n        }\r\n\r\n        >span:nth-child(2) {\r\n            font-family: DIN, DIN;\r\n            font-weight: 500;\r\n            font-size: 40px;\r\n            line-height: 46px;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            color: #C1E1FF;\r\n        }\r\n    }\r\n\r\n    .jk-list {\r\n        display: flex;\r\n        width: 656px;\r\n        height: 1458px;\r\n        flex-direction: column;\r\n        overflow-y: scroll;\r\n        overflow-x: hidden;\r\n\r\n        .list {\r\n            border-top: 2px solid;\r\n            border-image: linear-gradient(90deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)) 2 2;\r\n            width: 656px;\r\n            height: 346px;\r\n            display: flex;\r\n            flex-direction: column;\r\n            padding-bottom: 40px;\r\n            position: relative;\r\n\r\n            >p:nth-child(1) {\r\n                height: 42px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 21px;\r\n                color: #D8E8EF;\r\n                line-height: 42px;\r\n                text-align: left;\r\n                font-style: normal;\r\n                text-transform: none;\r\n            }\r\n\r\n            >p:nth-child(2) {\r\n                display: flex;\r\n                align-items: center;\r\n\r\n                >img {\r\n                    width: 94px;\r\n                    height: 76px;\r\n                    margin-right: 23px;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: bold;\r\n                    font-size: 42px;\r\n                    color: #D2D8DA;\r\n                    line-height: 49px;\r\n                    text-align: left;\r\n                    font-style: normal;\r\n                    margin-right: 39px;\r\n                }\r\n\r\n                >span:nth-child(3) {\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 400;\r\n                    font-size: 22px;\r\n                    color: #E79720;\r\n                    line-height: 31px;\r\n                    text-align: left;\r\n                    font-style: normal;\r\n                    text-transform: none;\r\n                }\r\n            }\r\n\r\n            >p:nth-child(3),\r\n            >p:nth-child(4),\r\n            >p:nth-child(5) {\r\n                display: flex;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 27px;\r\n                color: rgba(255, 255, 255, 0.7);\r\n                line-height: 42px;\r\n                text-align: left;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                margin-top: 16px;\r\n\r\n                >span {\r\n                    color: #FFFFFF;\r\n                }\r\n            }\r\n\r\n            >img {\r\n                position: absolute;\r\n                bottom: 37px;\r\n                right: 0;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AA4BA,SAAAA,QAAA;AACA,SAAAC,cAAA,EAAAC,iBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,MAAAC,EAAA;MACA,IAAAA,EAAA,IAAAA,EAAA,CAAAC,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,MAAA,CAAAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAhB,QAAA;MACAU,KAAA,EAAAO,KAAA,IAAAA,KAAA,CAAAC;IACA;EACA;EAEAC,QAAA;IACA,KAAAlB,cAAA;EACA;EAEAmB,QAAA,GAEA;EACAC,OAAA;IACA;IACA,MAAAC,eAAAC,MAAA;MACA;MACA,MAAArB,iBAAA,CAAAqB,MAAA,EAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArB,IAAA,CAAAsB,IAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAArB,IAAA,CAAAwB,KAAA;UAEA,IAAAD,IAAA,CAAAE,MAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;UAEA;UACA,IAAAR,MAAA;YACApB,IAAA;YACA6B,IAAA;YACAC,KAAA,QAAAC,UAAA,KAAAP,IAAA;YACAQ,MAAA;UACA;UACA,KAAAC,SAAA,CAAAvB,KAAA,eAAAU,MAAA;QAEA;MACA;IACA;IACAc,UAAA;MACA,IAAAd,MAAA;QACA;MACA;MACA,KAAAa,SAAA,CAAAvB,KAAA,eAAAU,MAAA;IACA;IACA;IACAe,WAAAC,IAAA;MACA,KAAAF,SAAA;MACA;QAAAG,KAAA;QAAAC,KAAA;QAAAC;MAAA,IAAAH,IAAA;MACA,IAAAC,KAAA,IAAAC,KAAA;QACA,KAAAL,SAAA,CAAAvB,KAAA;QACA;QACA;QACA;QACA;QACA,IAAAU,MAAA;UACA;UACA;YACA,QAAAmB,QAAA;YACA;YACA,OAAAD,KAAA;YACA,OAAAD,KAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA;QAEA;QACA,KAAAJ,SAAA,CAAAvB,KAAA,eAAAU,MAAA;MAEA;QACA,KAAAa,SAAA,CAAAvB,KAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;MAEA;MACAQ,IAAA,CAAAI,MAAA;MACA,KAAA7B,MAAA,CAAAC,MAAA,8BAAAwB,IAAA;MACA,KAAAzB,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;IACA;IACA;IACA,MAAAd,eAAAsB,MAAA;MACA,MAAAtB,cAAA,CAAAsB,MAAA,EAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArB,IAAA,CAAAsB,IAAA;UACA,KAAArB,SAAA,GAAAoB,GAAA,CAAArB,IAAA,CAAAwB,KAAA,CAAAxB,IAAA;UACA,KAAAE,KAAA,GAAAmB,GAAA,CAAArB,IAAA,CAAAwB,KAAA,CAAAtB,KAAA;UACA,KAAAC,SAAA,GAAAqC,IAAA,CAAAC,IAAA,MAAAvC,KAAA,QAAAwC,QAAA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}