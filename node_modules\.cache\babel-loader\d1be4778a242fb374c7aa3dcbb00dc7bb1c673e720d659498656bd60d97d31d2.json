{"ast": null, "code": "import warningInfo from './warningInfo.vue';\nimport warningTable from './warningTable.vue';\nimport warningHistory from './warningHistory.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'warningIndex',\n  components: {\n    warningInfo,\n    warningTable,\n    warningHistory\n  },\n  data() {\n    return {};\n  },\n  beforeCreate() {\n    // 初始化数据\n    this.$store.commit('warningType/getOriginal_State');\n  },\n  watch: {\n    isScreenShow(val) {\n      if (val) {\n        this.$store.commit('warningType/getOriginal_State');\n      }\n    }\n  },\n  computed: {\n    ...mapState('warningType', ['isShowAccumulationArr', 'isShowHistoryEventList', 'isShowEventInfoOne']),\n    isScreenShow() {\n      return this.$store.state.action.isScreenShow;\n    }\n  },\n  methods: {}\n};", "map": {"version": 3, "names": ["warningInfo", "warningTable", "warningHistory", "mapState", "name", "components", "data", "beforeCreate", "$store", "commit", "watch", "isScreenShow", "val", "computed", "state", "action", "methods"], "sources": ["src/views/dataScreen/fileType/waterWarningType/warningIndex.vue"], "sourcesContent": ["<!--水务预警 -->\r\n<template>\r\n    <div class=\"index\">\r\n        <warningInfo v-if=\"isShowEventInfoOne\"></warningInfo>\r\n        <warningTable v-if=\"isShowAccumulationArr\"></warningTable>\r\n        <warningHistory v-if=\"isShowHistoryEventList\"></warningHistory>\r\n    </div>\r\n</template>\r\n<script>\r\nimport warningInfo from './warningInfo.vue'\r\nimport warningTable from './warningTable.vue'\r\nimport warningHistory from './warningHistory.vue'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'warningIndex',\r\n    components: {\r\n        warningInfo,\r\n        warningTable,\r\n        warningHistory\r\n    },\r\n    data() {\r\n        return {\r\n\r\n        }\r\n    },\r\n    beforeCreate(){\r\n        // 初始化数据\r\n        this.$store.commit('warningType/getOriginal_State')\r\n    },\r\n    watch: {\r\n        isScreenShow(val){\r\n            if(val){\r\n                this.$store.commit('warningType/getOriginal_State')\r\n            }\r\n        }   \r\n    },\r\n    computed: {\r\n         ...mapState('warningType',['isShowAccumulationArr','isShowHistoryEventList','isShowEventInfoOne']),\r\n         isScreenShow(){\r\n            return this.$store.state.action.isScreenShow\r\n         }\r\n    },\r\n\r\n\r\n    methods: {\r\n \r\n    }\r\n\r\n}\r\n\r\n</script>\r\n<style lang=\"less\" scoped></style>"], "mappings": "AASA,OAAAA,WAAA;AACA,OAAAC,YAAA;AACA,OAAAC,cAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAL,WAAA;IACAC,YAAA;IACAC;EACA;EACAI,KAAA;IACA,QAEA;EACA;EACAC,aAAA;IACA;IACA,KAAAC,MAAA,CAAAC,MAAA;EACA;EACAC,KAAA;IACAC,aAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAJ,MAAA,CAAAC,MAAA;MACA;IACA;EACA;EACAI,QAAA;IACA,GAAAV,QAAA;IACAQ,aAAA;MACA,YAAAH,MAAA,CAAAM,KAAA,CAAAC,MAAA,CAAAJ,YAAA;IACA;EACA;EAGAK,OAAA,GAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}