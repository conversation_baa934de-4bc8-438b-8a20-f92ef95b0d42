{"ast": null, "code": "function createInfoWindow(title, content, callback) {\n  var info = document.createElement(\"div\"); //\n  info.className = \"custom-info input-card content-window-card\";\n  info.style.backgroundColor = \"rgba(27, 137, 225, 0.6)\";\n  info.style.color = \"#fff\";\n  info.style.padding = '10px';\n  //可以通过下面的方式修改自定义窗体的宽高\n  info.style.width = \"360px\";\n  // 定义顶部标题\n  var top = document.createElement(\"div\");\n\n  // var titleD = document.createElement(\"div\");\n  var closeX = document.createElement(\"img\");\n  top.className = \"info-top\";\n  closeX.src = require(\"@/assets/images/mainPics/player-close.png\");\n  closeX.style.cursor = \"pointer\";\n  closeX.onclick = callback;\n  // top.appendChild(titleD);\n  top.innerHTML = title;\n  top.style.display = \"flex\";\n  top.style.justifyContent = \"space-between\";\n  top.style.marginBottom = \"10px\";\n  top.appendChild(closeX);\n  info.appendChild(top);\n  // 定义中部内容\n  var middle = document.createElement(\"div\");\n  middle.className = \"info-middle\";\n  // middle.style.backgroundColor = \"rgba(27, 137, 225, 0.4)\";\n  middle.innerHTML = content;\n  info.appendChild(middle);\n  // 定义底部内容\n  var bottom = document.createElement(\"div\");\n  bottom.className = \"info-bottom\";\n  bottom.style.position = \"relative\";\n  bottom.style.top = \"0px\";\n  bottom.style.margin = \"0 auto\";\n  info.appendChild(bottom);\n  return info;\n}\nexport default {\n  createInfoWindow\n};", "map": {"version": 3, "names": ["createInfoWindow", "title", "content", "callback", "info", "document", "createElement", "className", "style", "backgroundColor", "color", "padding", "width", "top", "closeX", "src", "require", "cursor", "onclick", "innerHTML", "display", "justifyContent", "marginBottom", "append<PERSON><PERSON><PERSON>", "middle", "bottom", "position", "margin"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/amap.js"], "sourcesContent": ["function createInfoWindow(title, content, callback) {\r\n    var info = document.createElement(\"div\"); //\r\n    info.className = \"custom-info input-card content-window-card\";\r\n    info.style.backgroundColor = \"rgba(27, 137, 225, 0.6)\";\r\n    info.style.color = \"#fff\";\r\n    info.style.padding='10px'\r\n    //可以通过下面的方式修改自定义窗体的宽高\r\n    info.style.width = \"360px\";\r\n    // 定义顶部标题\r\n    var top = document.createElement(\"div\");\r\n\r\n    // var titleD = document.createElement(\"div\");\r\n    var closeX = document.createElement(\"img\");\r\n    top.className = \"info-top\";\r\n    closeX.src = require(\"@/assets/images/mainPics/player-close.png\");\r\n    closeX.style.cursor = \"pointer\";\r\n    closeX.onclick = callback;\r\n    // top.appendChild(titleD);\r\n    top.innerHTML = title;\r\n    top.style.display = \"flex\";\r\n    top.style.justifyContent= \"space-between\";\r\n    top.style.marginBottom = \"10px\";\r\n    top.appendChild(closeX);\r\n    info.appendChild(top);\r\n    // 定义中部内容\r\n    var middle = document.createElement(\"div\");\r\n    middle.className = \"info-middle\";\r\n    // middle.style.backgroundColor = \"rgba(27, 137, 225, 0.4)\";\r\n    middle.innerHTML = content;\r\n    info.appendChild(middle);\r\n    // 定义底部内容\r\n    var bottom = document.createElement(\"div\");\r\n    bottom.className = \"info-bottom\";\r\n    bottom.style.position = \"relative\";\r\n    bottom.style.top = \"0px\";\r\n    bottom.style.margin = \"0 auto\";\r\n    info.appendChild(bottom);\r\n    return info;\r\n}\r\n\r\nexport default {\r\n    createInfoWindow\r\n} "], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAChD,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1CF,IAAI,CAACG,SAAS,GAAG,4CAA4C;EAC7DH,IAAI,CAACI,KAAK,CAACC,eAAe,GAAG,yBAAyB;EACtDL,IAAI,CAACI,KAAK,CAACE,KAAK,GAAG,MAAM;EACzBN,IAAI,CAACI,KAAK,CAACG,OAAO,GAAC,MAAM;EACzB;EACAP,IAAI,CAACI,KAAK,CAACI,KAAK,GAAG,OAAO;EAC1B;EACA,IAAIC,GAAG,GAAGR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;;EAEvC;EACA,IAAIQ,MAAM,GAAGT,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC1CO,GAAG,CAACN,SAAS,GAAG,UAAU;EAC1BO,MAAM,CAACC,GAAG,GAAGC,OAAO,CAAC,2CAA2C,CAAC;EACjEF,MAAM,CAACN,KAAK,CAACS,MAAM,GAAG,SAAS;EAC/BH,MAAM,CAACI,OAAO,GAAGf,QAAQ;EACzB;EACAU,GAAG,CAACM,SAAS,GAAGlB,KAAK;EACrBY,GAAG,CAACL,KAAK,CAACY,OAAO,GAAG,MAAM;EAC1BP,GAAG,CAACL,KAAK,CAACa,cAAc,GAAE,eAAe;EACzCR,GAAG,CAACL,KAAK,CAACc,YAAY,GAAG,MAAM;EAC/BT,GAAG,CAACU,WAAW,CAACT,MAAM,CAAC;EACvBV,IAAI,CAACmB,WAAW,CAACV,GAAG,CAAC;EACrB;EACA,IAAIW,MAAM,GAAGnB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC1CkB,MAAM,CAACjB,SAAS,GAAG,aAAa;EAChC;EACAiB,MAAM,CAACL,SAAS,GAAGjB,OAAO;EAC1BE,IAAI,CAACmB,WAAW,CAACC,MAAM,CAAC;EACxB;EACA,IAAIC,MAAM,GAAGpB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC1CmB,MAAM,CAAClB,SAAS,GAAG,aAAa;EAChCkB,MAAM,CAACjB,KAAK,CAACkB,QAAQ,GAAG,UAAU;EAClCD,MAAM,CAACjB,KAAK,CAACK,GAAG,GAAG,KAAK;EACxBY,MAAM,CAACjB,KAAK,CAACmB,MAAM,GAAG,QAAQ;EAC9BvB,IAAI,CAACmB,WAAW,CAACE,MAAM,CAAC;EACxB,OAAOrB,IAAI;AACf;AAEA,eAAe;EACXJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}