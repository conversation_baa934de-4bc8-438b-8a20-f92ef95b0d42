/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../support/FeatureSet.js";import t from"../support/IdSet.js";import{FeatureServiceDatabaseType as r,IdState as s}from"../support/shared.js";import{resolve as a,reject as u}from"../../../core/promiseUtils.js";class n extends e{constructor(e){super(e),this.declaredClass="esri.layers.featureset.sources.Empty",this._maxProcessing=1e3,this._wset=new t([],[],!1,null),this._parent=e.parentfeatureset,this._databaseType=r.Standardised}_getSet(){return a(this._wset)}optimisePagingFeatureQueries(){}_isInFeatureSet(){return s.NotInFeatureSet}_getFeature(){return u(new Error("No Feature Found in EmptySet"))}queryAttachments(){return a([])}_getFeatures(){return a("success")}_featureFromCache(){return null}_fetchAndRefineFeatures(){return u(new Error("Fetch and Refine should not be called in this featureset"))}_getFilteredSet(){return a(new t([],[],!1,null))}_stat(e,t,r,s,a,u,n){return this._manualStat(e,t,u,n)}_canDoAggregates(){return a(!1)}}export{n as default};
