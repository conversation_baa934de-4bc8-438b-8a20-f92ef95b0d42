/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import t from"../ArcadePortal.js";import r from"../Attachment.js";import n from"../Dictionary.js";import{p as e,d as o,t as a,O as i,v as u,C as s,z as c,u as f,a as l,b as d,M as p,k as h,P as y,Q as m,T as g,U as A,V as w,f as I,o as v,i as b,j,c as E,q as F,r as P}from"../../chunks/languageUtils.js";import{layerFieldEsriConstants as U}from"../featureset/support/shared.js";import{convertDirection as x}from"./convertdirection.js";import{XXH as C}from"./hash.js";import T from"../../geometry/Extent.js";import N from"../../geometry/Multipoint.js";import k from"../../geometry/Point.js";import L from"../../geometry/Polygon.js";import R from"../../geometry/Polyline.js";import M from"../../geometry/SpatialReference.js";function H(t,r){if(t.x===r.x&&t.y===r.y){if(t.hasZ){if(t.z!==r.z)return!1}else if(r.hasZ)return!1;if(t.hasM){if(t.m!==r.m)return!1}else if(r.hasM)return!1;return!0}return!1}function S(e,o,a){if(null!==e)if(l(e)){if(o.updateUint8Array([61]),a.map.has(e)){const t=a.map.get(e);o.updateIntArray([61237541^t])}else{a.map.set(e,a.currentLength++);for(const t of e)S(t,o,a);a.map.delete(e),a.currentLength--}o.updateUint8Array([199])}else if(d(e)){if(o.updateUint8Array([61]),a.map.has(e)){const t=a.map.get(e);o.updateIntArray([61237541^t])}else{a.map.set(e,a.currentLength++);for(const t of e.toArray())S(t,o,a);a.map.delete(e),a.currentLength--}o.updateUint8Array([199])}else{if(v(e))return o.updateIntArray([e.getTime()]),void o.updateUint8Array([241]);if(I(e))return o.updateIntArray([e.length]),o.updateWithString(e),void o.updateUint8Array([41]);if(b(e))o.updateUint8Array([!0===e?1:0,113]);else{if(j(e))return o.updateFloatArray([e]),void o.updateUint8Array([173]);if(e instanceof r)throw new Error("Type not supported in Hash");if(e instanceof t)throw new Error("Type not supported in Hash");if(!(e instanceof n)){if(h(e))throw new Error("Type not supported in Hash");if(e instanceof k)return o.updateIntArray([3833836621]),o.updateIntArray([0]),o.updateFloatArray([e.x]),o.updateIntArray([1]),o.updateFloatArray([e.y]),e.hasZ&&(o.updateIntArray([2]),o.updateFloatArray([e.z])),e.hasM&&(o.updateIntArray([3]),o.updateFloatArray([e.m])),o.updateIntArray([3765347959]),void S(e.spatialReference.wkid,o,a);if(e instanceof L){o.updateIntArray([1266616829]);for(let t=0;t<e.rings.length;t++){const r=e.rings[t],n=[];let i=null,u=null;for(let o=0;o<r.length;o++){const a=e.getPoint(t,o);if(0===o)i=a;else if(H(u,a))continue;u=a,o===r.length-1&&H(i,a)||n.push(a)}o.updateIntArray([1397116793,n.length]);for(let t=0;t<n.length;t++){const r=n[t];o.updateIntArray([3962308117,t]),S(r,o,a),o.updateIntArray([2716288009])}o.updateIntArray([2278822459])}return o.updateIntArray([3878477243]),void S(e.spatialReference.wkid,o,a)}if(e instanceof R){o.updateIntArray([4106883559]);for(let t=0;t<e.paths.length;t++){const r=e.paths[t];o.updateIntArray([1397116793,r.length]);for(let n=0;n<r.length;n++)o.updateIntArray([3962308117,n]),S(e.getPoint(t,n),o,a),o.updateIntArray([2716288009]);o.updateIntArray([2278822459])}return o.updateIntArray([2568784753]),void S(e.spatialReference.wkid,o,a)}if(e instanceof N){o.updateIntArray([588535921,e.points.length]);for(let t=0;t<e.points.length;t++){const r=e.getPoint(t);o.updateIntArray([t]),S(r,o,a)}return o.updateIntArray([1700171621]),void S(e.spatialReference.wkid,o,a)}if(e instanceof T)return o.updateIntArray([3483648373]),o.updateIntArray([0]),o.updateFloatArray([e.xmax]),o.updateIntArray([1]),o.updateFloatArray([e.xmin]),o.updateIntArray([2]),o.updateFloatArray([e.ymax]),o.updateIntArray([3]),o.updateFloatArray([e.ymin]),e.hasZ&&(o.updateIntArray([4]),o.updateFloatArray([e.zmax]),o.updateIntArray([5]),o.updateFloatArray([e.zmin])),e.hasM&&(o.updateIntArray([6]),o.updateFloatArray([e.mmax]),o.updateIntArray([7]),o.updateFloatArray([e.mmin])),o.updateIntArray([3622027469]),void S(e.spatialReference.wkid,o,a);if(e instanceof M)return o.updateIntArray([14]),void 0!==e.wkid&&null!==e.wkid&&o.updateIntArray([e.wkid]),void(e.wkt&&o.updateWithString(e.wkt));if(E(e))throw new Error("Type not supported in Hash");if(F(e))throw new Error("Type not supported in Hash");if(P(e))throw new Error("Type not supported in Hash");if(e===u)throw new Error("Type not supported in Hash");throw new Error("Type not supported in Hash")}if(o.updateUint8Array([223]),a.map.has(e)){const t=a.map.get(e);o.updateIntArray([61237541^t])}else{a.map.set(e,a.currentLength++);for(const t of e.keys()){o.updateIntArray([t.length]),o.updateWithString(t),o.updateUint8Array([251]);S(e.field(t),o,a),o.updateUint8Array([239])}a.map.delete(e),a.currentLength--}o.updateUint8Array([73])}}else o.updateUint8Array([0,139])}function O(r,v){r.portal=function(r,n){return v(r,n,(function(r,n,a){return e(a,1,1),new t(o(a[0]))}))},r.trim=function(t,r){return v(t,r,(function(t,r,n){return e(n,1,1),o(n[0]).trim()}))},r.tohex=function(t,r){return v(t,r,(function(t,r,n){e(n,1,1);const o=a(n[0]);return isNaN(o)?o:o.toString(16)}))},r.upper=function(t,r){return v(t,r,(function(t,r,n){return e(n,1,1),o(n[0]).toUpperCase()}))},r.proper=function(t,r){return v(t,r,(function(t,r,n){e(n,1,2);let a=1;2===n.length&&"firstword"===o(n[1]).toLowerCase()&&(a=2);const i=/\s/,u=o(n[0]);let s="",c=!0;for(let e=0;e<u.length;e++){let t=u[e];if(i.test(t))1===a&&(c=!0);else{t.toUpperCase()!==t.toLowerCase()&&(c?(t=t.toUpperCase(),c=!1):t=t.toLowerCase())}s+=t}return s}))},r.lower=function(t,r){return v(t,r,(function(t,r,n){return e(n,1,1),o(n[0]).toLowerCase()}))},r.guid=function(t,r){return v(t,r,(function(t,r,n){if(e(n,0,1),n.length>0)switch(o(n[0]).toLowerCase()){case"digits":return i().replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return i();case"digits-hyphen-braces":return"{"+i()+"}";case"digits-hyphen-parentheses":return"("+i()+")"}return"{"+i()+"}"}))},r.console=function(t,r){return v(t,r,(function(r,n,e){return 0===e.length||(1===e.length?t.console(o(e[0])):t.console(o(e))),u}))},r.mid=function(t,r){return v(t,r,(function(t,r,n){e(n,2,3);let i=a(n[1]);if(isNaN(i))return"";if(i<0&&(i=0),2===n.length)return o(n[0]).substr(i);let u=a(n[2]);return isNaN(u)?"":(u<0&&(u=0),o(n[0]).substr(i,u))}))},r.find=function(t,r){return v(t,r,(function(t,r,n){e(n,2,3);let i=0;if(n.length>2){if(i=a(s(n[2],0)),isNaN(i))return-1;i<0&&(i=0)}return o(n[1]).indexOf(o(n[0]),i)}))},r.left=function(t,r){return v(t,r,(function(t,r,n){e(n,2,2);let i=a(n[1]);return isNaN(i)?"":(i<0&&(i=0),o(n[0]).substr(0,i))}))},r.right=function(t,r){return v(t,r,(function(t,r,n){e(n,2,2);let i=a(n[1]);return isNaN(i)?"":(i<0&&(i=0),o(n[0]).substr(-1*i,i))}))},r.split=function(t,r){return v(t,r,(function(t,r,n){let i;e(n,2,4);let u=a(s(n[2],-1));const f=c(s(n[3],!1));if(-1===u||null===u||!0===f?i=o(n[0]).split(o(n[1])):(isNaN(u)&&(u=-1),u<-1&&(u=-1),i=o(n[0]).split(o(n[1]),u)),!1===f)return i;const l=[];for(let e=0;e<i.length&&!(-1!==u&&l.length>=u);e++)""!==i[e]&&void 0!==i[e]&&l.push(i[e]);return l}))},r.text=function(t,r){return v(t,r,(function(t,r,n){return e(n,1,2),f(n[0],n[1])}))},r.concatenate=function(t,r){return v(t,r,(function(t,r,n){const e=[];if(n.length<1)return"";if(l(n[0])){const t=s(n[2],"");for(let r=0;r<n[0].length;r++)e[r]=f(n[0][r],t);return n.length>1?e.join(n[1]):e.join("")}if(d(n[0])){const t=s(n[2],"");for(let r=0;r<n[0].length();r++)e[r]=f(n[0].get(r),t);return n.length>1?e.join(n[1]):e.join("")}for(let o=0;o<n.length;o++)e[o]=f(n[o]);return e.join("")}))},r.reverse=function(t,r){return v(t,r,(function(t,r,n){if(e(n,1,1),l(n[0])){const t=n[0].slice(0);return t.reverse(),t}if(d(n[0])){const t=n[0].toArray().slice(0);return t.reverse(),t}throw new Error("Invalid Parameter")}))},r.replace=function(t,r){return v(t,r,(function(t,r,n){e(n,3,4);const a=o(n[0]),i=o(n[1]),u=o(n[2]);return 4!==n.length||c(n[3])?p(a,i,u):a.replace(i,u)}))},r.schema=function(t,r){return v(t,r,(function(t,r,e){if(h(e[0])){const t=y(e[0]);return t?n.convertObjectToArcadeDictionary(t):null}throw new Error("Invalid Parameter")}))},r.subtypes=function(t,r){return v(t,r,(function(t,r,o){if(e(o,1,1),h(o[0])){const t=m(o[0]);return t?n.convertObjectToArcadeDictionary(t):null}throw new Error("Invalid Parameter")}))},r.subtypecode=function(t,r){return v(t,r,(function(t,r,n){if(e(n,1,1),h(n[0])){const t=m(n[0]);if(!t)return null;if(t.subtypeField&&n[0].hasField(t.subtypeField)){const r=n[0].field(t.subtypeField);for(const n of t.subtypes)if(n.code===r)return n.code;return null}return null}throw new Error("Invalid Parameter")}))},r.subtypename=function(t,r){return v(t,r,(function(t,r,n){if(e(n,1,1),h(n[0])){const t=m(n[0]);if(!t)return"";if(t.subtypeField&&n[0].hasField(t.subtypeField)){const r=n[0].field(t.subtypeField);for(const n of t.subtypes)if(n.code===r)return n.name;return""}return""}throw new Error("Invalid Parameter")}))},r.gdbversion=function(t,r){return v(t,r,(function(t,r,n){if(e(n,1,1),h(n[0]))return n[0].gdbVersion();throw new Error("Invalid Parameter")}))},r.domain=function(t,r){return v(t,r,(function(t,r,i){if(e(i,2,3),h(i[0])){const t=g(i[0],o(i[1]),void 0===i[2]?void 0:a(i[2]));return t&&t.domain?"coded-value"===t.domain.type||"codedValue"===t.domain.type?n.convertObjectToArcadeDictionary({type:"codedValue",name:t.domain.name,dataType:U[t.field.type],codedValues:t.domain.codedValues.map((t=>({name:t.name,code:t.code})))}):n.convertObjectToArcadeDictionary({type:"range",name:t.domain.name,dataType:U[t.field.type],min:t.domain.min,max:t.domain.max}):null}throw new Error("Invalid Parameter")}))},r.domainname=function(t,r){return v(t,r,(function(t,r,n){if(e(n,2,4),h(n[0]))return A(n[0],o(n[1]),n[2],void 0===n[3]?void 0:a(n[3]));throw new Error("Invalid Parameter")}))},r.domaincode=function(t,r){return v(t,r,(function(t,r,n){if(e(n,2,4),h(n[0]))return w(n[0],o(n[1]),n[2],void 0===n[3]?void 0:a(n[3]));throw new Error("Invalid Parameter")}))},r.urlencode=function(t,r){return v(t,r,(function(t,r,a){if(e(a,1,1),null===a[0])return"";if(a[0]instanceof n){let t="";for(const r of a[0].keys()){const n=a[0].field(r);""!==t&&(t+="&"),t+=null===n?encodeURIComponent(r)+"=":encodeURIComponent(r)+"="+encodeURIComponent(n)}return t}return encodeURIComponent(o(a[0]))}))},r.hash=function(t,r){return v(t,r,(function(t,r,n){e(n,1,1);const o=new C(0);return S(n[0],o,{map:new Map,currentLength:0}),o.digest()}))},r.convertdirection=function(t,r){return v(t,r,(function(t,r,n){return e(n,3,3),x(n[0],n[1],n[2])}))},r.fromjson=function(t,r){return v(t,r,(function(t,r,a){if(e(a,1,1),!1===I(a[0]))throw new Error("Invalid Parameter");return n.convertJsonToArcade(JSON.parse(o(a[0])))}))},r.expects=function(t,r){return v(t,r,(function(t,r,n){if(n.length<1)throw new Error("Function called with wrong number of Parameters");return u}))},r.tocharcode=function(t,r){return v(t,r,(function(t,r,n){e(n,1,2);const i=a(s(n[1],0)),u=o(n[0]);if(0===u.length&&1===n.length)return null;if(u.length<=i||i<0)throw new Error("Illegal argument");return u.charCodeAt(i)}))},r.tocodepoint=function(t,r){return v(t,r,(function(t,r,n){e(n,1,2);const i=a(s(n[1],0)),u=o(n[0]);if(0===u.length&&1===n.length)return null;if(u.length<=i||i<0)throw new Error("Illegal argument");return u.codePointAt(i)}))},r.fromcharcode=function(t,r){return v(t,r,(function(t,r,n){if(n.length<1)throw new Error("Function called with wrong number of Parameters");const e=n.map((t=>Math.trunc(a(t)))).filter((t=>t>=0&&t<=65535));return 0===e.length?null:String.fromCharCode.apply(null,e)}))},r.fromcodepoint=function(t,r){return v(t,r,(function(t,r,n){if(n.length<1)throw new Error("Function called with wrong number of Parameters");let e;try{e=n.map((t=>Math.trunc(a(t)))).filter((t=>t<=1114111&&t>>>0===t))}catch(o){return null}return 0===e.length?null:String.fromCodePoint.apply(null,e)}))}}export{O as registerFunctions};
