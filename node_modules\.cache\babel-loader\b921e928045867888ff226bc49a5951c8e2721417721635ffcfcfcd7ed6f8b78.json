{"ast": null, "code": "import { mapState } from 'vuex';\nimport { getCommunityTop25 } from '@/api/userMenu.js';\nimport { getTakeAmountDepartment } from '@/api/index.js';\nimport EventDealProgress from '@/components/peopleDem/EventDealProgress.vue';\nimport WaterEventInfo from '@/views/dataScreen/fileType/WaterEventInfo/WaterEventInfo.vue';\nimport ReportEvent from '@/components/smartWater/ReportEvent.vue';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      depDealSum: 0,\n      //小区办件量总数\n      dealEventTopDep: [],\n      showEventPanel: false,\n      depDealDetailDataConfig: {},\n      animate: false,\n      eventSubBox: false //事件订阅弹窗\n    };\n  },\n  components: {\n    ReportEvent,\n    WaterEventInfo,\n    EventDealProgress\n  },\n  watch: {\n    // 事件告警状态\n    eventSubBox: {\n      handler(nv) {\n        this.$store.commit('action/getEventSubBoxFlag', nv);\n      },\n      immediate: true\n    },\n    eventSubBoxFlag: {\n      handler(nv) {\n        this.eventSubBox = nv;\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState({\n      eventSubBoxFlag: state => state.action.eventSubBoxFlag\n    })\n  },\n  mounted() {\n    this.initData();\n  },\n  updated() {},\n  methods: {\n    initData() {\n      getTakeAmountDepartment().then(res => {\n        let dataList = res.data.data;\n        for (var i = 0; i < dataList.length; i++) {\n          //需要比较多少轮\n          //每一轮的比较都是当前的值和他后面的所有元素比较\n          for (var j = i + 1; j < dataList.length; j++) {\n            if (dataList[i].total * 1 < dataList[j].total * 1) {\n              var swap = dataList[i];\n              dataList[i] = dataList[j];\n              dataList[j] = swap;\n            }\n          }\n        }\n        this.dealEventTopDep = dataList.splice(0, 5);\n      });\n      getCommunityTop25({\n        pageNum: 1,\n        pageSize: 25\n      }).then(res => {\n        if (res.status == 200) {\n          // this.depDealDetailData = res.data.rows\n          let list = res.data.rows;\n          let configList = {\n            header: ['排行', '小区名称', '办件量'],\n            data: [],\n            headerBGC: \"rgba(18, 76, 111, .45)\",\n            evenRowBGC: 'rgba(0,0,0, 0)',\n            oddRowBGC: 'rgba(0,0,0, 0)',\n            fontSize: 20\n          };\n          let sum = 0;\n          list.forEach((item, index) => {\n            configList.data.push([index, item.name, item.count]);\n            sum += item.count * 1;\n          });\n          this.depDealDetailDataConfig = configList;\n          this.depDealSum = sum;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "getCommunityTop25", "getTakeAmountDepartment", "EventDealProgress", "WaterEventInfo", "ReportEvent", "name", "data", "depDealSum", "dealEventTopDep", "showEventPanel", "depDealDetailDataConfig", "animate", "eventSubBox", "components", "watch", "handler", "nv", "$store", "commit", "immediate", "eventSubBoxFlag", "computed", "state", "action", "mounted", "initData", "updated", "methods", "then", "res", "dataList", "i", "length", "j", "total", "swap", "splice", "pageNum", "pageSize", "status", "list", "rows", "configList", "header", "headerBGC", "evenRowBGC", "oddRowBGC", "fontSize", "sum", "for<PERSON>ach", "item", "index", "push", "count"], "sources": ["src/views/peopleLivelihood/components/people-realtime/left-realtime.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leftView\">\r\n        <div class=\"title-box\">\r\n            <span>实时诉求</span>\r\n        </div>\r\n        <div class=\"two-title\">部门事件办理排行</div>\r\n        <div class=\"Department_incident\">\r\n            <div class=\"topTwoDep\">\r\n                <div class=\"diffDepBox\" v-for=\"(item, i) of dealEventTopDep\" :key=\"i\">\r\n                    <div class=\"txt\">\r\n                        {{ item.deptName }}\r\n                    </div>\r\n                    <div class=\"num\">{{ item.total }} </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"two-title\">辖区诉求量</div>\r\n        <div class=\"event_processing_ progress\">\r\n\r\n            <div class=\"monthDealNum\">\r\n                <div class=\"left\">\r\n                    诉求量各区详情\r\n                </div>\r\n                <div class=\"right\">\r\n                    同比 <span>18</span> %\r\n                </div>\r\n                <img src=\"@/assets/images/right_slices/people/down.png\" alt=\"\" width=\"6%\">\r\n            </div>\r\n\r\n            <div class=\"eventProcessingChart\">\r\n                <EventDealProgress></EventDealProgress>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"two-title\">部门事件办理详情</div>\r\n        <div class=\"real-bg\" @click=\"eventSubBox = true\"><span>实时列表</span></div>\r\n        <div class=\"yeartotalDeal\">\r\n            <div class=\"left\">\r\n                <span class=\"txt\"> 小区办件量 </span>\r\n                <span class=\"num-top\">Top25</span>\r\n                <div class=\"num\">{{ depDealSum }}</div>\r\n            </div>\r\n            <div class=\"icon\">\r\n                <img src=\"@/assets/images/right_slices/people/light.png\" alt=\"\" width=\"100%\">\r\n            </div>\r\n        </div>\r\n        <div class=\"detailsDataPush\">\r\n            <dv-scroll-board :config=\"depDealDetailDataConfig\" style=\"width:94%;height:500px;text-align:center;\" />\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { getCommunityTop25 } from '@/api/userMenu.js'\r\nimport { getTakeAmountDepartment } from '@/api/index.js'\r\n\r\nimport EventDealProgress from '@/components/peopleDem/EventDealProgress.vue'\r\n\r\nimport WaterEventInfo from '@/views/dataScreen/fileType/WaterEventInfo/WaterEventInfo.vue'\r\nimport ReportEvent from '@/components/smartWater/ReportEvent.vue'\r\nexport default {\r\n    name: 'leftView',\r\n    data() {\r\n        return {\r\n            depDealSum: 0,//小区办件量总数\r\n            dealEventTopDep: [],\r\n            showEventPanel: false,\r\n            depDealDetailDataConfig: {},\r\n            animate: false,\r\n            eventSubBox: false,//事件订阅弹窗\r\n        };\r\n    },\r\n\r\n    components: {\r\n        ReportEvent,\r\n        WaterEventInfo,\r\n        EventDealProgress,\r\n    },\r\n    watch: {\r\n        // 事件告警状态\r\n        eventSubBox: {\r\n            handler(nv) {\r\n                this.$store.commit('action/getEventSubBoxFlag', nv)\r\n            },\r\n            immediate: true\r\n\r\n        },\r\n        eventSubBoxFlag: {\r\n            handler(nv) {\r\n                this.eventSubBox = nv;\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            eventSubBoxFlag: state => state.action.eventSubBoxFlag,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n        this.initData()\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        initData() {\r\n            getTakeAmountDepartment().then(res => {\r\n                let dataList = res.data.data;\r\n                for (var i = 0; i < dataList.length; i++) {//需要比较多少轮\r\n                    //每一轮的比较都是当前的值和他后面的所有元素比较\r\n                    for (var j = i + 1; j < dataList.length; j++) {\r\n                        if (dataList[i].total * 1 < dataList[j].total * 1) {\r\n                            var swap = dataList[i];\r\n                            dataList[i] = dataList[j];\r\n                            dataList[j] = swap;\r\n                        }\r\n                    }\r\n                }\r\n                this.dealEventTopDep = dataList.splice(0, 5)\r\n            })\r\n            getCommunityTop25({ pageNum: 1, pageSize: 25 }).then(res => {\r\n                if (res.status == 200) {\r\n                    // this.depDealDetailData = res.data.rows\r\n                    let list = res.data.rows\r\n                    let configList = {\r\n                        header: ['排行', '小区名称', '办件量'],\r\n                        data: [],\r\n                        headerBGC: \"rgba(18, 76, 111, .45)\",\r\n                        evenRowBGC: 'rgba(0,0,0, 0)',\r\n                        oddRowBGC: 'rgba(0,0,0, 0)',\r\n                        fontSize: 20,\r\n                    }\r\n                    let sum = 0\r\n                    list.forEach((item, index) => {\r\n                        configList.data.push([index, item.name, item.count])\r\n                        sum += item.count * 1\r\n                    })\r\n                    this.depDealDetailDataConfig = configList\r\n                    this.depDealSum = sum\r\n                }\r\n            })\r\n        },\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    width: 700px;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    margin-top: 20px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n\r\n.leftView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .Department_incident {\r\n        // position: relative;\r\n        width: 100%;\r\n        height: 22rem;\r\n        margin: 48px -34px;\r\n        background: url('@/assets/images/right_slices/people/rbgIcon2.png') no-repeat center;\r\n        background-size: 61% 94%;\r\n\r\n        .topTwoDep {\r\n            position: relative;\r\n            display: flex;\r\n            justify-content: space-between;\r\n\r\n            >div:nth-child(1) {\r\n                left: 250px;\r\n                top: -20px;\r\n            }\r\n\r\n            >div:nth-child(2) {\r\n                left: 390px;\r\n                top: -20px;\r\n            }\r\n\r\n            >div:nth-child(3) {\r\n                left: 140px;\r\n                top: 170px;\r\n            }\r\n\r\n            >div:nth-child(4) {\r\n                left: 320px;\r\n                top: 170px;\r\n            }\r\n\r\n            >div:nth-child(5) {\r\n                left: 500px;\r\n                top: 170px;\r\n            }\r\n\r\n            >div {\r\n                position: absolute;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n                align-items: center;\r\n                width: 120px;\r\n\r\n                .txt {\r\n                    line-height: 25px;\r\n                    color: rgba(57, 228, 230, 1);\r\n                }\r\n\r\n                .num {\r\n                    font-size: 1.6rem;\r\n                    font-weight: bold;\r\n                    margin-top: 15px;\r\n\r\n                    >span {\r\n                        display: block;\r\n                        text-align: center;\r\n\r\n                        font-size: 1.9rem;\r\n\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n\r\n    }\r\n\r\n    .real-bg {\r\n        width: 180px;\r\n        height: 45px;\r\n        background: url('@/assets/images/right_slices/people/real-bg.png') no-repeat center;\r\n        // background-size: 61% 94%;\r\n        background-size: cover;\r\n        position: relative;\r\n        left: 490px;\r\n        top: -46px;\r\n        cursor: pointer;\r\n        z-index: 999;\r\n\r\n        span {\r\n            width: 128px;\r\n            height: 57px;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 32px;\r\n            color: #FFFFFF;\r\n            line-height: 56px;\r\n            text-shadow: 0px 5px 7px rgba(0, 0, 0, 0.72), 0px 5px 13px #00FF1E;\r\n            text-align: center;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            white-space: nowrap;\r\n            position: absolute;\r\n            margin-left: 24px;\r\n            top: -8px;\r\n        }\r\n    }\r\n\r\n    .yeartotalDeal {\r\n        padding: 0 2rem;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 2.5rem;\r\n\r\n        .txt {\r\n            font-size: 1.7rem;\r\n            width: 180px;\r\n            height: 28px;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 36px;\r\n            color: #00FFFC;\r\n            line-height: 28px;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            margin-right: 70px;\r\n        }\r\n\r\n        .num-top {\r\n            width: 158px;\r\n            height: 28px;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 46px;\r\n            line-height: 28px;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            color: #FFA56C;\r\n            // background: linear-gradient(269.9999999999991deg, #FFA56C 0%, rgba(255, 255, 178, 0.78) 100%);\r\n\r\n        }\r\n\r\n        .num {\r\n            width: 125px;\r\n            height: 28px;\r\n            font-family: DIN, DIN;\r\n            font-weight: bold;\r\n            font-size: 46px;\r\n            line-height: 28px;\r\n            text-shadow: 0px 0px 13px #03A0F9;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            color: #59C9DD;\r\n            margin-top: 40px;\r\n            // background: linear-gradient(90deg, #FFFFFF 0%, #59C9DD 100%);\r\n        }\r\n\r\n        .icon {\r\n\r\n            width: 124px;\r\n            height: 136px;\r\n\r\n\r\n            position: relative;\r\n            left: -60px;\r\n            top: -20px;\r\n            // width: 6rem;\r\n        }\r\n    }\r\n\r\n    .detailsDataPush {\r\n        padding-left: 30px;\r\n\r\n        ::v-deep .dv-scroll-board .header {\r\n            font-size: 22px;\r\n            font-weight: bold;\r\n        }\r\n\r\n        ::v-deep .dv-scroll-board .rows .row-item {\r\n            font-size: 22px;\r\n\r\n        }\r\n\r\n        ::v-deep .dv-scroll-board .rows .row-item:hover {\r\n            padding: 10px;\r\n            color: rgb(61, 117, 238);\r\n\r\n        }\r\n\r\n        .thead {\r\n            overflow: hidden;\r\n            height: 3rem;\r\n            background-color: rgba(18, 137, 220, .4);\r\n\r\n        }\r\n\r\n        .bodyBox {\r\n            overflow: hidden;\r\n        }\r\n\r\n        .tbody {\r\n            margin-top: .7rem;\r\n            height: 12rem;\r\n        }\r\n\r\n        .communityProPush {\r\n            width: 100%;\r\n            height: 230px;\r\n            position: relative;\r\n\r\n        }\r\n\r\n        .animate {\r\n            // 过渡\r\n            transition: all .5s ease-in;\r\n        }\r\n\r\n        .tbody ul {\r\n            width: 100%;\r\n            position: relative;\r\n            padding: 0rem .2rem;\r\n            list-style: none;\r\n            display: flex;\r\n\r\n            /* justify-content: space-between; */\r\n            >li {\r\n                height: 3rem;\r\n                font-size: 1.4rem;\r\n                text-align: center;\r\n\r\n                &:nth-of-type(1) {\r\n                    width: 40%;\r\n\r\n                }\r\n\r\n                &:nth-of-type(2) {\r\n                    width: 10%;\r\n                }\r\n\r\n                &:nth-of-type(3) {\r\n                    width: 50%;\r\n                    text-align: center;\r\n                }\r\n\r\n\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n\r\n    .event-panel-container {\r\n        position: fixed;\r\n        top: 50%;\r\n        left: 65%;\r\n        transform: translate(-50%, -50%);\r\n        width: 80%;\r\n        // z-index: 1000;\r\n\r\n        // animation: fadeIn 0.3s ease-in-out;\r\n    }\r\n\r\n    // @keyframes fadeIn {\r\n    //     from {\r\n    //         opacity: 0;\r\n    //         transform: translate(-50%, -48%);\r\n    //     }\r\n    //     to {\r\n    //         opacity: 1;\r\n    //         transform: translate(-50%, -50%);\r\n    //     }\r\n    // }\r\n\r\n    .monthDealNum {\r\n        margin-top: 1.3rem;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 94%;\r\n        height: 3rem;\r\n        line-height: 3rem;\r\n        background: url('@/assets/images/right_slices/people/bgIcon_event.png')no-repeat center;\r\n        background-size: 100%;\r\n\r\n        .left {\r\n            >span {\r\n                font-size: 2rem;\r\n                margin-left: .5rem;\r\n            }\r\n        }\r\n\r\n        .right {\r\n            margin-left: 3rem;\r\n            padding-right: .5rem;\r\n\r\n            >span {\r\n                font-size: 2rem;\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    .eventProcessingChart {\r\n        // position: relative;\r\n        // left: -28px;\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAqDA,SAAAA,QAAA;AACA,SAAAC,iBAAA;AACA,SAAAC,uBAAA;AAEA,OAAAC,iBAAA;AAEA,OAAAC,cAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MAAA;MACAC,eAAA;MACAC,cAAA;MACAC,uBAAA;MACAC,OAAA;MACAC,WAAA;IACA;EACA;EAEAC,UAAA;IACAT,WAAA;IACAD,cAAA;IACAD;EACA;EACAY,KAAA;IACA;IACAF,WAAA;MACAG,QAAAC,EAAA;QACA,KAAAC,MAAA,CAAAC,MAAA,8BAAAF,EAAA;MACA;MACAG,SAAA;IAEA;IACAC,eAAA;MACAL,QAAAC,EAAA;QACA,KAAAJ,WAAA,GAAAI,EAAA;MACA;MACAG,SAAA;IACA;EACA;EACAE,QAAA;IACA,GAAAtB,QAAA;MACAqB,eAAA,EAAAE,KAAA,IAAAA,KAAA,CAAAC,MAAA,CAAAH;IACA;EACA;EAEAI,QAAA;IACA,KAAAC,QAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAF,SAAA;MACAxB,uBAAA,GAAA2B,IAAA,CAAAC,GAAA;QACA,IAAAC,QAAA,GAAAD,GAAA,CAAAvB,IAAA,CAAAA,IAAA;QACA,SAAAyB,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAE,MAAA,EAAAD,CAAA;UAAA;UACA;UACA,SAAAE,CAAA,GAAAF,CAAA,MAAAE,CAAA,GAAAH,QAAA,CAAAE,MAAA,EAAAC,CAAA;YACA,IAAAH,QAAA,CAAAC,CAAA,EAAAG,KAAA,OAAAJ,QAAA,CAAAG,CAAA,EAAAC,KAAA;cACA,IAAAC,IAAA,GAAAL,QAAA,CAAAC,CAAA;cACAD,QAAA,CAAAC,CAAA,IAAAD,QAAA,CAAAG,CAAA;cACAH,QAAA,CAAAG,CAAA,IAAAE,IAAA;YACA;UACA;QACA;QACA,KAAA3B,eAAA,GAAAsB,QAAA,CAAAM,MAAA;MACA;MACApC,iBAAA;QAAAqC,OAAA;QAAAC,QAAA;MAAA,GAAAV,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAU,MAAA;UACA;UACA,IAAAC,IAAA,GAAAX,GAAA,CAAAvB,IAAA,CAAAmC,IAAA;UACA,IAAAC,UAAA;YACAC,MAAA;YACArC,IAAA;YACAsC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA,IAAAC,GAAA;UACAR,IAAA,CAAAS,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACAT,UAAA,CAAApC,IAAA,CAAA8C,IAAA,EAAAD,KAAA,EAAAD,IAAA,CAAA7C,IAAA,EAAA6C,IAAA,CAAAG,KAAA;YACAL,GAAA,IAAAE,IAAA,CAAAG,KAAA;UACA;UACA,KAAA3C,uBAAA,GAAAgC,UAAA;UACA,KAAAnC,UAAA,GAAAyC,GAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}