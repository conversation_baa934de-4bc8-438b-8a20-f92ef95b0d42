{"ast": null, "code": "import { getMatch, getOnlineMatch } from '@/api/userMenu';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'addressSearch',\n  data() {\n    return {\n      inpVal: '',\n      list: [],\n      total: 0,\n      tabIndex: 0,\n      tabsArr: ['标准地址', '在线地址']\n    };\n  },\n  components: {},\n  watch: {\n    isShowSearch: {\n      handler(nv) {\n        // console.log(nv, 'ceshi')\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState({\n      isShowSearch: state => state.action.isShowSearch,\n      ueTxt: state => state.dataChannelText\n    })\n  },\n  mounted() {\n    // this.getMatchList();\n  },\n  updated() {},\n  methods: {\n    tabsFn(index) {\n      this.tabIndex = index;\n      this.inpVal = null;\n      this.list = [];\n      this.deletePoi();\n    },\n    seturl(index) {\n      return require(`@/assets/${index == this.tabIndex ? 'poi-active' : 'poi'}.png`);\n    },\n    clearRadar() {\n      let params = {\n        \"name\": \"雷达图\",\n        \"jd\": \"121.094821540\",\n        \"wd\": \"31.29846693558\",\n        \"high\": \"320\",\n        \"scale\": \"20\",\n        \"delete\": \"删除\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    sendEvent(item) {\n      this.deletePoi();\n      // this.clearRadar();\n      let params = {\n        \"mode\": \"add\",\n        \"sources\": [{\n          \"Name\": item.dzQdz || item.name,\n          \"channalCode\": item.dzid || null,\n          \"Lng\": item.dzGpsX || item.lng84,\n          \"Lat\": item.dzGpsY || item.lat84,\n          \"Type\": \"leida\",\n          \"IconType\": \"1\",\n          \"Height\": \"100\",\n          \"TextX\": \"1\",\n          \"TextY\": \"-31\",\n          \"TextSize\": \"11\",\n          \"ImageX\": \"0.27\",\n          \"ImageY\": \"0.27\"\n        }]\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n      this.$eventBus.$emit('senTxtToUe', '全局视角');\n    },\n    deletePoi() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    closeFn() {\n      // this.deletePoi();\n      this.$store.commit('action/getIsShowSearch', false);\n    },\n    showlist() {\n      this.deletePoi();\n      this.list = [];\n      this.total = 0;\n      this.inpVal = '';\n    },\n    // 获取地址数据\n    getMatchList() {\n      getMatch({\n        keyword: this.inpVal,\n        pageSize: 100000,\n        pageNum: 1\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.list = res.data.extra.records;\n          this.total = res.data.extra.records.length + 1;\n        }\n      });\n    },\n    // 在线地址查询\n    getOnlineMatchList() {\n      getOnlineMatch({\n        keyword: this.inpVal,\n        pageSize: 100000,\n        pageNum: 1\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.list = res.data.extra;\n          this.total = res.data.extra.length + 1;\n        }\n      });\n    },\n    // 搜索\n    handleQuery() {\n      if (this.tabIndex == 0) {\n        this.getMatchList();\n      } else {\n        this.getOnlineMatchList();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getMatch", "getOnlineMatch", "mapState", "name", "data", "inpVal", "list", "total", "tabIndex", "tabsArr", "components", "watch", "isShowSearch", "handler", "nv", "immediate", "computed", "state", "action", "ueTxt", "dataChannelText", "mounted", "updated", "methods", "tabsFn", "index", "deletePoi", "seturl", "require", "clearRadar", "params", "$eventBus", "$emit", "sendEvent", "item", "dzQdz", "dzid", "dzGpsX", "lng84", "dzGpsY", "lat84", "closeFn", "$store", "commit", "showlist", "getMatchList", "keyword", "pageSize", "pageNum", "then", "res", "code", "extra", "records", "length", "getOnlineMatchList", "handleQuery"], "sources": ["src/components/addressSearch.vue"], "sourcesContent": ["<template>\r\n    <div class=\"search-box\" v-if=\"isShowSearch\">\r\n        <img @click=\"closeFn\" src=\"@/assets/close1.png\" alt=\"\">\r\n        <div class=\"tabs\">\r\n            <p v-for=\"(item, index) in tabsArr\">\r\n                <img :src=\"seturl(index)\" @click=\"tabsFn(index)\" alt=\"\">\r\n                <span>{{ item }}</span>\r\n            </p>\r\n        </div>\r\n        <div class=\"inp\">\r\n            <el-input v-model=\"inpVal\" placeholder=\"请输入关键字\" style=\"caret-color: #fff;\"></el-input>\r\n            <span @click=\"handleQuery\">搜索</span>\r\n        </div>\r\n        <div class=\"content\" v-if=\"list.length > 0\">\r\n            <div class=\"top\">\r\n                <span>搜索结果 （{{ total }}）</span>\r\n                <img @click=\"showlist\" src=\"@/assets/close2.png\" alt=\"\">\r\n            </div>\r\n            <div class=\"list\">\r\n                <p v-for=\"item in list\" @click=\"sendEvent(item)\">{{ item.dzQdz || item.name }}</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"content\" v-else style=\"font-size: 30px; text-align: center;line-height: 130px;\">\r\n            暂无数据\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getMatch, getOnlineMatch } from '@/api/userMenu'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'addressSearch',\r\n    data() {\r\n        return {\r\n            inpVal: '',\r\n            list: [],\r\n            total: 0,\r\n            tabIndex: 0,\r\n            tabsArr: ['标准地址', '在线地址']\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n        isShowSearch: {\r\n            handler(nv) {\r\n                // console.log(nv, 'ceshi')\r\n            }, immediate: true,\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            isShowSearch: state => state.action.isShowSearch,\r\n            ueTxt: state => state.dataChannelText,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n        // this.getMatchList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        tabsFn(index) {\r\n            this.tabIndex = index;\r\n            this.inpVal = null;\r\n            this.list = [];\r\n            this.deletePoi();\r\n        },\r\n        seturl(index) {\r\n            return require(`@/assets/${index == this.tabIndex ? 'poi-active' : 'poi'}.png`)\r\n        },\r\n        clearRadar() {\r\n            let params = {\r\n                \"name\": \"雷达图\",\r\n                \"jd\": \"121.094821540\",\r\n                \"wd\": \"31.29846693558\",\r\n                \"high\": \"320\",\r\n                \"scale\": \"20\",\r\n                \"delete\": \"删除\"\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params)\r\n        },\r\n        sendEvent(item) {\r\n            this.deletePoi();\r\n            // this.clearRadar();\r\n            let params = {\r\n                \"mode\": \"add\",\r\n                \"sources\": [{\r\n                    \"Name\": item.dzQdz || item.name,\r\n                    \"channalCode\": item.dzid || null,\r\n                    \"Lng\": item.dzGpsX || item.lng84,\r\n                    \"Lat\": item.dzGpsY || item.lat84,\r\n                    \"Type\": \"leida\",\r\n                    \"IconType\": \"1\",\r\n                    \"Height\": \"100\",\r\n                    \"TextX\": \"1\",\r\n                    \"TextY\": \"-31\",\r\n                    \"TextSize\": \"11\",\r\n                    \"ImageX\": \"0.27\",\r\n                    \"ImageY\": \"0.27\"\r\n                }]\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params)\r\n            this.$eventBus.$emit('senTxtToUe', '全局视角')\r\n        },\r\n        deletePoi() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        closeFn() {\r\n            // this.deletePoi();\r\n            this.$store.commit('action/getIsShowSearch', false)\r\n        },\r\n        showlist() {\r\n            this.deletePoi();\r\n            this.list = [];\r\n            this.total = 0;\r\n            this.inpVal = '';\r\n        },\r\n        // 获取地址数据\r\n        getMatchList() {\r\n            getMatch({ keyword: this.inpVal, pageSize: 100000, pageNum: 1 }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.list = res.data.extra.records;\r\n                    this.total = res.data.extra.records.length + 1;\r\n                }\r\n            })\r\n        },\r\n        // 在线地址查询\r\n        getOnlineMatchList() {\r\n            getOnlineMatch({ keyword: this.inpVal, pageSize: 100000, pageNum: 1 }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.list = res.data.extra;\r\n                    this.total = res.data.extra.length + 1;\r\n                }\r\n            })\r\n        },\r\n        // 搜索\r\n        handleQuery() {\r\n            if (this.tabIndex == 0) {\r\n                this.getMatchList();\r\n            } else {\r\n                this.getOnlineMatchList();\r\n            }\r\n\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n:deep(.el-input) {\r\n    width: 588px;\r\n    height: 67px;\r\n    background: RGBA(15, 60, 104, 1);\r\n    border-radius: 4px;\r\n\r\n    .el-input__inner {\r\n        height: 67px !important;\r\n        border: none;\r\n        background-color: transparent;\r\n        color: #fff;\r\n        font-size: 28px;\r\n    }\r\n\r\n    .el-input__inner::-webkit-input-placeholder {\r\n        color: #fff\r\n    }\r\n}\r\n\r\n.search-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: fixed;\r\n    top: 160px;\r\n    right: 180px;\r\n    width: 730px;\r\n    pointer-events: stroke;\r\n\r\n    // transform: scale(0.5, 0.445833);\r\n    .tabs {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 528px;\r\n        height: 51px;\r\n        background-size: 100% 100%;\r\n        background-image: url('@/assets/tabs-bg.png');\r\n        padding-left: 25px;\r\n\r\n        >p {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 100px;\r\n\r\n            >img {\r\n                width: 26px;\r\n                height: 26px;\r\n                cursor: pointer;\r\n            }\r\n\r\n            >span {\r\n                margin-left: 8px;\r\n                font-family: Inter, Inter;\r\n                font-weight: 500;\r\n                font-size: 28px;\r\n                color: #FFFFFF;\r\n                line-height: 33px;\r\n                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\r\n                text-align: left;\r\n                font-style: normal;\r\n                text-transform: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    >img {\r\n        cursor: pointer;\r\n        width: 37px;\r\n        height: 37px;\r\n        position: absolute;\r\n        right: 17px;\r\n        top: -57px;\r\n        pointer-events: stroke;\r\n    }\r\n\r\n    .inp {\r\n        width: 730px;\r\n        height: 97px;\r\n        background: rgba(10, 27, 54, 0.8);\r\n        border-radius: 0px 0px 0px 0px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 18px;\r\n        margin-bottom: 52px;\r\n\r\n        >span {\r\n            width: 115px;\r\n            height: 67px;\r\n            background: rgba(54, 168, 255, 0.65);\r\n            border-radius: 5px 5px 5px 5px;\r\n            cursor: pointer;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 34px;\r\n            color: #FFFFFF;\r\n            line-height: 67px;\r\n            text-align: center;\r\n            margin-left: 10px;\r\n\r\n        }\r\n    }\r\n\r\n    .content {\r\n        display: flex;\r\n        flex-direction: column;\r\n        background-image: url(\"@/assets/content-bg.png\");\r\n        background-size: 100% 100%;\r\n        width: 750px;\r\n        height: 737px;\r\n\r\n        .top {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            padding: 0 42px;\r\n            height: 100px;\r\n            line-height: 100px;\r\n            border-bottom: 1px solid #9CB2C3;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 34px;\r\n            color: #FFFFFF;\r\n\r\n            >img {\r\n                cursor: pointer;\r\n                pointer-events: stroke;\r\n            }\r\n        }\r\n\r\n        .list {\r\n            width: 720px;\r\n            height: 565px;\r\n            display: flex;\r\n            flex-direction: column;\r\n            padding: 0 8px;\r\n            overflow-y: scroll;\r\n            overflow-x: hidden;\r\n\r\n            >p {\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 34px;\r\n                color: #FFFFFF;\r\n                width: 700px;\r\n                height: 101px;\r\n                padding-left: 20px;\r\n                // padding: 0 94px;\r\n                white-space: wrap;\r\n                border-bottom: 1px solid #9CB2C3;\r\n                cursor: pointer;\r\n                min-height: 101px;\r\n                display: flex;\r\n                align-items: center;\r\n                pointer-events: stroke;\r\n            }\r\n\r\n            >p:hover {\r\n                background: #3AA0F3;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AA6BA,SAAAA,QAAA,EAAAC,cAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,IAAA;MACAC,KAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,YAAA;MACAC,QAAAC,EAAA;QACA;MAAA,CACA;MAAAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAd,QAAA;MACAU,YAAA,EAAAK,KAAA,IAAAA,KAAA,CAAAC,MAAA,CAAAN,YAAA;MACAO,KAAA,EAAAF,KAAA,IAAAA,KAAA,CAAAG;IACA;EACA;EAEAC,QAAA;IACA;EAAA,CACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,OAAAC,KAAA;MACA,KAAAjB,QAAA,GAAAiB,KAAA;MACA,KAAApB,MAAA;MACA,KAAAC,IAAA;MACA,KAAAoB,SAAA;IACA;IACAC,OAAAF,KAAA;MACA,OAAAG,OAAA,aAAAH,KAAA,SAAAjB,QAAA;IACA;IACAqB,WAAA;MACA,IAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAG,UAAAC,IAAA;MACA,KAAAR,SAAA;MACA;MACA,IAAAI,MAAA;QACA;QACA;UACA,QAAAI,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA/B,IAAA;UACA,eAAA+B,IAAA,CAAAE,IAAA;UACA,OAAAF,IAAA,CAAAG,MAAA,IAAAH,IAAA,CAAAI,KAAA;UACA,OAAAJ,IAAA,CAAAK,MAAA,IAAAL,IAAA,CAAAM,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MACA,KAAAT,SAAA,CAAAC,KAAA,eAAAF,MAAA;MACA,KAAAC,SAAA,CAAAC,KAAA;IACA;IACAN,UAAA;MACA,IAAAI,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IACAW,QAAA;MACA;MACA,KAAAC,MAAA,CAAAC,MAAA;IACA;IACAC,SAAA;MACA,KAAAlB,SAAA;MACA,KAAApB,IAAA;MACA,KAAAC,KAAA;MACA,KAAAF,MAAA;IACA;IACA;IACAwC,aAAA;MACA7C,QAAA;QAAA8C,OAAA,OAAAzC,MAAA;QAAA0C,QAAA;QAAAC,OAAA;MAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA9C,IAAA,CAAA+C,IAAA;UACA,KAAA7C,IAAA,GAAA4C,GAAA,CAAA9C,IAAA,CAAAgD,KAAA,CAAAC,OAAA;UACA,KAAA9C,KAAA,GAAA2C,GAAA,CAAA9C,IAAA,CAAAgD,KAAA,CAAAC,OAAA,CAAAC,MAAA;QACA;MACA;IACA;IACA;IACAC,mBAAA;MACAtD,cAAA;QAAA6C,OAAA,OAAAzC,MAAA;QAAA0C,QAAA;QAAAC,OAAA;MAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA9C,IAAA,CAAA+C,IAAA;UACA,KAAA7C,IAAA,GAAA4C,GAAA,CAAA9C,IAAA,CAAAgD,KAAA;UACA,KAAA7C,KAAA,GAAA2C,GAAA,CAAA9C,IAAA,CAAAgD,KAAA,CAAAE,MAAA;QACA;MACA;IACA;IACA;IACAE,YAAA;MACA,SAAAhD,QAAA;QACA,KAAAqC,YAAA;MACA;QACA,KAAAU,kBAAA;MACA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}