{"ast": null, "code": "import { getDevices } from '@/api/bigScreen.js';\nexport default {\n  name: 'SmokeEquipment',\n  data() {\n    return {\n      dialogVisible: true,\n      title: '烟感设备',\n      smokeTableList: []\n    };\n  },\n  props: {\n    smokeEqInfoFlag: {\n      type: Boolean,\n      default: false\n    }\n  },\n  mounted() {\n    this.initData();\n  },\n  methods: {\n    initData() {\n      // 物联设备\n      getDevices().then(res => {\n        if (res.data.code == \"SUCCESS\") {\n          this.smokeTableList = res.data.data;\n        }\n      }).catch(err => {\n        // this.initData()\n      });\n    },\n    handleClose(done) {\n      this.dialogVisible = false;\n      this.$emit('clickSmoke', this.dialogVisible);\n    },\n    handlePosition(index, row) {\n      let latlng = {\n        lat: row.lat,\n        lng: row.lng,\n        name: row.name,\n        device_type: row.device_type,\n        type: this.title\n      };\n      this.$eventBus.$emit('senTxtToUe', latlng);\n    }\n  }\n};", "map": {"version": 3, "names": ["getDevices", "name", "data", "dialogVisible", "title", "smokeTableList", "props", "smokeEqInfoFlag", "type", "Boolean", "default", "mounted", "initData", "methods", "then", "res", "code", "catch", "err", "handleClose", "done", "$emit", "handlePosition", "index", "row", "latlng", "lat", "lng", "device_type", "$eventBus"], "sources": ["src/views/dataScreen/fileType/SmokeEquipment.vue"], "sourcesContent": ["<template>\r\n    <div class=\"smoke\">\r\n        <el-dialog :title=\"title\" :visible.sync=\"smokeEqInfoFlag\" width=\"40%\" :before-close=\"handleClose\" append-to-body>\r\n            <el-table :data=\"smokeTableList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"300\" :header-cell-style=\"{\r\n                // color: '#fff',\r\n                fontWeight: '700',\r\n\r\n            }\">\r\n                <el-table-column prop=\"name\" label=\"名称\" width=\"150\" :show-overflow-tooltip=\"true\" align=\"center\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"device_type\" label=\"设备类型\" width=\"150\" :show-overflow-tooltip=\"true\" align=\"center\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"device_brand\" label=\"设备品牌\" width=\"150\" :show-overflow-tooltip=\"true\" align=\"center\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"address\" label=\"地址\" width=\"150\" :show-overflow-tooltip=\"true\" align=\"center\">\r\n                </el-table-column>\r\n                <el-table-column fixed=\"right\" label=\"操作\" width=\"100\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button @click.native.prevent=\"handlePosition(scope.$index, scope.row)\" type=\"text\" size=\"small\">\r\n                            定位\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDevices } from '@/api/bigScreen.js'\r\n\r\nexport default {\r\n    name: 'SmokeEquipment',\r\n    data() {\r\n        return {\r\n            dialogVisible: true,\r\n            title: '烟感设备',\r\n            smokeTableList: []\r\n        }\r\n    },\r\n    props: {\r\n        smokeEqInfoFlag:{\r\n            type:Boolean,\r\n            default:false\r\n        }\r\n    },\r\n    mounted() {\r\n        this.initData()\r\n    },\r\n    methods: {\r\n        initData() {\r\n            // 物联设备\r\n            getDevices().then(res => {\r\n                if (res.data.code == \"SUCCESS\") {\r\n                    this.smokeTableList = res.data.data\r\n                }\r\n            }).catch(err => {\r\n                // this.initData()\r\n            })\r\n        },\r\n        handleClose(done) {\r\n            this.dialogVisible = false\r\n            this.$emit('clickSmoke', this.dialogVisible)\r\n        },\r\n        handlePosition(index, row){\r\n            let latlng = {\r\n                lat: row.lat,\r\n                lng: row.lng,\r\n                name:row.name,\r\n                device_type: row.device_type,\r\n                type:this.title\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', latlng)\r\n           \r\n        }\r\n    }\r\n}\r\n</script>\r\n \r\n<style lang=\"less\" scoped>\r\n/deep/ .el-dialog__header {\r\n    // padding: 5rem;\r\n}\r\n\r\n/deep/.el-input__inner {\r\n    background-color: transparent !important;\r\n    border: 1px solid #1296db;\r\n    color: #fff;\r\n}\r\n\r\n/deep/.el-table,\r\n/deep/ .el-table tr,\r\n/deep/ .el-table td,\r\n/deep/ .el-table th {\r\n    color: #fff;\r\n    background-color: transparent !important;\r\n    border: 0!important;\r\n}\r\n\r\n/deep/ .el-table__cell.is-leaf {\r\n    border: 0;\r\n\r\n}\r\n\r\n/deep/ .el-table::before {\r\n    height: 0; // 将高度修改为0\r\n}\r\n\r\n::v-deep .el-dialog {\r\n    /* background-color: transparent; */\r\n    background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n    background-size: 100% 100%;\r\n\r\n    .el-dialog__header {\r\n        padding: 3rem 4rem 0;\r\n\r\n        .el-dialog__title {\r\n            color: #fff;\r\n            font-size: 1.4rem;\r\n\r\n        }\r\n\r\n        .el-dialog__close {\r\n            color: #fff;\r\n            padding: 1.6rem 1rem 0;\r\n        }\r\n    }\r\n}\r\n\r\n.smoke {\r\n \r\n\r\n\r\n}\r\n</style>"], "mappings": "AA+BA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,KAAA;MACAC,cAAA;IACA;EACA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,SAAA;MACA;MACAZ,UAAA,GAAAc,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAb,IAAA,CAAAc,IAAA;UACA,KAAAX,cAAA,GAAAU,GAAA,CAAAb,IAAA,CAAAA,IAAA;QACA;MACA,GAAAe,KAAA,CAAAC,GAAA;QACA;MAAA,CACA;IACA;IACAC,YAAAC,IAAA;MACA,KAAAjB,aAAA;MACA,KAAAkB,KAAA,oBAAAlB,aAAA;IACA;IACAmB,eAAAC,KAAA,EAAAC,GAAA;MACA,IAAAC,MAAA;QACAC,GAAA,EAAAF,GAAA,CAAAE,GAAA;QACAC,GAAA,EAAAH,GAAA,CAAAG,GAAA;QACA1B,IAAA,EAAAuB,GAAA,CAAAvB,IAAA;QACA2B,WAAA,EAAAJ,GAAA,CAAAI,WAAA;QACApB,IAAA,OAAAJ;MACA;MACA,KAAAyB,SAAA,CAAAR,KAAA,eAAAI,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}