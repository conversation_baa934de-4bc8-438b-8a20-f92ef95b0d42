{"ast": null, "code": "import areaVideoPlay from '@/components/hlvJsVideo/hlsCreated.vue';\nexport default {\n  name: \"allVideoStream\",\n  components: {\n    areaVideoPlay\n  },\n  props: {\n    videoOption: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n  data() {\n    return {\n      list: [{\n        ape_id: \"32058368001310147287\",\n        name: \"rxsb_通达广场\"\n      }, {\n        ape_id: \"32058368521318401382\",\n        name: \"胜巷新村18#2单元\"\n      }, {\n        access_type: \"0\",\n        altitude: 0,\n        ape_id: \"32058368521318423775\",\n        cascaded_id: \"32058300002000000130\",\n        cascaded_org_index: \"32058359002160032407\",\n        create_time: 1713174637070,\n        decode_mode: \"0\",\n        dev_ape_id: \"32058368526318423775\",\n        dev_is_online: \"1\",\n        dev_name: \"胜巷新村42#2单元\",\n        dev_place: \"0.0.0.0\",\n        dev_producer: \"-1\",\n        dev_producer_name: \"\",\n        dev_resource_type: \"1\",\n        dev_sub_type: \"1\",\n        enabled: \"1\",\n        id: 44188,\n        idx: 0,\n        ip_addr: \"127.0.0.1\",\n        is_online: \"1\",\n        latitude: 31.30694,\n        longitude: 121.14031,\n        model: \"3\",\n        name: \"胜巷新村42#2单元\",\n        org_index: \"32058359002160032407\",\n        org_name: \"曹安派出所（视频综合）\",\n        original_id: \"32058300002000000130\",\n        owner_aps_id: \"32058368526318423775\",\n        place: \"0.0.0.0\",\n        place_code: \"320583\",\n        port: 1,\n        resource_code: \"49978af7f5cf431dbb14802b6af840c3\",\n        resource_type: \"6\",\n        sub_type: \"1\",\n        unique_id: 24397,\n        update_time: 1713174637000\n      }],\n      dialogVisible: true,\n      radio: '',\n      currentWin: 4,\n      //高空监控几路\n      areaTitInfo: null,\n      totalVideoList: 0,\n      videoTypeName: '',\n      winPlayer: 6,\n      //区域监控几路\n      isAction: false\n    };\n  },\n  computed: {\n    // 弹出窗的id 属性;\n    modalId() {\n      return `video-container`;\n    }\n  },\n  watch: {\n    videoOption: {\n      handler(nv, old) {\n        if (nv) {\n          this.radio = nv.name;\n          this.clickHandleItem(nv);\n        }\n      },\n      immediate: true\n    },\n    dialogVisible(nv) {\n      if (!nv) {\n        this.$parent.closeFun();\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {});\n  },\n  beforeDestroy() {},\n  methods: {\n    //   左侧菜单按钮\n    clickHandleItem(data, index) {\n      this.areaTitInfo = data;\n    },\n    // 撒点点击单个监控播放\n    getUeVideoFun(nv) {\n      if (nv != {}) {\n        let params = {\n          ...nv,\n          ape_id: nv.id\n        };\n        this.winPlayer = 1;\n        this.radio = nv.name;\n        this.areaTitInfo = params;\n      }\n    },\n    changeIsAction(val) {\n      this.isAction = val;\n    },\n    ChangeWinPlayer(val) {\n      this.winPlayer = val;\n    }\n  }\n};", "map": {"version": 3, "names": ["areaVideoPlay", "name", "components", "props", "videoOption", "type", "Object", "default", "data", "list", "ape_id", "access_type", "altitude", "cascaded_id", "cascaded_org_index", "create_time", "decode_mode", "dev_ape_id", "dev_is_online", "dev_name", "dev_place", "dev_producer", "dev_producer_name", "dev_resource_type", "dev_sub_type", "enabled", "id", "idx", "ip_addr", "is_online", "latitude", "longitude", "model", "org_index", "org_name", "original_id", "owner_aps_id", "place", "place_code", "port", "resource_code", "resource_type", "sub_type", "unique_id", "update_time", "dialogVisible", "radio", "currentWin", "areaTitInfo", "totalVideoList", "videoTypeName", "winPlayer", "isAction", "computed", "modalId", "watch", "handler", "nv", "old", "clickHandleItem", "immediate", "$parent", "closeFun", "mounted", "$nextTick", "<PERSON><PERSON><PERSON><PERSON>", "methods", "index", "getUeVideoFun", "params", "changeIsAction", "val", "ChangeWinPlayer"], "sources": ["src/components/hlvJsVideo/videoPlayerList.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <el-dialog class=\"videoDialog\" title=\"区域监控\" :visible.sync=\"dialogVisible\" width=\"55%\" append-to-body>\r\n            <div class=\"videoCon\" v-if=\"dialogVisible\">\r\n                <div class=\"leftPlaces\">\r\n                    <el-radio v-for=\"( item, index ) of  list \" :key=\"index\" v-model=\"radio\" :label=\"item.name\"\r\n                        :class=\"radio == item.name ? 'radioAction' : ''\" @input=\"clickHandleItem(item)\">\r\n                        <span>{{ item.name }}</span>\r\n                    </el-radio>\r\n\r\n                </div>\r\n                \r\n                <!-- 区域监控(华智) -->\r\n                <div class=\"areaRightVideo rightVideoShow\">\r\n                    <div class=\"numInitChange\">\r\n                        <span @click=\"ChangeWinPlayer(1)\" :class=\"{ 'selectedSpn': winPlayer == 1 }\">1路</span>\r\n                        <span @click=\"ChangeWinPlayer(4)\" :class=\"{ 'selectedSpn': winPlayer == 4 }\">4路</span>\r\n                        <span @click=\"ChangeWinPlayer(6)\" :class=\"{ 'selectedSpn': winPlayer == 6 }\">6路</span>\r\n                    </div>\r\n                    <areaVideoPlay @sendIsAction=\"changeIsAction\" :areaTitInfo=\"areaTitInfo\" :winPlayer=\"winPlayer\"\r\n                        :isAction=isAction>\r\n                    </areaVideoPlay>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nimport areaVideoPlay from '@/components/hlvJsVideo/hlsCreated.vue'\r\nexport default {\r\n    name: \"allVideoStream\",\r\n    components: {\r\n        areaVideoPlay,\r\n    },\r\n    props: {\r\n        videoOption: {\r\n            type: Object,\r\n            default: () => {\r\n                return {}\r\n            }\r\n        }\r\n\r\n    },\r\n\r\n    data() {\r\n        return {\r\n            list: [\r\n                {\r\n                    ape_id: \"32058368001310147287\",\r\n                    name: \"rxsb_通达广场\"\r\n\r\n                },\r\n                {\r\n                    ape_id: \"32058368521318401382\",\r\n                    name: \"胜巷新村18#2单元\",\r\n                },\r\n                {\r\n                    access_type: \"0\",\r\n                    altitude: 0,\r\n                    ape_id: \"32058368521318423775\",\r\n                    cascaded_id: \"32058300002000000130\",\r\n                    cascaded_org_index: \"32058359002160032407\",\r\n                    create_time: 1713174637070,\r\n                    decode_mode: \"0\",\r\n                    dev_ape_id: \"32058368526318423775\",\r\n                    dev_is_online: \"1\",\r\n                    dev_name: \"胜巷新村42#2单元\",\r\n                    dev_place: \"0.0.0.0\",\r\n                    dev_producer: \"-1\",\r\n                    dev_producer_name: \"\",\r\n                    dev_resource_type: \"1\",\r\n                    dev_sub_type: \"1\",\r\n                    enabled: \"1\",\r\n                    id: 44188,\r\n                    idx: 0,\r\n                    ip_addr: \"127.0.0.1\",\r\n                    is_online: \"1\",\r\n                    latitude: 31.30694,\r\n                    longitude: 121.14031,\r\n                    model: \"3\",\r\n                    name: \"胜巷新村42#2单元\",\r\n                    org_index: \"32058359002160032407\",\r\n                    org_name: \"曹安派出所（视频综合）\",\r\n                    original_id: \"32058300002000000130\",\r\n                    owner_aps_id: \"32058368526318423775\",\r\n                    place: \"0.0.0.0\",\r\n                    place_code: \"320583\",\r\n                    port: 1,\r\n                    resource_code: \"49978af7f5cf431dbb14802b6af840c3\",\r\n                    resource_type: \"6\",\r\n                    sub_type: \"1\",\r\n                    unique_id: 24397,\r\n                    update_time: 1713174637000,\r\n                }\r\n            ],\r\n            dialogVisible: true,\r\n            radio: '',\r\n            currentWin: 4, //高空监控几路\r\n            areaTitInfo: null,\r\n            totalVideoList: 0,\r\n            videoTypeName: '',\r\n            winPlayer: 6,//区域监控几路\r\n            isAction: false,\r\n        }\r\n    },\r\n    computed: {\r\n        // 弹出窗的id 属性;\r\n        modalId() {\r\n            return `video-container`;\r\n        }\r\n    },\r\n\r\n    watch: {\r\n        videoOption:{\r\n            \r\n            handler(nv,old){\r\n                if(nv){\r\n                    this.radio = nv.name\r\n                  this.clickHandleItem(nv)\r\n                }\r\n            },\r\n            immediate:true\r\n\r\n        },\r\n        dialogVisible(nv) {\r\n            if (!nv) {\r\n                this.$parent.closeFun()\r\n\r\n            }\r\n        }\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        //   左侧菜单按钮\r\n        clickHandleItem(data, index) {\r\n            this.areaTitInfo = data\r\n        },\r\n        // 撒点点击单个监控播放\r\n        getUeVideoFun(nv) {\r\n\r\n            if (nv != {}) {\r\n                let params = {\r\n                    ...nv,\r\n                    ape_id: nv.id,\r\n                }\r\n\r\n                this.winPlayer = 1\r\n                this.radio = nv.name\r\n                this.areaTitInfo = params\r\n            }\r\n        },\r\n\r\n        changeIsAction(val) {\r\n            this.isAction = val\r\n        },\r\n        ChangeWinPlayer(val) {\r\n            this.winPlayer = val\r\n        }\r\n\r\n    },\r\n\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n::v-deep .el-dialog {\r\n    /* background-color: transparent; */\r\n    background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n    background-size: 100% 100%;\r\n\r\n    .el-dialog__header {\r\n        padding: 3rem 4rem 0;\r\n\r\n        .el-dialog__title {\r\n            color: #fff;\r\n            font-size: 1.4rem;\r\n\r\n        }\r\n\r\n        .el-dialog__close {\r\n            color: #fff;\r\n            padding: 1.6rem 1rem 0;\r\n        }\r\n    }\r\n\r\n    .el-dialog__body {\r\n        padding-bottom: 40px;\r\n    }\r\n}\r\n\r\n\r\n\r\n.videoCon {\r\n    display: flex;\r\n    width: 100%;\r\n    position: relative;\r\n\r\n    .leftPlaces {\r\n        width: 30%;\r\n        height: 30rem;\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n        padding-left: 2rem;\r\n\r\n        ::v-deep .el-radio__input {\r\n            display: none;\r\n\r\n        }\r\n\r\n        ::v-deep .el-radio {\r\n            color: #fff;\r\n        }\r\n\r\n        ::v-deep .el-radio .el-radio__label {\r\n            font-size: 17px;\r\n        }\r\n\r\n        .el-radio {\r\n            width: 100%;\r\n            height: 3rem;\r\n            line-height: 3rem;\r\n        }\r\n\r\n        .radioAction {\r\n            background-color: rgba(27, 137, 225, 0.2);\r\n        }\r\n\r\n        .el-dropdown-link {\r\n            cursor: pointer;\r\n            color: #409EFF;\r\n        }\r\n\r\n        .el-icon-arrow-down {\r\n            font-size: 12px;\r\n        }\r\n\r\n        .demonstration {\r\n            display: block;\r\n            color: #8492a6;\r\n            font-size: 14px;\r\n            margin-bottom: 20px;\r\n        }\r\n    }\r\n\r\n    .rightVideoShow {\r\n        width: 70%;\r\n\r\n    }\r\n\r\n    .el-overlay {\r\n        margin-left: 2rem;\r\n    }\r\n\r\n    .canvas-player-list-container {\r\n        position: relative;\r\n    }\r\n\r\n    .fullOrSmall {\r\n        position: absolute;\r\n        right: 10px;\r\n        top: -16px;\r\n        z-index: 200;\r\n        cursor: pointer;\r\n        display: inline-block;\r\n        width: 20px;\r\n        height: 20px;\r\n        background-repeat: no-repeat;\r\n        background-position: center;\r\n        background-size: 20px 20px;\r\n        background-image: url('@/assets/images/video/fullscreen.png');\r\n\r\n        &.isFull {\r\n            background-image: url('@/assets/images/video/exitfull.png');\r\n        }\r\n    }\r\n\r\n    .rightVideoShow {\r\n        position: absolute;\r\n        top: 43%;\r\n        left: 35%;\r\n        transform: translateY(-50%);\r\n        width: 60%;\r\n        height: 90%;\r\n        display: flex;\r\n        justify-content: space-evenly;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .numInitChange {\r\n            position: absolute;\r\n            top: -3px;\r\n            display: block;\r\n            color: #fff;\r\n\r\n            >span {\r\n                display: inline-block;\r\n                padding: 2px 3px;\r\n                border: 1px solid #fff;\r\n                cursor: pointer;\r\n            }\r\n\r\n            .selectedSpn {\r\n                background-color: rgba(9, 171, 212, .3);\r\n            }\r\n        }\r\n\r\n        .videoList {\r\n            width: 100%;\r\n            height: 100%;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .watervideoShowMany {\r\n            width: 30%;\r\n            height: 47%;\r\n\r\n            video {\r\n                // object-fit: fill;\r\n                width: 100%;\r\n                height: 100%;\r\n\r\n            }\r\n        }\r\n\r\n\r\n\r\n        // 播放按钮\r\n        video::-webkit-media-controls-play-button {\r\n            display: none !important;\r\n        }\r\n\r\n        // 当前播放时间\r\n        video::-webkit-media-controls-current-time-display {\r\n            display: none !important;\r\n        }\r\n\r\n        // 剩余时间\r\n        video::-webkit-media-controls-time-remaining-display {\r\n            display: none !important;\r\n        }\r\n\r\n        // 音量按钮\r\n        video::-webkit-media-controls-volume-control-container {\r\n            display: none !important;\r\n        }\r\n\r\n        // // 全屏\r\n        // video::-webkit-media-controls-fullscreen-button {\r\n        //     display: none !important;\r\n        // }\r\n\r\n        // 时间轴\r\n        video::-webkit-media-controls-timeline {\r\n            display: none !important;\r\n        }\r\n\r\n\r\n\r\n    }\r\n}\r\n\r\n/* 整个滚动条 */\r\n::-webkit-scrollbar {\r\n    position: absolute;\r\n    width: 5px !important;\r\n    height: 10px !important;\r\n}\r\n\r\n/* 滚动条上的滚动滑块 */\r\n::-webkit-scrollbar-thumb {\r\n    background: rgba(27, 137, 225, 0.4);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 滚动条上的滚动滑块悬浮效果 */\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(27, 137, 225, 0.4);\r\n\r\n}\r\n\r\n/* 滚动条没有滑块的轨道部分 */\r\n::-webkit-scrollbar-track-piece {\r\n    background: rgba(27, 137, 225, 0.2);\r\n\r\n}\r\n\r\n/*滚动条上的按钮(上下箭头)  */\r\n::-webkit-scrollbar-button {\r\n    background-color: transparent;\r\n}\r\n</style>"], "mappings": "AA6BA,OAAAA,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;MACA;IACA;EAEA;EAEAC,KAAA;IACA;MACAC,IAAA,GACA;QACAC,MAAA;QACAT,IAAA;MAEA,GACA;QACAS,MAAA;QACAT,IAAA;MACA,GACA;QACAU,WAAA;QACAC,QAAA;QACAF,MAAA;QACAG,WAAA;QACAC,kBAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,aAAA;QACAC,QAAA;QACAC,SAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,YAAA;QACAC,OAAA;QACAC,EAAA;QACAC,GAAA;QACAC,OAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,KAAA;QACA/B,IAAA;QACAgC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,YAAA;QACAC,KAAA;QACAC,UAAA;QACAC,IAAA;QACAC,aAAA;QACAC,aAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;MACA,EACA;MACAC,aAAA;MACAC,KAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MACAC,cAAA;MACAC,aAAA;MACAC,SAAA;MAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,QAAA;MACA;IACA;EACA;EAEAC,KAAA;IACAnD,WAAA;MAEAoD,QAAAC,EAAA,EAAAC,GAAA;QACA,IAAAD,EAAA;UACA,KAAAX,KAAA,GAAAW,EAAA,CAAAxD,IAAA;UACA,KAAA0D,eAAA,CAAAF,EAAA;QACA;MACA;MACAG,SAAA;IAEA;IACAf,cAAAY,EAAA;MACA,KAAAA,EAAA;QACA,KAAAI,OAAA,CAAAC,QAAA;MAEA;IACA;EAEA;EAEAC,QAAA;IACA,KAAAC,SAAA,QAEA;EACA;EACAC,cAAA,GAEA;EAEAC,OAAA;IACA;IACAP,gBAAAnD,IAAA,EAAA2D,KAAA;MACA,KAAAnB,WAAA,GAAAxC,IAAA;IACA;IACA;IACA4D,cAAAX,EAAA;MAEA,IAAAA,EAAA;QACA,IAAAY,MAAA;UACA,GAAAZ,EAAA;UACA/C,MAAA,EAAA+C,EAAA,CAAA/B;QACA;QAEA,KAAAyB,SAAA;QACA,KAAAL,KAAA,GAAAW,EAAA,CAAAxD,IAAA;QACA,KAAA+C,WAAA,GAAAqB,MAAA;MACA;IACA;IAEAC,eAAAC,GAAA;MACA,KAAAnB,QAAA,GAAAmB,GAAA;IACA;IACAC,gBAAAD,GAAA;MACA,KAAApB,SAAA,GAAAoB,GAAA;IACA;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}