{"ast": null, "code": "import Home from '@/views/Home.vue';\nexport default {\n  name: 'App',\n  data() {\n    return {};\n  },\n  components: {\n    Home\n  },\n  watch: {},\n  computed: {},\n  mounted() {},\n  updated() {},\n  methods: {}\n};", "map": {"version": 3, "names": ["Home", "name", "data", "components", "watch", "computed", "mounted", "updated", "methods"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <Home />\n    <router-view />\n  </div>\n</template>\n\n<script>\n\nimport Home from '@/views/Home.vue'\nexport default {\n  name: 'App',\n  data() {\n    return {\n    };\n  },\n  components: {\n    Home,\n  },\n  watch: {\n\n  },\n  computed: {\n\n  },\n\n  mounted() {\n  },\n\n  updated() {\n\n  },\n\n  methods: {\n  },\n};\n</script>\n\n<style scoped>\n* {\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\n\n#app {\n  width: 100%;\n  height: 100vh;\n  user-select: none;\n\n}\n</style>"], "mappings": "AASA,OAAAA,IAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA,QACA;EACA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA,GACA;EAEAC,QAAA,GAEA;EAEAC,OAAA,GACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}