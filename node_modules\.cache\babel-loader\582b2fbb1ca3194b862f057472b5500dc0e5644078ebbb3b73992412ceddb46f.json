{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"videoShowItem\",\n    style: {\n      minHeight: _vm.height\n    },\n    on: {\n      click: _vm.clickVideo\n    }\n  }, [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_vm._v(_vm._s(_vm.channalName))]), _c(\"video\", {\n    ref: \"videoElement\" + _vm.id,\n    staticClass: \"hide-progress-bar\",\n    style: {\n      objectFit: _vm.isFullScreen ? \"contain\" : \"fill\"\n    },\n    attrs: {\n      id: _vm.id,\n      muted: \"\",\n      autoplay: \"\"\n    },\n    domProps: {\n      muted: true\n    },\n    on: {\n      loadedmetadata: _vm.onVideoMetadataLoaded\n    }\n  }), !_vm.isPlay ? _c(\"div\", {\n    staticClass: \"errorTxt\"\n  }, [_vm._v(\"...加载失败\")]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "minHeight", "height", "on", "click", "clickVideo", "_v", "_s", "channal<PERSON>ame", "ref", "id", "objectFit", "isFullScreen", "attrs", "muted", "autoplay", "domProps", "loadedmetadata", "onVideoMetadataLoaded", "isPlay", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/hlvJsVideo/hlsItem.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"videoShowItem\",\n      style: { minHeight: _vm.height },\n      on: { click: _vm.clickVideo },\n    },\n    [\n      _c(\"div\", { staticClass: \"title\" }, [_vm._v(_vm._s(_vm.channalName))]),\n      _c(\"video\", {\n        ref: \"videoElement\" + _vm.id,\n        staticClass: \"hide-progress-bar\",\n        style: { objectFit: _vm.isFullScreen ? \"contain\" : \"fill\" },\n        attrs: { id: _vm.id, muted: \"\", autoplay: \"\" },\n        domProps: { muted: true },\n        on: { loadedmetadata: _vm.onVideoMetadataLoaded },\n      }),\n      !_vm.isPlay\n        ? _c(\"div\", { staticClass: \"errorTxt\" }, [_vm._v(\"...加载失败\")])\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MAAEC,SAAS,EAAEL,GAAG,CAACM;IAAO,CAAC;IAChCC,EAAE,EAAE;MAAEC,KAAK,EAAER,GAAG,CAACS;IAAW;EAC9B,CAAC,EACD,CACER,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAACH,GAAG,CAACU,EAAE,CAACV,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC,EACtEX,EAAE,CAAC,OAAO,EAAE;IACVY,GAAG,EAAE,cAAc,GAAGb,GAAG,CAACc,EAAE;IAC5BX,WAAW,EAAE,mBAAmB;IAChCC,KAAK,EAAE;MAAEW,SAAS,EAAEf,GAAG,CAACgB,YAAY,GAAG,SAAS,GAAG;IAAO,CAAC;IAC3DC,KAAK,EAAE;MAAEH,EAAE,EAAEd,GAAG,CAACc,EAAE;MAAEI,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC;IAC9CC,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAK,CAAC;IACzBX,EAAE,EAAE;MAAEc,cAAc,EAAErB,GAAG,CAACsB;IAAsB;EAClD,CAAC,CAAC,EACF,CAACtB,GAAG,CAACuB,MAAM,GACPtB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CAACH,GAAG,CAACU,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAC3DV,GAAG,CAACwB,EAAE,CAAC,CAAC,CAEhB,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB1B,MAAM,CAAC2B,aAAa,GAAG,IAAI;AAE3B,SAAS3B,MAAM,EAAE0B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}