{"ast": null, "code": "import DashesPie from '@/components/comprehensive/DashesPie.vue';\nimport PopTrend from '@/components/comprehensive/PopTrend.vue';\nimport AreaSchoolMembersChart from '@/components/peopleDem/AreaSchoolMembersChart.vue';\nimport { getkeyArea } from '@/api/index.js';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      icons: [{\n        name: '花桥社区',\n        flag: false,\n        url: require('@/assets/images/comprehensiveSituation/sq.png')\n      }, {\n        name: '花桥小区',\n        flag: false,\n        url: require('@/assets/images/comprehensiveSituation/xq.png')\n      }],\n      colorPalette: {\n        name: \"常驻人口\",\n        color: \"rgba(36, 201, 255, 1)\",\n        color2: 'rgba(204,204,204,.1)',\n        bgColor: 'rgba(36, 201, 255, .2)',\n        innerRingColor: 'rgba(43, 218, 246,.3)',\n        dottedLineCOlor: 'rgba(43, 218, 246,1)'\n      },\n      colorPalette2: {\n        name: \"流动人口\",\n        color: \"rgba(255, 205, 71, 1)\",\n        color2: 'rgba(204,204,204,.1)',\n        bgColor: 'rgba(36, 201, 255, .2)',\n        innerRingColor: 'rgba(4231, 221, 105,.3)',\n        dottedLineCOlor: 'rgba(231, 221, 105,1)'\n      },\n      schoolTypes: [{\n        name: \"幼儿园\",\n        num: 24\n      }, {\n        name: \"小学\",\n        num: 9\n      }, {\n        name: \"初中\",\n        num: 3\n      }, {\n        name: \"高中及其他\",\n        num: 11\n      }],\n      schoolIndex: null,\n      iconIndex: null,\n      trafficFlag: false,\n      trafficFlag1: false\n    };\n  },\n  components: {\n    DashesPie,\n    PopTrend,\n    AreaSchoolMembersChart\n  },\n  watch: {},\n  computed: {},\n  mounted() {},\n  updated() {},\n  methods: {\n    showWeather() {\n      this.$eventBus.$emit('senTxtToUe', '天气模拟');\n    },\n    trafficTabs(name) {\n      this.trafficFlag = !this.trafficFlag;\n      if (this.trafficFlag) {\n        this.getkeyAreaList(name);\n        this.trafficFlag1 = false;\n        this.deleteFn();\n      } else {\n        this.deleteFn();\n      }\n    },\n    trafficTabs1(name) {\n      this.trafficFlag1 = !this.trafficFlag1;\n      if (this.trafficFlag1) {\n        this.getkeyAreaList(name);\n        this.trafficFlag = false;\n        this.deleteFn();\n      } else {\n        this.deleteFn();\n      }\n    },\n    getkeyAreaList(name) {\n      getkeyArea({\n        name\n      }).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra.data;\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": JSON.parse(list[0].points)\n          };\n          this.$eventBus.$emit('senTxtToUe', params); //撒点\n          this.$eventBus.$emit('senTxtToUe', list[0].angle); //聚焦到当前位置\n        }\n      });\n    },\n    ueEventFn(name, index) {\n      this.deleteFn();\n      if (this.iconIndex == index) {\n        this.iconIndex = null;\n      } else {\n        this.iconIndex = index;\n        this.getkeyAreaList(name);\n      }\n    },\n    openPop(name) {\n      this.deleteFn();\n      this.$eventBus.$emit('senTxtToUe', name); //聚焦\n    },\n    // 切换高亮并撒点\n    tabsFn(item, index) {\n      this.deleteFn();\n      if (this.schoolIndex == index) {\n        this.schoolIndex = null;\n      } else {\n        this.schoolIndex = index;\n        this.getkeyAreaList(item.name);\n      }\n    },\n    //删除点位\n    deleteFn() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    setUrl(index) {\n      return require(`@/assets/images/comprehensiveSituation/type-bg${index + 1}${this.schoolIndex == index ? '-active' : ''}.png`);\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON>es<PERSON><PERSON>", "PopTrend", "AreaSchoolMembersChart", "getkeyArea", "name", "data", "icons", "flag", "url", "require", "colorPalette", "color", "color2", "bgColor", "innerRingColor", "dottedLineCOlor", "colorPalette2", "schoolTypes", "num", "schoolIndex", "iconIndex", "trafficFlag", "trafficFlag1", "components", "watch", "computed", "mounted", "updated", "methods", "<PERSON><PERSON><PERSON><PERSON>", "$eventBus", "$emit", "trafficTabs", "getkeyAreaList", "deleteFn", "trafficTabs1", "then", "res", "code", "list", "extra", "params", "JSON", "parse", "points", "angle", "ueEventFn", "index", "openPop", "tabsFn", "item", "setUrl"], "sources": ["src/views/comprehensiveSituation/components/city-manage/left-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leftView\">\r\n        <div class=\"title-box\">\r\n            <span>城市管理</span>\r\n            <span @click=\"showWeather\">天气仿真</span>\r\n        </div>\r\n        <div class=\"sumDataNum\">\r\n            <div class=\"peopleNum\">\r\n                <div class=\"txt\">总人口<span>(万人)</span></div>\r\n                <div class=\"num\">\r\n                    <span> 37.5 </span>\r\n                </div>\r\n            </div>\r\n            <div class=\"placeArea\">\r\n                <div class=\"txt\">区域面积<span>(平方公里)</span></div>\r\n                <div class=\"num\">52.3</div>\r\n            </div>\r\n        </div>\r\n        <!-- 基础数据 -->\r\n        <div class=\"data-box\">\r\n            <div class=\"two-title\">基础数据</div>\r\n            <div class=\"icons\">\r\n                <img v-for=\"(item, index) in icons\" @click=\"ueEventFn(item.name, index)\" :title=\"item.name\"\r\n                    :src=\"item.url\" alt=\"\">\r\n                <!-- <img @click=\"ueEventFn('花桥小区', false)\" title=\"花桥小区\" src=\"@/assets/images/comprehensiveSituation/xq.png\"\r\n                    alt=\"\">-->\r\n                <img @click=\"openPop('万科魅力花园')\" title=\"万科魅力花园\" src=\"@/assets/images/comprehensiveSituation/zh.png\"\r\n                    alt=\"\">\r\n                <img @click=\"openPop('建筑衍生中骏世界城')\" title=\"建筑衍生中骏世界城\" src=\"@/assets/images/comprehensiveSituation/jz.png\"\r\n                    alt=\"\">\r\n                    <img @click=\"openPop('城市展示中心-漫游')\" title=\"城市展示中心漫游\" src=\"@/assets/images/comprehensiveSituation/my.png\"\r\n                    alt=\"\">\r\n            </div>\r\n            <div class=\"data-main\">\r\n                <div class=\"people-num\">\r\n                    <div>\r\n                        <img src=\"@/assets/images/comprehensiveSituation/man.png\" alt=\"\">\r\n                        <p>\r\n                            <span>男性占比</span>\r\n                            <span>52.67%</span>\r\n                        </p>\r\n                    </div>\r\n                    <div>\r\n                        <img src=\"@/assets/images/comprehensiveSituation/woman.png\" alt=\"\">\r\n                        <p>\r\n                            <span>女性占比</span>\r\n                            <span>47.33%</span>\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n                <div class=\"total\">总人口37.5万人</div>\r\n                <div class=\"peoplePer\">\r\n                    <img class=\"pic\" src=\"@/assets/images/left_slices/p_logo.png\" alt=\"\">\r\n                    <div class=\"per\">\r\n                        <div class=\"perInfo\">\r\n                            <div class=\"charts\">\r\n                                <DashesPie :colorPalette=\"colorPalette\"></DashesPie>\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                {{ colorPalette.name }}\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"perInfo\">\r\n                            <div class=\"charts\">\r\n                                <DashesPie :colorPalette=\"colorPalette2\"></DashesPie>\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                {{ colorPalette2.name }}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"pop_trend\">\r\n                    <PopTrend></PopTrend>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <!-- 学校资源概览 -->\r\n        <div class=\"school-box\">\r\n            <div class=\"two-title\">学校资源概览</div>\r\n            <div class=\"types\">\r\n                <p v-for=\"(item, index) in schoolTypes\" @click=\"tabsFn(item, index)\">\r\n                    <span>{{ item.num }}</span>\r\n                    <span>{{ item.name }}</span>\r\n                    <span>(所)</span>\r\n                    <img :src=\"setUrl(index)\" alt=\"\">\r\n                </p>\r\n            </div>\r\n            <div class=\"areaSchoolMembersNum\">\r\n                <AreaSchoolMembersChart></AreaSchoolMembersChart>\r\n            </div>\r\n        </div>\r\n        <!-- 智慧交通 -->\r\n        <div class=\"traffic-box\">\r\n            <div class=\"two-title\">智慧交通</div>\r\n            <div class=\"traffic-main\">\r\n                <div class=\"resourcesPer\">\r\n\r\n                    <div :class=\"trafficFlag ? 'trafficSum trafficSumActive' : 'trafficSum'\"\r\n                        @click=\"trafficTabs('停车场')\">\r\n                        <div class=\"txtSum\">\r\n                            停车场总数\r\n                        </div>\r\n                        <div class=\"num\">\r\n                            57\r\n                        </div>\r\n                        <img class=\"tl\" src=\"@/assets/images/left_slices/top_left.png\" alt=\"\">\r\n                        <img class=\"tr\" src=\"@/assets/images/left_slices/top_right.png\" alt=\"\">\r\n                        <img class=\"bl\" src=\"@/assets/images/left_slices/bottom_left.png\" alt=\"\">\r\n                        <img class=\"br\" src=\"@/assets/images/left_slices/bottom_right.png\" alt=\"\">\r\n                    </div>\r\n                    <div :class=\"trafficFlag1 ? 'per perActive' : 'per'\" @click=\"trafficTabs1('充电桩')\">\r\n                        汽车充电站\r\n                        <div class=\"perNum\">\r\n                            81\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"car\">\r\n                    <img src=\"@/assets/images/left_slices/car.png\" alt=\"\">\r\n\r\n                </div>\r\n            </div>\r\n\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport DashesPie from '@/components/comprehensive/DashesPie.vue'\r\nimport PopTrend from '@/components/comprehensive/PopTrend.vue'\r\nimport AreaSchoolMembersChart from '@/components/peopleDem/AreaSchoolMembersChart.vue'\r\nimport { getkeyArea } from '@/api/index.js'\r\nexport default {\r\n    name: 'leftView',\r\n    data() {\r\n        return {\r\n            icons: [\r\n                { name: '花桥社区', flag: false, url: require('@/assets/images/comprehensiveSituation/sq.png') },\r\n                { name: '花桥小区', flag: false, url: require('@/assets/images/comprehensiveSituation/xq.png') },\r\n            ],\r\n            colorPalette: {\r\n                name: \"常驻人口\",\r\n                color: \"rgba(36, 201, 255, 1)\",\r\n                color2: 'rgba(204,204,204,.1)',\r\n                bgColor: 'rgba(36, 201, 255, .2)',\r\n                innerRingColor: 'rgba(43, 218, 246,.3)',\r\n                dottedLineCOlor: 'rgba(43, 218, 246,1)',\r\n            },\r\n            colorPalette2: {\r\n                name: \"流动人口\",\r\n                color: \"rgba(255, 205, 71, 1)\",\r\n                color2: 'rgba(204,204,204,.1)',\r\n                bgColor: 'rgba(36, 201, 255, .2)',\r\n                innerRingColor: 'rgba(4231, 221, 105,.3)',\r\n                dottedLineCOlor: 'rgba(231, 221, 105,1)',\r\n            },\r\n            schoolTypes: [\r\n                { name: \"幼儿园\", num: 24 },\r\n                { name: \"小学\", num: 9 },\r\n                { name: \"初中\", num: 3 },\r\n                { name: \"高中及其他\", num: 11 },\r\n            ],\r\n            schoolIndex: null,\r\n            iconIndex: null,\r\n            trafficFlag: false,\r\n            trafficFlag1: false\r\n        };\r\n    },\r\n    components: {\r\n        DashesPie,\r\n        PopTrend,\r\n        AreaSchoolMembersChart\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        showWeather() {\r\n            this.$eventBus.$emit('senTxtToUe', '天气模拟');\r\n        },\r\n        trafficTabs(name) {\r\n            this.trafficFlag = !this.trafficFlag;\r\n            if (this.trafficFlag) {\r\n                this.getkeyAreaList(name);\r\n                this.trafficFlag1 = false;\r\n                this.deleteFn();\r\n            } else {\r\n                this.deleteFn();\r\n            }\r\n        },\r\n        trafficTabs1(name) {\r\n            this.trafficFlag1 = !this.trafficFlag1;\r\n            if (this.trafficFlag1) {\r\n                this.getkeyAreaList(name);\r\n                this.trafficFlag = false;\r\n                this.deleteFn();\r\n            } else {\r\n                this.deleteFn();\r\n            }\r\n        },\r\n        getkeyAreaList(name) {\r\n            getkeyArea({ name }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra.data;\r\n                    let params = {\r\n                        \"mode\": \"add\",\r\n                        \"sources\": JSON.parse(list[0].points)\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params);//撒点\r\n                    this.$eventBus.$emit('senTxtToUe', list[0].angle);//聚焦到当前位置\r\n                }\r\n            })\r\n        },\r\n        ueEventFn(name, index) {\r\n            this.deleteFn();\r\n            if (this.iconIndex == index) {\r\n                this.iconIndex = null;\r\n            } else {\r\n                this.iconIndex = index;\r\n                this.getkeyAreaList(name);\r\n            }\r\n        },\r\n        openPop(name) {\r\n            this.deleteFn();\r\n            this.$eventBus.$emit('senTxtToUe', name);//聚焦\r\n        },\r\n        // 切换高亮并撒点\r\n        tabsFn(item, index) {\r\n            this.deleteFn();\r\n            if (this.schoolIndex == index) {\r\n                this.schoolIndex = null\r\n            } else {\r\n                this.schoolIndex = index;\r\n                this.getkeyAreaList(item.name);\r\n            }\r\n        },\r\n        //删除点位\r\n        deleteFn() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        setUrl(index) {\r\n            return require(`@/assets/images/comprehensiveSituation/type-bg${index + 1}${this.schoolIndex == index ? '-active' : ''}.png`)\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n.leftView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n\r\n        >span:nth-child(2) {\r\n            cursor: pointer;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 32px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 5px 7px rgba(0, 0, 0, 0.72), 0px 5px 13px #FFBB00;\r\n            text-align: center;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            width: 197px;\r\n            height: 66px;\r\n            background-size: 100% 100%;\r\n            background-image: url(@/assets/images/comprehensiveSituation/weather-btn.png);\r\n        }\r\n    }\r\n\r\n    .sumDataNum {\r\n        display: flex;\r\n        margin-top: 11px;\r\n\r\n        .placeArea {\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/people-bg.png\");\r\n        }\r\n\r\n        .peopleNum {\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/area-bg.png\");\r\n        }\r\n\r\n        >div {\r\n            width: 312px;\r\n            height: 112px;\r\n            margin-left: 35px;\r\n            background-size: 100% 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            text-align: center;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 34px;\r\n            color: #FFFFFF;\r\n            line-height: 40px;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            padding-left: 80px;\r\n\r\n            .txt span {\r\n                font-weight: 400;\r\n                font-size: 20px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .data-box {\r\n        display: flex;\r\n        flex-direction: column;\r\n        margin-top: 22px;\r\n        padding: 0 25px;\r\n        position: relative;\r\n\r\n        .icons {\r\n            display: flex;\r\n            position: absolute;\r\n            right: 20px;\r\n            top: 15px;\r\n\r\n            >img {\r\n                cursor: pointer;\r\n                width: 30px;\r\n                height: 30px;\r\n                margin-right: 20px;\r\n            }\r\n\r\n        }\r\n\r\n        .data-main {\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .people-num {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                padding: 0 40px;\r\n\r\n                >div {\r\n                    display: flex;\r\n\r\n                    >img {\r\n                        width: 110px;\r\n                        width: 114px;\r\n                    }\r\n\r\n                    >p {\r\n                        display: flex;\r\n                        flex-direction: column;\r\n                        justify-content: space-around;\r\n                        font-family: Source Han Sans CN, Source Han Sans CN;\r\n                        font-weight: 400;\r\n                        font-size: 32px;\r\n                        color: #FFFFFF;\r\n                        line-height: 28px;\r\n                        text-align: left;\r\n                        font-style: normal;\r\n                        text-transform: none;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .total {\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                line-height: 28px;\r\n                letter-spacing: 3px;\r\n                text-align: left;\r\n                font-style: normal;\r\n                text-transform: none;\r\n                padding-left: 40px;\r\n                margin-top: 30px;\r\n            }\r\n\r\n            .peoplePer {\r\n                display: flex;\r\n                margin-top: 17px;\r\n\r\n                .pic {\r\n                    width: 207px;\r\n                    height: 199px;\r\n                    background-image: url(@/assets/images/left_slices/p_logo.png);\r\n                    background-size: 100% 100%;\r\n                }\r\n\r\n                .per {\r\n                    display: flex;\r\n                    box-sizing: border-box;\r\n\r\n                    .perInfo {\r\n                        width: 236px;\r\n                        height: 236px;\r\n                        position: relative;\r\n\r\n                        .charts {\r\n                            position: absolute;\r\n                            top: 0;\r\n                            left: 0;\r\n                            width: 300px;\r\n                            height: 300px;\r\n                        }\r\n\r\n                        .txt {\r\n                            bottom: 20px;\r\n                            left: 20px;\r\n                            position: absolute;\r\n                            font-family: Source Han Sans CN, Source Han Sans CN;\r\n                            font-weight: bold;\r\n                            font-size: 32px;\r\n                            color: #FFFFFF;\r\n                            line-height: 28px;\r\n                            letter-spacing: 3px;\r\n                            text-align: left;\r\n                            font-style: normal;\r\n                            text-transform: none;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n    }\r\n\r\n    .school-box {\r\n        margin-top: 33px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 0 25px;\r\n\r\n        .types {\r\n            margin-top: 28px;\r\n            display: flex;\r\n            height: 149px;\r\n            // background-size: 100% 100%;\r\n            // background-image: url(@/assets/images/comprehensiveSituation/types-bg.png);\r\n\r\n            >p {\r\n                display: flex;\r\n                flex-direction: column;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                color: #FFFFFF;\r\n                text-align: center;\r\n                width: 186px;\r\n                height: 149px;\r\n                padding-top: 19px;\r\n                position: relative;\r\n                cursor: pointer;\r\n\r\n                >span:nth-child(1) {\r\n                    font-size: 48px;\r\n                    line-height: 56px;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    font-size: 32px;\r\n                    line-height: 38px;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    font-size: 22px;\r\n                    line-height: 26px;\r\n                }\r\n\r\n                >img {\r\n                    width: 186px;\r\n                    height: 149px;\r\n                    position: absolute;\r\n                    left: 0;\r\n                    top: 0;\r\n                    z-index: -1;\r\n                }\r\n            }\r\n        }\r\n\r\n        .areaSchoolMembersNum {\r\n            margin-top: 42px;\r\n\r\n        }\r\n    }\r\n\r\n    .traffic-box {\r\n        padding: 0 25px;\r\n\r\n        .traffic-main {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-top: 36px;\r\n        }\r\n\r\n        .resourcesPer {\r\n            display: flex;\r\n            flex-direction: column;\r\n\r\n            .perActive {\r\n                background: rgba(255, 187, 0, 0.3) !important;\r\n            }\r\n\r\n            .per {\r\n                box-sizing: border-box;\r\n                padding-left: 1.5rem;\r\n                display: flex;\r\n                align-items: center;\r\n                width: 323px;\r\n                height: 64px;\r\n                background: rgba(76, 245, 255, .3);\r\n                border-radius: 1.28rem;\r\n                font-size: 32px;\r\n                position: relative;\r\n                margin-top: 32px;\r\n                cursor: pointer;\r\n\r\n                .perNum {\r\n                    position: absolute;\r\n                    right: -50px;\r\n                    text-align: center;\r\n                    width: 97px;\r\n                    height: 97px;\r\n                    line-height: 97px;\r\n                    background: url('@/assets/images/left_slices/circle_bg.png') no-repeat center;\r\n                    background-size: 100%;\r\n                    font-size: 32px;\r\n                }\r\n            }\r\n\r\n            .trafficSumActive {\r\n                background: rgba(255, 187, 0, 0.3) !important;\r\n            }\r\n\r\n            .trafficSum {\r\n                margin-top: 1.2rem;\r\n                position: relative;\r\n                display: flex;\r\n                justify-content: space-around;\r\n                align-items: center;\r\n                width: 311px;\r\n                height: 72px;\r\n                font-size: 32px;\r\n                background: rgba(86, 132, 188, .08);\r\n                cursor: pointer;\r\n\r\n                >img {\r\n                    position: absolute;\r\n                }\r\n\r\n                .tl {\r\n                    top: 0;\r\n                    left: 0;\r\n                }\r\n\r\n                .tr {\r\n                    top: 0;\r\n                    right: 0;\r\n                }\r\n\r\n                .bl {\r\n                    bottom: 0;\r\n                    left: 0;\r\n                }\r\n\r\n                .br {\r\n                    bottom: 0;\r\n                    right: 0;\r\n                }\r\n            }\r\n\r\n\r\n        }\r\n\r\n        .car {\r\n            margin-left: 68px;\r\n            width: 288px;\r\n            height: 166px;\r\n\r\n            img {\r\n                width: 100%;\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAkIA,OAAAA,SAAA;AACA,OAAAC,QAAA;AACA,OAAAC,sBAAA;AACA,SAAAC,UAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,KAAA,GACA;QAAAF,IAAA;QAAAG,IAAA;QAAAC,GAAA,EAAAC,OAAA;MAAA,GACA;QAAAL,IAAA;QAAAG,IAAA;QAAAC,GAAA,EAAAC,OAAA;MAAA,EACA;MACAC,YAAA;QACAN,IAAA;QACAO,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,cAAA;QACAC,eAAA;MACA;MACAC,aAAA;QACAZ,IAAA;QACAO,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,cAAA;QACAC,eAAA;MACA;MACAE,WAAA,GACA;QAAAb,IAAA;QAAAc,GAAA;MAAA,GACA;QAAAd,IAAA;QAAAc,GAAA;MAAA,GACA;QAAAd,IAAA;QAAAc,GAAA;MAAA,GACA;QAAAd,IAAA;QAAAc,GAAA;MAAA,EACA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,UAAA;IACAvB,SAAA;IACAC,QAAA;IACAC;EACA;EACAsB,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA,GAEA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,YAAA;MACA,KAAAC,SAAA,CAAAC,KAAA;IACA;IACAC,YAAA5B,IAAA;MACA,KAAAiB,WAAA,SAAAA,WAAA;MACA,SAAAA,WAAA;QACA,KAAAY,cAAA,CAAA7B,IAAA;QACA,KAAAkB,YAAA;QACA,KAAAY,QAAA;MACA;QACA,KAAAA,QAAA;MACA;IACA;IACAC,aAAA/B,IAAA;MACA,KAAAkB,YAAA,SAAAA,YAAA;MACA,SAAAA,YAAA;QACA,KAAAW,cAAA,CAAA7B,IAAA;QACA,KAAAiB,WAAA;QACA,KAAAa,QAAA;MACA;QACA,KAAAA,QAAA;MACA;IACA;IACAD,eAAA7B,IAAA;MACAD,UAAA;QAAAC;MAAA,GAAAgC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,CAAAiC,IAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAAhC,IAAA,CAAAmC,KAAA,CAAAnC,IAAA;UACA,IAAAoC,MAAA;YACA;YACA,WAAAC,IAAA,CAAAC,KAAA,CAAAJ,IAAA,IAAAK,MAAA;UACA;UACA,KAAAd,SAAA,CAAAC,KAAA,eAAAU,MAAA;UACA,KAAAX,SAAA,CAAAC,KAAA,eAAAQ,IAAA,IAAAM,KAAA;QACA;MACA;IACA;IACAC,UAAA1C,IAAA,EAAA2C,KAAA;MACA,KAAAb,QAAA;MACA,SAAAd,SAAA,IAAA2B,KAAA;QACA,KAAA3B,SAAA;MACA;QACA,KAAAA,SAAA,GAAA2B,KAAA;QACA,KAAAd,cAAA,CAAA7B,IAAA;MACA;IACA;IACA4C,QAAA5C,IAAA;MACA,KAAA8B,QAAA;MACA,KAAAJ,SAAA,CAAAC,KAAA,eAAA3B,IAAA;IACA;IACA;IACA6C,OAAAC,IAAA,EAAAH,KAAA;MACA,KAAAb,QAAA;MACA,SAAAf,WAAA,IAAA4B,KAAA;QACA,KAAA5B,WAAA;MACA;QACA,KAAAA,WAAA,GAAA4B,KAAA;QACA,KAAAd,cAAA,CAAAiB,IAAA,CAAA9C,IAAA;MACA;IACA;IACA;IACA8B,SAAA;MACA,IAAAO,MAAA;QACA;MACA;MACA,KAAAX,SAAA,CAAAC,KAAA,eAAAU,MAAA;IACA;IACAU,OAAAJ,KAAA;MACA,OAAAtC,OAAA,kDAAAsC,KAAA,YAAA5B,WAAA,IAAA4B,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}