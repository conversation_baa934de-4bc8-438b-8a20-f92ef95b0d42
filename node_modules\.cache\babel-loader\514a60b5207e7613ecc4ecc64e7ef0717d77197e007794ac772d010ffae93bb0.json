{"ast": null, "code": "import { getCimLayer } from '@/api/userMenu';\nimport { getDeviceOrg, getResourcePage, getVideoListInfo } from '@/api/hzVideo.js';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'videoPlayerTree',\n  props: {},\n  data() {\n    return {\n      dataList: [],\n      typeEquipmentAll: [],\n      defaultProps: {\n        children: 'child',\n        label: 'org_name'\n      },\n      newArr: [],\n      checkedKeys: [],\n      inputValue: '',\n      checkNodeData: null\n    };\n  },\n  watch: {\n    TypeVideoAll(nv) {\n      // console.log('树形结构===',nv)\n      if (nv.data.length > 0) {\n        let params2 = {\n          type: \"区域监控\",\n          data: nv.data,\n          flag: true\n        };\n      }\n    }\n  },\n  computed: {\n    ...mapState(\"action\", [\"TypeVideoAll\"])\n  },\n  mounted() {\n    setTimeout(() => {\n      this.initData();\n    }, 1000);\n  },\n  beforeDestroy() {\n    this.$store.commit(\"action/clearAll\", false);\n  },\n  methods: {\n    handleNodeClick(val, flag) {\n      if (flag && !val.disabled) {\n        // 选中之后我们就重新设置下选中的节点（只设置当前选中的节点，即单选）\n        this.$refs.Tree.setCheckedNodes([val]);\n      }\n    },\n    // 点击节点\n    handleCheckAll(node, val) {\n      // console.log(this.checkNodeData,'====')\n      if (val.checkedKeys.length > 0) {\n        this.checkNodeData = node;\n        this.openInfo(true);\n        let params = {\n          org_index: node.org_index\n        };\n        this.$store.dispatch('action/getTypeVideoAll', params);\n      } else {\n        this.checkNodeData = null;\n        this.openInfo(false);\n      }\n    },\n    async initData(params = {}) {\n      // await getCimLayer().then(res => {\n      //     this.dataList = res.data.extra\n      //     // 所有设备\n      //     this.typeEquipmentAll = this.dataList\n      //     let i = 1\n      //     for (var v of res.data.extra) {\n      //         v.content = v.category\n      //         v.id = i;\n      //         i++;\n      //     }\n      // })\n      getDeviceOrg(params).then(res => {\n        if (res.status == 200) {\n          res.data.data.forEach(ele => {\n            let val = this.addParams(ele.child);\n            this.getChildList(val);\n          });\n        }\n      });\n    },\n    // 递归添加参数\n    addParams(val) {\n      val.forEach(ele => {\n        if (ele.child) {\n          ele.disabled = true;\n          this.addParams(ele.child);\n        } else {\n          ele.disabled = false;\n        }\n      });\n      return val;\n    },\n    // 递归获取子集\n    getChildList(val) {\n      val.forEach(ele => {\n        if (ele.org_name == '昆山市') {\n          this.typeEquipmentAll = ele.child;\n          return;\n        } else {\n          this.getChildList(ele.child);\n        }\n      });\n    },\n    // 搜索框\n    inputChange(val) {\n      this.checkNodeData == null;\n      let params = {\n        search: val\n      };\n      this.initData(params);\n    },\n    closeTree() {\n      this.$store.commit(\"action/clearAll\", false);\n    },\n    // 打开监控\n    openVideo() {\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n    },\n    // 打开信息\n    openInfo(val) {\n      this.$store.commit(\"action/getInformationBox\", val);\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDeviceOrg", "getResourcePage", "getVideoListInfo", "mapState", "name", "props", "data", "dataList", "typeEquipmentAll", "defaultProps", "children", "label", "newArr", "checked<PERSON>eys", "inputValue", "checkNodeData", "watch", "TypeVideoAll", "nv", "length", "params2", "type", "flag", "computed", "mounted", "setTimeout", "initData", "<PERSON><PERSON><PERSON><PERSON>", "$store", "commit", "methods", "handleNodeClick", "val", "disabled", "$refs", "Tree", "setCheckedNodes", "handleCheckAll", "node", "openInfo", "params", "org_index", "dispatch", "then", "res", "status", "for<PERSON>ach", "ele", "addParams", "child", "getChildList", "org_name", "inputChange", "search", "closeTree", "openVideo"], "sources": ["src/components/videoTypeOption/videoTabsList/HZVideoPlayer.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container_box\">\r\n        <div class=\"keyWordsSearch\">\r\n            <div class=\"input\">\r\n                <el-input placeholder=\"请输入关键词搜索\" @change=\"inputChange\" v-model=\"inputValue\" :clearable=\"true\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n            </div>\r\n        </div>\r\n        <div class=\"treeShow\">\r\n            <el-tree ref=\"Tree\" :data=\"typeEquipmentAll\" show-checkbox node-key=\"org_id\"\r\n                :default-expanded-keys=\"checkedKeys\" :props=\"defaultProps\" @check-change=\"handleNodeClick\"\r\n                @check=\"handleCheckAll\">\r\n                <span slot-scope=\"{node,data}\" class=\"slotTxt\">\r\n                    <div class=\"pr10 over-ellipsis\">\r\n                        <a href=\"javascript:;\" class=\"tree-a\" :title=\"data.org_name\" v-if=\"node.level == 1\">\r\n                            {{ data.org_name }} ({{ data.child?.length ? data.child?.length : 0 }})\r\n                        </a>\r\n                        <a href=\"javascript:;\" class=\"tree-a\" :title=\"data.org_name\" v-else>\r\n                            {{ data.org_name }}\r\n\r\n                        </a>\r\n\r\n                    </div>\r\n\r\n                    <div class=\"rightIcon\" v-if=\"!node.disabled\">\r\n                        <div class=\"point\" v-if=\"checkNodeData?.org_name == data.org_name\">\r\n                            <img src=\"@/assets/images/mainPics/i_info.png\" alt=\"\" @click=\"openInfo(true)\">\r\n                        </div>\r\n                        <div class=\"poin\" v-else>\r\n                            <img src=\"@/assets/images/mainPics/i_Frame.png\" alt=\"\">\r\n\r\n                        </div>\r\n                    </div>\r\n                </span>\r\n            </el-tree>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCimLayer } from '@/api/userMenu'\r\nimport { getDeviceOrg, getResourcePage, getVideoListInfo } from '@/api/hzVideo.js'\r\nimport { mapState } from 'vuex'\r\n\r\n\r\nexport default {\r\n    name: 'videoPlayerTree',\r\n    props: {\r\n\r\n    },\r\n    data() {\r\n        return {\r\n            dataList: [],\r\n            typeEquipmentAll: [],\r\n            defaultProps: {\r\n                children: 'child',\r\n                label: 'org_name'\r\n            },\r\n            newArr: [],\r\n            checkedKeys: [],\r\n            inputValue: '',\r\n            checkNodeData: null,\r\n        };\r\n    },\r\n    watch: {\r\n        TypeVideoAll(nv) {\r\n            // console.log('树形结构===',nv)\r\n            if (nv.data.length > 0) {\r\n                let params2 = {\r\n                    type: \"区域监控\",\r\n                    data: nv.data,\r\n                    flag: true\r\n                }\r\n\r\n\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(\"action\", [\r\n            \"TypeVideoAll\"\r\n        ]),\r\n    },\r\n\r\n    mounted() {\r\n        setTimeout(() => {\r\n            this.initData()\r\n\r\n        }, 1000)\r\n\r\n    },\r\n    beforeDestroy() {\r\n        this.$store.commit(\"action/clearAll\", false)\r\n\r\n    },\r\n\r\n    methods: {\r\n\r\n        handleNodeClick(val, flag) {\r\n            if (flag && !val.disabled) {  // 选中之后我们就重新设置下选中的节点（只设置当前选中的节点，即单选）\r\n                this.$refs.Tree.setCheckedNodes([val])\r\n            }\r\n\r\n        },\r\n        // 点击节点\r\n        handleCheckAll(node, val) {\r\n            // console.log(this.checkNodeData,'====')\r\n            if (val.checkedKeys.length > 0) {\r\n                this.checkNodeData = node\r\n                this.openInfo(true)\r\n                let params = {\r\n                    org_index: node.org_index\r\n                }\r\n                this.$store.dispatch('action/getTypeVideoAll', params)\r\n\r\n\r\n\r\n            } else {\r\n                this.checkNodeData = null\r\n                this.openInfo(false)\r\n            }\r\n\r\n        },\r\n\r\n        async initData(params = {}) {\r\n            // await getCimLayer().then(res => {\r\n            //     this.dataList = res.data.extra\r\n            //     // 所有设备\r\n            //     this.typeEquipmentAll = this.dataList\r\n            //     let i = 1\r\n            //     for (var v of res.data.extra) {\r\n            //         v.content = v.category\r\n            //         v.id = i;\r\n            //         i++;\r\n            //     }\r\n            // })\r\n            getDeviceOrg(params).then(res => {\r\n                if (res.status == 200) {\r\n                    res.data.data.forEach(ele => {\r\n                        let val = this.addParams(ele.child)\r\n                        this.getChildList(val)\r\n                    });\r\n                }\r\n            })\r\n        },\r\n        // 递归添加参数\r\n        addParams(val) {\r\n            val.forEach(ele => {\r\n                if (ele.child) {\r\n                    ele.disabled = true\r\n                    this.addParams(ele.child)\r\n\r\n                } else {\r\n                    ele.disabled = false\r\n                }\r\n            })\r\n            return val\r\n        },\r\n        // 递归获取子集\r\n        getChildList(val) {\r\n            val.forEach(ele => {\r\n                if (ele.org_name == '昆山市') {\r\n                    this.typeEquipmentAll = ele.child\r\n                    return;\r\n\r\n                } else {\r\n                    this.getChildList(ele.child)\r\n                }\r\n            })\r\n        },\r\n        // 搜索框\r\n        inputChange(val) {\r\n            this.checkNodeData == null\r\n            let params = {\r\n                search: val\r\n            }\r\n            this.initData(params)\r\n        },\r\n\r\n        closeTree() {\r\n            this.$store.commit(\"action/clearAll\", false)\r\n\r\n        },\r\n        // 打开监控\r\n        openVideo() {\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n        },\r\n        // 打开信息\r\n        openInfo(val) {\r\n            this.$store.commit(\"action/getInformationBox\", val)\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.container_box {\r\n    // background: url('@/assets/images/mainPics/bgicon6.png') no-repeat center;\r\n    // background-size: 100% 100%;\r\n\r\n\r\n    .treeShow {\r\n        width: 100%;\r\n        height: 70vh;\r\n        overflow: auto;\r\n        padding: 0 10px;\r\n        box-sizing: border-box;\r\n\r\n        .slotTxt {\r\n            display: flex\r\n        }\r\n\r\n        .rightIcon {\r\n            position: absolute;\r\n            right: 20px;\r\n        }\r\n\r\n        .over-ellipsis {\r\n            display: block;\r\n            width: 140PX;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            -webkit-line-clamp: 1;\r\n\r\n            .tree-a {\r\n                color: #fff;\r\n                text-decoration: none;\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n\r\n\r\n\r\n\r\n    .keyWordsSearch {\r\n        margin: 10px 0;\r\n        padding: 0 20px;\r\n\r\n        ::v-deep .el-input {\r\n            .el-input__suffix {\r\n                right: 40px;\r\n            }\r\n\r\n            .el-input__prefix {\r\n                right: 5px;\r\n                left: auto;\r\n                padding: 0 10px;\r\n                font-size: 18px;\r\n                cursor: pointer;\r\n                font-weight: bold;\r\n            }\r\n\r\n            .el-input__inner {\r\n                background-color: transparent;\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n\r\n    ::v-deep .el-tree {\r\n        background: transparent;\r\n        color: #fff;\r\n\r\n        .el-checkbox__input.is-disabled {\r\n            display: none;\r\n        }\r\n\r\n        .el-tree-node.is-focusable {\r\n            margin-top: .6em;\r\n        }\r\n\r\n        .el-checkbox__inner {\r\n            background-color: rgba(0, 0, 0, 0) !important;\r\n        }\r\n\r\n        img {\r\n            margin-right: 5px;\r\n        }\r\n\r\n        .el-tree-node:focus>.el-tree-node__content {\r\n            background: linear-gradient(160deg, rgba(0, 163, 255, 0.25) 22%, rgba(0, 142, 221, 0.2) 57%, rgba(0, 133, 255, 0.26) 100%);\r\n            border-radius: 6px 6px 6px 6px;\r\n            border: 1px solid;\r\n            border-image: linear-gradient(160deg, rgba(0, 178, 255, 1), rgba(0, 142, 221, 0.77), rgba(0, 133, 255, 0.26)) 1 1;\r\n        }\r\n\r\n        >.el-tree-node>.el-tree-node__content {\r\n            position: relative;\r\n            width: 99%;\r\n            height: 2rem;\r\n            box-sizing: border-box;\r\n\r\n            &:hover {\r\n                background: linear-gradient(160deg, rgba(0, 163, 255, 0.25) 22%, rgba(0, 142, 221, 0.2) 57%, rgba(0, 133, 255, 0.26) 100%);\r\n                border-radius: 6px 6px 6px 6px;\r\n                border: 1px solid;\r\n                border-image: linear-gradient(160deg, rgba(0, 178, 255, 1), rgba(0, 142, 221, 0.77), rgba(0, 133, 255, 0.26)) 1 1;\r\n            }\r\n\r\n\r\n            >.el-icon-caret-right {\r\n                // position: absolute;\r\n                // right: 45px;\r\n                font-size: 16px;\r\n                color: #fff;\r\n            }\r\n\r\n            .el-icon-caret-right:before {\r\n                content: \"\\e6df\";\r\n            }\r\n\r\n            .el-tree-node__label {\r\n                font-size: .9rem;\r\n                font-weight: bold;\r\n            }\r\n        }\r\n\r\n        .el-tree-node__children .el-tree-node__content {\r\n            background: rgba(255, 255, 255, 0);\r\n\r\n            .el-tree-node__label {\r\n                font-size: .8rem;\r\n                font-weight: 400;\r\n            }\r\n\r\n        }\r\n\r\n\r\n\r\n\r\n    }\r\n}\r\n\r\n/* 整个滚动条 */\r\n::-webkit-scrollbar {\r\n    position: absolute;\r\n    width: 5px !important;\r\n    height: 10px !important;\r\n}\r\n\r\n/* 滚动条上的滚动滑块 */\r\n::-webkit-scrollbar-thumb {\r\n    background: rgba(27, 137, 225, 0.4);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 滚动条上的滚动滑块悬浮效果 */\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(27, 137, 225, 0.4);\r\n\r\n}\r\n\r\n/* 滚动条没有滑块的轨道部分 */\r\n::-webkit-scrollbar-track-piece {\r\n    background-color: rgba(255, 255, 255, .3);\r\n}\r\n\r\n/*滚动条上的按钮(上下箭头)  */\r\n::-webkit-scrollbar-button {\r\n    background-color: transparent;\r\n}\r\n</style>"], "mappings": "AAyCA,SAAAA,WAAA;AACA,SAAAC,YAAA,EAAAC,eAAA,EAAAC,gBAAA;AACA,SAAAC,QAAA;AAGA;EACAC,IAAA;EACAC,KAAA,GAEA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,gBAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,MAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAC,aAAAC,EAAA;MACA;MACA,IAAAA,EAAA,CAAAZ,IAAA,CAAAa,MAAA;QACA,IAAAC,OAAA;UACAC,IAAA;UACAf,IAAA,EAAAY,EAAA,CAAAZ,IAAA;UACAgB,IAAA;QACA;MAGA;IACA;EACA;EACAC,QAAA;IACA,GAAApB,QAAA,YACA,eACA;EACA;EAEAqB,QAAA;IACAC,UAAA;MACA,KAAAC,QAAA;IAEA;EAEA;EACAC,cAAA;IACA,KAAAC,MAAA,CAAAC,MAAA;EAEA;EAEAC,OAAA;IAEAC,gBAAAC,GAAA,EAAAV,IAAA;MACA,IAAAA,IAAA,KAAAU,GAAA,CAAAC,QAAA;QAAA;QACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,eAAA,EAAAJ,GAAA;MACA;IAEA;IACA;IACAK,eAAAC,IAAA,EAAAN,GAAA;MACA;MACA,IAAAA,GAAA,CAAAnB,WAAA,CAAAM,MAAA;QACA,KAAAJ,aAAA,GAAAuB,IAAA;QACA,KAAAC,QAAA;QACA,IAAAC,MAAA;UACAC,SAAA,EAAAH,IAAA,CAAAG;QACA;QACA,KAAAb,MAAA,CAAAc,QAAA,2BAAAF,MAAA;MAIA;QACA,KAAAzB,aAAA;QACA,KAAAwB,QAAA;MACA;IAEA;IAEA,MAAAb,SAAAc,MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAxC,YAAA,CAAAwC,MAAA,EAAAG,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACAD,GAAA,CAAAtC,IAAA,CAAAA,IAAA,CAAAwC,OAAA,CAAAC,GAAA;YACA,IAAAf,GAAA,QAAAgB,SAAA,CAAAD,GAAA,CAAAE,KAAA;YACA,KAAAC,YAAA,CAAAlB,GAAA;UACA;QACA;MACA;IACA;IACA;IACAgB,UAAAhB,GAAA;MACAA,GAAA,CAAAc,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAE,KAAA;UACAF,GAAA,CAAAd,QAAA;UACA,KAAAe,SAAA,CAAAD,GAAA,CAAAE,KAAA;QAEA;UACAF,GAAA,CAAAd,QAAA;QACA;MACA;MACA,OAAAD,GAAA;IACA;IACA;IACAkB,aAAAlB,GAAA;MACAA,GAAA,CAAAc,OAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAI,QAAA;UACA,KAAA3C,gBAAA,GAAAuC,GAAA,CAAAE,KAAA;UACA;QAEA;UACA,KAAAC,YAAA,CAAAH,GAAA,CAAAE,KAAA;QACA;MACA;IACA;IACA;IACAG,YAAApB,GAAA;MACA,KAAAjB,aAAA;MACA,IAAAyB,MAAA;QACAa,MAAA,EAAArB;MACA;MACA,KAAAN,QAAA,CAAAc,MAAA;IACA;IAEAc,UAAA;MACA,KAAA1B,MAAA,CAAAC,MAAA;IAEA;IACA;IACA0B,UAAA;MACA,KAAA3B,MAAA,CAAAC,MAAA;IACA;IACA;IACAU,SAAAP,GAAA;MACA,KAAAJ,MAAA,CAAAC,MAAA,6BAAAG,GAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}