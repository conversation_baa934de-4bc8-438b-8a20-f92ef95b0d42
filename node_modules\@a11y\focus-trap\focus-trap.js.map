{"version": 3, "file": "focus-trap.js", "sourceRoot": "", "sources": ["../src/lib/focus-trap.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAU3C;;GAEG;AACH,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACpD,QAAQ,CAAC,SAAS,GAAG;;;;;CAKpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,SAAU,SAAQ,WAAW;IAuCzC;;OAEG;IACH;QACC,KAAK,EAAE,CAAC;QAnBT,qFAAqF;QAC7E,eAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;QAKtC,aAAQ,GAAG,KAAK,CAAC;QAexB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,aAAa,CAAc,SAAS,CAAE,CAAC;QAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAc,QAAQ,CAAE,CAAC;QAC3D,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,aAAa,CAAc,MAAM,CAAE,CAAC;QAEvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAtDD,iFAAiF;IACjF,MAAM,KAAK,kBAAkB;QAC5B,OAAO;YACN,UAAU;SACV,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,QAAQ,CAAE,KAAc;QAC3B,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IAC9E,CAAC;IAaD;;OAEG;IACH,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACtB,CAAC;IAqBD;;OAEG;IACH,iBAAiB;QAChB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE5D,yEAAyE;QACzE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,EAAE,CAAC;IACf,CAAC;IAGD;;OAEG;IACH,oBAAoB;QACnB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/D,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACvB,IAAI,CAAC,MAAM,EAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB;QAChB,IAAI,CAAC,SAAS,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,oBAAoB;QACnB,OAAO,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACO,SAAS,CAAE,SAAmB;QACvC,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE1B,IAAI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACpD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,IAAI,SAAS,EAAE;gBACd,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aACxD;iBAAM;gBACN,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aAC7B;YAED,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACN,oEAAoE;YACpE,oEAAoE;YACpE,iEAAiE;YACjE,iEAAiE;YACjE,iBAAiB;YACjB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACrB;IACF,CAAC;IAGD;;OAEG;IACK,SAAS;QAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,UAAU;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACK,aAAa,CAAE,KAAc;QACpC,QAAQ,CAAC,GAAG,EAAE;YACb,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;gBAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,MAAM,EAAE,CAAC;aACd;QACF,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACO,MAAM;QACf,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClF,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;CACD;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC"}