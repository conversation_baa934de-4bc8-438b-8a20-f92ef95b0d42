{"ast": null, "code": "import request from '@/utils/api/serviceApi.js';\nexport function getPipeNetlnfo() {\n  return request.get('/waterFour/app/big/pipeNetInfo');\n}\n// 污水液位\nexport function getSewageLevel() {\n  return request.get('/waterFour/largeScreen/sewageLevel');\n}\n// 河道液位\nexport function getRiverLevel() {\n  return request.get('/waterFour/largeScreen/riverLevel');\n}\n// 河长公示牌\nexport function getRowList() {\n  return request.get('/waterFour/largeScreen/rowList');\n}\n// 水质监测\nexport function getWaterMonitor() {\n  return request.get('/waterFour/largeScreen/waterQuality3T');\n}\n// 水质情况\nexport function getWaterQuality() {\n  return request.get('/waterFour/app/big/waterQuality');\n}\n// 达标区域流量监测\nexport function waterSewagePipeMonitoring() {\n  return request.get('/waterFour/largeScreen/waterSewagePipeMonitoring');\n}", "map": {"version": 3, "names": ["request", "getPipeNetlnfo", "get", "getSewageLevel", "getRiverLevel", "getRowList", "getWaterMonitor", "getWaterQuality", "waterSewagePipeMonitoring"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/waterApi.js"], "sourcesContent": ["import request from '@/utils/api/serviceApi.js'\r\nexport function getPipeNetlnfo() {\r\n    return request.get('/waterFour/app/big/pipeNetInfo')\r\n}\r\n// 污水液位\r\nexport function getSewageLevel() {\r\n    return request.get('/waterFour/largeScreen/sewageLevel')\r\n}\r\n// 河道液位\r\nexport function getRiverLevel() {\r\n    return request.get('/waterFour/largeScreen/riverLevel')\r\n}\r\n// 河长公示牌\r\nexport function getRowList() {\r\n    return request.get('/waterFour/largeScreen/rowList')\r\n}\r\n// 水质监测\r\nexport function getWaterMonitor() {\r\n    return request.get('/waterFour/largeScreen/waterQuality3T')\r\n}\r\n// 水质情况\r\nexport function getWaterQuality() {\r\n    return request.get('/waterFour/app/big/waterQuality')\r\n}\r\n// 达标区域流量监测\r\nexport function waterSewagePipeMonitoring() {\r\n    return request.get('/waterFour/largeScreen/waterSewagePipeMonitoring')\r\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,2BAA2B;AAC/C,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC7B,OAAOD,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;AACxD;AACA;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC7B,OAAOH,OAAO,CAACE,GAAG,CAAC,oCAAoC,CAAC;AAC5D;AACA;AACA,OAAO,SAASE,aAAaA,CAAA,EAAG;EAC5B,OAAOJ,OAAO,CAACE,GAAG,CAAC,mCAAmC,CAAC;AAC3D;AACA;AACA,OAAO,SAASG,UAAUA,CAAA,EAAG;EACzB,OAAOL,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;AACxD;AACA;AACA,OAAO,SAASI,eAAeA,CAAA,EAAG;EAC9B,OAAON,OAAO,CAACE,GAAG,CAAC,uCAAuC,CAAC;AAC/D;AACA;AACA,OAAO,SAASK,eAAeA,CAAA,EAAG;EAC9B,OAAOP,OAAO,CAACE,GAAG,CAAC,iCAAiC,CAAC;AACzD;AACA;AACA,OAAO,SAASM,yBAAyBA,CAAA,EAAG;EACxC,OAAOR,OAAO,CAACE,GAAG,CAAC,kDAAkD,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}