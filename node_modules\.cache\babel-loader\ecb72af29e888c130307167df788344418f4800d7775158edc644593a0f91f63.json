{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getHotTopTypes10 } from \"@/api/userMenu.js\";\nexport default {\n  name: 'dealComNumChart',\n  data() {\n    return {\n      schoolChart: '',\n      uid: null,\n      data: [],\n      dataList: {\n        id: 1,\n        style: {\n          width: '100%',\n          height: '500px'\n        },\n        yData: [],\n        data: []\n      }\n    };\n  },\n  beforeDestroy() {\n    if (this.schoolChart) {\n      this.schoolChart.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.uid = Math.floor(Math.random() * 100);\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.schoolChart != null && this.schoolChart != \"\" && this.schoolChart != undefined) {\n          this.schoolChart.dispose();\n        }\n        this.schoolChart = echarts.init(document.getElementById(this.uid));\n        this.schoolChart.clear();\n        getHotTopTypes10({\n          pageNum: 1,\n          pageSize: 10\n        }).then(res => {\n          let list = res.data.rows;\n          list.forEach(item => {\n            this.dataList.yData.push(item.name);\n            this.dataList.data.push(item.count);\n          });\n          let max = Math.max(...this.dataList.data);\n          for (let i of this.dataList.data) {\n            this.data.push(max);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let color = ['rgba(4, 153, 222,1)', 'rgba(10, 222, 236, 1)'];\n      let option = {\n        title: {\n          show: false\n        },\n        tooltip: {\n          trigger: 'false'\n        },\n        grid: {\n          left: '20%',\n          right: '20%',\n          bottom: '0%',\n          top: '5%'\n        },\n        xAxis: {\n          type: 'value',\n          // max: max,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: false\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: this.dataList.yData,\n          inverse: true,\n          axisLabel: {\n            align: 'left',\n            margin: 100,\n            color: \"rgba(150, 203, 238, 1)\",\n            fontSize: 23\n          },\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            show: false\n          }\n        },\n        series: [{\n          type: 'bar',\n          data: this.dataList.data,\n          barWidth: '30%',\n          itemStyle: {\n            color: (value, i) => {\n              if (value.dataIndex % 2 == 0) {\n                return color[0];\n              } else {\n                return color[1];\n              }\n            },\n            borderRadius: [20]\n          },\n          label: {},\n          label: {\n            show: true,\n            color: 'inherit',\n            position: \"right\",\n            fontSize: 23,\n            offset: [10, 0]\n          }\n        }]\n      };\n      if (option && typeof option === 'object') {\n        this.schoolChart.setOption(option);\n      }\n      window.onresize = this.schoolChart.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getHotTopTypes10", "name", "data", "schoolChart", "uid", "dataList", "id", "style", "width", "height", "yData", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "Math", "floor", "random", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "document", "getElementById", "pageNum", "pageSize", "res", "list", "rows", "for<PERSON>ach", "item", "push", "count", "max", "i", "setOption", "color", "option", "title", "show", "tooltip", "trigger", "grid", "left", "right", "bottom", "top", "xAxis", "type", "axisTick", "axisLine", "splitLine", "yAxis", "inverse", "axisLabel", "align", "margin", "fontSize", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "value", "dataIndex", "borderRadius", "label", "position", "offset", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/HotAppealType.vue"], "sourcesContent": ["<template>\r\n    <div :id=\"uid\" ref=\"schoolChart\" :style=\"dataList.style\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { getHotTopTypes10 } from \"@/api/userMenu.js\"\r\nexport default {\r\n    name: 'dealComNumChart',\r\n    data() {\r\n        return {\r\n            schoolChart: '',\r\n            uid: null,\r\n            data: [],\r\n            dataList: {\r\n                id: 1,\r\n                style: {\r\n                    width: '100%',\r\n                    height: '500px',\r\n                },\r\n                yData: [],\r\n                data: []\r\n\r\n            },\r\n\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.schoolChart) {\r\n            this.schoolChart.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.uid = Math.floor(Math.random() * 100);\r\n            this.myChart()\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.schoolChart != null && this.schoolChart != \"\" && this.schoolChart != undefined) {\r\n                    this.schoolChart.dispose();\r\n                }\r\n                this.schoolChart = echarts.init(document.getElementById(this.uid));\r\n                this.schoolChart.clear()\r\n                getHotTopTypes10({ pageNum: 1, pageSize: 10 }).then(res => {\r\n                    let list = res.data.rows;\r\n                    list.forEach(item => {\r\n                        this.dataList.yData.push(item.name)\r\n                        this.dataList.data.push(item.count)\r\n                    })\r\n                    let max = Math.max(...this.dataList.data);\r\n                    for (let i of this.dataList.data) {\r\n                        this.data.push(max)\r\n                    }\r\n                    this.setOption()\r\n                })\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n            let color = ['rgba(4, 153, 222,1)', 'rgba(10, 222, 236, 1)']\r\n\r\n            let option = {\r\n                title: {\r\n                    show: false\r\n                },\r\n                tooltip: {\r\n                    trigger: 'false',\r\n                },\r\n\r\n                grid: {\r\n                    left: '20%',\r\n                    right: '20%',\r\n                    bottom: '0%',\r\n                    top: '5%',\r\n                },\r\n\r\n                xAxis: {\r\n                    type: 'value',\r\n                    // max: max,\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n                    },\r\n\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                },\r\n                yAxis: {\r\n                    type: 'category',\r\n                    data: this.dataList.yData,\r\n                    inverse: true,\r\n                    axisLabel: {\r\n                        align: 'left',\r\n                        margin: 100,\r\n                        color: \"rgba(150, 203, 238, 1)\",\r\n                        fontSize: 23,\r\n\r\n                    },\r\n\r\n\r\n                    axisTick: {\r\n                        show: false,\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n\r\n                    },\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                },\r\n                series: [\r\n                    {\r\n                        type: 'bar',\r\n                        data: this.dataList.data,\r\n                        barWidth: '30%',\r\n                        itemStyle: {\r\n                            color: (value, i) => {\r\n                                if (value.dataIndex % 2 == 0) {\r\n                                    return color[0]\r\n                                } else {\r\n                                    return color[1]\r\n                                }\r\n                            },\r\n                            borderRadius: [20],\r\n                        },\r\n                        label: {\r\n                        },\r\n                        label: {\r\n                            show: true,\r\n                            color: 'inherit',\r\n                            position: \"right\",\r\n                            fontSize: 23,\r\n                            offset: [10, 0]\r\n                        },\r\n                    },\r\n                ]\r\n            };\r\n\r\n\r\n            if (option && typeof option === 'object') {\r\n                this.schoolChart.setOption(option);\r\n            }\r\n            window.onresize = this.schoolChart.resize;\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped></style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,gBAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,GAAA;MACAF,IAAA;MACAG,QAAA;QACAC,EAAA;QACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;QACAR,IAAA;MAEA;IAEA;EACA;EACAS,cAAA;IACA,SAAAR,WAAA;MACA,KAAAA,WAAA,CAAAS,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAV,GAAA,GAAAW,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA,KAAAC,OAAA;IAEA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAnB,WAAA,iBAAAA,WAAA,eAAAA,WAAA,IAAAoB,SAAA;UACA,KAAApB,WAAA,CAAAqB,OAAA;QACA;QACA,KAAArB,WAAA,GAAAJ,OAAA,CAAA0B,IAAA,CAAAC,QAAA,CAAAC,cAAA,MAAAvB,GAAA;QACA,KAAAD,WAAA,CAAAS,KAAA;QACAZ,gBAAA;UAAA4B,OAAA;UAAAC,QAAA;QAAA,GAAAP,IAAA,CAAAQ,GAAA;UACA,IAAAC,IAAA,GAAAD,GAAA,CAAA5B,IAAA,CAAA8B,IAAA;UACAD,IAAA,CAAAE,OAAA,CAAAC,IAAA;YACA,KAAA7B,QAAA,CAAAK,KAAA,CAAAyB,IAAA,CAAAD,IAAA,CAAAjC,IAAA;YACA,KAAAI,QAAA,CAAAH,IAAA,CAAAiC,IAAA,CAAAD,IAAA,CAAAE,KAAA;UACA;UACA,IAAAC,GAAA,GAAAtB,IAAA,CAAAsB,GAAA,SAAAhC,QAAA,CAAAH,IAAA;UACA,SAAAoC,CAAA,SAAAjC,QAAA,CAAAH,IAAA;YACA,KAAAA,IAAA,CAAAiC,IAAA,CAAAE,GAAA;UACA;UACA,KAAAE,SAAA;QACA;MAGA;IACA;IACAA,UAAA;MACA,IAAAC,KAAA;MAEA,IAAAC,MAAA;QACAC,KAAA;UACAC,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;QACA;QAEAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;QACA;QAEAC,KAAA;UACAC,IAAA;UACA;UACAC,QAAA;YACAV,IAAA;UACA;UACAW,QAAA;YACAX,IAAA;UACA;UAEAY,SAAA;YACAZ,IAAA;UACA;QACA;QACAa,KAAA;UACAJ,IAAA;UACAlD,IAAA,OAAAG,QAAA,CAAAK,KAAA;UACA+C,OAAA;UACAC,SAAA;YACAC,KAAA;YACAC,MAAA;YACApB,KAAA;YACAqB,QAAA;UAEA;UAGAR,QAAA;YACAV,IAAA;UACA;UACAW,QAAA;YACAX,IAAA;UAEA;UACAY,SAAA;YACAZ,IAAA;UACA;QACA;QACAmB,MAAA,GACA;UACAV,IAAA;UACAlD,IAAA,OAAAG,QAAA,CAAAH,IAAA;UACA6D,QAAA;UACAC,SAAA;YACAxB,KAAA,EAAAA,CAAAyB,KAAA,EAAA3B,CAAA;cACA,IAAA2B,KAAA,CAAAC,SAAA;gBACA,OAAA1B,KAAA;cACA;gBACA,OAAAA,KAAA;cACA;YACA;YACA2B,YAAA;UACA;UACAC,KAAA,GACA;UACAA,KAAA;YACAzB,IAAA;YACAH,KAAA;YACA6B,QAAA;YACAR,QAAA;YACAS,MAAA;UACA;QACA;MAEA;MAGA,IAAA7B,MAAA,WAAAA,MAAA;QACA,KAAAtC,WAAA,CAAAoC,SAAA,CAAAE,MAAA;MACA;MACA8B,MAAA,CAAAC,QAAA,QAAArE,WAAA,CAAAsE,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}