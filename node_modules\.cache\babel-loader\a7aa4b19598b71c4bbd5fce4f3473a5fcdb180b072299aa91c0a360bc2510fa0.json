{"ast": null, "code": "import { mapState } from 'vuex';\nimport { getkeyArea } from '@/api/index.js';\nimport Recorder from 'js-audio-recorder';\nexport default {\n  name: 'voiceChat',\n  data() {\n    return {\n      voiceInfo: [{\n        type: 1,\n        text: \"我是您的专属顾问，很高兴为您服务\"\n      }],\n      //type 1：用户文字 2：ai文字 3：语音条信息\n      isShow: false,\n      //聊天面板展示隐藏\n      inpVal: '',\n      toggle: false,\n      voiceWS: null,\n      recorder: null,\n      //录音对象\n      reconnectAttempts: 0,\n      reconnectInterval: 1000,\n      voiceFlag: false,\n      //语音条状态\n      recordingFlag: false\n    };\n  },\n  components: {},\n  watch: {},\n  computed: {\n    ...mapState('action', [\"childrenBtn\"])\n  },\n  mounted() {\n    // 临时禁用语音聊天WebSocket连接，避免连接失败错误\n    // this.connectVoiceWebSocket();\n  },\n  updated() {},\n  methods: {\n    getkeyAreaList(name) {\n      getkeyArea({\n        name\n      }).then(res => {\n        if (res.data.code == 200) {\n          let list = res.data.extra.data;\n          let params = {\n            \"mode\": \"add\",\n            \"sources\": JSON.parse(list[0].points)\n          };\n          this.$eventBus.$emit('senTxtToUe', params); //撒点\n          this.$eventBus.$emit('senTxtToUe', list[0].angle); //聚焦到当前位置\n        }\n      });\n    },\n    // 监听音频播放完毕\n    onAudioEnded() {\n      this.voiceInfo.forEach(item => {\n        if (item.type == 3) {\n          item.flag = false;\n        }\n      });\n    },\n    // 语音对话面板展示隐藏\n    isShowVoiceChat() {\n      this.isShow = !this.isShow;\n      this.recorder = null;\n      this.voiceInfo = [{\n        type: 1,\n        text: \"我是您的专属顾问，很高兴为您服务\"\n      }];\n    },\n    // 将 PCM 数据转换为 Int16Array\n    trimAudioByVolume(pcmData, threshold) {\n      const samples = new Int16Array(pcmData.buffer);\n      let startIndex = -1;\n      let endIndex = -1;\n      // 遍历 PCM 数据，找到音量大于阈值的部分\n      for (let i = 0; i < samples.length; i++) {\n        const amplitude = Math.abs(samples[i]); // 取绝对值表示音量\n        if (amplitude > threshold) {\n          if (startIndex === -1) {\n            startIndex = i; // 记录起始位置\n          }\n          endIndex = i; // 更新结束位置\n        }\n      }\n\n      // 如果没有找到符合条件的部分，返回空\n      if (startIndex === -1 || endIndex === -1) {\n        console.warn('未找到音量大于阈值的部分');\n        return new Int16Array(0);\n      }\n\n      // 截取有效部分\n      const trimmedSamples = samples.slice(startIndex, endIndex + 1);\n      return new Int16Array(trimmedSamples);\n    },\n    // 开始录音\n    startRecording() {\n      this.recorder = new Recorder({\n        sampleRate: 16000,\n        // 采样率\n        numChannels: 1,\n        // 声道数\n        bitRate: 16 // 比特率\n      });\n      this.recorder.start();\n      this.recordingFlag = true;\n    },\n    // 结束录音\n    stopRecording() {\n      this.recorder.stop();\n      // console.log('录音结束');\n      const trimmedData = this.trimAudioByVolume(this.recorder.getPCM(), 30);\n      this.voiceWS.send(trimmedData);\n      this.recordingFlag = false;\n    },\n    // 将滚动条移动到最底部\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.$refs.scrollContainer.scrollTop = this.$refs.scrollContainer.scrollHeight;\n      });\n    },\n    //删除点位\n    deleteFn() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    voiceOnmessage(data) {\n      let that = this;\n      that.deleteFn();\n      that.voiceInfo.push({\n        type: 2,\n        text: data.question\n      });\n      if (data.type == 1) {\n        //语音介绍\n        if (data.question && data.answer) {\n          that.voiceInfo.push({\n            type: 1,\n            text: data.answer\n          });\n          that.scrollToBottom();\n        } else if (data.question && data.keyword) {\n          setTimeout(() => {\n            that.voiceInfo.push({\n              type: 1,\n              text: \"好的\"\n            });\n            that.voiceInfo.push({\n              type: 1,\n              text: data.command\n            });\n          }, 1000);\n          that.createAudioIntroduce(data.answerUrl, data.keyword);\n        }\n      } else if (data.type == 2) {\n        //展示撒点\n        setTimeout(() => {\n          that.voiceInfo.push({\n            type: 1,\n            text: data.answer == '没有匹配到关键词' ? data.answer : \"好的\"\n          });\n        }, 1000);\n        let params = {\n          \"mode\": \"add\",\n          \"sources\": JSON.parse(data.points)\n        };\n        this.$eventBus.$emit('senTxtToUe', params); //撒点\n      } else if (data.type == 4) {\n        //面板跳转\n        setTimeout(() => {\n          that.voiceInfo.push({\n            type: 1,\n            text: data.answer == '没有匹配到关键词' ? data.answer : \"好的\"\n          });\n        }, 1000);\n        let pathName = data.parent == '综合态势' ? 'ComprehensiveSituation' : data.parent == '民生诉求' ? 'PeopleDemands' : 'SmartWater';\n        this.$route.path.indexOf(pathName) == -1 ? this.$router.push({\n          name: pathName\n        }) : '';\n        this.$eventBus.$emit(\"senTxtToUe\", data.parent); //场景聚焦\n        this.$store.commit('action/getChangeScreenValue', data.parent); //一级菜单高亮\n        this.childrenBtn[data.parent].forEach((item, index) => {\n          if (item == data.panel) {\n            this.$store.commit('action/getBtnIndex', index); //二级菜单高亮\n          }\n        });\n        this.$store.commit('setFooterTbsItem', {\n          item: data.panel\n        });\n        this.createAudioIntroduce(`/show/upload/${data.panel}看板.MP3,`, data.keyword);\n      }\n    },\n    //生成音频介绍\n    createAudioIntroduce(url, keyword) {\n      let that = this;\n      setTimeout(() => {\n        that.onAudioEnded(); //清除语音条状态\n        that.voiceInfo.push({\n          type: 3,\n          answerUrl: url,\n          flag: true\n        });\n        that.$nextTick(() => {\n          //播放最后一条语音\n          const audio = document.querySelectorAll('#myAudio');\n          audio.forEach(item => item.pause());\n          audio[audio.length - 1].play();\n          that.$eventBus.$emit('senTxtToUe', keyword); //聚焦到当前位置\n          that.scrollToBottom();\n        });\n      }, 2000);\n    },\n    // 建立连接并发送语音字节流\n    connectVoiceWebSocket() {\n      let that = this;\n      let num = Math.floor(Math.random() * 1000000) + 1;\n      // 使用环境变量配置WebSocket地址，如果服务器不可用会自动重连\n      const wsUrl = `${process.env.VUE_APP_UE_URL}:81/ws/voicepd/${num}`;\n      console.log('尝试连接WebSocket:', wsUrl);\n      this.voiceWS = new WebSocket(wsUrl);\n      //voiceWS = new WebSocket(\"ws://192.168.1.39:9198/ws/voice/\" + num);\n      this.voiceWS.binaryType = 'arraybuffer'; //传输的是 ArrayBuffer 类型的数据\n      this.voiceWS.onopen = function () {\n        console.log('握手成功');\n      };\n      this.voiceWS.onmessage = function (msg) {\n        let data = JSON.parse(msg.data);\n        that.voiceOnmessage(data);\n        console.log(data, 'data');\n      };\n      this.voiceWS.onerror = function (err) {\n        console.error(err);\n        that.reconnect();\n      };\n      this.voiceWS.onclose = function (msg) {\n        console.info(msg);\n        that.reconnect();\n      };\n    },\n    // websocket重连\n    reconnect() {\n      let that = this;\n      if (that.reconnectAttempts === undefined) {\n        that.reconnectAttempts = 0;\n      }\n      if (that.reconnectInterval === undefined) {\n        that.reconnectInterval = 1000; // 初始重连间隔为1秒\n      }\n      if (that.reconnectAttempts < 5) {\n        // 设置最大重连次数为5次\n        setTimeout(function () {\n          that.reconnectAttempts++;\n          console.log(`尝试重连第 ${that.reconnectAttempts} 次`);\n          that.connectVoiceWebSocket(); // 重新调用连接函数\n        }, that.reconnectInterval);\n\n        // 指数退避机制，重连间隔逐步增加\n        that.reconnectInterval *= 2;\n        if (that.reconnectInterval > 32000) {\n          // 最大重连间隔设置为32秒\n          that.reconnectInterval = 32000;\n        }\n      } else {\n        console.error('重连次数已达上限，不再重连');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "getkeyArea", "Recorder", "name", "data", "voiceInfo", "type", "text", "isShow", "inpVal", "toggle", "voiceWS", "recorder", "reconnectAttempts", "reconnectInterval", "voiceFlag", "recordingFlag", "components", "watch", "computed", "mounted", "updated", "methods", "getkeyAreaList", "then", "res", "code", "list", "extra", "params", "JSON", "parse", "points", "$eventBus", "$emit", "angle", "onAudioEnded", "for<PERSON>ach", "item", "flag", "isShowVoiceChat", "trimAudioByVolume", "pcmData", "threshold", "samples", "Int16Array", "buffer", "startIndex", "endIndex", "i", "length", "amplitude", "Math", "abs", "console", "warn", "trimmedSamples", "slice", "startRecording", "sampleRate", "numChannels", "bitRate", "start", "stopRecording", "stop", "trimmedData", "getPCM", "send", "scrollToBottom", "$nextTick", "$refs", "scrollContainer", "scrollTop", "scrollHeight", "deleteFn", "voiceOnmessage", "that", "push", "question", "answer", "keyword", "setTimeout", "command", "createAudioIntroduce", "answerUrl", "pathName", "parent", "$route", "path", "indexOf", "$router", "$store", "commit", "childrenBtn", "index", "panel", "url", "audio", "document", "querySelectorAll", "pause", "play", "connectVoiceWebSocket", "num", "floor", "random", "wsUrl", "process", "env", "VUE_APP_UE_URL", "log", "WebSocket", "binaryType", "onopen", "onmessage", "msg", "onerror", "err", "error", "reconnect", "onclose", "info", "undefined"], "sources": ["src/components/VoiceChat/index.vue"], "sourcesContent": ["<template>\r\n\r\n    <!-- <v-scale-screen width=\"3840\" height=\"2160\" :fullScreen=\"true\"> -->\r\n    <div class=\"voiceChat\" ref=\"voice_page\">\r\n\r\n        <img @click=\"isShowVoiceChat\" src=\"@/assets/images/voice_chat/voice_icon.png\" alt=\"\" class=\"voiceIcon\">\r\n        <div class=\"voiceFrame\" v-show=\"isShow\">\r\n            <div class=\"text-main\" ref=\"scrollContainer\">\r\n                <div class=\"text-box\" v-for=\"item in voiceInfo\">\r\n                    <p class=\"aitext\" v-if=\"item.type == 1\">\r\n                        <img src=\"@/assets/images/voice_chat/avatar2.png\" alt=\"\">\r\n                        <span>{{ item.text }}</span>\r\n                    </p>\r\n                    <p class=\"myText\" v-if=\"item.type == 2\">\r\n                        <img src=\"@/assets/images/voice_chat/avatar1.png\" alt=\"\">\r\n                        <span>{{ item.text }}</span>\r\n                    </p>\r\n                    <p v-if=\"item.type == 3\">\r\n                        <img src=\"@/assets/images/voice_chat/avatar2.png\" alt=\"\">\r\n                        <span class=\"voiceBar\">\r\n                            <img v-if=\"item.flag\" src=\"@/assets/images/voice_chat/voice-bg2.gif\" alt=\"\">\r\n                            <img v-else src=\"@/assets/images/voice_chat/voice-bg1.png\" alt=\"\">\r\n                        </span>\r\n                        <audio id=\"myAudio\" :src=\"item.answerUrl\" @ended=\"onAudioEnded\"></audio>\r\n                    </p>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"sendInfo\">\r\n                <p>\r\n                    <img src=\"@/assets/images/voice_chat/voice_icon1.png\" />\r\n                    <!-- <img src=\"@/assets/images/voice_chat/voice_icon2.png\" /> -->\r\n                </p>\r\n                <p @mousedown=\"startRecording\" @mouseup=\"stopRecording\">\r\n                    <!-- <el-input v-if=\"toggle\" v-model=\"inpVal\"></el-input> -->\r\n\r\n                    <span class=\"up\" v-if=\"recordingFlag\">识别中...</span>\r\n                    <span class=\"down\" v-else>按住说话</span>\r\n                </p>\r\n                <p>发送</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- </v-scale-screen> -->\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { getkeyArea } from '@/api/index.js'\r\nimport Recorder from 'js-audio-recorder';\r\nexport default {\r\n    name: 'voiceChat',\r\n    data() {\r\n        return {\r\n            voiceInfo: [{ type: 1, text: \"我是您的专属顾问，很高兴为您服务\" }],//type 1：用户文字 2：ai文字 3：语音条信息\r\n            isShow: false,//聊天面板展示隐藏\r\n            inpVal: '',\r\n            toggle: false,\r\n            voiceWS: null,\r\n            recorder: null,//录音对象\r\n            reconnectAttempts: 0,\r\n            reconnectInterval: 1000,\r\n            voiceFlag: false,//语音条状态\r\n            recordingFlag: false\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n        ...mapState('action', [\"childrenBtn\"]),\r\n    },\r\n\r\n    mounted() {\r\n        // 临时禁用语音聊天WebSocket连接，避免连接失败错误\r\n        // this.connectVoiceWebSocket();\r\n\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        getkeyAreaList(name) {\r\n            getkeyArea({ name }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    let list = res.data.extra.data;\r\n                    let params = {\r\n                        \"mode\": \"add\",\r\n                        \"sources\": JSON.parse(list[0].points)\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params);//撒点\r\n                    this.$eventBus.$emit('senTxtToUe', list[0].angle);//聚焦到当前位置\r\n                }\r\n            })\r\n        },\r\n        // 监听音频播放完毕\r\n        onAudioEnded() {\r\n            this.voiceInfo.forEach(item => {\r\n                if (item.type == 3) {\r\n                    item.flag = false;\r\n                }\r\n            })\r\n        },\r\n        // 语音对话面板展示隐藏\r\n        isShowVoiceChat() {\r\n            this.isShow = !this.isShow;\r\n            this.recorder = null;\r\n            this.voiceInfo = [{ type: 1, text: \"我是您的专属顾问，很高兴为您服务\" }];\r\n        },\r\n        // 将 PCM 数据转换为 Int16Array\r\n        trimAudioByVolume(pcmData, threshold) {\r\n            const samples = new Int16Array(pcmData.buffer);\r\n            let startIndex = -1;\r\n            let endIndex = -1;\r\n            // 遍历 PCM 数据，找到音量大于阈值的部分\r\n            for (let i = 0; i < samples.length; i++) {\r\n                const amplitude = Math.abs(samples[i]); // 取绝对值表示音量\r\n                if (amplitude > threshold) {\r\n                    if (startIndex === -1) {\r\n                        startIndex = i; // 记录起始位置\r\n                    }\r\n                    endIndex = i; // 更新结束位置\r\n                }\r\n            }\r\n\r\n            // 如果没有找到符合条件的部分，返回空\r\n            if (startIndex === -1 || endIndex === -1) {\r\n                console.warn('未找到音量大于阈值的部分');\r\n                return new Int16Array(0);\r\n            }\r\n\r\n            // 截取有效部分\r\n            const trimmedSamples = samples.slice(startIndex, endIndex + 1);\r\n            return new Int16Array(trimmedSamples);\r\n        },\r\n        // 开始录音\r\n        startRecording() {\r\n            this.recorder = new Recorder({\r\n                sampleRate: 16000, // 采样率\r\n                numChannels: 1,    // 声道数\r\n                bitRate: 16,      // 比特率\r\n            });\r\n            this.recorder.start();\r\n            this.recordingFlag = true;\r\n        },\r\n        // 结束录音\r\n        stopRecording() {\r\n            this.recorder.stop();\r\n            // console.log('录音结束');\r\n            const trimmedData = this.trimAudioByVolume(this.recorder.getPCM(), 30);\r\n            this.voiceWS.send(trimmedData);\r\n            this.recordingFlag = false;\r\n        },\r\n        // 将滚动条移动到最底部\r\n        scrollToBottom() {\r\n            this.$nextTick(() => {\r\n                this.$refs.scrollContainer.scrollTop = this.$refs.scrollContainer.scrollHeight;\r\n            })\r\n        },\r\n        //删除点位\r\n        deleteFn() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        voiceOnmessage(data) {\r\n            let that = this;\r\n            that.deleteFn();\r\n            that.voiceInfo.push({ type: 2, text: data.question })\r\n            if (data.type == 1) {//语音介绍\r\n                if (data.question && data.answer) {\r\n                    that.voiceInfo.push({ type: 1, text: data.answer })\r\n                    that.scrollToBottom();\r\n                }\r\n                else if (data.question && data.keyword) {\r\n                    setTimeout(() => {\r\n                        that.voiceInfo.push({ type: 1, text: \"好的\" })\r\n                        that.voiceInfo.push({ type: 1, text: data.command })\r\n                    }, 1000)\r\n                    that.createAudioIntroduce(data.answerUrl, data.keyword);\r\n                }\r\n            } else if (data.type == 2) {//展示撒点\r\n                setTimeout(() => {\r\n                    that.voiceInfo.push({ type: 1, text: data.answer == '没有匹配到关键词' ? data.answer : \"好的\" })\r\n                }, 1000)\r\n                let params = {\r\n                    \"mode\": \"add\",\r\n                    \"sources\": JSON.parse(data.points)\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params);//撒点\r\n            } else if (data.type == 4) {//面板跳转\r\n                setTimeout(() => {\r\n                    that.voiceInfo.push({ type: 1, text: data.answer == '没有匹配到关键词' ? data.answer : \"好的\" })\r\n                }, 1000)\r\n                let pathName = data.parent == '综合态势' ? 'ComprehensiveSituation' : data.parent == '民生诉求' ? 'PeopleDemands' : 'SmartWater';\r\n                this.$route.path.indexOf(pathName) == -1 ? this.$router.push({\r\n                    name: pathName\r\n                }) : '';\r\n                this.$eventBus.$emit(\"senTxtToUe\", data.parent)//场景聚焦\r\n                this.$store.commit('action/getChangeScreenValue', data.parent)//一级菜单高亮\r\n                this.childrenBtn[data.parent].forEach((item, index) => {\r\n                    if (item == data.panel) {\r\n                        this.$store.commit('action/getBtnIndex', index)//二级菜单高亮\r\n                    }\r\n                })\r\n                this.$store.commit('setFooterTbsItem', { item: data.panel })\r\n                this.createAudioIntroduce(`/show/upload/${data.panel}看板.MP3,`, data.keyword);\r\n            }\r\n        },\r\n        //生成音频介绍\r\n        createAudioIntroduce(url, keyword) {\r\n            let that = this;\r\n            setTimeout(() => {\r\n                that.onAudioEnded();//清除语音条状态\r\n                that.voiceInfo.push({ type: 3, answerUrl: url, flag: true })\r\n                that.$nextTick(() => {//播放最后一条语音\r\n                    const audio = document.querySelectorAll('#myAudio')\r\n                    audio.forEach(item => item.pause());\r\n                    audio[audio.length - 1].play();\r\n                    that.$eventBus.$emit('senTxtToUe', keyword);//聚焦到当前位置\r\n                    that.scrollToBottom();\r\n                })\r\n            }, 2000)\r\n        },\r\n        // 建立连接并发送语音字节流\r\n        connectVoiceWebSocket() {\r\n            let that = this;\r\n            let num = Math.floor(Math.random() * 1000000) + 1;\r\n            // 使用环境变量配置WebSocket地址，如果服务器不可用会自动重连\r\n            const wsUrl = `${process.env.VUE_APP_UE_URL}:81/ws/voicepd/${num}`;\r\n            console.log('尝试连接WebSocket:', wsUrl);\r\n            this.voiceWS = new WebSocket(wsUrl);\r\n            //voiceWS = new WebSocket(\"ws://192.168.1.39:9198/ws/voice/\" + num);\r\n            this.voiceWS.binaryType = 'arraybuffer'; //传输的是 ArrayBuffer 类型的数据\r\n            this.voiceWS.onopen = function () {\r\n                console.log('握手成功');\r\n            };\r\n            this.voiceWS.onmessage = function (msg) {\r\n                let data = JSON.parse(msg.data)\r\n                that.voiceOnmessage(data);\r\n                console.log(data, 'data')\r\n\r\n            };\r\n\r\n            this.voiceWS.onerror = function (err) {\r\n                console.error(err);\r\n                that.reconnect();\r\n            };\r\n\r\n            this.voiceWS.onclose = function (msg) {\r\n                console.info(msg);\r\n                that.reconnect();\r\n\r\n            };\r\n\r\n        },\r\n        // websocket重连\r\n        reconnect() {\r\n            let that = this;\r\n            if (that.reconnectAttempts === undefined) {\r\n                that.reconnectAttempts = 0;\r\n            }\r\n            if (that.reconnectInterval === undefined) {\r\n                that.reconnectInterval = 1000; // 初始重连间隔为1秒\r\n            }\r\n\r\n            if (that.reconnectAttempts < 5) { // 设置最大重连次数为5次\r\n                setTimeout(function () {\r\n                    that.reconnectAttempts++;\r\n                    console.log(`尝试重连第 ${that.reconnectAttempts} 次`);\r\n                    that.connectVoiceWebSocket(); // 重新调用连接函数\r\n                }, that.reconnectInterval);\r\n\r\n                // 指数退避机制，重连间隔逐步增加\r\n                that.reconnectInterval *= 2;\r\n                if (that.reconnectInterval > 32000) { // 最大重连间隔设置为32秒\r\n                    that.reconnectInterval = 32000;\r\n                }\r\n            } else {\r\n                console.error('重连次数已达上限，不再重连');\r\n            }\r\n        },\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.screen-box {\r\n    pointer-events: none !important;\r\n    background-color: transparent !important;\r\n}\r\n\r\n.voiceChat {\r\n    position: absolute;\r\n    pointer-events: stroke;\r\n    z-index: 99999 !important;\r\n    // transform: scale(0.5, 0.445833);\r\n    // width: 3840px;\r\n    // height: 2160px;\r\n\r\n}\r\n\r\n.voiceIcon {\r\n    position: fixed;\r\n    cursor: pointer;\r\n    width: 138px;\r\n    height: 138px;\r\n    right: 0px;\r\n    bottom: 0px;\r\n    pointer-events: stroke;\r\n    transform: scale(0.5, 0.445833);\r\n}\r\n\r\n.voiceFrame {\r\n    position: fixed;\r\n    width: 750px;\r\n    height: 1341px;\r\n    right: -120px;\r\n    bottom: -200px;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/voice_chat/voice_frame.png);\r\n    padding: 40px 28px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    pointer-events: stroke;\r\n    transform: scale(0.5, 0.445833);\r\n\r\n    .text-main {\r\n        width: 100%;\r\n        height: 1130px;\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n    }\r\n\r\n    .text-box>p {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 22px;\r\n\r\n        >img {\r\n            width: 79px;\r\n            height: 98px;\r\n        }\r\n\r\n        >span {\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 34px;\r\n            color: #FFFFFF;\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n            line-height: 67px;\r\n            background-size: 100% 100%;\r\n            padding: 20px;\r\n        }\r\n    }\r\n\r\n    .voiceBar {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        min-width: 222px;\r\n        line-height: 67px;\r\n        padding: 20px;\r\n        background-size: 100% 100%;\r\n        background-image: url(@/assets/images/voice_chat/aiText.png);\r\n\r\n        >img {\r\n            width: 196px;\r\n            height: 50px;\r\n        }\r\n    }\r\n\r\n    .aitext {\r\n        flex-direction: row;\r\n        position: relative;\r\n\r\n        >span {\r\n            margin-left: 15px;\r\n            background: rgba(255, 255, 255, 0.31);\r\n            // background-image: url(@/assets/images/voice_chat/aiText.png);\r\n        }\r\n\r\n        >span::before {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: 30px;\r\n            /* Adjust this value to move the triangle vertically */\r\n            left: 79px;\r\n            /* Adjust this value to move the triangle horizontally */\r\n            border-top: 15px solid transparent;\r\n            border-right: 15px solid rgba(255, 255, 255, 0.31);\r\n            border-bottom: 15px solid transparent;\r\n            width: 0;\r\n            height: 0;\r\n        }\r\n    }\r\n\r\n    .myText {\r\n        flex-direction: row-reverse;\r\n\r\n        >span {\r\n            background-image: url(@/assets/images/voice_chat/myText.png);\r\n        }\r\n    }\r\n\r\n    .sendInfo {\r\n        display: flex;\r\n        position: absolute;\r\n        bottom: 40px;\r\n        left: 50px;\r\n        align-items: center;\r\n\r\n        >p:nth-child(1) {\r\n            width: 79px;\r\n            height: 67px;\r\n            background: rgba(54, 168, 255, 0.65);\r\n            border-radius: 5px 5px 5px 5px;\r\n            text-align: center;\r\n\r\n            >img {\r\n                width: 47px;\r\n                height: 47px;\r\n                margin-top: 10px;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n            width: 462px;\r\n            height: 67px;\r\n            margin: 0 8px;\r\n\r\n            span {\r\n                display: inline-block;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 30px;\r\n                color: #FFFFFF;\r\n                line-height: 67px;\r\n                text-align: center;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n\r\n        .down {\r\n            width: 462px;\r\n            height: 67px;\r\n            background: rgba(0, 0, 0, 0.2) rgba(0, 134, 243, 0.27);\r\n            box-shadow: inset 0px 0px 11px 0px #36A8FF;\r\n            border-radius: 5px 5px 5px 5px;\r\n\r\n        }\r\n\r\n        .up {\r\n            width: 462px;\r\n            height: 67px;\r\n            background: rgba(236, 223, 127, 0.27);\r\n            box-shadow: inset 0px 0px 11px 0px #E0F2FF;\r\n            border-radius: 5px 5px 5px 5px;\r\n        }\r\n\r\n        >p:nth-child(3) {\r\n            width: 115px;\r\n            height: 67px;\r\n            background: rgba(54, 168, 255, 0.65);\r\n            border-radius: 5px 5px 5px 5px;\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 34px;\r\n            color: #FFFFFF;\r\n            line-height: 67px;\r\n            text-align: center;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAkDA,SAAAA,QAAA;AACA,SAAAC,UAAA;AACA,OAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;QAAAC,IAAA;QAAAC,IAAA;MAAA;MAAA;MACAC,MAAA;MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,OAAA;MACAC,QAAA;MAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,SAAA;MAAA;MACAC,aAAA;IACA;EACA;EACAC,UAAA,GACA;EACAC,KAAA,GAEA;EACAC,QAAA;IACA,GAAAnB,QAAA;EACA;EAEAoB,QAAA;IACA;IACA;EAAA,CAEA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,eAAApB,IAAA;MACAF,UAAA;QAAAE;MAAA,GAAAqB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArB,IAAA,CAAAsB,IAAA;UACA,IAAAC,IAAA,GAAAF,GAAA,CAAArB,IAAA,CAAAwB,KAAA,CAAAxB,IAAA;UACA,IAAAyB,MAAA;YACA;YACA,WAAAC,IAAA,CAAAC,KAAA,CAAAJ,IAAA,IAAAK,MAAA;UACA;UACA,KAAAC,SAAA,CAAAC,KAAA,eAAAL,MAAA;UACA,KAAAI,SAAA,CAAAC,KAAA,eAAAP,IAAA,IAAAQ,KAAA;QACA;MACA;IACA;IACA;IACAC,aAAA;MACA,KAAA/B,SAAA,CAAAgC,OAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,CAAAhC,IAAA;UACAgC,IAAA,CAAAC,IAAA;QACA;MACA;IACA;IACA;IACAC,gBAAA;MACA,KAAAhC,MAAA,SAAAA,MAAA;MACA,KAAAI,QAAA;MACA,KAAAP,SAAA;QAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAkC,kBAAAC,OAAA,EAAAC,SAAA;MACA,MAAAC,OAAA,OAAAC,UAAA,CAAAH,OAAA,CAAAI,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,QAAA;MACA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,OAAA,CAAAM,MAAA,EAAAD,CAAA;QACA,MAAAE,SAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAT,OAAA,CAAAK,CAAA;QACA,IAAAE,SAAA,GAAAR,SAAA;UACA,IAAAI,UAAA;YACAA,UAAA,GAAAE,CAAA;UACA;UACAD,QAAA,GAAAC,CAAA;QACA;MACA;;MAEA;MACA,IAAAF,UAAA,WAAAC,QAAA;QACAM,OAAA,CAAAC,IAAA;QACA,WAAAV,UAAA;MACA;;MAEA;MACA,MAAAW,cAAA,GAAAZ,OAAA,CAAAa,KAAA,CAAAV,UAAA,EAAAC,QAAA;MACA,WAAAH,UAAA,CAAAW,cAAA;IACA;IACA;IACAE,eAAA;MACA,KAAA9C,QAAA,OAAAV,QAAA;QACAyD,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,OAAA;MACA;MACA,KAAAjD,QAAA,CAAAkD,KAAA;MACA,KAAA9C,aAAA;IACA;IACA;IACA+C,cAAA;MACA,KAAAnD,QAAA,CAAAoD,IAAA;MACA;MACA,MAAAC,WAAA,QAAAxB,iBAAA,MAAA7B,QAAA,CAAAsD,MAAA;MACA,KAAAvD,OAAA,CAAAwD,IAAA,CAAAF,WAAA;MACA,KAAAjD,aAAA;IACA;IACA;IACAoD,eAAA;MACA,KAAAC,SAAA;QACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,SAAA,QAAAF,KAAA,CAAAC,eAAA,CAAAE,YAAA;MACA;IACA;IACA;IACAC,SAAA;MACA,IAAA7C,MAAA;QACA;MACA;MACA,KAAAI,SAAA,CAAAC,KAAA,eAAAL,MAAA;IACA;IACA8C,eAAAvE,IAAA;MACA,IAAAwE,IAAA;MACAA,IAAA,CAAAF,QAAA;MACAE,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;QAAAvE,IAAA;QAAAC,IAAA,EAAAH,IAAA,CAAA0E;MAAA;MACA,IAAA1E,IAAA,CAAAE,IAAA;QAAA;QACA,IAAAF,IAAA,CAAA0E,QAAA,IAAA1E,IAAA,CAAA2E,MAAA;UACAH,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;YAAAvE,IAAA;YAAAC,IAAA,EAAAH,IAAA,CAAA2E;UAAA;UACAH,IAAA,CAAAR,cAAA;QACA,OACA,IAAAhE,IAAA,CAAA0E,QAAA,IAAA1E,IAAA,CAAA4E,OAAA;UACAC,UAAA;YACAL,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;cAAAvE,IAAA;cAAAC,IAAA;YAAA;YACAqE,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;cAAAvE,IAAA;cAAAC,IAAA,EAAAH,IAAA,CAAA8E;YAAA;UACA;UACAN,IAAA,CAAAO,oBAAA,CAAA/E,IAAA,CAAAgF,SAAA,EAAAhF,IAAA,CAAA4E,OAAA;QACA;MACA,WAAA5E,IAAA,CAAAE,IAAA;QAAA;QACA2E,UAAA;UACAL,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;YAAAvE,IAAA;YAAAC,IAAA,EAAAH,IAAA,CAAA2E,MAAA,iBAAA3E,IAAA,CAAA2E,MAAA;UAAA;QACA;QACA,IAAAlD,MAAA;UACA;UACA,WAAAC,IAAA,CAAAC,KAAA,CAAA3B,IAAA,CAAA4B,MAAA;QACA;QACA,KAAAC,SAAA,CAAAC,KAAA,eAAAL,MAAA;MACA,WAAAzB,IAAA,CAAAE,IAAA;QAAA;QACA2E,UAAA;UACAL,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;YAAAvE,IAAA;YAAAC,IAAA,EAAAH,IAAA,CAAA2E,MAAA,iBAAA3E,IAAA,CAAA2E,MAAA;UAAA;QACA;QACA,IAAAM,QAAA,GAAAjF,IAAA,CAAAkF,MAAA,wCAAAlF,IAAA,CAAAkF,MAAA;QACA,KAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAJ,QAAA,eAAAK,OAAA,CAAAb,IAAA;UACA1E,IAAA,EAAAkF;QACA;QACA,KAAApD,SAAA,CAAAC,KAAA,eAAA9B,IAAA,CAAAkF,MAAA;QACA,KAAAK,MAAA,CAAAC,MAAA,gCAAAxF,IAAA,CAAAkF,MAAA;QACA,KAAAO,WAAA,CAAAzF,IAAA,CAAAkF,MAAA,EAAAjD,OAAA,EAAAC,IAAA,EAAAwD,KAAA;UACA,IAAAxD,IAAA,IAAAlC,IAAA,CAAA2F,KAAA;YACA,KAAAJ,MAAA,CAAAC,MAAA,uBAAAE,KAAA;UACA;QACA;QACA,KAAAH,MAAA,CAAAC,MAAA;UAAAtD,IAAA,EAAAlC,IAAA,CAAA2F;QAAA;QACA,KAAAZ,oBAAA,iBAAA/E,IAAA,CAAA2F,KAAA,WAAA3F,IAAA,CAAA4E,OAAA;MACA;IACA;IACA;IACAG,qBAAAa,GAAA,EAAAhB,OAAA;MACA,IAAAJ,IAAA;MACAK,UAAA;QACAL,IAAA,CAAAxC,YAAA;QACAwC,IAAA,CAAAvE,SAAA,CAAAwE,IAAA;UAAAvE,IAAA;UAAA8E,SAAA,EAAAY,GAAA;UAAAzD,IAAA;QAAA;QACAqC,IAAA,CAAAP,SAAA;UAAA;UACA,MAAA4B,KAAA,GAAAC,QAAA,CAAAC,gBAAA;UACAF,KAAA,CAAA5D,OAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA8D,KAAA;UACAH,KAAA,CAAAA,KAAA,CAAA/C,MAAA,MAAAmD,IAAA;UACAzB,IAAA,CAAA3C,SAAA,CAAAC,KAAA,eAAA8C,OAAA;UACAJ,IAAA,CAAAR,cAAA;QACA;MACA;IACA;IACA;IACAkC,sBAAA;MACA,IAAA1B,IAAA;MACA,IAAA2B,GAAA,GAAAnD,IAAA,CAAAoD,KAAA,CAAApD,IAAA,CAAAqD,MAAA;MACA;MACA,MAAAC,KAAA,MAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,kBAAAN,GAAA;MACAjD,OAAA,CAAAwD,GAAA,mBAAAJ,KAAA;MACA,KAAA/F,OAAA,OAAAoG,SAAA,CAAAL,KAAA;MACA;MACA,KAAA/F,OAAA,CAAAqG,UAAA;MACA,KAAArG,OAAA,CAAAsG,MAAA;QACA3D,OAAA,CAAAwD,GAAA;MACA;MACA,KAAAnG,OAAA,CAAAuG,SAAA,aAAAC,GAAA;QACA,IAAA/G,IAAA,GAAA0B,IAAA,CAAAC,KAAA,CAAAoF,GAAA,CAAA/G,IAAA;QACAwE,IAAA,CAAAD,cAAA,CAAAvE,IAAA;QACAkD,OAAA,CAAAwD,GAAA,CAAA1G,IAAA;MAEA;MAEA,KAAAO,OAAA,CAAAyG,OAAA,aAAAC,GAAA;QACA/D,OAAA,CAAAgE,KAAA,CAAAD,GAAA;QACAzC,IAAA,CAAA2C,SAAA;MACA;MAEA,KAAA5G,OAAA,CAAA6G,OAAA,aAAAL,GAAA;QACA7D,OAAA,CAAAmE,IAAA,CAAAN,GAAA;QACAvC,IAAA,CAAA2C,SAAA;MAEA;IAEA;IACA;IACAA,UAAA;MACA,IAAA3C,IAAA;MACA,IAAAA,IAAA,CAAA/D,iBAAA,KAAA6G,SAAA;QACA9C,IAAA,CAAA/D,iBAAA;MACA;MACA,IAAA+D,IAAA,CAAA9D,iBAAA,KAAA4G,SAAA;QACA9C,IAAA,CAAA9D,iBAAA;MACA;MAEA,IAAA8D,IAAA,CAAA/D,iBAAA;QAAA;QACAoE,UAAA;UACAL,IAAA,CAAA/D,iBAAA;UACAyC,OAAA,CAAAwD,GAAA,UAAAlC,IAAA,CAAA/D,iBAAA;UACA+D,IAAA,CAAA0B,qBAAA;QACA,GAAA1B,IAAA,CAAA9D,iBAAA;;QAEA;QACA8D,IAAA,CAAA9D,iBAAA;QACA,IAAA8D,IAAA,CAAA9D,iBAAA;UAAA;UACA8D,IAAA,CAAA9D,iBAAA;QACA;MACA;QACAwC,OAAA,CAAAgE,KAAA;MACA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}