/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
function t(t,e){return t===e?0:null===t?-1:null===e?1:t<e?-1:1}class e{constructor(t){const e=t.split(",");this._fields=[],this._directions=[];for(let s=0;s<e.length;s++){const t=e[s].match(/\S+/g);this._fields.push(t[0]),2===t.length?"asc"===t[1].toLowerCase()?this._directions.push(1):this._directions.push(0):this._directions.push(1)}}constructClause(){let t="";for(let e=0;e<this._fields.length;e++)0!==e&&(t+=","),t+=this._fields[e],1===this._directions[e]?t+=" ASC":t+=" DESC";return t}order(e){e.sort(((e,s)=>{for(let i=0;i<this._fields.length;i++){const r=this.featureValue(e.feature,this._fields[i],i),l=this.featureValue(s.feature,this._fields[i],i);let o=0;if(o=1===this._directions[i]?t(r,l):-1*t(r,l),0!==o)return o}return 0}))}scanForField(t){for(let e=0;e<this._fields.length;e++)if(this._fields[e].toLowerCase().trim()===t.toLowerCase().trim())return!0;return!1}replaceFields(t){let s="";for(let e=0;e<this._fields.length;e++){0!==e&&(s+=",");let i=this._fields[e];for(const e of t)if(i.toLowerCase()===e.field.toLowerCase()){i=e.newfield;break}s+=i,1===this._directions[e]?s+=" ASC":s+=" DESC"}return new e(s)}featureValue(t,e,s){const i=t.attributes[e];if(void 0!==i)return i;for(const r in t.attributes)if(e.toLowerCase()===r.toLowerCase())return this._fields[s]=r,t.attributes[r];return null}}export{e as default};
