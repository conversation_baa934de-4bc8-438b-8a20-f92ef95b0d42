{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"inform boxBgStyle\",\n    style: _vm.sizeStyle\n  }, [_c(\"div\", {\n    staticClass: \"topMain\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"delete\",\n    on: {\n      click: _vm.closeInfo\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/mainPics/i_delete.png\"),\n      alt: \"\",\n      width: \"30px\"\n    }\n  })])]), _c(\"div\", {\n    staticClass: \"mainCon\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"div\", {\n    staticClass: \"name\"\n  }, [_vm._v(\"视频名称：\")]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_vm._v(_vm._s(_vm.videoInfo?.channalName))])]), _c(\"li\", [_c(\"div\", {\n    staticClass: \"name\"\n  }, [_vm._v(\"所属组织：\")]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_vm._v(_vm._s(_vm.videoInfo?.organization))])]), _c(\"li\", [_c(\"div\", {\n    staticClass: \"name\"\n  }, [_vm._v(\"摄像头类型：\")]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_vm._v(_vm._s(_vm.videoInfo?.cameraType))])]), _c(\"li\", [_c(\"div\", {\n    staticClass: \"name\"\n  }, [_vm._v(\"视频连接：\")]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_vm._v(_vm._s(_vm.videoInfo?.connectWay))])]), _c(\"li\", [_c(\"div\", {\n    staticClass: \"name\"\n  }, [_vm._v(\"设备类型：\")]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_vm._v(_vm._s(_vm.videoInfo?.deviceType))])])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"txt fz30\"\n  }, [_vm._v(\"监控详情\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "sizeStyle", "_m", "on", "click", "closeInfo", "attrs", "src", "require", "alt", "width", "_v", "_s", "videoInfo", "channal<PERSON>ame", "organization", "cameraType", "connectWay", "deviceType", "staticRenderFns", "_withStripped"], "sources": ["D:/Project/HuaQiaoSanQi/src/components/videoTypeOption/getVideoInfo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"inform boxBgStyle\", style: _vm.sizeStyle }, [\n    _c(\"div\", { staticClass: \"topMain\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"delete\", on: { click: _vm.closeInfo } }, [\n        _c(\"img\", {\n          attrs: {\n            src: require(\"@/assets/images/mainPics/i_delete.png\"),\n            alt: \"\",\n            width: \"30px\",\n          },\n        }),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"mainCon\" }, [\n      _c(\"ul\", [\n        _c(\"li\", [\n          _c(\"div\", { staticClass: \"name\" }, [_vm._v(\"视频名称：\")]),\n          _c(\"div\", { staticClass: \"txt\" }, [\n            _vm._v(_vm._s(_vm.videoInfo?.channalName)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"div\", { staticClass: \"name\" }, [_vm._v(\"所属组织：\")]),\n          _c(\"div\", { staticClass: \"txt\" }, [\n            _vm._v(_vm._s(_vm.videoInfo?.organization)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"div\", { staticClass: \"name\" }, [_vm._v(\"摄像头类型：\")]),\n          _c(\"div\", { staticClass: \"txt\" }, [\n            _vm._v(_vm._s(_vm.videoInfo?.cameraType)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"div\", { staticClass: \"name\" }, [_vm._v(\"视频连接：\")]),\n          _c(\"div\", { staticClass: \"txt\" }, [\n            _vm._v(_vm._s(_vm.videoInfo?.connectWay)),\n          ]),\n        ]),\n        _c(\"li\", [\n          _c(\"div\", { staticClass: \"name\" }, [_vm._v(\"设备类型：\")]),\n          _c(\"div\", { staticClass: \"txt\" }, [\n            _vm._v(_vm._s(_vm.videoInfo?.deviceType)),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"title\" }, [\n      _c(\"div\", { staticClass: \"txt fz30\" }, [_vm._v(\"监控详情\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE,mBAAmB;IAAEC,KAAK,EAAEJ,GAAG,CAACK;EAAU,CAAC,EAAE,CAC3EJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,GAAG,CAACM,EAAE,CAAC,CAAC,CAAC,EACTL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE,QAAQ;IAAEI,EAAE,EAAE;MAAEC,KAAK,EAAER,GAAG,CAACS;IAAU;EAAE,CAAC,EAAE,CACjER,EAAE,CAAC,KAAK,EAAE;IACRS,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,uCAAuC,CAAC;MACrDC,GAAG,EAAE,EAAE;MACPC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACrDd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,SAAS,EAAEC,WAAW,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,EACFjB,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACrDd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,SAAS,EAAEE,YAAY,CAAC,CAAC,CAC5C,CAAC,CACH,CAAC,EACFlB,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EACtDd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,SAAS,EAAEG,UAAU,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,EACFnB,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACrDd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,SAAS,EAAEI,UAAU,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,EACFpB,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACrDd,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,SAAS,EAAEK,UAAU,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIvB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CAACH,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACzD,CAAC;AACJ,CAAC,CACF;AACDhB,MAAM,CAACyB,aAAa,GAAG,IAAI;AAE3B,SAASzB,MAAM,EAAEwB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}