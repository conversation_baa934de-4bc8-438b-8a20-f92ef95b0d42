/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import r from"../core/Accessor.js";import{ClonableMixin as t}from"../core/Clonable.js";import{IdentifiableMixin as e}from"../core/Identifiable.js";import{isSome as s}from"../core/maybe.js";import{property as i}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as p}from"../core/accessorSupport/decorators/subclass.js";let l=0,n=class extends(t(e(r))){constructor(o){super(o),this.id=`${Date.now().toString(16)}-analysis-${l++}`,this.title=null,this.parent=null,this.visible=!0}get isEditable(){return this.requiredPropertiesForEditing.every(s)}};o([i({type:String,constructOnly:!0,clonable:!1})],n.prototype,"id",void 0),o([i({type:String})],n.prototype,"title",void 0),o([i({constructOnly:!0})],n.prototype,"type",void 0),o([i({clonable:!1})],n.prototype,"parent",void 0),o([i({readOnly:!0})],n.prototype,"extent",void 0),o([i({type:Boolean})],n.prototype,"visible",void 0),o([i({readOnly:!0})],n.prototype,"isEditable",null),o([i({readOnly:!0})],n.prototype,"requiredPropertiesForEditing",void 0),o([i({readOnly:!0})],n.prototype,"nonEditableMessage",void 0),n=o([p("esri.analysis.Analysis")],n);const a=n;export{a as default};
