{"ast": null, "code": "import axios from 'axios';\nlet http = axios.create({\n  // 通用请求前缀\n  baseURL: process.env.VUE_APP_API_VIDEO_URL,\n  timeout: 20000\n});\n// 添加请求拦截器\nhttp.interceptors.request.use(function (config) {\n  // 设置请求头\n  return config;\n}, function (error) {\n  // 对请求错误做些什么\n  return Promise.reject(error);\n});\n\n// 添加响应拦截器\nhttp.interceptors.response.use(function (response) {\n  // 2xx 范围内的状态码都会触发该函数。\n  // 对响应数据做点什么\n\n  // 判断后端返回的错误信息\n  if (response.data.code == 200 || response.data.code == 0) {\n    return Promise.resolve(response);\n  } else {\n    return Promise.reject('error');\n  }\n  return response;\n}, function (error) {\n  // 超出 2xx 范围的状态码都会触发该函数。\n  // 对响应错误做点什么\n  if (error.response) {}\n  return Promise.reject(error);\n});\nexport default http;", "map": {"version": 3, "names": ["axios", "http", "create", "baseURL", "process", "env", "VUE_APP_API_VIDEO_URL", "timeout", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "code", "resolve"], "sources": ["D:/Project/HuaQiaoSanQi/src/utils/api/getVideo.js"], "sourcesContent": ["import axios from 'axios'; \r\n\r\nlet http = axios.create({\r\n    // 通用请求前缀\r\n    baseURL:process.env.VUE_APP_API_VIDEO_URL,\r\n    timeout: 20000,\r\n});\r\n// 添加请求拦截器\r\nhttp.interceptors.request.use(\r\n    function (config) {\r\n         \r\n        // 设置请求头\r\n        return config;\r\n    },\r\n    function (error) {\r\n        // 对请求错误做些什么\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// 添加响应拦截器\r\nhttp.interceptors.response.use(\r\n    function (response) {\r\n        // 2xx 范围内的状态码都会触发该函数。\r\n        // 对响应数据做点什么\r\n\r\n        // 判断后端返回的错误信息\r\n        if (response.data.code == 200 || response.data.code==0) {\r\n            return Promise.resolve(response)\r\n\r\n        }else{\r\n            return Promise.reject('error')\r\n\r\n        }\r\n        return response;\r\n    },\r\n    function (error) {\r\n        // 超出 2xx 范围的状态码都会触发该函数。\r\n        // 对响应错误做点什么\r\n        if (error.response) {\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n)\r\n\r\nexport default http;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,IAAIC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAAC;EACpB;EACAC,OAAO,EAACC,OAAO,CAACC,GAAG,CAACC,qBAAqB;EACzCC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACAN,IAAI,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzB,UAAUC,MAAM,EAAE;EAEd;EACA,OAAOA,MAAM;AACjB,CAAC,EACD,UAAUC,KAAK,EAAE;EACb;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAX,IAAI,CAACO,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1B,UAAUK,QAAQ,EAAE;EAChB;EACA;;EAEA;EACA,IAAIA,QAAQ,CAACC,IAAI,CAACC,IAAI,IAAI,GAAG,IAAIF,QAAQ,CAACC,IAAI,CAACC,IAAI,IAAE,CAAC,EAAE;IACpD,OAAOJ,OAAO,CAACK,OAAO,CAACH,QAAQ,CAAC;EAEpC,CAAC,MAAI;IACD,OAAOF,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAElC;EACA,OAAOC,QAAQ;AACnB,CAAC,EACD,UAAUH,KAAK,EAAE;EACb;EACA;EACA,IAAIA,KAAK,CAACG,QAAQ,EAAE,CACpB;EACA,OAAOF,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAeX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}