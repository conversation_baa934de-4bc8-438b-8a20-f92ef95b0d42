{"ast": null, "code": "import { gedImDpDzSjhGaj, deleteDimDpDzSjhGaj } from '@/api/userMenu';\nimport addressDialog from './addressDialog.vue';\nexport default {\n  name: 'layerManage',\n  data() {\n    return {\n      // 查询参数\n      queryParams: {\n        // state: '',\n        pageSize: 10,\n        pageNum: 1,\n        // startDate: '',\n        // endDate: '',\n        // content: '',\n        dzQdz: \"\"\n      },\n      total: '',\n      tabList: [],\n      time: ['', ''],\n      searchRules: {\n        content: [{\n          pattern: /^[\\u4e00-\\u9fa5_a-zA-Z0-9.·-]+$/,\n          message: \"名称不支持特殊字符\",\n          trigger: \"blur\"\n        }, {\n          max: 30,\n          message: \"长度在 30 个字符内\",\n          trigger: \"blur\"\n        }]\n      }\n    };\n  },\n  components: {\n    addressDialog\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.gedImDpDzSjhGajList();\n  },\n  updated() {},\n  methods: {\n    editFn(item) {\n      let data = JSON.parse(JSON.stringify(item));\n      this.$refs.openModal.openAciton(\"edit\", data);\n    },\n    addFn() {\n      this.$refs.openModal.openAciton(\"add\", {});\n    },\n    // 搜索\n    handleQuery() {\n      this.gedImDpDzSjhGajList();\n    },\n    //重置\n    resetQuery() {\n      this.queryParams.dzQdz = '';\n      this.queryParams.pageNum = 1;\n      this.gedImDpDzSjhGajList();\n    },\n    //监听页码变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.gedImDpDzSjhGajList();\n    },\n    // 获取图层数据\n    gedImDpDzSjhGajList() {\n      this.queryParams.startDate = this.time[0];\n      this.queryParams.endDate = this.time[1];\n      gedImDpDzSjhGaj(this.queryParams).then(res => {\n        if (res.data.code == 200) {\n          this.tabList = res.data.extra.data;\n          this.total = res.data.extra.total;\n          this.queryParams.pageNum = res.data.extra.pageNum;\n          this.queryParams.pageSize = res.data.extra.pageSize;\n        }\n      });\n    },\n    // 根据id删除地址数据 \n    deleteFn(id) {\n      this.$confirm('此操作将永久删除, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteDimDpDzSjhGaj({\n          ids: id\n        }).then(res => {\n          if (res.data.code == 200) {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.gedImDpDzSjhGajList();\n          }\n        });\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    }\n  },\n  beforeDestroy() {}\n};", "map": {"version": 3, "names": ["gedImDpDzSjhGaj", "deleteDimDpDzSjhGaj", "addressDialog", "name", "data", "queryParams", "pageSize", "pageNum", "dzQdz", "total", "tabList", "time", "searchRules", "content", "pattern", "message", "trigger", "max", "components", "watch", "computed", "mounted", "gedImDpDzSjhGajList", "updated", "methods", "editFn", "item", "JSON", "parse", "stringify", "$refs", "openModal", "openAciton", "addFn", "handleQuery", "reset<PERSON><PERSON>y", "handleCurrentChange", "val", "startDate", "endDate", "then", "res", "code", "extra", "deleteFn", "id", "$confirm", "confirmButtonText", "cancelButtonText", "type", "ids", "$message", "catch", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/mainPage/components/resource-center/components/addressManage.vue"], "sourcesContent": ["<template>\r\n    <div class=\"layerManage\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" :rules=\"searchRules\">\r\n            <el-form-item label=\"标准地址\" prop=\"dzid\">\r\n                <el-input v-model.trim=\"queryParams.dzQdz\" placeholder=\"请输入地址内容\" clearable size=\"small\"\r\n                    style=\"caret-color: #fff;\" />\r\n            </el-form-item>\r\n            <!-- <el-form-item label=\"发布时间\" prop=\"time\">\r\n                <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"time\" type=\"daterange\" range-separator=\"至\"\r\n                    start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" style=\"caret-color: #fff;\">\r\n                </el-date-picker>\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"state\">\r\n                <el-select v-model=\"queryParams.state\" placeholder=\"图层状态\" clearable size=\"small\">\r\n                    <el-option key=\"1\" label=\"正常\" value=\"1\" />\r\n                    <el-option key=\"0\" label=\"停用\" value=\"0\" />\r\n                </el-select>\r\n            </el-form-item> -->\r\n            <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n        <el-button class=\"publish\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addFn\">新增</el-button>\r\n        <el-table :data=\"tabList\">\r\n            <el-table-column prop=\"dzid\" label=\"标准地址ID\" width=\"302\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"dzQdz\" label=\"标准地址\" width=\"400\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"dzMph\" label=\"门牌号\" width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"dzLzh\" label=\"楼栋号\" width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"dzLsh\" label=\"楼室号\" width=\"200\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qzmc\" label=\"所属区镇\" width=\"300\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column prop=\"csqmc\" label=\"所属社区\" width=\"300\" show-overflow-tooltip>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                    <el-button type=\"text\" size=\"small\" @click=\"editFn(scope.row)\">修改</el-button>\r\n                    <el-button type=\"text\" size=\"small\" @click=\"deleteFn(scope.row.dzid)\">删除</el-button>\r\n                </template>\r\n            </el-table-column>\r\n        </el-table>\r\n        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"queryParams.pageNum\"\r\n            :page-size=\"queryParams.pageSize\" layout=\"total, prev, pager, next, jumper\" :total=\"total\">\r\n        </el-pagination>\r\n        <addressDialog ref=\"openModal\" @resetList=\"resetQuery\" />\r\n    </div>\r\n</template>\r\n<script>\r\nimport { gedImDpDzSjhGaj, deleteDimDpDzSjhGaj } from '@/api/userMenu'\r\nimport addressDialog from './addressDialog.vue';\r\nexport default {\r\n    name: 'layerManage',\r\n    data() {\r\n        return {\r\n            // 查询参数\r\n            queryParams: {\r\n                // state: '',\r\n                pageSize: 10,\r\n                pageNum: 1,\r\n                // startDate: '',\r\n                // endDate: '',\r\n                // content: '',\r\n                dzQdz: \"\",\r\n            },\r\n            total: '',\r\n            tabList: [],\r\n            time: ['', ''],\r\n            searchRules: {\r\n                content: [\r\n                    {\r\n                        pattern: /^[\\u4e00-\\u9fa5_a-zA-Z0-9.·-]+$/,\r\n                        message: \"名称不支持特殊字符\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                    { max: 30, message: \"长度在 30 个字符内\", trigger: \"blur\" },\r\n                ],\r\n            },\r\n        };\r\n    },\r\n    components: {\r\n        addressDialog,\r\n    },\r\n    watch: {\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        this.gedImDpDzSjhGajList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        editFn(item) {\r\n            let data = JSON.parse(JSON.stringify(item));\r\n            this.$refs.openModal.openAciton(\"edit\", data);\r\n        },\r\n        addFn() {\r\n            this.$refs.openModal.openAciton(\"add\", {});\r\n        },\r\n        // 搜索\r\n        handleQuery() {\r\n            this.gedImDpDzSjhGajList();\r\n        },\r\n        //重置\r\n        resetQuery() {\r\n\r\n            this.queryParams.dzQdz = '';\r\n            this.queryParams.pageNum = 1;\r\n            this.gedImDpDzSjhGajList();\r\n        },\r\n        //监听页码变化\r\n        handleCurrentChange(val) {\r\n            this.queryParams.pageNum = val;\r\n            this.gedImDpDzSjhGajList();\r\n        },\r\n        // 获取图层数据\r\n        gedImDpDzSjhGajList() {\r\n            this.queryParams.startDate = this.time[0];\r\n            this.queryParams.endDate = this.time[1];\r\n            gedImDpDzSjhGaj(this.queryParams).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.tabList = res.data.extra.data;\r\n                    this.total = res.data.extra.total;\r\n                    this.queryParams.pageNum = res.data.extra.pageNum;\r\n                    this.queryParams.pageSize = res.data.extra.pageSize;\r\n                }\r\n            })\r\n        },\r\n        // 根据id删除地址数据 \r\n        deleteFn(id) {\r\n            this.$confirm('此操作将永久删除, 是否继续?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                deleteDimDpDzSjhGaj({ ids: id }).then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.$message({\r\n                            type: 'success',\r\n                            message: '删除成功!'\r\n                        });\r\n                        this.gedImDpDzSjhGajList();\r\n                    }\r\n                })\r\n\r\n            }).catch(() => {\r\n                this.$message({\r\n                    type: 'info',\r\n                    message: '已取消删除'\r\n                });\r\n            });\r\n        }\r\n    },\r\n    beforeDestroy() {\r\n    }\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.layerManage {\r\n    width: 100%;\r\n    height: calc(100% - 6vh);\r\n    margin-top: 6vh;\r\n    padding-top: 45px;\r\n    color: #fff;\r\n\r\n\r\n\r\n    // position: relative;\r\n    :deep(.el-form) {\r\n        display: flex;\r\n        margin-left: 36px;\r\n\r\n        .el-form-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 78px;\r\n\r\n            .el-form-item__label {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 18px;\r\n                color: #D1DEE2;\r\n                line-height: 25px;\r\n            }\r\n\r\n            .el-input__inner {\r\n                width: 300px;\r\n                height: 35px;\r\n                background: #103452;\r\n                border-radius: 5px;\r\n                border: 1px solid #6A8096;\r\n                color: #fff;\r\n            }\r\n\r\n            .el-range-input {\r\n                background: transparent;\r\n            }\r\n\r\n            .el-button {\r\n                color: #fff;\r\n                border: none;\r\n            }\r\n\r\n            .el-button:nth-child(1) {\r\n                background: #0785c9;\r\n            }\r\n\r\n            .el-button:nth-child(2) {\r\n                background: #07c9b2;\r\n            }\r\n        }\r\n\r\n\r\n\r\n\r\n    }\r\n\r\n    :deep(.publish) {\r\n        color: #fff;\r\n        border: none;\r\n        background: #00D0FF;\r\n        margin-left: 36px;\r\n    }\r\n\r\n    :deep(.el-table) {\r\n        background: #1F405C;\r\n        margin: 25px 12px 0 12px;\r\n        width: calc(100% - 24px);\r\n        color: #D1DEE2;\r\n        border: none;\r\n\r\n\r\n        thead {\r\n            border: 2px solid #3B678C;\r\n            height: 73px;\r\n\r\n            tr th {\r\n                background: #124C6F;\r\n\r\n                color: #D1DEE2;\r\n            }\r\n        }\r\n\r\n        .el-table__row {\r\n            background: #1F405C;\r\n        }\r\n\r\n        .el-table__row:hover>td {\r\n            background-color: transparent !important;\r\n        }\r\n\r\n\r\n        .el-table td,\r\n        .el-table th {\r\n            border-bottom: 1px solid #3B678C;\r\n            border-right: none;\r\n            border-left: none;\r\n            border-top: none;\r\n        }\r\n\r\n        .cell {\r\n            max-height: 23px !important;\r\n        }\r\n    }\r\n\r\n    :deep(.el-pagination) {\r\n        margin-top: 40px;\r\n        margin-bottom: 30px;\r\n        text-align: center;\r\n\r\n        .el-pagination__total {\r\n            color: #fff;\r\n        }\r\n\r\n        .el-pagination__sizes {\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        >button {\r\n            color: #fff;\r\n            background-color: transparent;\r\n        }\r\n\r\n        ul {\r\n            li {\r\n                background-color: transparent;\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        .el-pager .active {\r\n            color: #4BDBFF;\r\n        }\r\n\r\n        .el-pagination__jump {\r\n            color: #fff;\r\n\r\n            input {\r\n                background: #072C44;\r\n                border-radius: 4px;\r\n                border: 1px solid #4BDBFF;\r\n                color: #4BDBFF;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAqDA,SAAAA,eAAA,EAAAC,mBAAA;AACA,OAAAC,aAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACA;MACAC,WAAA;QACA;QACAC,QAAA;QACAC,OAAA;QACA;QACA;QACA;QACAC,KAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,IAAA;MACAC,WAAA;QACAC,OAAA,GACA;UACAC,OAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,UAAA;IACAhB;EACA;EACAiB,KAAA,GACA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,mBAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,OAAAC,IAAA;MACA,IAAAtB,IAAA,GAAAuB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,IAAA;MACA,KAAAI,KAAA,CAAAC,SAAA,CAAAC,UAAA,SAAA5B,IAAA;IACA;IACA6B,MAAA;MACA,KAAAH,KAAA,CAAAC,SAAA,CAAAC,UAAA;IACA;IACA;IACAE,YAAA;MACA,KAAAZ,mBAAA;IACA;IACA;IACAa,WAAA;MAEA,KAAA9B,WAAA,CAAAG,KAAA;MACA,KAAAH,WAAA,CAAAE,OAAA;MACA,KAAAe,mBAAA;IACA;IACA;IACAc,oBAAAC,GAAA;MACA,KAAAhC,WAAA,CAAAE,OAAA,GAAA8B,GAAA;MACA,KAAAf,mBAAA;IACA;IACA;IACAA,oBAAA;MACA,KAAAjB,WAAA,CAAAiC,SAAA,QAAA3B,IAAA;MACA,KAAAN,WAAA,CAAAkC,OAAA,QAAA5B,IAAA;MACAX,eAAA,MAAAK,WAAA,EAAAmC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAArC,IAAA,CAAAsC,IAAA;UACA,KAAAhC,OAAA,GAAA+B,GAAA,CAAArC,IAAA,CAAAuC,KAAA,CAAAvC,IAAA;UACA,KAAAK,KAAA,GAAAgC,GAAA,CAAArC,IAAA,CAAAuC,KAAA,CAAAlC,KAAA;UACA,KAAAJ,WAAA,CAAAE,OAAA,GAAAkC,GAAA,CAAArC,IAAA,CAAAuC,KAAA,CAAApC,OAAA;UACA,KAAAF,WAAA,CAAAC,QAAA,GAAAmC,GAAA,CAAArC,IAAA,CAAAuC,KAAA,CAAArC,QAAA;QACA;MACA;IACA;IACA;IACAsC,SAAAC,EAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAT,IAAA;QACAvC,mBAAA;UAAAiD,GAAA,EAAAL;QAAA,GAAAL,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAArC,IAAA,CAAAsC,IAAA;YACA,KAAAS,QAAA;cACAF,IAAA;cACAlC,OAAA;YACA;YACA,KAAAO,mBAAA;UACA;QACA;MAEA,GAAA8B,KAAA;QACA,KAAAD,QAAA;UACAF,IAAA;UACAlC,OAAA;QACA;MACA;IACA;EACA;EACAsC,cAAA,GACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}