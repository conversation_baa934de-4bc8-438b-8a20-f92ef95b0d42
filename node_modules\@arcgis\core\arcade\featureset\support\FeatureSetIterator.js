/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{create as t,resolve as e}from"../../../core/promiseUtils.js";class n{constructor(t,e){this._lastId=-1,this._progress=e,this._parent=t}reset(){this._lastId=-1}nextBatch(e){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((t=>this.nextBatch(e)),(t=>this.nextBatch(e)));const n={returnpromise:null,hasset:!1},s=[];return n.returnpromise=t(((t,a)=>{this._parent._getSet(this._progress).then((h=>{let _=h._known.length-1;if("GETPAGES"===h._known[h._known.length-1]&&(_-=1),this._lastId+e>_&&h._known.length>0&&"GETPAGES"===h._known[h._known.length-1])this._parent._expandPagedSet(h,this._parent._maxQueryRate(),0,0,this._progress).then((s=>{n.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(t,a)}),(t=>{n.hasset=!0,this._parent._mainSetInUse=null,a(t)}));else{if(_>=this._lastId+e||0===h._candidates.length){for(let t=0;t<e;t++){const e=t+this._lastId+1;if(e>=h._known.length)break;s[t]=h._known[e]}return this._lastId+=s.length,0===s.length&&(n.hasset=!0,this._parent._mainSetInUse=null,t([])),void this._parent._getFeatureBatch(s,this._progress).then((e=>{n.hasset=!0,this._parent._mainSetInUse=null,t(e)}),(t=>{n.hasset=!0,this._parent._mainSetInUse=null,a(t)}))}this._parent._refineSetBlock(h,this._parent._maxProcessingRate(),this._progress).then((()=>{n.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(e).then(t,a)}),(t=>{n.hasset=!0,this._parent._mainSetInUse=null,a(t)}))}}),(t=>{n.hasset=!0,this._parent._mainSetInUse=null,a(t)}))})),!1===n.hasset&&(this._parent._mainSetInUse=n.returnpromise,n.hasset=!0),n.returnpromise}next(){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((t=>this.next()),(t=>this.next()));const e={returnpromise:null,hasset:!1};return e.returnpromise=t(((t,n)=>{this._parent._getSet(this._progress).then((s=>{this._lastId<s._known.length-1?"GETPAGES"===s._known[this._lastId+1]?this._parent._expandPagedSet(s,this._parent._maxQueryRate(),0,0,this._progress).then((t=>(e.hasset=!0,this._parent._mainSetInUse=null,this.next()))).then(t,n):(this._lastId+=1,this._parent._getFeature(s,s._known[this._lastId],this._progress).then((n=>{e.hasset=!0,this._parent._mainSetInUse=null,t(n)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)}))):s._candidates.length>0?this._parent._refineSetBlock(s,this._parent._maxProcessingRate(),this._progress).then((()=>{e.hasset=!0,this._parent._mainSetInUse=null,this.next().then(t,n)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)})):(e.hasset=!0,this._parent._mainSetInUse=null,t(null))}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)}))})),!1===e.hasset&&(this._parent._mainSetInUse=e.returnpromise,e.hasset=!0),e.returnpromise}count(){return-1!==this._parent._totalCount?e(this._parent._totalCount):this._parent._getSet(this._progress).then((t=>this._refineAllSets(t))).then((t=>(this._parent._totalCount=t._known.length,e(this._parent._totalCount))))}_refineAllSets(t){return t._known.length>0&&"GETPAGES"===t._known[t._known.length-1]?this._parent._expandPagedSet(t,this._parent._maxQueryRate(),0,1,this._progress).then((e=>this._refineAllSets(t))).then((t=>e(t))):t._candidates.length>0?"GETPAGES"===t._known[t._candidates.length-1]?this._parent._expandPagedSet(t,this._parent._maxQueryRate(),0,2,this._progress).then((e=>this._refineAllSets(t))).then((t=>e(t))):this._parent._refineSetBlock(t,this._parent._maxProcessingRate(),this._progress).then((t=>t._candidates.length>0?this._refineAllSets(t):e(t))):e(t)}}export{n as default};
