{"ast": null, "code": "import { getLayersList, getStatistic } from '@/api/userMenu';\nexport default {\n  name: 'specificLayer',\n  data() {\n    return {\n      mapList: [],\n      subjectList: [],\n      labelList: [],\n      statisticList: [],\n      btns: [{\n        name: \"主题\"\n      }, {\n        name: \"类型\"\n      }],\n      activeArr: [\"阴影分析：阴影模拟\", \"限高分析：限高模拟\", \"标高分析：标高模拟\"],\n      activeIndex2: 0,\n      activeIndex: 0,\n      total: 0,\n      pageSize: 6,\n      pageNum: 1,\n      inputValue: \"\",\n      subject: '',\n      key: \"\",\n      flag: true,\n      detailObj: {},\n      fullscreen: false,\n      basemapListActive: false,\n      selectBasemap: 0,\n      basemap: '',\n      clickQueryResultDialog: false,\n      clickQueryResultFeature: null,\n      basemaps2: [`/HQGIS/Hosted/ks_hqyxdt_2022/MapServer`,\n      //影像\n      `/HQGIS/Hosted/ks_hqssdt_2023_yjdz/MapServer`,\n      //深色\n      `/HQGIS/Hosted/ks_hq_csdt_1/MapServer` //浅色\n      ]\n    };\n  },\n  components: {},\n  watch: {},\n  computed: {},\n  mounted() {\n    this.layersList();\n    this.getStatisticList();\n    // this.$nextTick(() => {\n\n    // })\n    //按ESC退出全屏时修改按钮状态\n    window.addEventListener(\"resize\", this.windowResize);\n  },\n  methods: {\n    activeHandle(index) {\n      this.activeIndex2 = index;\n      //移除之前添加的分析\n      this.removeAnalysisWIdget();\n      switch (index) {\n        case 2:\n          this.kscim.analysis.ElevationAnalysis.addArcgisElevationAnalysis();\n          break;\n        case 1:\n          this.kscim.analysis.LimitHighAnalysis.addArcgisLimitHighAnalysis();\n          break;\n        case 0:\n          this.kscim.analysis.WeatherAnalysis.addArcgisWeatherAnalysis(null, null, \"top-right\");\n          document.querySelector('#kscim-weather-analysis-toolbar').style.display = \"none\";\n          break;\n        default:\n          this.removeAnalysisWIdget();\n          break;\n      }\n    },\n    removeAnalysisWIdget() {\n      this.kscim.analysis.TransverseAnalysis.removeArcgisTransverseAnalysis();\n      this.kscim.analysis.ViewshedAnalysis.removeArcgisViewshedAnalysis();\n      this.kscim.analysis.VisibilityAnalysis.removeArcgisVisibilityAnalysis();\n      this.kscim.analysis.ProfileAnalysis.removeArcgisProfileAnalysis();\n      this.kscim.analysis.WeatherAnalysis.removeArcgisWeatherAnalysis();\n      this.kscim.analysis.LimitHighAnalysis.removeArcgisLimitHighAnalysis();\n      this.kscim.analysis.ElevationAnalysis.removeArcgisElevationAnalysis();\n      if (this.kscim.ArcgisView.environment) {\n        this.kscim.ArcgisView.environment.starsEnabled = false;\n        this.kscim.ArcgisView.environment.atmosphereEnabled = false;\n      }\n    },\n    resetQuery() {\n      this.inputValue = \"\";\n      this.pageNum = 1;\n      this.layersList();\n    },\n    toDetail(item) {\n      this.flag = false;\n      this.detailObj = item;\n      let that = this;\n      //加载arcgis js文件\n      that.arcgisscript = document.createElement(\"SCRIPT\");\n      that.arcgisscript.setAttribute(\"src\", \"/AIRCIMSCENE/kscim/kscim.js\");\n      that.arcgisscript.setAttribute(\"async\", \"\");\n      that.arcgisscript.setAttribute(\"defer\", \"\");\n      document.body.appendChild(that.arcgisscript);\n      that.arcgisscript.onload = () => {\n        that.kscim = new kscim(\"mapContainer\"); //地图初始化,自定义kscim为地图对象\n        if (item.loadtype == \"GeomtetryService\") {\n          //加载深色地图\n          // that.loadMapLayer({ loadtype: \"Tile\", url: \"/HQGIS/Hosted/ks_hqssdt_2023_yjdz/MapServer\" })\n          //加载白模\n          that.loadMapLayer({\n            loadtype: \"SceneServer\",\n            url: \"/HQGIS/Hosted/ks_hqz_bm/SceneServer\"\n          });\n          that.mapurl('2');\n          that.activeHandle(0);\n        } else {\n          that.loadMapLayer(item);\n        }\n      };\n    },\n    mapurl(type) {\n      let that = this;\n      if (that.basemap) {\n        that.kscim.layer.remove(that.basemap);\n        that.basemap = '';\n      }\n      if (that.selectBasemap == type) {\n        that.selectBasemap = 0;\n        return false;\n      }\n      that.selectBasemap = type;\n      if (type == '3') {\n        that.basemap = that.kscim.layer.addArcgisTileLayer({\n          url: that.basemaps2[0]\n        });\n      } else if (type == '1') {\n        that.basemap = that.kscim.layer.addArcgisTileLayer({\n          url: that.basemaps2[2]\n        });\n      } else if (type == '2') {\n        that.basemap = that.kscim.layer.addArcgisTileLayer({\n          url: that.basemaps2[1]\n        });\n      }\n      if (that.detailObj.loadType == 'multFeatureLayer' || that.detailObj.loadType == 'mapLetteringLayer') {\n        for (let i in that.layer) {\n          that.kscim.ArcgisView.map.reorder(that.layer[i], 1000);\n        }\n      } else {\n        that.kscim.ArcgisView.map.reorder(that.layer, 1000);\n      }\n    },\n    loadMapLayer(item) {\n      let that = this;\n      if (item.loadtype == \"Tile\") {\n        that.kscim.init2D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        //添加arcgis tile地图服务\n        that.layer = that.kscim.layer.addArcgisTileLayer({\n          url: item.url\n        });\n      } else if (item.loadtype == \"IntegratedMeshLayer\") {\n        that.kscim.init3D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        //倾斜摄影\n        that.layer = that.kscim.layer.addArcgisIntegratedMeshLayer({\n          url: item.url\n        });\n      } else if (item.loadtype == \"MapImageLayer\") {\n        that.kscim.init2D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        //添加MapServer\n        that.layer = that.kscim.layer.addArcgisMapImageLayer({\n          url: item.url\n        });\n        that.layer.when(() => {\n          that.kscim.ArcgisView.goTo(that.layer.fullExtent.extent.expand(4), {\n            speedFactor: 0.5\n          });\n        });\n      } else if (item.loadtype == \"SceneServer\") {\n        that.kscim.init3D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        //三维白模\n        that.layer = that.kscim.layer.addArcgisSceneLayer({\n          url: item.url\n        });\n      } else if (item.loadtype == \"FeatureLayer\") {\n        //初始化预览地图\n        that.kscim.init2D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        let url = item.url;\n        //要素图层\n        that.layer = that.kscim.layer.addArcgisFeatureLayer({\n          url: url,\n          outFields: '*'\n        });\n        // that.getFeatureCount(url);\n      } else if (item.loadtype == \"multFeatureLayer\") {\n        //初始化预览地图\n        that.kscim.init2D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        let url = item.url;\n        //要素图层\n        that.layer = that.kscim.layer.addArcgisFeatureSubLayer({\n          url: url\n        });\n        // that.getFeatureCount(url);\n      } else if (item.loadtype == \"mapLetteringLayer\") {\n        //初始化预览地图\n        that.kscim.init3D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        that.kscim.widget.addHomeWidget();\n        let url = item.url;\n        //注记图层\n        that.layer = that.kscim.layer.addArcgisFeatureSubLayer({\n          url: url,\n          labelingInfo: [{\n            symbol: {\n              type: \"label-3d\",\n              // autocasts as new LabelSymbol3D()\n              symbolLayers: [{\n                type: \"text\",\n                // autocasts as new TextSymbol3DLayer()\n                material: {\n                  color: \"white\"\n                },\n                halo: {\n                  color: 'black',\n                  // autocasts as Color\n                  size: 1\n                },\n                size: '10pt'\n              }]\n            },\n            labelPlacement: \"above-center\",\n            labelExpressionInfo: {\n              expression: '$feature.name'\n            }\n          }]\n        });\n        // that.getFeatureCount(url);\n      } else if (item.loadtype == \"ClusterLayer\") {\n        //初始化预览地图\n        that.kscim.init2D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.bigScreen();\n        //要素图层\n        let url = item.url;\n        that.layer = that.kscim.layer.Cluster2DLayer.addArcgis2DClusterLayer(url);\n        // that.getFeatureCount(url);\n      } else if (item.loadtype == \"BuildingSceneLayer\") {\n        //BIM模型\n        that.kscim.init3D();\n        that.kscim.ArcgisView.ui.remove(\"attribution\");\n        that.kscim.widget.addHomeWidget();\n        that.bigScreen();\n        that.layer = that.kscim.layer.addArcgisBuildingSceneLayer({\n          url: item.url\n        });\n        that.layer.when(() => {\n          that.layer.allSublayers.forEach(item => {\n            item.visible = true;\n          });\n        });\n      } else {\n        this.$message(\"正在开发中...\");\n        return;\n      }\n      that.kscim.ArcgisMap.ground.surfaceColor = {\n        r: 23,\n        g: 57,\n        b: 86,\n        a: 1\n      };\n      that.kscim.ArcgisView.background = {\n        color: {\n          r: 23,\n          g: 57,\n          b: 86,\n          a: 1\n        }\n      };\n      that.kscim.widget.MapClickEvent.addMapClickEvent(that.clickQueryCallBack);\n    },\n    clickQueryCallBack(result) {\n      if (result.results) {\n        const res = result.results[0].graphic;\n        //点到白膜或要素点\n        if (res.layer.fields) {\n          res.layer.fields.push({\n            name: \"X\",\n            alias: \"X\"\n          });\n          res.layer.fields.push({\n            name: \"Y\",\n            alias: \"Y\"\n          });\n        }\n        if (res.geometry !== null) {\n          res.attributes.x = res.geometry.x;\n          res.attributes.y = res.geometry.y;\n        }\n        this.clickQueryResultFeature = res;\n        this.clickQueryResultDialog = true;\n      }\n    },\n    //关闭属性弹窗\n    myModalClose() {\n      this.clickQueryResultFeature = null;\n      this.clickQueryResultDialog = false;\n    },\n    //使用别名\n    formatSearchName(e) {\n      let array = this.clickQueryResultFeature.layer.fields;\n      let res = \"\";\n      if (e === \"x\" || e === \"X\") return \"经度\";\n      if (e === \"y\" || e === \"Y\") return \"纬度\";\n      for (let i in array) {\n        if (array[i].name == e) {\n          res = array[i].alias;\n        }\n      }\n      return res || e;\n    },\n    backFn() {\n      this.flag = true;\n      document.body.removeChild(this.arcgisscript);\n      document.querySelector('.esri-view-root').remove();\n      this.kscim = undefined;\n      this.arcgisscript = null;\n    },\n    // 根据资源中心类型筛选数据\n    changeList(name) {\n      if (this.activeIndex == 0) {\n        this.subject = name;\n        this.key = \"\";\n      } else {\n        this.key = name;\n        this.subject = \"\";\n      }\n      this.pageNum = 1;\n      this.layersList();\n    },\n    //资源中心tabs切换\n    tabsFn(index) {\n      this.activeIndex = index;\n      index == 0 ? this.statisticList = this.subjectList : this.statisticList = this.labelList;\n    },\n    // 图片高亮切换\n    setUrl(index) {\n      return require(`@/assets/images/mainPics/tabs-bg${index == this.activeIndex ? '-active' : ''}.png`);\n    },\n    //监听页码并更新数据\n    handleCurrentChange(val) {\n      this.pageNum = val;\n      this.layersList();\n    },\n    //获取专题图层数据\n    layersList() {\n      getLayersList({\n        pageSize: this.pageSize,\n        pageNum: this.pageNum,\n        key: this.key,\n        subject: this.subject,\n        content: this.inputValue\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.mapList = res.data.extra.data;\n          this.total = res.data.extra.total;\n          this.pageNum = res.data.extra.pageNum;\n          this.pageSize = res.data.extra.pageSize;\n        }\n      });\n    },\n    //获取主题及标签的数量信息\n    getStatisticList() {\n      getStatistic().then(res => {\n        if (res.data.code == 200) {\n          this.labelList = res.data.extra.label;\n          this.subjectList = res.data.extra.subject;\n          this.statisticList = this.subjectList;\n        }\n      });\n    },\n    bigScreen() {\n      // 在控件中创建全屏展示图标\n      let bigBox = document.createElement(\"div\");\n      bigBox.classList.add(\"fullScreen\");\n      bigBox.style.cursor = \"pointer\";\n      bigBox.style.pointerEvents = \"auto\";\n      let img = document.createElement(\"img\");\n      img.src = require(\"@/assets/images/mainPics/bigScreen.png\");\n      bigBox.appendChild(img);\n      let esriUi = document.querySelector(\".esri-ui-top-left\");\n      esriUi.appendChild(bigBox);\n      let element = document.getElementById(\"mapContainer\");\n      let bigScreen = document.querySelector(\".fullScreen\");\n      // 实现全屏展示\n      bigScreen.addEventListener(\"click\", () => {\n        if (this.fullscreen) {\n          if (document.exitFullscreen) {\n            document.exitFullscreen();\n          } else if (document.webkitCancelFullScreen) {\n            document.webkitCancelFullScreen();\n          } else if (document.mozCancelFullScreen) {\n            document.mozCancelFullScreen();\n          } else if (document.msExitFullscreen) {\n            document.msExitFullscreen();\n          }\n        } else {\n          if (element.requestFullscreen) {\n            element.requestFullscreen();\n          } else if (element.webkitRequestFullScreen) {\n            element.webkitRequestFullScreen();\n          } else if (element.mozRequestFullScreen) {\n            element.mozRequestFullScreen();\n          } else if (element.msRequestFullscreen) {\n            element.msRequestFullscreen();\n          }\n        }\n        this.fullscreen = !this.fullscreen;\n      });\n    },\n    // 判断当前是否为全屏状态\n    isFullSrceen() {\n      let isFull = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;\n      if (!isFull) {\n        isFull = false;\n      } else {\n        isFull = true;\n      }\n      return isFull;\n    },\n    windowResize() {\n      let that = this;\n      if (!that.isFullSrceen() && that.fullscreen) {\n        that.fullscreen = false;\n      }\n    }\n  },\n  beforeDestroy() {\n    window.removeEventListener(\"resize\", this.windowResize);\n  }\n};", "map": {"version": 3, "names": ["getLayersList", "getStatistic", "name", "data", "mapList", "subjectList", "labelList", "statisticList", "btns", "activeArr", "activeIndex2", "activeIndex", "total", "pageSize", "pageNum", "inputValue", "subject", "key", "flag", "detailObj", "fullscreen", "basemapListActive", "selectBasemap", "basemap", "clickQueryResultDialog", "clickQueryResultFeature", "basemaps2", "components", "watch", "computed", "mounted", "layersList", "getStatisticList", "window", "addEventListener", "windowResize", "methods", "activeHandle", "index", "removeAnalysisWIdget", "kscim", "analysis", "ElevationAnalysis", "addArcgisElevationAnalysis", "LimitHighAnalysis", "addArcgisLimitHighAnalysis", "WeatherAnalysis", "addArcgisWeatherAnalysis", "document", "querySelector", "style", "display", "TransverseAnalysis", "removeArcgisTransverseAnalysis", "ViewshedAnalysis", "removeArcgisViewshedAnalysis", "VisibilityAnalysis", "removeArcgisVisibilityAnalysis", "ProfileAnalysis", "removeArcgisProfileAnalysis", "removeArcgisWeatherAnalysis", "removeArcgisLimitHighAnalysis", "removeArcgisElevationAnalysis", "<PERSON><PERSON><PERSON><PERSON>", "environment", "starsEnabled", "atmosphereEnabled", "reset<PERSON><PERSON>y", "toDetail", "item", "that", "arcgisscript", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "onload", "loadtype", "loadMapLayer", "url", "mapurl", "type", "layer", "remove", "addArcgisTileLayer", "loadType", "i", "map", "reorder", "init2D", "ui", "bigScreen", "init3D", "addArcgisIntegratedMeshLayer", "addArcgisMapImageLayer", "when", "goTo", "fullExtent", "extent", "expand", "speedFactor", "addArcgisSceneLayer", "addArcgisFeatureLayer", "outFields", "addArcgisFeatureSubLayer", "widget", "addHomeWidget", "labelingInfo", "symbol", "symbolLayers", "material", "color", "halo", "size", "labelPlacement", "labelExpressionInfo", "expression", "Cluster2DLayer", "addArcgis2DClusterLayer", "addArcgisBuildingSceneLayer", "allSublayers", "for<PERSON>ach", "visible", "$message", "ArcgisMap", "ground", "surfaceColor", "r", "g", "b", "a", "background", "MapClickEvent", "addMapClickEvent", "clickQueryCallBack", "result", "results", "res", "graphic", "fields", "push", "alias", "geometry", "attributes", "x", "y", "myModalClose", "formatSearchName", "e", "array", "backFn", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "changeList", "tabsFn", "setUrl", "require", "handleCurrentChange", "val", "content", "then", "code", "extra", "label", "bigBox", "classList", "add", "cursor", "pointerEvents", "img", "src", "esriUi", "element", "getElementById", "exitFullscreen", "webkitCancelFullScreen", "mozCancelFullScreen", "msExitFullscreen", "requestFullscreen", "webkitRequestFullScreen", "mozRequestFullScreen", "msRequestFullscreen", "isFullSrceen", "isFull", "fullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "msFullscreenElement", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/views/mainPage/components/resource-center/components/specificLayer.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\" id=\"map-container\">\r\n        <div class=\"left-list\" v-if=\"flag\">\r\n            <div class=\"btns\">\r\n                <p v-for=\"(item, index) in btns\" @click=\"tabsFn(index)\">\r\n                    <img :src=\"setUrl(index)\" alt=\"\">\r\n                    <span>{{ item.name }}</span>\r\n                </p>\r\n            </div>\r\n            <div class=\"statistic-list\">\r\n                <div class=\"list\" v-for=\"(item, index) in statisticList\" @click=\"changeList(item.name)\">\r\n                    <span>{{ item.name }}</span>\r\n                    <span>({{ item.count }})</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"main-box\" v-if=\"flag\">\r\n            <div class=\"header-search\">\r\n                <div class=\"title\">资源查询</div>\r\n                <el-input placeholder=\"请输入关键词搜索\" v-model=\"inputValue\" style=\"caret-color: #fff;\">\r\n                    <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n                </el-input>\r\n                <span class=\"search-btn\" @click=\"() => {\r\n                    pageNum = 1;\r\n                    layersList();\r\n                }\">查询</span>\r\n                <span class=\"refresh-btn\" @click=\"resetQuery\">重置</span>\r\n            </div>\r\n            <div class=\"map-container\">\r\n                <div class=\"map-list\">\r\n                    <div class=\"list\" v-for=\"item in mapList\" :key=\"item.id\">\r\n                        <div class=\"title\">{{ item.content }}</div>\r\n                        <div class=\"content\">\r\n                            <img :src=\"item.thumbnail\" alt=\"\">\r\n                            <div class=\"info\">\r\n                                <span>{{ item.time }}</span>\r\n                                <!-- <span> 共享单位：{{ item.provideUnit }}</span> -->\r\n                                <span>资源类型：{{ item.key }}</span>\r\n                                <span>适用范围: {{ item.suitableLoadEngine }}</span>\r\n                                <span>坐标系统: {{ item.coordinateSystem }}</span>\r\n                                <div class=\"btn\" @click=\"toDetail(item)\">\r\n                                    <img src=\"@/assets/images/mainPics/detail.png\" alt=\"\">\r\n                                    详情\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"pageNum\" :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next, jumper\" :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div class=\"map-detail\" v-if=\"!flag\">\r\n            <div class=\"title\">\r\n                <p>\r\n                    <span>{{ this.detailObj.subject }} / </span>\r\n                    <span>{{ this.detailObj.content }}</span>\r\n                </p>\r\n                <p @click=\"backFn\">\r\n                    <img src=\"@/assets/images/mainPics/back.png\" alt=\"\">\r\n                    返回\r\n                </p>\r\n            </div>\r\n            <div class=\"content\">\r\n                <div class=\"map-box\">\r\n                    <div id=\"mapContainer\" style=\" width: 1493px;height: 914px;\" ref=\"mapContainer\">\r\n                        <div class=\"myModalCon\" v-show=\"clickQueryResultDialog\">\r\n                            <!-- title -->\r\n                            <div class=\"title drag__header\">\r\n                                <div>属性信息</div>\r\n                                <div class=\"title-btn\">\r\n                                    <!-- 关闭按钮 -->\r\n                                    <i class=\"el-icon-error close\" @click=\"myModalClose\"></i>\r\n                                </div>\r\n                            </div>\r\n                            <!-- body -->\r\n                            <div class=\"feature\" v-if=\"clickQueryResultFeature != null &&\r\n                                clickQueryResultFeature.attributes != null\r\n                            \">\r\n                                <div v-for=\"(item, key) in Object.keys(clickQueryResultFeature.attributes)\" :key=\"key\">\r\n                                    <div v-if=\"item != 'objectid' &&\r\n                                        item != 'objectid_1' &&\r\n                                        clickQueryResultFeature.attributes[item]\r\n                                    \" class=\"feature-attribute-item\">\r\n                                        <div class=\"feature-label\">{{ formatSearchName(item) }}:</div>\r\n                                        <div class=\"value\">\r\n                                            {{ clickQueryResultFeature.attributes[item] }}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"basemap-list \" :class=\"{ active: basemapListActive }\"\r\n                        @mouseenter=\"basemapListActive = true\" @mouseleave=\"basemapListActive = false\">\r\n                        <span class=\"1\">\r\n                            <img src=\"@/assets/images/mainPics/csdt.png\" alt=\"\"\r\n                                :class=\"{ selected: selectBasemap == 1 }\" @click=\"mapurl('1')\" />\r\n                        </span>\r\n                        <span class=\"2\">\r\n                            <img src=\"@/assets/images/mainPics/ssdt.png\" :class=\"{ selected: selectBasemap == 2 }\"\r\n                                @click=\"mapurl('2')\" />\r\n                        </span>\r\n                        <span class=\"0\">\r\n                            <img src=\"@/assets/images/mainPics/yxdt.png\" alt=\"\"\r\n                                :class=\"{ selected: selectBasemap == 3 }\" @click=\"mapurl('3')\" />\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"infos\">\r\n                    <p>资源名称: {{ this.detailObj.subject }}</p>\r\n                    <p>资源介绍：<br />{{ this.detailObj.description }}</p>\r\n                    <p class=\"info-title\"><span>资源详情：</span></p>\r\n                    <div class=\"detail-info\">\r\n                        <!-- <p>共享单位：{{ this.detailObj.provideUnit }}</p> -->\r\n                        <!-- <p>共享模式：{{\r\n                            this.detailObj.sharePattern == 0 ? \"无条件共享\" :\r\n                                this.detailObj.sharePattern == 1 ? \"普通有条件共享\" : \"资规有条件共享\" }}\r\n                        </p> -->\r\n                        <p>数据范围：{{ this.detailObj.coverage }}</p>\r\n                        <p>坐标系：{{ this.detailObj.coordinateSystem }}</p>\r\n                        <!-- <p>数量：{{ this.detailObj.viewNum }}</p> -->\r\n                        <p>上线时间：{{ this.detailObj.createTime?.split(\" \")[0] }}</p>\r\n                        <!-- <p>采集时间：{{ this.detailObj.releaseTime }}</p>\r\n                        <p>更新时间：{{ this.detailObj.updateTime }}</p> -->\r\n                    </div>\r\n                    <p class=\"info-title\" v-if=\"this.detailObj.content == '几何分析服务'\"><span>基础分析：</span></p>\r\n                    <div class=\"detail-info infos\" style=\"height: 200px;\" v-if=\"this.detailObj.content == '几何分析服务'\">\r\n                        <p :class=\"activeIndex2 == index ? 'list-active' : ''\" v-for=\"(item, index) in activeArr\"\r\n                            @click=\"activeHandle(index)\">\r\n                            {{ item }}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getLayersList, getStatistic } from '@/api/userMenu'\r\n\r\nexport default {\r\n    name: 'specificLayer',\r\n    data() {\r\n        return {\r\n            mapList: [],\r\n            subjectList: [],\r\n            labelList: [],\r\n            statisticList: [],\r\n            btns: [{ name: \"主题\" }, { name: \"类型\" }],\r\n            activeArr: [\"阴影分析：阴影模拟\", \"限高分析：限高模拟\", \"标高分析：标高模拟\"],\r\n            activeIndex2: 0,\r\n            activeIndex: 0,\r\n            total: 0,\r\n            pageSize: 6,\r\n            pageNum: 1,\r\n            inputValue: \"\",\r\n            subject: '',\r\n            key: \"\",\r\n            flag: true,\r\n            detailObj: {},\r\n            fullscreen: false,\r\n            basemapListActive: false,\r\n            selectBasemap: 0,\r\n            basemap: '',\r\n            clickQueryResultDialog: false,\r\n            clickQueryResultFeature: null,\r\n            basemaps2: [\r\n                `/HQGIS/Hosted/ks_hqyxdt_2022/MapServer`,//影像\r\n                `/HQGIS/Hosted/ks_hqssdt_2023_yjdz/MapServer`,//深色\r\n                `/HQGIS/Hosted/ks_hq_csdt_1/MapServer`,//浅色\r\n            ],\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n    mounted() {\r\n        this.layersList();\r\n        this.getStatisticList();\r\n        // this.$nextTick(() => {\r\n\r\n        // })\r\n        //按ESC退出全屏时修改按钮状态\r\n        window.addEventListener(\"resize\", this.windowResize);\r\n    },\r\n\r\n    methods: {\r\n        activeHandle(index) {\r\n            this.activeIndex2 = index;\r\n            //移除之前添加的分析\r\n            this.removeAnalysisWIdget();\r\n            switch (index) {\r\n                case 2:\r\n                    this.kscim.analysis.ElevationAnalysis.addArcgisElevationAnalysis();\r\n                    break;\r\n                case 1:\r\n                    this.kscim.analysis.LimitHighAnalysis.addArcgisLimitHighAnalysis();\r\n                    break;\r\n                case 0:\r\n                    this.kscim.analysis.WeatherAnalysis.addArcgisWeatherAnalysis(null, null, \"top-right\");\r\n                    document.querySelector('#kscim-weather-analysis-toolbar').style.display = \"none\";\r\n                    break;\r\n                default:\r\n                    this.removeAnalysisWIdget();\r\n                    break;\r\n\r\n\r\n            }\r\n        },\r\n        removeAnalysisWIdget() {\r\n            this.kscim.analysis.TransverseAnalysis.removeArcgisTransverseAnalysis();\r\n            this.kscim.analysis.ViewshedAnalysis.removeArcgisViewshedAnalysis();\r\n            this.kscim.analysis.VisibilityAnalysis.removeArcgisVisibilityAnalysis();\r\n            this.kscim.analysis.ProfileAnalysis.removeArcgisProfileAnalysis();\r\n            this.kscim.analysis.WeatherAnalysis.removeArcgisWeatherAnalysis();\r\n            this.kscim.analysis.LimitHighAnalysis.removeArcgisLimitHighAnalysis();\r\n            this.kscim.analysis.ElevationAnalysis.removeArcgisElevationAnalysis();\r\n            if (this.kscim.ArcgisView.environment) {\r\n                this.kscim.ArcgisView.environment.starsEnabled = false;\r\n                this.kscim.ArcgisView.environment.atmosphereEnabled = false;\r\n            }\r\n        },\r\n        resetQuery() {\r\n            this.inputValue = \"\";\r\n            this.pageNum = 1;\r\n            this.layersList();\r\n        },\r\n        toDetail(item) {\r\n            this.flag = false;\r\n            this.detailObj = item;\r\n            let that = this\r\n            //加载arcgis js文件\r\n            that.arcgisscript = document.createElement(\"SCRIPT\");\r\n            that.arcgisscript.setAttribute(\"src\", \"/AIRCIMSCENE/kscim/kscim.js\");\r\n            that.arcgisscript.setAttribute(\"async\", \"\");\r\n            that.arcgisscript.setAttribute(\"defer\", \"\");\r\n            document.body.appendChild(that.arcgisscript);\r\n\r\n            that.arcgisscript.onload = () => {\r\n                that.kscim = new kscim(\"mapContainer\"); //地图初始化,自定义kscim为地图对象\r\n                if (item.loadtype == \"GeomtetryService\") {\r\n                    //加载深色地图\r\n                    // that.loadMapLayer({ loadtype: \"Tile\", url: \"/HQGIS/Hosted/ks_hqssdt_2023_yjdz/MapServer\" })\r\n                    //加载白模\r\n                    that.loadMapLayer({ loadtype: \"SceneServer\", url: \"/HQGIS/Hosted/ks_hqz_bm/SceneServer\" }\r\n                    )\r\n                    that.mapurl('2');\r\n                    that.activeHandle(0);\r\n                } else {\r\n                    that.loadMapLayer(item);\r\n                }\r\n            };\r\n\r\n        },\r\n        mapurl(type) {\r\n            let that = this;\r\n            if (that.basemap) {\r\n                that.kscim.layer.remove(that.basemap)\r\n                that.basemap = ''\r\n            }\r\n            if (that.selectBasemap == type) {\r\n                that.selectBasemap = 0\r\n                return false\r\n            }\r\n            that.selectBasemap = type\r\n            if (type == '3') {\r\n                that.basemap = that.kscim.layer.addArcgisTileLayer({\r\n                    url: that.basemaps2[0]\r\n                })\r\n            }\r\n            else if (type == '1') {\r\n                that.basemap = that.kscim.layer.addArcgisTileLayer({\r\n                    url: that.basemaps2[2]\r\n                })\r\n            }\r\n            else if (type == '2') {\r\n                that.basemap = that.kscim.layer.addArcgisTileLayer({\r\n                    url: that.basemaps2[1]\r\n                })\r\n            }\r\n            if (that.detailObj.loadType == 'multFeatureLayer' || that.detailObj.loadType == 'mapLetteringLayer') {\r\n                for (let i in that.layer) {\r\n                    that.kscim.ArcgisView.map.reorder(that.layer[i], 1000)\r\n                }\r\n            }\r\n            else {\r\n                that.kscim.ArcgisView.map.reorder(that.layer, 1000)\r\n            }\r\n\r\n        },\r\n        loadMapLayer(item) {\r\n            let that = this;\r\n            if (item.loadtype == \"Tile\") {\r\n                that.kscim.init2D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                //添加arcgis tile地图服务\r\n                that.layer = that.kscim.layer.addArcgisTileLayer({\r\n                    url: item.url,\r\n                });\r\n\r\n            } else if (\r\n                item.loadtype == \"IntegratedMeshLayer\"\r\n            ) {\r\n                that.kscim.init3D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                //倾斜摄影\r\n                that.layer = that.kscim.layer.addArcgisIntegratedMeshLayer({\r\n                    url: item.url,\r\n                });\r\n            } else if (item.loadtype == \"MapImageLayer\") {\r\n                that.kscim.init2D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                //添加MapServer\r\n                that.layer = that.kscim.layer.addArcgisMapImageLayer({\r\n                    url: item.url,\r\n                });\r\n                that.layer.when(() => {\r\n                    that.kscim.ArcgisView.goTo(that.layer.fullExtent.extent.expand(4), {\r\n                        speedFactor: 0.5\r\n                    })\r\n                });\r\n            } else if (item.loadtype == \"SceneServer\") {\r\n                that.kscim.init3D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                //三维白模\r\n                that.layer = that.kscim.layer.addArcgisSceneLayer({\r\n                    url: item.url,\r\n                });\r\n            } else if (item.loadtype == \"FeatureLayer\") {\r\n                //初始化预览地图\r\n                that.kscim.init2D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                let url = item.url;\r\n                //要素图层\r\n                that.layer = that.kscim.layer.addArcgisFeatureLayer({\r\n                    url: url,\r\n                    outFields: '*',\r\n                });\r\n                // that.getFeatureCount(url);\r\n            } else if (item.loadtype == \"multFeatureLayer\") {\r\n                //初始化预览地图\r\n                that.kscim.init2D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                let url = item.url;\r\n                //要素图层\r\n                that.layer = that.kscim.layer.addArcgisFeatureSubLayer({\r\n                    url: url,\r\n                });\r\n                // that.getFeatureCount(url);\r\n            } else if (item.loadtype == \"mapLetteringLayer\") {\r\n                //初始化预览地图\r\n                that.kscim.init3D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                that.kscim.widget.addHomeWidget();\r\n                let url = item.url;\r\n                //注记图层\r\n                that.layer = that.kscim.layer.addArcgisFeatureSubLayer({\r\n                    url: url,\r\n                    labelingInfo: [{\r\n                        symbol: {\r\n                            type: \"label-3d\", // autocasts as new LabelSymbol3D()\r\n                            symbolLayers: [\r\n                                {\r\n                                    type: \"text\", // autocasts as new TextSymbol3DLayer()\r\n                                    material: {\r\n                                        color: \"white\",\r\n                                    },\r\n                                    halo: {\r\n                                        color: 'black', // autocasts as Color\r\n                                        size: 1\r\n                                    },\r\n                                    size: '10pt',\r\n                                }\r\n                            ]\r\n                        },\r\n                        labelPlacement: \"above-center\",\r\n                        labelExpressionInfo: {\r\n                            expression: '$feature.name'\r\n                        }\r\n                    }]\r\n                });\r\n                // that.getFeatureCount(url);\r\n            } else if (item.loadtype == \"ClusterLayer\") {\r\n                //初始化预览地图\r\n                that.kscim.init2D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.bigScreen();\r\n                //要素图层\r\n                let url = item.url;\r\n                that.layer =\r\n                    that.kscim.layer.Cluster2DLayer.addArcgis2DClusterLayer(url);\r\n                // that.getFeatureCount(url);\r\n            } else if (\r\n                item.loadtype == \"BuildingSceneLayer\"\r\n            ) {\r\n                //BIM模型\r\n                that.kscim.init3D();\r\n                that.kscim.ArcgisView.ui.remove(\"attribution\");\r\n                that.kscim.widget.addHomeWidget();\r\n                that.bigScreen();\r\n                that.layer = that.kscim.layer.addArcgisBuildingSceneLayer({\r\n                    url: item.url,\r\n                });\r\n                that.layer.when(() => {\r\n                    that.layer.allSublayers.forEach((item) => {\r\n                        item.visible = true;\r\n                    });\r\n                });\r\n            } else {\r\n                this.$message(\"正在开发中...\");\r\n                return;\r\n            }\r\n            that.kscim.ArcgisMap.ground.surfaceColor = {\r\n                r: 23,\r\n                g: 57,\r\n                b: 86,\r\n                a: 1,\r\n            };\r\n            that.kscim.ArcgisView.background = {\r\n                color: { r: 23, g: 57, b: 86, a: 1 },\r\n            };\r\n\r\n\r\n            that.kscim.widget.MapClickEvent.addMapClickEvent(that.clickQueryCallBack)\r\n        },\r\n        clickQueryCallBack(result) {\r\n            if (result.results) {\r\n                const res = result.results[0].graphic;\r\n                //点到白膜或要素点\r\n                if (res.layer.fields) {\r\n                    res.layer.fields.push({ name: \"X\", alias: \"X\" });\r\n                    res.layer.fields.push({ name: \"Y\", alias: \"Y\" });\r\n                }\r\n                if (res.geometry !== null) {\r\n                    res.attributes.x = res.geometry.x;\r\n                    res.attributes.y = res.geometry.y;\r\n                }\r\n                this.clickQueryResultFeature = res;\r\n                this.clickQueryResultDialog = true;\r\n            }\r\n        },\r\n        //关闭属性弹窗\r\n        myModalClose() {\r\n            this.clickQueryResultFeature = null;\r\n            this.clickQueryResultDialog = false;\r\n        },\r\n        //使用别名\r\n        formatSearchName(e) {\r\n            let array = this.clickQueryResultFeature.layer.fields;\r\n            let res = \"\";\r\n            if (e === \"x\" || e === \"X\") return \"经度\";\r\n            if (e === \"y\" || e === \"Y\") return \"纬度\";\r\n\r\n            for (let i in array) {\r\n                if (array[i].name == e) {\r\n                    res = array[i].alias;\r\n                }\r\n            }\r\n            return res || e;\r\n        },\r\n        backFn() {\r\n            this.flag = true;\r\n            document.body.removeChild(this.arcgisscript);\r\n            document.querySelector('.esri-view-root').remove();\r\n            this.kscim = undefined;\r\n            this.arcgisscript = null;\r\n        },\r\n        // 根据资源中心类型筛选数据\r\n        changeList(name) {\r\n            if (this.activeIndex == 0) {\r\n                this.subject = name;\r\n                this.key = \"\";\r\n            } else {\r\n                this.key = name;\r\n                this.subject = \"\";\r\n            }\r\n            this.pageNum = 1;\r\n            this.layersList();\r\n        },\r\n        //资源中心tabs切换\r\n        tabsFn(index) {\r\n            this.activeIndex = index;\r\n            index == 0 ? this.statisticList = this.subjectList : this.statisticList = this.labelList;\r\n        },\r\n        // 图片高亮切换\r\n        setUrl(index) {\r\n            return require(`@/assets/images/mainPics/tabs-bg${index == this.activeIndex ? '-active' : ''}.png`);\r\n        },\r\n        //监听页码并更新数据\r\n        handleCurrentChange(val) {\r\n            this.pageNum = val;\r\n            this.layersList();\r\n        },\r\n        //获取专题图层数据\r\n        layersList() {\r\n            getLayersList({ pageSize: this.pageSize, pageNum: this.pageNum, key: this.key, subject: this.subject, content: this.inputValue }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.mapList = res.data.extra.data;\r\n                    this.total = res.data.extra.total;\r\n                    this.pageNum = res.data.extra.pageNum;\r\n                    this.pageSize = res.data.extra.pageSize;\r\n                }\r\n            })\r\n        },\r\n        //获取主题及标签的数量信息\r\n        getStatisticList() {\r\n            getStatistic().then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.labelList = res.data.extra.label;\r\n                    this.subjectList = res.data.extra.subject;\r\n                    this.statisticList = this.subjectList\r\n                }\r\n            })\r\n        },\r\n        bigScreen() {\r\n            // 在控件中创建全屏展示图标\r\n            let bigBox = document.createElement(\"div\");\r\n            bigBox.classList.add(\"fullScreen\");\r\n            bigBox.style.cursor = \"pointer\"\r\n            bigBox.style.pointerEvents = \"auto\"\r\n            let img = document.createElement(\"img\")\r\n            img.src = require(\"@/assets/images/mainPics/bigScreen.png\");\r\n            bigBox.appendChild(img)\r\n            let esriUi = document.querySelector(\".esri-ui-top-left\");\r\n            esriUi.appendChild(bigBox)\r\n\r\n            let element = document.getElementById(\"mapContainer\");\r\n            let bigScreen = document.querySelector(\".fullScreen\")\r\n            // 实现全屏展示\r\n            bigScreen.addEventListener(\"click\", () => {\r\n                if (this.fullscreen) {\r\n                    if (document.exitFullscreen) {\r\n                        document.exitFullscreen();\r\n                    } else if (document.webkitCancelFullScreen) {\r\n                        document.webkitCancelFullScreen();\r\n                    } else if (document.mozCancelFullScreen) {\r\n                        document.mozCancelFullScreen();\r\n                    } else if (document.msExitFullscreen) {\r\n                        document.msExitFullscreen();\r\n                    }\r\n                } else {\r\n                    if (element.requestFullscreen) {\r\n                        element.requestFullscreen();\r\n                    } else if (element.webkitRequestFullScreen) {\r\n                        element.webkitRequestFullScreen();\r\n                    } else if (element.mozRequestFullScreen) {\r\n                        element.mozRequestFullScreen();\r\n                    } else if (element.msRequestFullscreen) {\r\n                        element.msRequestFullscreen();\r\n                    }\r\n                }\r\n                this.fullscreen = !this.fullscreen;\r\n            })\r\n        },\r\n        // 判断当前是否为全屏状态\r\n        isFullSrceen() {\r\n            let isFull =\r\n                document.fullscreenElement ||\r\n                document.mozFullScreenElement ||\r\n                document.webkitFullscreenElement ||\r\n                document.msFullscreenElement;\r\n            if (!isFull) {\r\n                isFull = false;\r\n            } else {\r\n                isFull = true;\r\n            }\r\n            return isFull;\r\n        },\r\n\r\n        windowResize() {\r\n            let that = this;\r\n            if (!that.isFullSrceen() && that.fullscreen) {\r\n                that.fullscreen = false;\r\n            }\r\n        },\r\n\r\n    },\r\n    beforeDestroy() {\r\n        window.removeEventListener(\"resize\", this.windowResize);\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n// 属性弹窗\r\n.myModalCon {\r\n    position: fixed;\r\n    width: 300px;\r\n    max-height: 460px;\r\n    top: 18vh;\r\n    left: 62vw;\r\n    background: rgba(0, 24, 47, .85);\r\n    border-radius: 3px;\r\n    color: white;\r\n    // z-index: 99999;\r\n\r\n    .title {\r\n        display: flex;\r\n        flex-direction: row;\r\n        padding: 8px 20px;\r\n        justify-content: space-between;\r\n        font-size: 18px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        color: #4b87ff;\r\n        line-height: 22px;\r\n        align-items: center;\r\n        font-weight: 700;\r\n\r\n        .title-btn {\r\n            display: flex;\r\n            color: white;\r\n\r\n            .close,\r\n            .close:hover {\r\n                display: flex;\r\n                justify-content: flex-end;\r\n                cursor: pointer;\r\n                margin-left: 20px;\r\n            }\r\n\r\n            .seeVideo,\r\n            .seeVideo:hover {\r\n                display: flex;\r\n                // justify-content: flex-end;\r\n                width: 20px;\r\n                height: 20px;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n    }\r\n\r\n    .feature {\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n        max-height: 418px;\r\n        align-content: center;\r\n        font-size: 10px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: white;\r\n        line-height: 20px;\r\n        padding: 0 12px 15px 12px;\r\n\r\n        &::-webkit-scrollbar {\r\n            background-color: rgba(0, 0, 0, 0);\r\n            width: 5px;\r\n        }\r\n\r\n        /* 滚动槽 */\r\n        &::-webkit-scrollbar-track {\r\n            border-radius: 10px;\r\n        }\r\n\r\n        /* 滚动条滑块 */\r\n        &::-webkit-scrollbar-thumb {\r\n            background-color: rgb(188, 190, 192);\r\n        }\r\n\r\n        .feature-attribute-item {\r\n            padding: 0px 10px;\r\n            display: flex;\r\n            margin-block-start: 5px;\r\n\r\n            // font-size: 13px;\r\n            // font-family: Source Han Sans CN;\r\n            .value {\r\n                margin-left: 0px;\r\n                display: flex;\r\n                word-break: break-all;\r\n            }\r\n\r\n            .feature-label {\r\n                display: flex;\r\n                color: white !important;\r\n                padding-right: 7px;\r\n                white-space: nowrap;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**示范应用视频弹框样式 */\r\n    ::v-deep .pilotp-application-video-dialog {\r\n        .el-dialog {\r\n            background: rgba(7, 39, 77, 0.8);\r\n            margin-top: 17vh !important;\r\n\r\n            .el-dialog__header {\r\n                background: transparent !important;\r\n                border-bottom: none !important;\r\n\r\n                .el-dialog__title {\r\n                    color: #a5a6aa;\r\n                    border-left: none !important;\r\n                }\r\n\r\n                .dialog-title-buttons {\r\n                    color: #a5a6aa;\r\n\r\n                    svg {\r\n                        color: #a5a6aa;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .video-wrap {\r\n                .placeholder {\r\n                    background: transparent;\r\n                }\r\n            }\r\n\r\n            .video-list {\r\n                margin-top: 0px;\r\n\r\n                li {\r\n                    border: #badafc 1px solid;\r\n\r\n                    .info-name {\r\n                        color: #a5a6aa;\r\n                    }\r\n                }\r\n            }\r\n\r\n            /**滚动条样式 */\r\n            ::v-deep .video-list::-webkit-scrollbar-track {\r\n                background: rgba(12, 51, 101, 0.8);\r\n                border-radius: 2px;\r\n            }\r\n\r\n            ::v-deep .video-list::-webkit-scrollbar-thumb {\r\n                background: #bfbfbf;\r\n                border-radius: 2px;\r\n            }\r\n\r\n            ::v-deep .video-list::-webkit-scrollbar-thumb:hover {\r\n                background: #fff;\r\n            }\r\n\r\n            ::v-deep .video-list::-webkit-scrollbar-corner {\r\n                background: #179a16;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.container {\r\n    width: 100%;\r\n    height: 100%;\r\n    // background: #1F405C;\r\n    display: flex;\r\n    position: relative;\r\n\r\n    .left-list {\r\n        margin-left: 10px;\r\n        margin-top: calc(6vh + 73px);\r\n        width: 343px;\r\n        height: 859px;\r\n        background-image: url(\"@/assets/images/mainPics/left-bg.png\");\r\n        background-size: 100% 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .btns {\r\n            display: flex;\r\n            margin-top: 70px;\r\n\r\n            >p {\r\n                width: 171px;\r\n                height: 38px;\r\n                cursor: pointer;\r\n                position: relative;\r\n\r\n                >img {\r\n                    position: absolute;\r\n                    width: 171px;\r\n                    height: 38px;\r\n                }\r\n\r\n                >span {\r\n                    width: 171px;\r\n                    position: absolute;\r\n                    z-index: 2;\r\n                    font-family: Source Han Sans CN;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FFFFFF;\r\n                    line-height: 38px;\r\n                    text-align: center;\r\n                }\r\n            }\r\n\r\n\r\n        }\r\n\r\n        .statistic-list {\r\n            display: flex;\r\n            flex-direction: column;\r\n            overflow-x: hidden;\r\n            overflow-y: scroll;\r\n            height: 700px;\r\n\r\n            .list {\r\n                cursor: pointer;\r\n                width: calc(100% - 70px);\r\n                height: 29px !important;\r\n                display: flex;\r\n                margin-top: 10px;\r\n                padding: 0 30px;\r\n                margin-left: 5px;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                background: rgba(27, 137, 225, 0.4);\r\n\r\n                >span:nth-child(1) {\r\n                    font-family: SourceHanSansSC;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FFFFFF;\r\n                    height: 29px;\r\n                    line-height: 29px;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    font-family: Source Han Sans CN;\r\n                    font-weight: 400;\r\n                    font-size: 13px;\r\n                    color: #FFFFFF;\r\n                    height: 29px;\r\n                    line-height: 29px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .main-box {\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .header-search {\r\n            margin-left: 10px;\r\n            margin-top: 73px;\r\n            width: 100%;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n\r\n            .title {\r\n                width: 168px;\r\n                height: 33px;\r\n                font-family: GLJ-GBT;\r\n                font-weight: 400;\r\n                font-size: 40px;\r\n                color: #6FBBFC;\r\n                line-height: 18px;\r\n            }\r\n\r\n            :deep(.el-input) {\r\n                margin-left: 76px;\r\n                width: 762px;\r\n                height: 51px;\r\n                background: rgba(0, 180, 255, 0.24);\r\n                border-radius: 4px;\r\n\r\n                .el-input__inner {\r\n                    height: 50px !important;\r\n                    border: none;\r\n                    background-color: transparent;\r\n                    color: #fff;\r\n                }\r\n\r\n                .el-input__inner::-webkit-input-placeholder {\r\n                    color: #fff\r\n                }\r\n\r\n                .el-icon-search {\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                }\r\n            }\r\n\r\n\r\n            >span {\r\n                width: 122px;\r\n                height: 51px;\r\n\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 500;\r\n                font-size: 21px;\r\n                color: #FFFFFF;\r\n                line-height: 51px;\r\n                text-align: center;\r\n                margin-left: 17px;\r\n                cursor: pointer;\r\n            }\r\n\r\n            .search-btn {\r\n                background: #007FEA;\r\n            }\r\n\r\n            .refresh-btn {\r\n                background: #07c9b2;\r\n            }\r\n\r\n        }\r\n\r\n        .map-container {\r\n            width: 1657px;\r\n            height: 859px;\r\n            margin-top: 15px;\r\n            margin-right: 22px;\r\n            margin-bottom: 40px;\r\n            display: flex;\r\n            flex-direction: column;\r\n            margin-left: 5px;\r\n            // flex: 1;\r\n            background: rgba(18, 76, 111, 0.45);\r\n            border-radius: 5px;\r\n\r\n            .map-list {\r\n                padding: 0 35px;\r\n                width: 1500px;\r\n                height: 100%;\r\n                display: flex;\r\n                flex-wrap: wrap;\r\n\r\n                .list {\r\n                    margin-top: 46px;\r\n                    width: 28%;\r\n                    height: 317px;\r\n                    background: #28688D;\r\n                    border-radius: 5px;\r\n                    border: 2px solid #ABDADF;\r\n                    margin-left: 22px;\r\n                    // margin-bottom: 109px;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    padding: 0 25px;\r\n\r\n                    .title {\r\n                        font-family: Source Han Sans CN;\r\n                        font-weight: 500;\r\n                        font-size: 15px;\r\n                        color: #FFFFFF;\r\n                        padding: 18px 0;\r\n                        border-bottom: 1px solid #ABDADF;\r\n                    }\r\n\r\n                    .content {\r\n                        display: flex;\r\n                        padding-top: 21px;\r\n                        position: relative;\r\n\r\n                        >img {\r\n                            width: 250px;\r\n                            height: 200px;\r\n                        }\r\n\r\n                        .info {\r\n                            display: flex;\r\n                            flex-direction: column;\r\n\r\n                            >span {\r\n                                font-family: Source Han Sans CN;\r\n                                font-weight: 500;\r\n                                font-size: 13px;\r\n                                color: #D1DEE2;\r\n                                line-height: 25px;\r\n                                white-space: nowrap;\r\n                                margin-left: 27px;\r\n\r\n                            }\r\n\r\n                            .btn {\r\n                                margin-top: 23px;\r\n                                margin-left: 37px;\r\n                                width: 98px;\r\n                                height: 36px;\r\n                                background: #00D0FF;\r\n                                border-radius: 4px;\r\n                                display: flex;\r\n                                align-items: center;\r\n                                font-family: Source Han Sans CN;\r\n                                font-weight: 500;\r\n                                font-size: 15px;\r\n                                color: #FFFFFF;\r\n                                justify-content: center;\r\n                                cursor: pointer;\r\n\r\n                                >img {\r\n                                    width: 16px;\r\n                                    height: 16px;\r\n                                    margin-right: 10px;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                }\r\n            }\r\n\r\n\r\n            :deep(.el-pagination) {\r\n                margin-bottom: 30px;\r\n                text-align: center;\r\n\r\n                .el-pagination__total {\r\n                    color: #fff;\r\n                }\r\n\r\n                .el-pagination__sizes {\r\n                    input {\r\n                        background: #072C44;\r\n                        border-radius: 4px;\r\n                        border: 1px solid #4BDBFF;\r\n                        color: #fff;\r\n                    }\r\n                }\r\n\r\n                >button {\r\n                    color: #fff;\r\n                    background-color: transparent;\r\n                }\r\n\r\n                ul {\r\n                    li {\r\n                        background-color: transparent;\r\n                        color: #fff;\r\n                    }\r\n                }\r\n\r\n                .el-pager .active {\r\n                    color: #4BDBFF;\r\n                }\r\n\r\n                .el-pagination__jump {\r\n                    color: #fff;\r\n\r\n                    input {\r\n                        background: #072C44;\r\n                        border-radius: 4px;\r\n                        border: 1px solid #4BDBFF;\r\n                        color: #4BDBFF;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n\r\n    }\r\n\r\n    .map-detail {\r\n        position: absolute;\r\n        left: 20px;\r\n        top: 112px;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        height: 100%;\r\n        background: #173956;\r\n        border-radius: 5px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .title {\r\n            display: flex;\r\n            padding: 30px 23px;\r\n            // align-items: center;\r\n            justify-content: space-between;\r\n\r\n            >p {\r\n                display: flex;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 18px;\r\n                line-height: 25px;\r\n\r\n                >span:nth-child(1) {\r\n                    color: #D1DEE2;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n                    color: #75B9FF;\r\n                }\r\n\r\n\r\n            }\r\n\r\n            >p:nth-child(2) {\r\n                width: 98px;\r\n                height: 36px;\r\n                background: #00D0FF;\r\n                border-radius: 4px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                cursor: pointer;\r\n\r\n                >img {\r\n                    width: 19px;\r\n                    height: 17px;\r\n                }\r\n            }\r\n        }\r\n\r\n        .content {\r\n            display: flex;\r\n\r\n            .infos {\r\n                width: 455px;\r\n                margin-left: 26px;\r\n                display: flex;\r\n                flex-direction: column;\r\n\r\n                >p {\r\n                    width: 100%;\r\n                    font-family: Source Han Sans CN;\r\n                    font-weight: 500;\r\n                    font-size: 18px;\r\n                    color: #D1DEE2;\r\n                    line-height: 20px;\r\n                    margin-bottom: 15px;\r\n                }\r\n\r\n                >p:nth-child(2) {\r\n                    height: 180px;\r\n                }\r\n\r\n                .info-title {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    height: 20px;\r\n                    margin-top: 25px;\r\n\r\n                    >span:nth-child(2) {\r\n                        font-family: Source Han Sans CN;\r\n                        font-weight: 400;\r\n                        font-size: 18px;\r\n                        color: #75B9FF;\r\n                        line-height: 25px;\r\n                    }\r\n                }\r\n\r\n                .detail-info {\r\n                    margin-left: 5px;\r\n                    // width: 420px;\r\n                    height: 303px;\r\n                    background: #103452;\r\n                    border-radius: 5px;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n\r\n                    >p {\r\n                        padding: 0 35px;\r\n                        width: 100%;\r\n                        font-family: Source Han Sans CN;\r\n                        font-weight: 500;\r\n                        font-size: 18px;\r\n                        color: #D1DEE2;\r\n                        line-height: 25px;\r\n                        margin-top: 28px;\r\n                    }\r\n                }\r\n\r\n                .infos {\r\n                    p {\r\n                        width: calc(100% - 70px);\r\n                        cursor: pointer;\r\n                        height: 40px;\r\n                        line-height: 40px;\r\n                        margin-top: 0px;\r\n                    }\r\n\r\n                    .list-active {\r\n                        background-color: #072C44;\r\n\r\n                    }\r\n                }\r\n            }\r\n\r\n            .map-box {\r\n                // position: relative;\r\n                width: 1493px;\r\n                height: 914px;\r\n\r\n                #mapContainer {\r\n                    width: 1493px;\r\n                    height: 914px;\r\n                    // position: absolute;\r\n                    // z-index: 2 !important;\r\n                }\r\n            }\r\n\r\n            .basemap-list {\r\n                position: absolute;\r\n                width: 86px;\r\n                height: 60px;\r\n                right: 50px;\r\n                bottom: 100px;\r\n                z-index: 2;\r\n\r\n                span {\r\n                    position: absolute;\r\n                    width: 90px;\r\n                    height: 60px;\r\n                    transition: 0.2s right ease, 0.2s z-index ease;\r\n                    cursor: pointer;\r\n\r\n\r\n                    img {\r\n                        position: relative;\r\n                        width: 86px;\r\n                        height: 60px;\r\n                        border: 2px solid white;\r\n                        box-shadow: 0 0 4px #d1d1d1;\r\n                        cursor: pointer;\r\n                    }\r\n\r\n                    img.selected {\r\n                        border: 2px solid #4b87ff;\r\n                    }\r\n                }\r\n\r\n                span:nth-child(1) {\r\n                    right: 0px;\r\n                    z-index: 4;\r\n                }\r\n\r\n                span:nth-child(2) {\r\n                    right: 24px;\r\n                    z-index: 3;\r\n                }\r\n\r\n                span:nth-child(3) {\r\n                    right: 48px;\r\n                    z-index: 2;\r\n                }\r\n\r\n                // span:nth-child(4) {\r\n                //     right: 36px;\r\n                //     z-index: 1;\r\n                // }\r\n            }\r\n\r\n            .basemap-list.active {\r\n                span:nth-child(1) {\r\n                    right: 0px !important;\r\n                }\r\n\r\n                span:nth-child(2) {\r\n                    right: 90px !important;\r\n                }\r\n\r\n                span:nth-child(3) {\r\n                    right: 180px !important;\r\n                }\r\n\r\n                span:nth-child(4) {\r\n                    right: 270px !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        :deep(.esri-view-root) {\r\n            canvas {\r\n                // background: #173956;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAiJA,SAAAA,aAAA,EAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;MACAC,IAAA;QAAAN,IAAA;MAAA;QAAAA,IAAA;MAAA;MACAO,SAAA;MACAC,YAAA;MACAC,WAAA;MACAC,KAAA;MACAC,QAAA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,SAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,sBAAA;MACAC,uBAAA;MACAC,SAAA,GACA;MAAA;MACA;MAAA;MACA;MAAA;IAEA;EACA;EACAC,UAAA,GACA;EACAC,KAAA,GAEA;EACAC,QAAA,GAEA;EACAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,gBAAA;IACA;;IAEA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EAEAC,OAAA;IACAC,aAAAC,KAAA;MACA,KAAA5B,YAAA,GAAA4B,KAAA;MACA;MACA,KAAAC,oBAAA;MACA,QAAAD,KAAA;QACA;UACA,KAAAE,KAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,0BAAA;UACA;QACA;UACA,KAAAH,KAAA,CAAAC,QAAA,CAAAG,iBAAA,CAAAC,0BAAA;UACA;QACA;UACA,KAAAL,KAAA,CAAAC,QAAA,CAAAK,eAAA,CAAAC,wBAAA;UACAC,QAAA,CAAAC,aAAA,oCAAAC,KAAA,CAAAC,OAAA;UACA;QACA;UACA,KAAAZ,oBAAA;UACA;MAGA;IACA;IACAA,qBAAA;MACA,KAAAC,KAAA,CAAAC,QAAA,CAAAW,kBAAA,CAAAC,8BAAA;MACA,KAAAb,KAAA,CAAAC,QAAA,CAAAa,gBAAA,CAAAC,4BAAA;MACA,KAAAf,KAAA,CAAAC,QAAA,CAAAe,kBAAA,CAAAC,8BAAA;MACA,KAAAjB,KAAA,CAAAC,QAAA,CAAAiB,eAAA,CAAAC,2BAAA;MACA,KAAAnB,KAAA,CAAAC,QAAA,CAAAK,eAAA,CAAAc,2BAAA;MACA,KAAApB,KAAA,CAAAC,QAAA,CAAAG,iBAAA,CAAAiB,6BAAA;MACA,KAAArB,KAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAoB,6BAAA;MACA,SAAAtB,KAAA,CAAAuB,UAAA,CAAAC,WAAA;QACA,KAAAxB,KAAA,CAAAuB,UAAA,CAAAC,WAAA,CAAAC,YAAA;QACA,KAAAzB,KAAA,CAAAuB,UAAA,CAAAC,WAAA,CAAAE,iBAAA;MACA;IACA;IACAC,WAAA;MACA,KAAApD,UAAA;MACA,KAAAD,OAAA;MACA,KAAAiB,UAAA;IACA;IACAqC,SAAAC,IAAA;MACA,KAAAnD,IAAA;MACA,KAAAC,SAAA,GAAAkD,IAAA;MACA,IAAAC,IAAA;MACA;MACAA,IAAA,CAAAC,YAAA,GAAAvB,QAAA,CAAAwB,aAAA;MACAF,IAAA,CAAAC,YAAA,CAAAE,YAAA;MACAH,IAAA,CAAAC,YAAA,CAAAE,YAAA;MACAH,IAAA,CAAAC,YAAA,CAAAE,YAAA;MACAzB,QAAA,CAAA0B,IAAA,CAAAC,WAAA,CAAAL,IAAA,CAAAC,YAAA;MAEAD,IAAA,CAAAC,YAAA,CAAAK,MAAA;QACAN,IAAA,CAAA9B,KAAA,OAAAA,KAAA;QACA,IAAA6B,IAAA,CAAAQ,QAAA;UACA;UACA;UACA;UACAP,IAAA,CAAAQ,YAAA;YAAAD,QAAA;YAAAE,GAAA;UAAA,CACA;UACAT,IAAA,CAAAU,MAAA;UACAV,IAAA,CAAAjC,YAAA;QACA;UACAiC,IAAA,CAAAQ,YAAA,CAAAT,IAAA;QACA;MACA;IAEA;IACAW,OAAAC,IAAA;MACA,IAAAX,IAAA;MACA,IAAAA,IAAA,CAAA/C,OAAA;QACA+C,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAC,MAAA,CAAAb,IAAA,CAAA/C,OAAA;QACA+C,IAAA,CAAA/C,OAAA;MACA;MACA,IAAA+C,IAAA,CAAAhD,aAAA,IAAA2D,IAAA;QACAX,IAAA,CAAAhD,aAAA;QACA;MACA;MACAgD,IAAA,CAAAhD,aAAA,GAAA2D,IAAA;MACA,IAAAA,IAAA;QACAX,IAAA,CAAA/C,OAAA,GAAA+C,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAE,kBAAA;UACAL,GAAA,EAAAT,IAAA,CAAA5C,SAAA;QACA;MACA,OACA,IAAAuD,IAAA;QACAX,IAAA,CAAA/C,OAAA,GAAA+C,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAE,kBAAA;UACAL,GAAA,EAAAT,IAAA,CAAA5C,SAAA;QACA;MACA,OACA,IAAAuD,IAAA;QACAX,IAAA,CAAA/C,OAAA,GAAA+C,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAE,kBAAA;UACAL,GAAA,EAAAT,IAAA,CAAA5C,SAAA;QACA;MACA;MACA,IAAA4C,IAAA,CAAAnD,SAAA,CAAAkE,QAAA,0BAAAf,IAAA,CAAAnD,SAAA,CAAAkE,QAAA;QACA,SAAAC,CAAA,IAAAhB,IAAA,CAAAY,KAAA;UACAZ,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAAwB,GAAA,CAAAC,OAAA,CAAAlB,IAAA,CAAAY,KAAA,CAAAI,CAAA;QACA;MACA,OACA;QACAhB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAAwB,GAAA,CAAAC,OAAA,CAAAlB,IAAA,CAAAY,KAAA;MACA;IAEA;IACAJ,aAAAT,IAAA;MACA,IAAAC,IAAA;MACA,IAAAD,IAAA,CAAAQ,QAAA;QACAP,IAAA,CAAA9B,KAAA,CAAAiD,MAAA;QACAnB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA;QACArB,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAE,kBAAA;UACAL,GAAA,EAAAV,IAAA,CAAAU;QACA;MAEA,WACAV,IAAA,CAAAQ,QAAA,2BACA;QACAP,IAAA,CAAA9B,KAAA,CAAAoD,MAAA;QACAtB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA;QACArB,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAW,4BAAA;UACAd,GAAA,EAAAV,IAAA,CAAAU;QACA;MACA,WAAAV,IAAA,CAAAQ,QAAA;QACAP,IAAA,CAAA9B,KAAA,CAAAiD,MAAA;QACAnB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA;QACArB,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAY,sBAAA;UACAf,GAAA,EAAAV,IAAA,CAAAU;QACA;QACAT,IAAA,CAAAY,KAAA,CAAAa,IAAA;UACAzB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAAiC,IAAA,CAAA1B,IAAA,CAAAY,KAAA,CAAAe,UAAA,CAAAC,MAAA,CAAAC,MAAA;YACAC,WAAA;UACA;QACA;MACA,WAAA/B,IAAA,CAAAQ,QAAA;QACAP,IAAA,CAAA9B,KAAA,CAAAoD,MAAA;QACAtB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA;QACArB,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAmB,mBAAA;UACAtB,GAAA,EAAAV,IAAA,CAAAU;QACA;MACA,WAAAV,IAAA,CAAAQ,QAAA;QACA;QACAP,IAAA,CAAA9B,KAAA,CAAAiD,MAAA;QACAnB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA,IAAAZ,GAAA,GAAAV,IAAA,CAAAU,GAAA;QACA;QACAT,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAoB,qBAAA;UACAvB,GAAA,EAAAA,GAAA;UACAwB,SAAA;QACA;QACA;MACA,WAAAlC,IAAA,CAAAQ,QAAA;QACA;QACAP,IAAA,CAAA9B,KAAA,CAAAiD,MAAA;QACAnB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA,IAAAZ,GAAA,GAAAV,IAAA,CAAAU,GAAA;QACA;QACAT,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAsB,wBAAA;UACAzB,GAAA,EAAAA;QACA;QACA;MACA,WAAAV,IAAA,CAAAQ,QAAA;QACA;QACAP,IAAA,CAAA9B,KAAA,CAAAoD,MAAA;QACAtB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACArB,IAAA,CAAA9B,KAAA,CAAAiE,MAAA,CAAAC,aAAA;QACA,IAAA3B,GAAA,GAAAV,IAAA,CAAAU,GAAA;QACA;QACAT,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAsB,wBAAA;UACAzB,GAAA,EAAAA,GAAA;UACA4B,YAAA;YACAC,MAAA;cACA3B,IAAA;cAAA;cACA4B,YAAA,GACA;gBACA5B,IAAA;gBAAA;gBACA6B,QAAA;kBACAC,KAAA;gBACA;gBACAC,IAAA;kBACAD,KAAA;kBAAA;kBACAE,IAAA;gBACA;gBACAA,IAAA;cACA;YAEA;YACAC,cAAA;YACAC,mBAAA;cACAC,UAAA;YACA;UACA;QACA;QACA;MACA,WAAA/C,IAAA,CAAAQ,QAAA;QACA;QACAP,IAAA,CAAA9B,KAAA,CAAAiD,MAAA;QACAnB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAAqB,SAAA;QACA;QACA,IAAAZ,GAAA,GAAAV,IAAA,CAAAU,GAAA;QACAT,IAAA,CAAAY,KAAA,GACAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAmC,cAAA,CAAAC,uBAAA,CAAAvC,GAAA;QACA;MACA,WACAV,IAAA,CAAAQ,QAAA,0BACA;QACA;QACAP,IAAA,CAAA9B,KAAA,CAAAoD,MAAA;QACAtB,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAA2B,EAAA,CAAAP,MAAA;QACAb,IAAA,CAAA9B,KAAA,CAAAiE,MAAA,CAAAC,aAAA;QACApC,IAAA,CAAAqB,SAAA;QACArB,IAAA,CAAAY,KAAA,GAAAZ,IAAA,CAAA9B,KAAA,CAAA0C,KAAA,CAAAqC,2BAAA;UACAxC,GAAA,EAAAV,IAAA,CAAAU;QACA;QACAT,IAAA,CAAAY,KAAA,CAAAa,IAAA;UACAzB,IAAA,CAAAY,KAAA,CAAAsC,YAAA,CAAAC,OAAA,CAAApD,IAAA;YACAA,IAAA,CAAAqD,OAAA;UACA;QACA;MACA;QACA,KAAAC,QAAA;QACA;MACA;MACArD,IAAA,CAAA9B,KAAA,CAAAoF,SAAA,CAAAC,MAAA,CAAAC,YAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;MACA;MACA5D,IAAA,CAAA9B,KAAA,CAAAuB,UAAA,CAAAoE,UAAA;QACApB,KAAA;UAAAgB,CAAA;UAAAC,CAAA;UAAAC,CAAA;UAAAC,CAAA;QAAA;MACA;MAGA5D,IAAA,CAAA9B,KAAA,CAAAiE,MAAA,CAAA2B,aAAA,CAAAC,gBAAA,CAAA/D,IAAA,CAAAgE,kBAAA;IACA;IACAA,mBAAAC,MAAA;MACA,IAAAA,MAAA,CAAAC,OAAA;QACA,MAAAC,GAAA,GAAAF,MAAA,CAAAC,OAAA,IAAAE,OAAA;QACA;QACA,IAAAD,GAAA,CAAAvD,KAAA,CAAAyD,MAAA;UACAF,GAAA,CAAAvD,KAAA,CAAAyD,MAAA,CAAAC,IAAA;YAAA1I,IAAA;YAAA2I,KAAA;UAAA;UACAJ,GAAA,CAAAvD,KAAA,CAAAyD,MAAA,CAAAC,IAAA;YAAA1I,IAAA;YAAA2I,KAAA;UAAA;QACA;QACA,IAAAJ,GAAA,CAAAK,QAAA;UACAL,GAAA,CAAAM,UAAA,CAAAC,CAAA,GAAAP,GAAA,CAAAK,QAAA,CAAAE,CAAA;UACAP,GAAA,CAAAM,UAAA,CAAAE,CAAA,GAAAR,GAAA,CAAAK,QAAA,CAAAG,CAAA;QACA;QACA,KAAAxH,uBAAA,GAAAgH,GAAA;QACA,KAAAjH,sBAAA;MACA;IACA;IACA;IACA0H,aAAA;MACA,KAAAzH,uBAAA;MACA,KAAAD,sBAAA;IACA;IACA;IACA2H,iBAAAC,CAAA;MACA,IAAAC,KAAA,QAAA5H,uBAAA,CAAAyD,KAAA,CAAAyD,MAAA;MACA,IAAAF,GAAA;MACA,IAAAW,CAAA,YAAAA,CAAA;MACA,IAAAA,CAAA,YAAAA,CAAA;MAEA,SAAA9D,CAAA,IAAA+D,KAAA;QACA,IAAAA,KAAA,CAAA/D,CAAA,EAAApF,IAAA,IAAAkJ,CAAA;UACAX,GAAA,GAAAY,KAAA,CAAA/D,CAAA,EAAAuD,KAAA;QACA;MACA;MACA,OAAAJ,GAAA,IAAAW,CAAA;IACA;IACAE,OAAA;MACA,KAAApI,IAAA;MACA8B,QAAA,CAAA0B,IAAA,CAAA6E,WAAA,MAAAhF,YAAA;MACAvB,QAAA,CAAAC,aAAA,oBAAAkC,MAAA;MACA,KAAA3C,KAAA,GAAAgH,SAAA;MACA,KAAAjF,YAAA;IACA;IACA;IACAkF,WAAAvJ,IAAA;MACA,SAAAS,WAAA;QACA,KAAAK,OAAA,GAAAd,IAAA;QACA,KAAAe,GAAA;MACA;QACA,KAAAA,GAAA,GAAAf,IAAA;QACA,KAAAc,OAAA;MACA;MACA,KAAAF,OAAA;MACA,KAAAiB,UAAA;IACA;IACA;IACA2H,OAAApH,KAAA;MACA,KAAA3B,WAAA,GAAA2B,KAAA;MACAA,KAAA,aAAA/B,aAAA,QAAAF,WAAA,QAAAE,aAAA,QAAAD,SAAA;IACA;IACA;IACAqJ,OAAArH,KAAA;MACA,OAAAsH,OAAA,oCAAAtH,KAAA,SAAA3B,WAAA;IACA;IACA;IACAkJ,oBAAAC,GAAA;MACA,KAAAhJ,OAAA,GAAAgJ,GAAA;MACA,KAAA/H,UAAA;IACA;IACA;IACAA,WAAA;MACA/B,aAAA;QAAAa,QAAA,OAAAA,QAAA;QAAAC,OAAA,OAAAA,OAAA;QAAAG,GAAA,OAAAA,GAAA;QAAAD,OAAA,OAAAA,OAAA;QAAA+I,OAAA,OAAAhJ;MAAA,GAAAiJ,IAAA,CAAAvB,GAAA;QACA,IAAAA,GAAA,CAAAtI,IAAA,CAAA8J,IAAA;UACA,KAAA7J,OAAA,GAAAqI,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAA/J,IAAA;UACA,KAAAS,KAAA,GAAA6H,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAAtJ,KAAA;UACA,KAAAE,OAAA,GAAA2H,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAApJ,OAAA;UACA,KAAAD,QAAA,GAAA4H,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAArJ,QAAA;QACA;MACA;IACA;IACA;IACAmB,iBAAA;MACA/B,YAAA,GAAA+J,IAAA,CAAAvB,GAAA;QACA,IAAAA,GAAA,CAAAtI,IAAA,CAAA8J,IAAA;UACA,KAAA3J,SAAA,GAAAmI,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAAC,KAAA;UACA,KAAA9J,WAAA,GAAAoI,GAAA,CAAAtI,IAAA,CAAA+J,KAAA,CAAAlJ,OAAA;UACA,KAAAT,aAAA,QAAAF,WAAA;QACA;MACA;IACA;IACAsF,UAAA;MACA;MACA,IAAAyE,MAAA,GAAApH,QAAA,CAAAwB,aAAA;MACA4F,MAAA,CAAAC,SAAA,CAAAC,GAAA;MACAF,MAAA,CAAAlH,KAAA,CAAAqH,MAAA;MACAH,MAAA,CAAAlH,KAAA,CAAAsH,aAAA;MACA,IAAAC,GAAA,GAAAzH,QAAA,CAAAwB,aAAA;MACAiG,GAAA,CAAAC,GAAA,GAAAd,OAAA;MACAQ,MAAA,CAAAzF,WAAA,CAAA8F,GAAA;MACA,IAAAE,MAAA,GAAA3H,QAAA,CAAAC,aAAA;MACA0H,MAAA,CAAAhG,WAAA,CAAAyF,MAAA;MAEA,IAAAQ,OAAA,GAAA5H,QAAA,CAAA6H,cAAA;MACA,IAAAlF,SAAA,GAAA3C,QAAA,CAAAC,aAAA;MACA;MACA0C,SAAA,CAAAzD,gBAAA;QACA,SAAAd,UAAA;UACA,IAAA4B,QAAA,CAAA8H,cAAA;YACA9H,QAAA,CAAA8H,cAAA;UACA,WAAA9H,QAAA,CAAA+H,sBAAA;YACA/H,QAAA,CAAA+H,sBAAA;UACA,WAAA/H,QAAA,CAAAgI,mBAAA;YACAhI,QAAA,CAAAgI,mBAAA;UACA,WAAAhI,QAAA,CAAAiI,gBAAA;YACAjI,QAAA,CAAAiI,gBAAA;UACA;QACA;UACA,IAAAL,OAAA,CAAAM,iBAAA;YACAN,OAAA,CAAAM,iBAAA;UACA,WAAAN,OAAA,CAAAO,uBAAA;YACAP,OAAA,CAAAO,uBAAA;UACA,WAAAP,OAAA,CAAAQ,oBAAA;YACAR,OAAA,CAAAQ,oBAAA;UACA,WAAAR,OAAA,CAAAS,mBAAA;YACAT,OAAA,CAAAS,mBAAA;UACA;QACA;QACA,KAAAjK,UAAA,SAAAA,UAAA;MACA;IACA;IACA;IACAkK,aAAA;MACA,IAAAC,MAAA,GACAvI,QAAA,CAAAwI,iBAAA,IACAxI,QAAA,CAAAyI,oBAAA,IACAzI,QAAA,CAAA0I,uBAAA,IACA1I,QAAA,CAAA2I,mBAAA;MACA,KAAAJ,MAAA;QACAA,MAAA;MACA;QACAA,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IAEApJ,aAAA;MACA,IAAAmC,IAAA;MACA,KAAAA,IAAA,CAAAgH,YAAA,MAAAhH,IAAA,CAAAlD,UAAA;QACAkD,IAAA,CAAAlD,UAAA;MACA;IACA;EAEA;EACAwK,cAAA;IACA3J,MAAA,CAAA4J,mBAAA,gBAAA1J,YAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}