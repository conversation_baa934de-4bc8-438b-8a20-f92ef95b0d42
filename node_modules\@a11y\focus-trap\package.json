{"name": "@a11y/focus-trap", "version": "1.0.5", "license": "MIT", "module": "index.js", "author": "<PERSON>", "description": "A lightweight web component that traps focus within a DOM node", "bugs": {"url": "https://github.com/andreasbm/focus-trap/issues"}, "publishConfig": {"access": "public"}, "homepage": "https://github.com/andreasbm/focus-trap#readme", "repository": {"type": "git", "url": "git+https://github.com/andreasbm/focus-trap.git"}, "keywords": ["custom", "elements", "web", "component", "custom element", "web component", "focus", "accessibility", "dialog", "focus trap", "trap"], "main": "index.js", "types": "index.d.ts", "scripts": {"ncu": "ncu -u -a && npm update && npm install", "test": "karma start", "b:dev": "rollup -c --environment NODE_ENV:dev", "b:prod": "rollup -c --environment NODE_ENV:prod", "s:dev": "rollup -c --watch --environment NODE_ENV:dev", "s:prod": "rollup -c --watch --environment NODE_ENV:prod", "s": "npm run s:dev", "start": "npm run s", "b:lib": "node pre-build.js && tsc -p tsconfig.build.json", "readme": "node node_modules/.bin/readme generate", "postversion": "npm run readme && npm run b:lib", "publish:patch": "np patch --contents=dist --no-cleanup", "publish:minor": "np minor --contents=dist --no-cleanup", "publish:major": "np major --contents=dist --no-cleanup"}, "devDependencies": {"@appnest/readme": "^1.2.5", "@appnest/web-config": "0.4.33"}}