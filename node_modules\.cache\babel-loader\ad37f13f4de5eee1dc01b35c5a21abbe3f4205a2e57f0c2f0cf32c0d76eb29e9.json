{"ast": null, "code": "import createInfoWindow from '@/utils/amap.js';\nimport genJson from '@/assets/json/geo.json';\nimport { getCimLayerInfo } from '@/api/userMenu';\nexport default {\n  name: 'MapView',\n  data() {\n    return {\n      map: null,\n      district: {},\n      markers: [],\n      mapMarkersList: [],\n      cluster: '',\n      infoWindow: null,\n      winTitle: \"\",\n      markList: [],\n      index: ''\n    };\n  },\n  props: {\n    layerCoordinateInfo: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n  created() {\n    window._AMapSecurityConfig = {\n      securityJsCode: \"7bb052192dcec8116e3213ccbeece92d\"\n    };\n  },\n  watch: {\n    layerCoordinateInfo(nv) {\n      this.index = nv.index;\n      if (nv.layers) {\n        let layerList = nv.layers;\n        layerList.forEach(ele => {\n          this.addPoints(ele, nv.isFlag);\n        });\n      } else {\n        this.addPoints(nv, nv.isFlag);\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.initMap();\n      }, 1000);\n    });\n    // this.initMap()\n  },\n  destroyed() {\n    if (this.map) {\n      this.map.destroy();\n      this.map = null;\n    }\n  },\n  methods: {\n    initMap() {\n      // this.district = new AMap.DistrictSearch({\n      //     subdistrict: 3,\n      //     extensions: 'all',\n      //     level: 'city'\n      // })\n      // this.district.search('昆山市', (status, result) => {\n      //     var bounds = result.districtList[0]['boundaries'];\n      //     var mask = [];\n      //     for (var i = 0; i < bounds.length; i++) {\n      //         mask.push([bounds[i]]);\n      //     }\n\n      //添加描边\n      // for (var i = 0; i < bounds.length; i++) {\n      //     new AMap.Polyline({\n      //         path: bounds[i],\n      //         strokeColor: '#3078AC',\n      //         strokeWeight: 2,\n      //         map: this.map\n      //     })\n      // }\n\n      // });\n      // var outer = [\n      //     new AMap.LngLat(-360, 90, true),\n      //     new AMap.LngLat(-360, -90, true),\n      //     new AMap.LngLat(360, -90, true),\n      //     new AMap.LngLat(360, 90, true),\n      // ];\n      // var pathArray = [\n      //     outer,\n      // ];\n      // pathArray.push(genJson);\n\n      //实例化地图\n      this.map = new AMap.Map('mapContainer', {\n        zoom: 14,\n        //设置当前显示级别\n        expandZoomRange: true,\n        //开启显示范围设置\n        zooms: [7, 20],\n        //最小显示级别为7，最大显示级别为20\n        center: [121.1, 31.29705],\n        // center: [121.073722,31.276133],//万科\n        viewMode: '2D',\n        //这里特别注意，设置为3D则其它地区不显示\n        zoomEnable: true,\n        //是否可以缩放地图\n        resizeEnable: true,\n        mapStyle: \"amap://styles/white\"\n      });\n      let polygon = new AMap.Polygon({\n        bubble: true,\n        path: genJson,\n        fillColor: '#b2cefe',\n        strokeOpacity: .5,\n        fillOpacity: .3,\n        strokeColor: '#1f4b96',\n        strokeWeight: 2,\n        strokeStyle: 'dashed',\n        strokeDasharray: [5, 5]\n      });\n      this.map.add(polygon);\n    },\n    addPoints(nv, flag) {\n      if (nv.cacheUrlJ02 && flag) {\n        let cacheUrlJ02 = nv.cacheUrlJ02.split('api')[1];\n        getCimLayerInfo(cacheUrlJ02).then(res => {\n          if (res.data != '') {\n            let list = res.data.features; //[0].geometry.coordinates\n            let lnglats = null;\n            let marker = null;\n            this.infoWindow = new AMap.InfoWindow({\n              anchor: \"top-left\",\n              offset: new AMap.Pixel(0, -30)\n            });\n            var startIcon = new AMap.Icon({\n              // 图标尺寸\n              size: new AMap.Size(10, 10),\n              // 图标的取图地址\n              // image: 'https://webapi.amap.com/images/mass/mass0.png',\n              image: '/icons/splice' + this.index * 1 + '.png',\n              // 图标所用图片大小\n              imageSize: new AMap.Size(10, 10),\n              // // 图标取图偏移量\n              imageOffset: new AMap.Pixel(0, 0)\n            });\n            list.forEach(item => {\n              let lg = item.geometry.coordinates;\n              // AMap.convertFrom(lg, '84', (status, result) => {\n              //     console.log(result, count++);\n              //     if (result.info === 'ok') {\n              //         item.geometry.lnglat = result.locations; // 转换后的高德坐标 Array.<LngLat> \n              //     }\n              // });\n\n              marker = new AMap.Marker({\n                position: lg,\n                // content: content,  //点标记显示内容，可以是HTML要素字符串或者HTML DOM对象。content有效时，icon属性将被覆盖\n                offset: new AMap.Pixel(0, 0),\n                icon: startIcon\n              });\n\n              // 添加弹窗\n              marker.title = `</span><span>${item.properties['名称']}`;\n              let arrTxt = [];\n              for (let key in item.properties) {\n                arrTxt.push(`${key}: ${item.properties[key]}`);\n              }\n              marker.content = JSON.stringify(arrTxt);\n              marker.layerName = nv.content;\n              marker.lnglat = lg;\n              marker.on('click', this.clickPoint);\n              this.markers.push(marker);\n            });\n            //实例化信息窗体\n            this.infoWindow = new AMap.InfoWindow({\n              isCustom: true,\n              //使用自定义窗体\n              content: this.winInfo,\n              offset: new AMap.Pixel(15, -35)\n            });\n            this.map.setFitView();\n            this.map.add(this.markers);\n          }\n        });\n      }\n      if (!flag) {\n        let count = this.markers.length;\n        for (let i = 0; i < count; i++) {\n          this.markers.forEach((ele, index) => {\n            if (ele.layerName == nv.content) {\n              this.map.remove(this.markers[index]);\n              let deleteMarker = this.markers.splice(index, 1);\n              //    this.cluster.removeMarker(deleteMarker);\n            }\n          });\n        }\n      }\n    },\n    clickPoint(e) {\n      this.winInfo = JSON.parse(e.target.content);\n      this.winTitle = e.target.title;\n      this.infoWindow.setContent(createInfoWindow.createInfoWindow(this.winTitle, this.winInfo.join(\"<br/>\"), () => {\n        // 关闭窗体\n        this.map.clearInfoWindow();\n      }));\n      // 打开窗体\n      this.infoWindow.open(this.map, e.target.getPosition());\n    }\n  }\n};", "map": {"version": 3, "names": ["createInfoWindow", "gen<PERSON><PERSON>", "getCimLayerInfo", "name", "data", "map", "district", "markers", "mapMarkersList", "cluster", "infoWindow", "winTitle", "markList", "index", "props", "layerCoordinateInfo", "type", "Object", "default", "created", "window", "_AMapSecurityConfig", "securityJsCode", "watch", "nv", "layers", "layerList", "for<PERSON>ach", "ele", "addPoints", "isFlag", "mounted", "$nextTick", "setTimeout", "initMap", "destroyed", "destroy", "methods", "AMap", "Map", "zoom", "expandZoomRange", "zooms", "center", "viewMode", "zoomEnable", "resizeEnable", "mapStyle", "polygon", "Polygon", "bubble", "path", "fillColor", "strokeOpacity", "fillOpacity", "strokeColor", "strokeWeight", "strokeStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "flag", "cacheUrlJ02", "split", "then", "res", "list", "features", "lnglats", "marker", "InfoWindow", "anchor", "offset", "Pixel", "startIcon", "Icon", "size", "Size", "image", "imageSize", "imageOffset", "item", "lg", "geometry", "coordinates", "<PERSON><PERSON>", "position", "icon", "title", "properties", "arrTxt", "key", "push", "content", "JSON", "stringify", "layerName", "lnglat", "on", "clickPoint", "isCustom", "winInfo", "setFitView", "count", "length", "i", "remove", "delete<PERSON><PERSON><PERSON>", "splice", "e", "parse", "target", "<PERSON><PERSON><PERSON><PERSON>", "join", "clearInfoWindow", "open", "getPosition"], "sources": ["src/views/mainPage/components/MapView.vue"], "sourcesContent": ["<template>\r\n    <div id=\"mapDiv\">\r\n        <div id=\"mapContainer\"></div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport createInfoWindow from '@/utils/amap.js'\r\nimport genJson from '@/assets/json/geo.json'\r\nimport { getCimLayerInfo } from '@/api/userMenu'\r\n\r\nexport default {\r\n    name: 'MapView',\r\n    data() {\r\n        return {\r\n            map: null,\r\n            district: {},\r\n            markers: [],\r\n            mapMarkersList: [],\r\n            cluster: '',\r\n            infoWindow: null,\r\n            winTitle: \"\",\r\n            markList: [],\r\n            index: '',\r\n        }\r\n    },\r\n    props: {\r\n        layerCoordinateInfo: {\r\n            type: Object,\r\n            default: () => {\r\n                return {}\r\n            }\r\n        }\r\n    },\r\n    created(){\r\n        window._AMapSecurityConfig = {\r\n    securityJsCode: \"7bb052192dcec8116e3213ccbeece92d\",\r\n};\r\n    },\r\n    watch: {\r\n        layerCoordinateInfo(nv) {\r\n            this.index = nv.index\r\n            if (nv.layers) {\r\n                let layerList = nv.layers\r\n                layerList.forEach(ele => {\r\n                    this.addPoints(ele, nv.isFlag)\r\n                });\r\n            } else {\r\n                this.addPoints(nv, nv.isFlag)\r\n            }\r\n\r\n        },\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            setTimeout(() => {\r\n            this.initMap()\r\n            \r\n        },1000)\r\n        })\r\n        // this.initMap()\r\n       \r\n    },\r\n    destroyed() {\r\n        if (this.map) {\r\n            this.map.destroy();\r\n            this.map = null;\r\n        }\r\n    },\r\n    methods: {\r\n        initMap() {\r\n\r\n            // this.district = new AMap.DistrictSearch({\r\n            //     subdistrict: 3,\r\n            //     extensions: 'all',\r\n            //     level: 'city'\r\n            // })\r\n            // this.district.search('昆山市', (status, result) => {\r\n            //     var bounds = result.districtList[0]['boundaries'];\r\n            //     var mask = [];\r\n            //     for (var i = 0; i < bounds.length; i++) {\r\n            //         mask.push([bounds[i]]);\r\n            //     }\r\n\r\n            //添加描边\r\n            // for (var i = 0; i < bounds.length; i++) {\r\n            //     new AMap.Polyline({\r\n            //         path: bounds[i],\r\n            //         strokeColor: '#3078AC',\r\n            //         strokeWeight: 2,\r\n            //         map: this.map\r\n            //     })\r\n            // }\r\n\r\n            // });\r\n            // var outer = [\r\n            //     new AMap.LngLat(-360, 90, true),\r\n            //     new AMap.LngLat(-360, -90, true),\r\n            //     new AMap.LngLat(360, -90, true),\r\n            //     new AMap.LngLat(360, 90, true),\r\n            // ];\r\n            // var pathArray = [\r\n            //     outer,\r\n            // ];\r\n            // pathArray.push(genJson);\r\n\r\n            //实例化地图\r\n            this.map = new AMap.Map('mapContainer', {\r\n                zoom: 14,                //设置当前显示级别\r\n                expandZoomRange: true,    //开启显示范围设置\r\n                zooms: [7, 20],          //最小显示级别为7，最大显示级别为20\r\n                center: [121.1, 31.29705],\r\n                // center: [121.073722,31.276133],//万科\r\n                viewMode: '2D',          //这里特别注意，设置为3D则其它地区不显示\r\n                zoomEnable: true,        //是否可以缩放地图\r\n                resizeEnable: true,\r\n                mapStyle: \"amap://styles/white\"\r\n            });\r\n\r\n            let polygon = new AMap.Polygon({\r\n                bubble: true,\r\n                path: genJson,\r\n                fillColor: '#b2cefe',\r\n                strokeOpacity: .5,\r\n                fillOpacity: .3,\r\n                strokeColor: '#1f4b96',\r\n                strokeWeight: 2,\r\n                strokeStyle: 'dashed',\r\n                strokeDasharray: [5, 5],\r\n            });\r\n\r\n            this.map.add(polygon);\r\n\r\n        },\r\n\r\n        addPoints(nv, flag) {\r\n            if (nv.cacheUrlJ02 && flag) {\r\n                let cacheUrlJ02 = nv.cacheUrlJ02.split('api')[1];\r\n                getCimLayerInfo(cacheUrlJ02).then(res => {\r\n                    if (res.data != '') {\r\n                        let list = res.data.features; //[0].geometry.coordinates\r\n                        let lnglats = null;\r\n                        let marker = null;\r\n                        this.infoWindow = new AMap.InfoWindow({\r\n                            anchor: \"top-left\",\r\n                            offset: new AMap.Pixel(0, -30)\r\n                        });\r\n                        var startIcon = new AMap.Icon({\r\n                            // 图标尺寸\r\n                            size: new AMap.Size(10, 10),\r\n                            // 图标的取图地址\r\n                            // image: 'https://webapi.amap.com/images/mass/mass0.png',\r\n                            image: '/icons/splice' + this.index * 1 + '.png',\r\n                            // 图标所用图片大小\r\n                            imageSize: new AMap.Size(10, 10),\r\n                            // // 图标取图偏移量\r\n                            imageOffset: new AMap.Pixel(0, 0)\r\n                        });\r\n                        list.forEach(item => {\r\n                            let lg = item.geometry.coordinates;\r\n                            // AMap.convertFrom(lg, '84', (status, result) => {\r\n                            //     console.log(result, count++);\r\n                            //     if (result.info === 'ok') {\r\n                            //         item.geometry.lnglat = result.locations; // 转换后的高德坐标 Array.<LngLat> \r\n                            //     }\r\n                            // });\r\n\r\n                            marker = new AMap.Marker({\r\n                                position: lg,\r\n                                // content: content,  //点标记显示内容，可以是HTML要素字符串或者HTML DOM对象。content有效时，icon属性将被覆盖\r\n                                offset: new AMap.Pixel(0, 0),\r\n                                icon: startIcon,\r\n\r\n                            });\r\n\r\n                            // 添加弹窗\r\n                            marker.title = `</span><span>${item.properties['名称']}`\r\n                            let arrTxt = []\r\n                            for (let key in item.properties) {\r\n                                arrTxt.push(`${key}: ${item.properties[key]}`)\r\n                            }\r\n                            marker.content = JSON.stringify(arrTxt);\r\n                            marker.layerName = nv.content;\r\n                            marker.lnglat = lg;\r\n                            marker.on('click', this.clickPoint);\r\n                            this.markers.push(marker)\r\n                        })\r\n                        //实例化信息窗体\r\n                        this.infoWindow = new AMap.InfoWindow({\r\n                            isCustom: true, //使用自定义窗体\r\n                            content: this.winInfo,\r\n                            offset: new AMap.Pixel(15, -35),\r\n                        });\r\n                        this.map.setFitView();\r\n                        this.map.add(this.markers)\r\n                    }\r\n\r\n\r\n                })\r\n\r\n            }\r\n\r\n            if (!flag) {\r\n                let count = this.markers.length;\r\n                for (let i = 0; i < count; i++) {\r\n                    this.markers.forEach((ele, index) => {\r\n                        if (ele.layerName == nv.content) {\r\n                            this.map.remove(this.markers[index])\r\n                            let deleteMarker = this.markers.splice(index, 1)\r\n                            //    this.cluster.removeMarker(deleteMarker);\r\n                        }\r\n                    })\r\n\r\n                }\r\n            }\r\n        },\r\n\r\n        clickPoint(e) {\r\n            this.winInfo = JSON.parse(e.target.content);\r\n            this.winTitle = e.target.title;\r\n            this.infoWindow.setContent(\r\n                createInfoWindow.createInfoWindow(\r\n                    this.winTitle,\r\n                    this.winInfo.join(\"<br/>\"),\r\n                    () => {\r\n                        // 关闭窗体\r\n                        this.map.clearInfoWindow();\r\n                    }\r\n                )\r\n            );\r\n            // 打开窗体\r\n            this.infoWindow.open(this.map, e.target.getPosition());\r\n        }\r\n\r\n\r\n    }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#mapDiv {\r\n    width: 100%;\r\n    height: 100vh;\r\n    overflow: hidden;\r\n    // h1 {\r\n    //     padding: 1px;\r\n    //     margin-top: 5rem;\r\n    //     border: 20px solid red;\r\n    // }\r\n\r\n    #mapContainer {\r\n        margin-top: 50px;\r\n        width: 100%;\r\n        height: 100vh;\r\n    }\r\n}\r\n</style>"], "mappings": "AAMA,OAAAA,gBAAA;AACA,OAAAC,OAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,GAAA;MACAC,QAAA;MACAC,OAAA;MACAC,cAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAC,mBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACAC,MAAA,CAAAC,mBAAA;MACAC,cAAA;IACA;EACA;EACAC,KAAA;IACAR,oBAAAS,EAAA;MACA,KAAAX,KAAA,GAAAW,EAAA,CAAAX,KAAA;MACA,IAAAW,EAAA,CAAAC,MAAA;QACA,IAAAC,SAAA,GAAAF,EAAA,CAAAC,MAAA;QACAC,SAAA,CAAAC,OAAA,CAAAC,GAAA;UACA,KAAAC,SAAA,CAAAD,GAAA,EAAAJ,EAAA,CAAAM,MAAA;QACA;MACA;QACA,KAAAD,SAAA,CAAAL,EAAA,EAAAA,EAAA,CAAAM,MAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACAC,UAAA;QACA,KAAAC,OAAA;MAEA;IACA;IACA;EAEA;EACAC,UAAA;IACA,SAAA9B,GAAA;MACA,KAAAA,GAAA,CAAA+B,OAAA;MACA,KAAA/B,GAAA;IACA;EACA;EACAgC,OAAA;IACAH,QAAA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,KAAA7B,GAAA,OAAAiC,IAAA,CAAAC,GAAA;QACAC,IAAA;QAAA;QACAC,eAAA;QAAA;QACAC,KAAA;QAAA;QACAC,MAAA;QACA;QACAC,QAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QACAC,QAAA;MACA;MAEA,IAAAC,OAAA,OAAAV,IAAA,CAAAW,OAAA;QACAC,MAAA;QACAC,IAAA,EAAAlD,OAAA;QACAmD,SAAA;QACAC,aAAA;QACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,WAAA;QACAC,eAAA;MACA;MAEA,KAAArD,GAAA,CAAAsD,GAAA,CAAAX,OAAA;IAEA;IAEAnB,UAAAL,EAAA,EAAAoC,IAAA;MACA,IAAApC,EAAA,CAAAqC,WAAA,IAAAD,IAAA;QACA,IAAAC,WAAA,GAAArC,EAAA,CAAAqC,WAAA,CAAAC,KAAA;QACA5D,eAAA,CAAA2D,WAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAA5D,IAAA;YACA,IAAA6D,IAAA,GAAAD,GAAA,CAAA5D,IAAA,CAAA8D,QAAA;YACA,IAAAC,OAAA;YACA,IAAAC,MAAA;YACA,KAAA1D,UAAA,OAAA4B,IAAA,CAAA+B,UAAA;cACAC,MAAA;cACAC,MAAA,MAAAjC,IAAA,CAAAkC,KAAA;YACA;YACA,IAAAC,SAAA,OAAAnC,IAAA,CAAAoC,IAAA;cACA;cACAC,IAAA,MAAArC,IAAA,CAAAsC,IAAA;cACA;cACA;cACAC,KAAA,yBAAAhE,KAAA;cACA;cACAiE,SAAA,MAAAxC,IAAA,CAAAsC,IAAA;cACA;cACAG,WAAA,MAAAzC,IAAA,CAAAkC,KAAA;YACA;YACAP,IAAA,CAAAtC,OAAA,CAAAqD,IAAA;cACA,IAAAC,EAAA,GAAAD,IAAA,CAAAE,QAAA,CAAAC,WAAA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEAf,MAAA,OAAA9B,IAAA,CAAA8C,MAAA;gBACAC,QAAA,EAAAJ,EAAA;gBACA;gBACAV,MAAA,MAAAjC,IAAA,CAAAkC,KAAA;gBACAc,IAAA,EAAAb;cAEA;;cAEA;cACAL,MAAA,CAAAmB,KAAA,mBAAAP,IAAA,CAAAQ,UAAA;cACA,IAAAC,MAAA;cACA,SAAAC,GAAA,IAAAV,IAAA,CAAAQ,UAAA;gBACAC,MAAA,CAAAE,IAAA,IAAAD,GAAA,KAAAV,IAAA,CAAAQ,UAAA,CAAAE,GAAA;cACA;cACAtB,MAAA,CAAAwB,OAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAL,MAAA;cACArB,MAAA,CAAA2B,SAAA,GAAAvE,EAAA,CAAAoE,OAAA;cACAxB,MAAA,CAAA4B,MAAA,GAAAf,EAAA;cACAb,MAAA,CAAA6B,EAAA,eAAAC,UAAA;cACA,KAAA3F,OAAA,CAAAoF,IAAA,CAAAvB,MAAA;YACA;YACA;YACA,KAAA1D,UAAA,OAAA4B,IAAA,CAAA+B,UAAA;cACA8B,QAAA;cAAA;cACAP,OAAA,OAAAQ,OAAA;cACA7B,MAAA,MAAAjC,IAAA,CAAAkC,KAAA;YACA;YACA,KAAAnE,GAAA,CAAAgG,UAAA;YACA,KAAAhG,GAAA,CAAAsD,GAAA,MAAApD,OAAA;UACA;QAGA;MAEA;MAEA,KAAAqD,IAAA;QACA,IAAA0C,KAAA,QAAA/F,OAAA,CAAAgG,MAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,KAAA,EAAAE,CAAA;UACA,KAAAjG,OAAA,CAAAoB,OAAA,EAAAC,GAAA,EAAAf,KAAA;YACA,IAAAe,GAAA,CAAAmE,SAAA,IAAAvE,EAAA,CAAAoE,OAAA;cACA,KAAAvF,GAAA,CAAAoG,MAAA,MAAAlG,OAAA,CAAAM,KAAA;cACA,IAAA6F,YAAA,QAAAnG,OAAA,CAAAoG,MAAA,CAAA9F,KAAA;cACA;YACA;UACA;QAEA;MACA;IACA;IAEAqF,WAAAU,CAAA;MACA,KAAAR,OAAA,GAAAP,IAAA,CAAAgB,KAAA,CAAAD,CAAA,CAAAE,MAAA,CAAAlB,OAAA;MACA,KAAAjF,QAAA,GAAAiG,CAAA,CAAAE,MAAA,CAAAvB,KAAA;MACA,KAAA7E,UAAA,CAAAqG,UAAA,CACA/G,gBAAA,CAAAA,gBAAA,CACA,KAAAW,QAAA,EACA,KAAAyF,OAAA,CAAAY,IAAA,WACA;QACA;QACA,KAAA3G,GAAA,CAAA4G,eAAA;MACA,CACA,CACA;MACA;MACA,KAAAvG,UAAA,CAAAwG,IAAA,MAAA7G,GAAA,EAAAuG,CAAA,CAAAE,MAAA,CAAAK,WAAA;IACA;EAGA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}