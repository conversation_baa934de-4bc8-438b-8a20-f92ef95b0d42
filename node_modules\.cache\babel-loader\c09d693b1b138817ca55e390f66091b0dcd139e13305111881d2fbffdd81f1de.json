{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getTakeAmountNum } from '@/api/bigScreen.js';\nexport default {\n  name: 'CommentArea',\n  data() {\n    return {\n      commentArea: null,\n      data: [],\n      indicator: []\n    };\n  },\n  beforeDestroy() {\n    if (this.commentArea) {\n      this.commentArea.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.commentArea != null && this.commentArea != undefined && this.commentArea != '') {\n          this.commentArea.dispose();\n        }\n        ;\n        this.commentArea = echarts.init(this.$refs.commentArea);\n        this.commentArea.clear();\n        getTakeAmountNum().then(res => {\n          let dataList = res.data.data[0];\n          let nameList = {\n            num1: '本年办件量',\n            num2: '本月办件量',\n            num3: '今日办件量',\n            num4: \"日均办件量\",\n            num5: '本季度办件量'\n          };\n          // let max = 0;\n          for (var i in dataList) {\n            for (var j in nameList) {\n              if (i == j) {\n                // if (dataList[i] > max) {\n                //     max = dataList[i]\n                // }\n                this.indicator.push({\n                  name: nameList[j],\n                  value: dataList[i]\n                  // max: max\n                });\n                this.data.push(dataList[i]);\n              }\n            }\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let ii = -1;\n      let option = {\n        radar: [{\n          indicator: this.indicator,\n          radius: 120,\n          center: ['50%', '54%'],\n          splitArea: {\n            // 坐标轴在 grid 区域中的分隔区域，默认不显示。\n            show: true,\n            reaStyle: {\n              // 分隔区域的样式设置。\n              color: ['rgba(255,255,255,0)', 'rgba(255,255,255,0)'] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。\n            }\n          },\n          axisLine: {\n            //指向外圈文本的分隔线样式\n            lineStyle: {\n              color: '#fff'\n            }\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#fff',\n              // 分隔线颜色\n              width: 1 // 分隔线线宽\n            }\n          },\n          axisName: {\n            rich: {\n              a: {\n                fontSize: 23,\n                color: '#fff',\n                align: 'left',\n                lineHeight: '20'\n              },\n              b: {\n                fontSize: 20,\n                fontWeight: \"bold\",\n                color: '#1DF9FC',\n                align: 'left',\n                fontWeight: 'bold'\n              }\n            },\n            formatter: a => {\n              ii++;\n              return `{a|${a}}\\n\\n {b|${this.data[ii]}}`;\n            }\n          }\n        }],\n        series: [{\n          type: 'radar',\n          tooltip: {\n            trigger: 'item'\n          },\n          areaStyle: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: '#56CCF2'\n            }, {\n              offset: 1,\n              color: '#2F80ED'\n            }])\n          },\n          data: [{\n            value: this.data,\n            itemStyle: {\n              // normal: {\n              borderWidth: 0,\n              color: '#02c7fd'\n              // borderColor: '#fff'\n              // }\n            },\n            lineStyle: {\n              // color: 'red',\n              width: 0\n            }\n          }]\n        }]\n      };\n      if (option && typeof option == 'object') {\n        this.commentArea.setOption(option);\n      }\n      window.onresize = this.commentArea.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getTakeAmountNum", "name", "data", "commentArea", "indicator", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "nameList", "num1", "num2", "num3", "num4", "num5", "i", "j", "push", "value", "setOption", "ii", "option", "radar", "radius", "center", "splitArea", "show", "reaStyle", "color", "axisLine", "lineStyle", "splitLine", "width", "axisName", "rich", "a", "fontSize", "align", "lineHeight", "b", "fontWeight", "formatter", "series", "type", "tooltip", "trigger", "areaStyle", "graphic", "LinearGradient", "offset", "itemStyle", "borderWidth", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/CommentArea.vue"], "sourcesContent": ["<template>\r\n    <div ref=\"commentArea\" id=\"commentArea\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import * as echarts from 'echarts';\r\n    import { getTakeAmountNum } from '@/api/bigScreen.js'\r\n\r\n    export default {\r\n        name: 'CommentArea',\r\n\r\n        data() {\r\n            return {\r\n                commentArea: null,\r\n                data: [],\r\n                indicator: []\r\n\r\n            };\r\n        },\r\n        beforeDestroy(){\r\n        if(this.commentArea){\r\n            this.commentArea.clear()\r\n        }\r\n    },\r\n        mounted() {\r\n            this.$nextTick(() => {\r\n                this.myChart()\r\n            });\r\n        },\r\n\r\n        methods: {\r\n            myChart() {\r\n                new Promise((resolve) => {\r\n                    resolve()\r\n                }).then(() => {\r\n                    if (this.commentArea != null && this.commentArea != undefined && this.commentArea != '') {\r\n                        this.commentArea.dispose()\r\n                    };\r\n                    this.commentArea = echarts.init(this.$refs.commentArea);\r\n                    this.commentArea.clear();\r\n                    getTakeAmountNum().then(res => {\r\n                        let dataList = res.data.data[0];\r\n                        let nameList = { num1: '本年办件量', num2: '本月办件量', num3: '今日办件量', num4: \"日均办件量\", num5: '本季度办件量' }\r\n                        // let max = 0;\r\n                        for (var i in dataList) {\r\n                            for (var j in nameList) {\r\n                                if (i == j) {\r\n                                    // if (dataList[i] > max) {\r\n                                    //     max = dataList[i]\r\n                                    // }\r\n                                    this.indicator.push({\r\n                                        name: nameList[j],\r\n                                        value: dataList[i],\r\n                                        // max: max\r\n                                    })\r\n                                    this.data.push(dataList[i])\r\n                                }\r\n                            }\r\n                        }\r\n                        this.setOption()\r\n                    })\r\n\r\n                })\r\n            },\r\n            setOption() {\r\n                let ii = -1;\r\n                let option = {\r\n\r\n                    radar: [\r\n\r\n                        {\r\n                            indicator: this.indicator,\r\n                            radius: 120,\r\n                            center: ['50%', '54%'],\r\n                            splitArea: { // 坐标轴在 grid 区域中的分隔区域，默认不显示。\r\n                                show: true,\r\n                                reaStyle: { // 分隔区域的样式设置。\r\n                                    color: ['rgba(255,255,255,0)', 'rgba(255,255,255,0)'], // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。\r\n                                }\r\n                            },\r\n\r\n                            axisLine: { //指向外圈文本的分隔线样式\r\n                                lineStyle: {\r\n                                    color: '#fff'\r\n                                }\r\n                            },\r\n                            splitLine: {\r\n                                lineStyle: {\r\n                                    color: '#fff', // 分隔线颜色\r\n                                    width: 1, // 分隔线线宽\r\n                                }\r\n                            },\r\n                            axisName: {\r\n                                rich: {\r\n                                    a: {\r\n                                        fontSize: 23,\r\n                                        color: '#fff',\r\n                                        align: 'left',\r\n                                        lineHeight: '20'\r\n                                    },\r\n                                    b: {\r\n                                        fontSize: 20,\r\n                                        fontWeight: \"bold\",\r\n                                        color: '#1DF9FC',\r\n                                        align: 'left',\r\n                                        fontWeight: 'bold'\r\n                                    }\r\n                                },\r\n\r\n                                formatter: (a) => {\r\n                                    ii++\r\n                                    return `{a|${a}}\\n\\n {b|${this.data[ii]}}`;\r\n                                }\r\n                            },\r\n\r\n\r\n\r\n                        },\r\n\r\n\r\n                    ],\r\n                    series: [\r\n                        {\r\n                            type: 'radar',\r\n                            tooltip: {\r\n                                trigger: 'item'\r\n                            },\r\n                            areaStyle: {\r\n                                color: new echarts.graphic.LinearGradient(\r\n                                    0, 0, 0, 1,\r\n                                    [\r\n                                        { offset: 0, color: '#56CCF2' },\r\n                                        { offset: 1, color: '#2F80ED' }\r\n                                    ]\r\n                                )\r\n\r\n                            },\r\n                            data: [\r\n                                {\r\n                                    value: this.data,\r\n                                    itemStyle: {\r\n                                        // normal: {\r\n                                            borderWidth: 0,\r\n                                            color: '#02c7fd',\r\n                                            // borderColor: '#fff'\r\n                                        // }\r\n                                    },\r\n                                    lineStyle: {\r\n                                        // color: 'red',\r\n                                        width: 0\r\n                                    },\r\n                                }\r\n                            ],\r\n\r\n\r\n                        },\r\n\r\n\r\n\r\n                    ]\r\n                }\r\n                if (option && typeof option == 'object') {\r\n                    this.commentArea.setOption(option)\r\n                }\r\n\r\n                window.onresize = this.commentArea.resize;\r\n            }\r\n        },\r\n    };\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n    #commentArea {\r\n        width: 100%;\r\n        height: 30rem;\r\n    }\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,gBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,WAAA;MACAD,IAAA;MACAE,SAAA;IAEA;EACA;EACAC,cAAA;IACA,SAAAF,WAAA;MACA,KAAAA,WAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IACA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAV,WAAA,iBAAAA,WAAA,IAAAW,SAAA,SAAAX,WAAA;UACA,KAAAA,WAAA,CAAAY,OAAA;QACA;QAAA;QACA,KAAAZ,WAAA,GAAAJ,OAAA,CAAAiB,IAAA,MAAAC,KAAA,CAAAd,WAAA;QACA,KAAAA,WAAA,CAAAG,KAAA;QACAN,gBAAA,GAAAa,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAhB,IAAA,CAAAA,IAAA;UACA,IAAAkB,QAAA;YAAAC,IAAA;YAAAC,IAAA;YAAAC,IAAA;YAAAC,IAAA;YAAAC,IAAA;UAAA;UACA;UACA,SAAAC,CAAA,IAAAP,QAAA;YACA,SAAAQ,CAAA,IAAAP,QAAA;cACA,IAAAM,CAAA,IAAAC,CAAA;gBACA;gBACA;gBACA;gBACA,KAAAvB,SAAA,CAAAwB,IAAA;kBACA3B,IAAA,EAAAmB,QAAA,CAAAO,CAAA;kBACAE,KAAA,EAAAV,QAAA,CAAAO,CAAA;kBACA;gBACA;gBACA,KAAAxB,IAAA,CAAA0B,IAAA,CAAAT,QAAA,CAAAO,CAAA;cACA;YACA;UACA;UACA,KAAAI,SAAA;QACA;MAEA;IACA;IACAA,UAAA;MACA,IAAAC,EAAA;MACA,IAAAC,MAAA;QAEAC,KAAA,GAEA;UACA7B,SAAA,OAAAA,SAAA;UACA8B,MAAA;UACAC,MAAA;UACAC,SAAA;YAAA;YACAC,IAAA;YACAC,QAAA;cAAA;cACAC,KAAA;YACA;UACA;UAEAC,QAAA;YAAA;YACAC,SAAA;cACAF,KAAA;YACA;UACA;UACAG,SAAA;YACAD,SAAA;cACAF,KAAA;cAAA;cACAI,KAAA;YACA;UACA;UACAC,QAAA;YACAC,IAAA;cACAC,CAAA;gBACAC,QAAA;gBACAR,KAAA;gBACAS,KAAA;gBACAC,UAAA;cACA;cACAC,CAAA;gBACAH,QAAA;gBACAI,UAAA;gBACAZ,KAAA;gBACAS,KAAA;gBACAG,UAAA;cACA;YACA;YAEAC,SAAA,EAAAN,CAAA;cACAf,EAAA;cACA,aAAAe,CAAA,iBAAA5C,IAAA,CAAA6B,EAAA;YACA;UACA;QAIA,EAGA;QACAsB,MAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACAC,OAAA;UACA;UACAC,SAAA;YACAlB,KAAA,MAAAxC,OAAA,CAAA2D,OAAA,CAAAC,cAAA,CACA,YACA,CACA;cAAAC,MAAA;cAAArB,KAAA;YAAA,GACA;cAAAqB,MAAA;cAAArB,KAAA;YAAA,EAEA;UAEA;UACArC,IAAA,GACA;YACA2B,KAAA,OAAA3B,IAAA;YACA2D,SAAA;cACA;cACAC,WAAA;cACAvB,KAAA;cACA;cACA;YACA;YACAE,SAAA;cACA;cACAE,KAAA;YACA;UACA;QAIA;MAKA;MACA,IAAAX,MAAA,WAAAA,MAAA;QACA,KAAA7B,WAAA,CAAA2B,SAAA,CAAAE,MAAA;MACA;MAEA+B,MAAA,CAAAC,QAAA,QAAA7D,WAAA,CAAA8D,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}