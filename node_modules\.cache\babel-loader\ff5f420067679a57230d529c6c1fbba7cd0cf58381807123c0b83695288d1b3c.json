{"ast": null, "code": "import Hls from \"hls.js\";\nimport \"video.js/dist/video-js.css\";\nexport default {\n  name: 'hlsItem',\n  data() {\n    return {\n      hlsVideo: null,\n      isPlay: true,\n      isTitle: false,\n      isFullScreen: false,\n      timer: null,\n      channalName: ''\n    };\n  },\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    currentCount: {\n      type: Number,\n      default: 0\n    },\n    playerList: {\n      type: Array,\n      default: () => []\n    },\n    areaTitInfo: {\n      type: Object,\n      default: () => {\n        return {\n          channalName: ''\n        };\n      }\n    },\n    height: {\n      type: String,\n      default: '100%'\n    }\n  },\n  computed: {},\n  watch: {},\n  beforeDestroy() {\n    // 移除监听事件\n\n    this.destroyVideo();\n    clearTimeout(this.timer);\n  },\n  mounted() {\n    this.$nextTick(() => {\n      // let length = this.currentCount / 1 + 1 / 1\n      // console.log('12',this.playerList.length)\n      // console.log('3',length)\n      // if (this.playerList.length >= length) {\n      //     this.hlsVideo = this.playerList[this.currentCount]\n      //     this.initPlayer()\n      // }\n\n      this.resizeScreen();\n      window.onresize = () => {\n        this.resizeScreen();\n      };\n    });\n  },\n  methods: {\n    onVideoMetadataLoaded(event) {\n      const videoElement = event.target;\n      this.aspectRatio = videoElement.videoWidth / videoElement.videoHeight;\n      this.$store.commit('getAspectRatio', this.aspectRatio);\n      // 你可以在这里使用 aspectRatio 进行其他操作\n    },\n    resizeScreen() {\n      let body = document.body.clientHeight;\n      let ele = document.getElementById(this.id);\n      if (ele) {\n        let doc = ele.clientHeight;\n        if (doc >= body) {\n          this.isFullScreen = true;\n        } else {\n          this.isFullScreen = false;\n        }\n      }\n    },\n    initPlayer() {\n      if (!this.hlsVideo.isPlay) {\n        this.isPlay = this.hlsVideo.isPlay;\n      } else {\n        this.timer = setTimeout(() => {\n          this.createVideo(this.hlsVideo.urlPlayer);\n        }, 500);\n      }\n    },\n    // 初始化播放 \n    createVideo(url, name) {\n      this.destroyVideo();\n      this.channalName = name;\n      this.isPlay = true;\n      let flag = 0;\n      this.hlsVideo = null;\n      const hlsVideoDom = document.getElementById(this.id); // 获取视频元素的引用\n      if (Hls.isSupported()) {\n        var timeEnd = 0;\n        var timeDisplay;\n        this.hlsVideo = new Hls();\n        this.hlsVideo.attachMedia(hlsVideoDom);\n        this.hlsVideo.loadSource(url);\n        this.hlsVideo.isPlay = this.isPlay;\n        let isPause = true;\n\n        // 播放 \n        this.hlsVideo.on(Hls.Events.MANIFEST_PARSED, (d, e) => {\n          hlsVideoDom.play().then(() => {\n            flag = 1;\n            this.isPlay = true;\n          }).catch(err => {\n            this.close();\n          });\n        });\n        this.hlsVideo.on(Hls.Events.BUFFER_APPENDING, (event, data) => {\n          // console.log('数据附加到缓冲区时触发') \n          let {\n            duration,\n            elementaryStreams\n          } = data.frag;\n          timeEnd = elementaryStreams.video.endDTS - duration + 2;\n          isPause = true;\n        });\n        this.hlsVideo.on(Hls.Events.FRAG_LOADED, (event, data) => {\n          if (!this.isPlay) this.isPlay = true;\n          if (isPause && hlsVideoDom) {\n            hlsVideoDom.play().catch(err => {\n              console.log('播放时发生错误:' + err);\n            });\n            isPause = false;\n          }\n        });\n        this.hlsVideo.on(Hls.Events.ERROR, (event, data) => {\n          const errType = data.type;\n          const errDetails = data.details;\n          const errFatal = data.fatal;\n          if (errFatal) {\n            switch (errType) {\n              case Hls.ErrorTypes.NETWORK_ERROR:\n                console.log('遇到网络错误，尝试恢复', errDetails);\n                this.hlsVideo.startLoad();\n                break;\n              case Hls.ErrorTypes.MEDIA_ERROR:\n                console.log('遇到媒体错误，尝试恢复', errDetails);\n                this.hlsVideo.recoverMediaError();\n                break;\n              default:\n                // cannot recover\n                console.log('播放时发生错误：', errDetails);\n                this.close();\n              // 关闭keep定时器，关闭播放器\n            }\n          }\n        });\n\n        // 12、waiting：视频加载等待。当视频由于需要缓冲下一帧而停止，等待时触发\n        hlsVideoDom.onwaiting = e => {\n          // console.log('视频加载等待') \n          if (!isPause) {\n            hlsVideoDom.pause();\n            isPause = true;\n          }\n        };\n        hlsVideoDom.onpause = e => {\n          isPause = true;\n          if (timeEnd && timeEnd > timeDisplay) {\n            hlsVideoDom.currentTime = timeEnd;\n          }\n        };\n        // 当浏览器开始请求数据时 \n        hlsVideoDom.ontimeupdate = (e, v) => {\n          timeDisplay = Math.floor(hlsVideoDom.currentTime);\n          if (timeEnd - timeDisplay > 60) {\n            hlsVideoDom.currentTime = timeEnd;\n            hlsVideoDom.play().catch(err => {\n              // console.log('1---','播放出错')\n            });\n            isPause = false;\n          }\n        };\n        this.$emit('sendPlayerList', this.hlsVideo, this.currentCount);\n      } else if (hlsVideoDom.canPlayType('application/vnd.apple.mpegurl')) {\n        // 如果支持原生播放\n        hlsVideoDom.src = url;\n        // 自动播放  \n        hlsVideoDom.onloadedmetadata = () => {\n          hlsVideoDom.play();\n        };\n      }\n    },\n    goBackTime(hlsVideoDom) {},\n    destroyVideo() {\n      if (this.hlsVideo) {\n        this.hlsVideo.destroy();\n        this.hlsVideo = null;\n      }\n    },\n    internalClose() {\n      const videoElement = document.getElementById(this.id); // 获取视频元素的引用\n      if (videoElement) {\n        videoElement.pause();\n        videoElement.removeAttribute('src');\n      }\n    },\n    close() {\n      if (this.isPlay) {\n        this.internalClose();\n        this.isPlay = false;\n      }\n      this.isPlay = false;\n    },\n    clickVideo() {\n      // this.destroyVideo();\n      if (this.hlsVideo) this.$parent.getVideoOne(this.hlsVideo);\n    }\n  }\n};", "map": {"version": 3, "names": ["Hls", "name", "data", "hlsVideo", "isPlay", "isTitle", "isFullScreen", "timer", "channal<PERSON>ame", "props", "id", "type", "String", "default", "currentCount", "Number", "playerList", "Array", "areaTitInfo", "Object", "height", "computed", "watch", "<PERSON><PERSON><PERSON><PERSON>", "destroyVideo", "clearTimeout", "mounted", "$nextTick", "resizeScreen", "window", "onresize", "methods", "onVideoMetadataLoaded", "event", "videoElement", "target", "aspectRatio", "videoWidth", "videoHeight", "$store", "commit", "body", "document", "clientHeight", "ele", "getElementById", "doc", "initPlayer", "setTimeout", "createVideo", "urlPlayer", "url", "flag", "hlsVideoDom", "isSupported", "timeEnd", "timeDisplay", "attachMedia", "loadSource", "isPause", "on", "Events", "MANIFEST_PARSED", "d", "e", "play", "then", "catch", "err", "close", "BUFFER_APPENDING", "duration", "elementaryStreams", "frag", "video", "endDTS", "FRAG_LOADED", "console", "log", "ERROR", "errType", "errDetails", "details", "errFatal", "fatal", "ErrorTypes", "NETWORK_ERROR", "startLoad", "MEDIA_ERROR", "recoverMediaError", "onwaiting", "pause", "onpause", "currentTime", "ontimeupdate", "v", "Math", "floor", "$emit", "canPlayType", "src", "onloadedmetadata", "goBackTime", "destroy", "internalClose", "removeAttribute", "clickVideo", "$parent", "getVideoOne"], "sources": ["src/components/hlvJsVideo/hlsItem.vue"], "sourcesContent": ["<template>\r\n\r\n    <div class=\"videoShowItem\" @click=\"clickVideo\" :style=\"{ minHeight: height }\">\r\n        <div class=\"title\">{{ channalName }}</div>\r\n        <video class=\"hide-progress-bar\" :ref=\"'videoElement' + id\" :id=\"id\"\r\n        :style=\"{ objectFit: isFullScreen ? 'contain' : 'fill' }\" muted autoplay\r\n        @loadedmetadata=\"onVideoMetadataLoaded\"></video>\r\n            <!-- :style=\"{ objectFit: isFullScreen ? 'contain' : 'fill' }\" muted autoplay -->\r\n            \r\n        <div class=\"errorTxt\" v-if=\"!isPlay\">...加载失败</div>\r\n    </div>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nimport Hls from \"hls.js\";\r\nimport \"video.js/dist/video-js.css\";\r\nexport default {\r\n    name: 'hlsItem',\r\n\r\n    data() {\r\n        return {\r\n            hlsVideo: null,\r\n            isPlay: true,\r\n            isTitle: false,\r\n            isFullScreen: false,\r\n            timer: null,\r\n            channalName: '',\r\n        }\r\n    },\r\n    props: {\r\n        id: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        currentCount: {\r\n            type: Number,\r\n            default: 0\r\n        },\r\n        playerList: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        areaTitInfo: {\r\n            type: Object,\r\n            default: () => {\r\n                return { channalName: '' }\r\n            }\r\n        },\r\n        height: {\r\n            type: String,\r\n            default: '100%'\r\n        }\r\n    },\r\n    computed: {\r\n       \r\n    },\r\n    watch: {\r\n\r\n\r\n    },\r\n    beforeDestroy() {\r\n        // 移除监听事件\r\n\r\n        this.destroyVideo();\r\n        clearTimeout(this.timer)\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            // let length = this.currentCount / 1 + 1 / 1\r\n            // console.log('12',this.playerList.length)\r\n            // console.log('3',length)\r\n            // if (this.playerList.length >= length) {\r\n            //     this.hlsVideo = this.playerList[this.currentCount]\r\n            //     this.initPlayer()\r\n            // }\r\n\r\n            this.resizeScreen()\r\n\r\n            window.onresize = () => {\r\n                this.resizeScreen()\r\n            }\r\n        })\r\n\r\n\r\n\r\n    },\r\n    methods: {\r\n        onVideoMetadataLoaded(event) {\r\n            const videoElement = event.target;\r\n            this.aspectRatio = videoElement.videoWidth / videoElement.videoHeight;\r\n            this.$store.commit('getAspectRatio', this.aspectRatio)\r\n            // 你可以在这里使用 aspectRatio 进行其他操作\r\n        },\r\n        resizeScreen() {\r\n\r\n            let body = document.body.clientHeight\r\n            let ele = document.getElementById(this.id)\r\n            if (ele) {\r\n                let doc = ele.clientHeight\r\n                if (doc >= body) {\r\n                    this.isFullScreen = true\r\n                } else {\r\n                    this.isFullScreen = false\r\n                }\r\n\r\n            }\r\n        },\r\n        initPlayer() {\r\n            if (!this.hlsVideo.isPlay) {\r\n                this.isPlay = this.hlsVideo.isPlay\r\n            } else {\r\n                this.timer = setTimeout(() => {\r\n                    this.createVideo(this.hlsVideo.urlPlayer)\r\n                }, 500)\r\n\r\n            }\r\n\r\n        },\r\n        // 初始化播放 \r\n        createVideo(url, name) {\r\n            this.destroyVideo();\r\n            this.channalName = name\r\n            this.isPlay = true;\r\n            let flag = 0;\r\n            this.hlsVideo = null;\r\n            const hlsVideoDom = document.getElementById(this.id); // 获取视频元素的引用\r\n            if (Hls.isSupported()) {\r\n\r\n                var timeEnd = 0;\r\n                var timeDisplay;\r\n\r\n                this.hlsVideo = new Hls()\r\n                this.hlsVideo.attachMedia(hlsVideoDom)\r\n                this.hlsVideo.loadSource(url);\r\n                this.hlsVideo.isPlay = this.isPlay\r\n                let isPause = true;\r\n\r\n                // 播放 \r\n                this.hlsVideo.on(Hls.Events.MANIFEST_PARSED, (d, e) => {\r\n                    hlsVideoDom.play().then(() => {\r\n                        flag = 1;\r\n                        this.isPlay = true\r\n                    }).catch((err) => {\r\n                        this.close();\r\n                    });\r\n                })\r\n\r\n                this.hlsVideo.on(Hls.Events.BUFFER_APPENDING, (event, data) => {\r\n                    // console.log('数据附加到缓冲区时触发') \r\n                    let { duration, elementaryStreams } = data.frag\r\n                    timeEnd = elementaryStreams.video.endDTS - duration + 2;\r\n                    isPause = true\r\n                })\r\n                this.hlsVideo.on(Hls.Events.FRAG_LOADED, (event, data) => {\r\n                    if (!this.isPlay) this.isPlay = true;\r\n                    if (isPause && hlsVideoDom) {\r\n\r\n                        hlsVideoDom.play().catch((err) => {\r\n                            console.log('播放时发生错误:' + err);\r\n\r\n                        });\r\n                        isPause = false\r\n                    }\r\n\r\n                })\r\n\r\n\r\n                this.hlsVideo.on(Hls.Events.ERROR, (event, data) => {\r\n                    const errType = data.type;\r\n                    const errDetails = data.details;\r\n                    const errFatal = data.fatal;\r\n\r\n                    if (errFatal) {\r\n                        switch (errType) {\r\n                            case Hls.ErrorTypes.NETWORK_ERROR:\r\n                                console.log('遇到网络错误，尝试恢复', errDetails);\r\n                                this.hlsVideo.startLoad();\r\n                                break;\r\n\r\n                            case Hls.ErrorTypes.MEDIA_ERROR:\r\n                                console.log('遇到媒体错误，尝试恢复', errDetails);\r\n                                this.hlsVideo.recoverMediaError();\r\n                                break;\r\n                            default:\r\n                                // cannot recover\r\n                                console.log('播放时发生错误：', errDetails);\r\n                                this.close(); // 关闭keep定时器，关闭播放器\r\n                        }\r\n                    }\r\n                })\r\n\r\n                // 12、waiting：视频加载等待。当视频由于需要缓冲下一帧而停止，等待时触发\r\n                hlsVideoDom.onwaiting = (e) => {\r\n                    // console.log('视频加载等待') \r\n                    if (!isPause) {\r\n                        hlsVideoDom.pause();\r\n                        isPause = true\r\n\r\n                    }\r\n\r\n                }\r\n                hlsVideoDom.onpause = (e) => {\r\n                    isPause = true\r\n                    if (timeEnd && timeEnd > timeDisplay) {\r\n                        hlsVideoDom.currentTime = timeEnd\r\n                    }\r\n\r\n                }\r\n                // 当浏览器开始请求数据时 \r\n                hlsVideoDom.ontimeupdate = (e, v) => {\r\n                    timeDisplay = Math.floor(hlsVideoDom.currentTime);\r\n                    if (timeEnd - timeDisplay > 60) {\r\n                        hlsVideoDom.currentTime = timeEnd\r\n                        hlsVideoDom.play().catch(err => {\r\n                            // console.log('1---','播放出错')\r\n                        })\r\n                        isPause = false\r\n                    }\r\n\r\n                };\r\n\r\n                this.$emit('sendPlayerList', this.hlsVideo, this.currentCount)\r\n            } else if (hlsVideoDom.canPlayType('application/vnd.apple.mpegurl')) {\r\n                // 如果支持原生播放\r\n                hlsVideoDom.src = url\r\n                // 自动播放  \r\n                hlsVideoDom.onloadedmetadata = () => {\r\n\r\n                    hlsVideoDom.play()\r\n                };\r\n            }\r\n        },\r\n\r\n        goBackTime(hlsVideoDom) {\r\n\r\n        },\r\n        destroyVideo() {\r\n            if (this.hlsVideo) {\r\n                this.hlsVideo.destroy();\r\n                this.hlsVideo = null;\r\n            }\r\n\r\n        },\r\n        internalClose() {\r\n            const videoElement = document.getElementById(this.id); // 获取视频元素的引用\r\n            if (videoElement) {\r\n                videoElement.pause()\r\n                videoElement.removeAttribute('src');\r\n\r\n            }\r\n        },\r\n        close() {\r\n            if (this.isPlay) {\r\n                this.internalClose();\r\n                this.isPlay = false;\r\n            }\r\n\r\n            this.isPlay = false;\r\n        },\r\n        clickVideo() {\r\n            // this.destroyVideo();\r\n            if (this.hlsVideo) this.$parent.getVideoOne(this.hlsVideo)\r\n        }\r\n\r\n\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.video-buffering::after {\r\n    z-index: 44;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    border: 13px dashed red;\r\n    border-top-color: red;\r\n    animation: spin 1s infinite linear;\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n        transform: translate(-50%, -50%) rotate(0deg);\r\n    }\r\n\r\n    100% {\r\n        transform: translate(-50%, -50%) rotate(360deg);\r\n    }\r\n}\r\n\r\n\r\n.videoShowItem {\r\n    background: linear-gradient(to bottom, rgba(24, 24, 24, 0.8) 10%, rgba(59, 59, 59, 0.7) 40%, rgba(59, 59, 59, 0.6) 50%);\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    .hide-progress-bar::-webkit-media-controls-timeline {\r\n        display: none !important;\r\n    }\r\n\r\n    .hide-progress-bar::-webkit-media-controls-buffering-bar {\r\n        display: none !important;\r\n    }\r\n\r\n    .hide-progress-bar::-moz-progress-bar {\r\n        display: none !important;\r\n    }\r\n\r\n    .hide-progress-bar::-ms-track {\r\n        display: none !important;\r\n\r\n    }\r\n\r\n    .hide-progress-bar::-ms-fill-lower {\r\n        display: none !important;\r\n    }\r\n\r\n    .title {\r\n        width: 100%;\r\n        height: 2rem;\r\n        line-height: 2rem;\r\n        background: linear-gradient(to bottom, rgba(24, 24, 24, 0.7) 10%, rgba(59, 59, 59, 0.3) 40%, rgba(59, 59, 59, 0.2) 50%);\r\n        position: absolute;\r\n        top: 0;\r\n        z-index: 2;\r\n        padding-left: 1rem;\r\n        font-size: 1rem;\r\n        color: rgb(226, 226, 226);\r\n\r\n    }\r\n\r\n    .active {\r\n        display: block;\r\n        animation-name: example;\r\n        animation-duration: .7s;\r\n    }\r\n\r\n    @keyframes example {\r\n        0% {\r\n            color: rgba(51, 51, 51, 0);\r\n            opacity: 0;\r\n        }\r\n\r\n\r\n        30% {\r\n            color: rgba(175, 175, 175, 0.5);\r\n            opacity: .5;\r\n\r\n        }\r\n\r\n        100% {\r\n            color: rgba(175, 175, 175, 1);\r\n\r\n            opacity: 1;\r\n        }\r\n    }\r\n\r\n    video {\r\n        object-fit: contain;\r\n\r\n\r\n\r\n    }\r\n\r\n    .errorTxt {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        font-size: 15px;\r\n        z-index: 99;\r\n        color: #f3f2f2;\r\n\r\n    }\r\n}\r\n</style>"], "mappings": "AAgBA,OAAAA,GAAA;AACA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,QAAA;MACAC,MAAA;MACAC,OAAA;MACAC,YAAA;MACAC,KAAA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,EAAAA,CAAA;IACA;IACAK,WAAA;MACAP,IAAA,EAAAQ,MAAA;MACAN,OAAA,EAAAA,CAAA;QACA;UAAAL,WAAA;QAAA;MACA;IACA;IACAY,MAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAQ,QAAA,GAEA;EACAC,KAAA,GAGA;EACAC,cAAA;IACA;;IAEA,KAAAC,YAAA;IACAC,YAAA,MAAAlB,KAAA;EACA;EACAmB,QAAA;IACA,KAAAC,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAC,YAAA;MAEAC,MAAA,CAAAC,QAAA;QACA,KAAAF,YAAA;MACA;IACA;EAIA;EACAG,OAAA;IACAC,sBAAAC,KAAA;MACA,MAAAC,YAAA,GAAAD,KAAA,CAAAE,MAAA;MACA,KAAAC,WAAA,GAAAF,YAAA,CAAAG,UAAA,GAAAH,YAAA,CAAAI,WAAA;MACA,KAAAC,MAAA,CAAAC,MAAA,wBAAAJ,WAAA;MACA;IACA;IACAR,aAAA;MAEA,IAAAa,IAAA,GAAAC,QAAA,CAAAD,IAAA,CAAAE,YAAA;MACA,IAAAC,GAAA,GAAAF,QAAA,CAAAG,cAAA,MAAAnC,EAAA;MACA,IAAAkC,GAAA;QACA,IAAAE,GAAA,GAAAF,GAAA,CAAAD,YAAA;QACA,IAAAG,GAAA,IAAAL,IAAA;UACA,KAAAnC,YAAA;QACA;UACA,KAAAA,YAAA;QACA;MAEA;IACA;IACAyC,WAAA;MACA,UAAA5C,QAAA,CAAAC,MAAA;QACA,KAAAA,MAAA,QAAAD,QAAA,CAAAC,MAAA;MACA;QACA,KAAAG,KAAA,GAAAyC,UAAA;UACA,KAAAC,WAAA,MAAA9C,QAAA,CAAA+C,SAAA;QACA;MAEA;IAEA;IACA;IACAD,YAAAE,GAAA,EAAAlD,IAAA;MACA,KAAAuB,YAAA;MACA,KAAAhB,WAAA,GAAAP,IAAA;MACA,KAAAG,MAAA;MACA,IAAAgD,IAAA;MACA,KAAAjD,QAAA;MACA,MAAAkD,WAAA,GAAAX,QAAA,CAAAG,cAAA,MAAAnC,EAAA;MACA,IAAAV,GAAA,CAAAsD,WAAA;QAEA,IAAAC,OAAA;QACA,IAAAC,WAAA;QAEA,KAAArD,QAAA,OAAAH,GAAA;QACA,KAAAG,QAAA,CAAAsD,WAAA,CAAAJ,WAAA;QACA,KAAAlD,QAAA,CAAAuD,UAAA,CAAAP,GAAA;QACA,KAAAhD,QAAA,CAAAC,MAAA,QAAAA,MAAA;QACA,IAAAuD,OAAA;;QAEA;QACA,KAAAxD,QAAA,CAAAyD,EAAA,CAAA5D,GAAA,CAAA6D,MAAA,CAAAC,eAAA,GAAAC,CAAA,EAAAC,CAAA;UACAX,WAAA,CAAAY,IAAA,GAAAC,IAAA;YACAd,IAAA;YACA,KAAAhD,MAAA;UACA,GAAA+D,KAAA,CAAAC,GAAA;YACA,KAAAC,KAAA;UACA;QACA;QAEA,KAAAlE,QAAA,CAAAyD,EAAA,CAAA5D,GAAA,CAAA6D,MAAA,CAAAS,gBAAA,GAAArC,KAAA,EAAA/B,IAAA;UACA;UACA;YAAAqE,QAAA;YAAAC;UAAA,IAAAtE,IAAA,CAAAuE,IAAA;UACAlB,OAAA,GAAAiB,iBAAA,CAAAE,KAAA,CAAAC,MAAA,GAAAJ,QAAA;UACAZ,OAAA;QACA;QACA,KAAAxD,QAAA,CAAAyD,EAAA,CAAA5D,GAAA,CAAA6D,MAAA,CAAAe,WAAA,GAAA3C,KAAA,EAAA/B,IAAA;UACA,UAAAE,MAAA,OAAAA,MAAA;UACA,IAAAuD,OAAA,IAAAN,WAAA;YAEAA,WAAA,CAAAY,IAAA,GAAAE,KAAA,CAAAC,GAAA;cACAS,OAAA,CAAAC,GAAA,cAAAV,GAAA;YAEA;YACAT,OAAA;UACA;QAEA;QAGA,KAAAxD,QAAA,CAAAyD,EAAA,CAAA5D,GAAA,CAAA6D,MAAA,CAAAkB,KAAA,GAAA9C,KAAA,EAAA/B,IAAA;UACA,MAAA8E,OAAA,GAAA9E,IAAA,CAAAS,IAAA;UACA,MAAAsE,UAAA,GAAA/E,IAAA,CAAAgF,OAAA;UACA,MAAAC,QAAA,GAAAjF,IAAA,CAAAkF,KAAA;UAEA,IAAAD,QAAA;YACA,QAAAH,OAAA;cACA,KAAAhF,GAAA,CAAAqF,UAAA,CAAAC,aAAA;gBACAT,OAAA,CAAAC,GAAA,gBAAAG,UAAA;gBACA,KAAA9E,QAAA,CAAAoF,SAAA;gBACA;cAEA,KAAAvF,GAAA,CAAAqF,UAAA,CAAAG,WAAA;gBACAX,OAAA,CAAAC,GAAA,gBAAAG,UAAA;gBACA,KAAA9E,QAAA,CAAAsF,iBAAA;gBACA;cACA;gBACA;gBACAZ,OAAA,CAAAC,GAAA,aAAAG,UAAA;gBACA,KAAAZ,KAAA;cAAA;YACA;UACA;QACA;;QAEA;QACAhB,WAAA,CAAAqC,SAAA,GAAA1B,CAAA;UACA;UACA,KAAAL,OAAA;YACAN,WAAA,CAAAsC,KAAA;YACAhC,OAAA;UAEA;QAEA;QACAN,WAAA,CAAAuC,OAAA,GAAA5B,CAAA;UACAL,OAAA;UACA,IAAAJ,OAAA,IAAAA,OAAA,GAAAC,WAAA;YACAH,WAAA,CAAAwC,WAAA,GAAAtC,OAAA;UACA;QAEA;QACA;QACAF,WAAA,CAAAyC,YAAA,IAAA9B,CAAA,EAAA+B,CAAA;UACAvC,WAAA,GAAAwC,IAAA,CAAAC,KAAA,CAAA5C,WAAA,CAAAwC,WAAA;UACA,IAAAtC,OAAA,GAAAC,WAAA;YACAH,WAAA,CAAAwC,WAAA,GAAAtC,OAAA;YACAF,WAAA,CAAAY,IAAA,GAAAE,KAAA,CAAAC,GAAA;cACA;YAAA,CACA;YACAT,OAAA;UACA;QAEA;QAEA,KAAAuC,KAAA,wBAAA/F,QAAA,OAAAW,YAAA;MACA,WAAAuC,WAAA,CAAA8C,WAAA;QACA;QACA9C,WAAA,CAAA+C,GAAA,GAAAjD,GAAA;QACA;QACAE,WAAA,CAAAgD,gBAAA;UAEAhD,WAAA,CAAAY,IAAA;QACA;MACA;IACA;IAEAqC,WAAAjD,WAAA,GAEA;IACA7B,aAAA;MACA,SAAArB,QAAA;QACA,KAAAA,QAAA,CAAAoG,OAAA;QACA,KAAApG,QAAA;MACA;IAEA;IACAqG,cAAA;MACA,MAAAtE,YAAA,GAAAQ,QAAA,CAAAG,cAAA,MAAAnC,EAAA;MACA,IAAAwB,YAAA;QACAA,YAAA,CAAAyD,KAAA;QACAzD,YAAA,CAAAuE,eAAA;MAEA;IACA;IACApC,MAAA;MACA,SAAAjE,MAAA;QACA,KAAAoG,aAAA;QACA,KAAApG,MAAA;MACA;MAEA,KAAAA,MAAA;IACA;IACAsG,WAAA;MACA;MACA,SAAAvG,QAAA,OAAAwG,OAAA,CAAAC,WAAA,MAAAzG,QAAA;IACA;EAGA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}