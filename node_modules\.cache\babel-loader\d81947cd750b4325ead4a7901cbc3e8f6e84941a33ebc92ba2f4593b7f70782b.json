{"ast": null, "code": "export default {\n  name: 'warmingInfoPop',\n  data() {\n    return {};\n  },\n  props: {\n    oneWarnPushInfo: {\n      type: Object,\n      default: {\n        return: {}\n      }\n    }\n  },\n  computed: {},\n  watch: {},\n  mounted() {\n    this.$nextTick(() => {});\n  },\n  methods: {\n    clickHandler() {\n      this.openInfo(true, this.oneWarnPushInfo);\n      this.closeFun();\n    },\n    closeFun() {\n      this.$store.commit('action/getEventSubBoxInfoFlag', false);\n    },\n    // 打开设备列表\n    openInfo(flag, val) {\n      this.$store.commit(\"action/getInformationBox\", flag);\n      let params = {\n        org_index: val.orgIndex\n      };\n      this.$store.dispatch(\"action/getTypeVideoAll\", params);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "props", "oneWarnPushInfo", "type", "Object", "default", "return", "computed", "watch", "mounted", "$nextTick", "methods", "clickHandler", "openInfo", "closeFun", "$store", "commit", "flag", "val", "params", "org_index", "orgIndex", "dispatch"], "sources": ["src/views/dataScreen/fileType/warmingInfoPop.vue"], "sourcesContent": ["<template>\r\n    <div class=\"eventSubscriptionCon boxBgStyle\">\r\n        <div class=\"box-card\">\r\n            <div class=\"topMain\">\r\n                <div class=\"titleTxt\">\r\n                    <div class=\"txt\">事件详情</div>\r\n                </div>\r\n                \r\n                <div class=\"orgTypeShow\">\r\n                        <a href=\"javascript:;\" class=\"txtName\" @click=\"clickHandler\">\r\n                            周边资源\r\n                        </a>\r\n                </div>\r\n                <div class=\"delete\" @click=\"closeFun\">\r\n\r\n                    <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n                </div>\r\n              \r\n            </div>\r\n\r\n            <div class=\"tableStyle\">\r\n\r\n                <div class=\"item_one\">\r\n                    <div class=\"itemTit flex\">\r\n                        <div class=\"left flex\">\r\n                            <div class=\"pic\">\r\n                                告警等级：\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                {{ oneWarnPushInfo?.alarmLevelName }}\r\n                            </div>\r\n\r\n                        </div>\r\n                     \r\n                    </div>\r\n                    <div class=\"item_content\">\r\n                        <div class=\"item\">\r\n                            <span>报警时间： </span>\r\n                            <span> {{ oneWarnPushInfo?.time }}</span>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <span>报警类型： </span>\r\n                            <span> {{ oneWarnPushInfo?.alarmTypeName }}</span>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <span>设备名称： </span>\r\n                            <span> {{ oneWarnPushInfo?.deviceName }}</span>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <span>所属组织： </span>\r\n                            <span> {{ oneWarnPushInfo?.orgName }}</span>\r\n                        </div>\r\n\r\n\r\n                    </div>\r\n                    \r\n                </div>\r\n\r\n            </div>\r\n\r\n        </div>\r\n\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nexport default {\r\n    name: 'warmingInfoPop',\r\n    data() {\r\n        return {\r\n          \r\n        }\r\n    },\r\n    props:{\r\n        oneWarnPushInfo:{\r\n            type:Object,\r\n            default:{\r\n                return:{\r\n                    \r\n                }\r\n            }\r\n        }\r\n    },\r\n    computed:{\r\n         \r\n    },\r\n    watch:{\r\n         \r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        clickHandler(){\r\n            this.openInfo(true,this.oneWarnPushInfo)\r\n            this.closeFun();\r\n\r\n        },\r\n        closeFun() {\r\n            this.$store.commit('action/getEventSubBoxInfoFlag', false)\r\n        },\r\n         // 打开设备列表\r\n        openInfo(flag, val) {\r\n            this.$store.commit(\"action/getInformationBox\", flag)\r\n          \r\n                let params = {\r\n                    org_index: val.orgIndex\r\n                }\r\n                this.$store.dispatch(\"action/getTypeVideoAll\", params)\r\n \r\n        }\r\n    }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.eventSubscriptionCon {\r\n    pointer-events: stroke;\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 100;\r\n\r\n    .topMain {\r\n        .titleTxt {\r\n            width: 300px;\r\n            position: relative;\r\n            left: -60px;\r\n            text-align: right;\r\n            height: 100px;\r\n            line-height: 100px;\r\n            font-size: 38px;\r\n            background: url('@/assets/images/mainPics/i_title.png') no-repeat 100%;\r\n            background-size: 100% 100%;\r\n            text-align: center;\r\n\r\n        }\r\n        .orgTypeShow{\r\n            width:40%;\r\n            text-align: right;\r\n            a{\r\n                font-size: 30px;\r\n                text-decoration: none;\r\n                color:rgb(78, 210, 233);\r\n                font-family:Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;\r\n            }\r\n        }\r\n        .delete {\r\n            width: 30px;\r\n\r\n            img {\r\n                width: 100%;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.flex {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n\r\n.tableStyle {\r\n    width: 700px;\r\n    max-height: 1200px;\r\n\r\n    font-size: 25px;\r\n    padding: 0 20px;\r\n    overflow-y: auto;\r\n\r\n    .item_one {\r\n        padding: 10px 0;\r\n        border-bottom: 2px solid #ccc;\r\n        \r\n    }\r\n\r\n    .itemTit {\r\n        font-size: 35px;\r\n        margin: 15px 0;\r\n\r\n        .pic {\r\n            margin-right: 20px;\r\n        }\r\n        .txt{\r\n            font-weight: bold;\r\n            color: red;\r\n        }\r\n\r\n        .right {\r\n            width: 40px;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n\r\n    .item_content {\r\n\r\n        .item {\r\n            padding: 10px 0;\r\n\r\n            span:nth-child(1) {\r\n                color: #ccc;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\n.box-card {\r\n    color: #fff;\r\n    width: fit-content;\r\n    // background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n    // background-size: 100% 100%;\r\n    border: 0;\r\n\r\n\r\n    .peoplePerShow {\r\n        display: flex;\r\n\r\n        .totalHouses {\r\n            margin-right: 20px;\r\n            line-height: 50px;\r\n        }\r\n    }\r\n\r\n    .TypeShow {\r\n        display: flex;\r\n\r\n        .TypeItem {\r\n            margin: 0 5px;\r\n            cursor: pointer;\r\n            padding: 5px 10px;\r\n            // box-shadow: 0 0 5px rgb(102, 177, 255), 0 0 5px #fff;\r\n            user-select: none;\r\n        }\r\n\r\n        .typeSelect_list {\r\n            display: flex;\r\n        }\r\n\r\n        .TypeItemAction {\r\n            color: #fff;\r\n            // border: 2px solid rgb(116, 164, 216);\r\n            padding: 30px 0;\r\n            font-size: 25px;\r\n            text-shadow: 0 0 5px rgb(46, 116, 192), 0 0 5px rgb(46, 116, 192);\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAkEA;EACAA,IAAA;EACAC,KAAA;IACA,QAEA;EACA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;QACAC,MAAA,GAEA;MACA;IACA;EACA;EACAC,QAAA,GAEA;EACAC,KAAA,GAEA;EACAC,QAAA;IACA,KAAAC,SAAA,QACA;EACA;EAEAC,OAAA;IACAC,aAAA;MACA,KAAAC,QAAA,YAAAX,eAAA;MACA,KAAAY,QAAA;IAEA;IACAA,SAAA;MACA,KAAAC,MAAA,CAAAC,MAAA;IACA;IACA;IACAH,SAAAI,IAAA,EAAAC,GAAA;MACA,KAAAH,MAAA,CAAAC,MAAA,6BAAAC,IAAA;MAEA,IAAAE,MAAA;QACAC,SAAA,EAAAF,GAAA,CAAAG;MACA;MACA,KAAAN,MAAA,CAAAO,QAAA,2BAAAH,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}