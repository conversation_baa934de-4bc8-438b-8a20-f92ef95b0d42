{"ast": null, "code": "import { getUserList, getUserMenuInfoByUserId, newUserMenu } from '@/api/userMenu';\nimport mainHeader from './components/mainHeader.vue';\nexport default {\n  name: 'AuthorityManagement',\n  components: {\n    mainHeader\n  },\n  data() {\n    return {\n      roleTableList: [],\n      currentRow: null,\n      //用户列表中选中的一条\n      AuthorityInfoByUserId: [],\n      //权限列表\n      multipleSelection: [] //权限列表选中\n    };\n  },\n  mounted() {\n    this.init();\n  },\n  destroyed() {},\n  methods: {\n    async init() {\n      // 所有用户\n      await getUserList().then(res => {\n        this.roleTableList = res.data.extra;\n        // 用户列表默认选中第一行\n        this.currentRow = this.roleTableList[0];\n        this.$refs.singleTable.setCurrentRow(this.currentRow);\n        this.getUserInfoAuthority(this.currentRow.userId);\n      });\n    },\n    // 点击用户列表\n    handleCurrentChange(val) {\n      this.currentRow = val;\n      this.getUserInfoAuthority(this.currentRow.userId);\n    },\n    // 获取选中用户的权限\n    async getUserInfoAuthority(userId) {\n      let res = await getUserMenuInfoByUserId(userId);\n      this.AuthorityInfoByUserId = res.data.extra;\n      this.isAuthorityFlag();\n    },\n    // 默认权限初始值\n    isAuthorityFlag() {\n      this.AuthorityInfoByUserId.forEach((item, index) => {\n        this.$nextTick(() => {\n          if (item.checked) {\n            this.$refs.multipleTable.toggleRowSelection(this.AuthorityInfoByUserId[index], true);\n          } else {\n            this.$refs.multipleTable.toggleRowSelection(this.AuthorityInfoByUserId[index], false);\n          }\n        });\n      });\n    },\n    // 权限列表选中\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n    // 取消按钮\n    cancelChecked() {\n      this.$refs.multipleTable.clearSelection();\n    },\n    // 恢复初始值\n    initialData() {\n      this.isAuthorityFlag();\n    },\n    // 提交按钮\n    async submitData() {\n      let authorityIdList = [];\n      this.multipleSelection.forEach(item => {\n        authorityIdList.push(item.id);\n      });\n      let params = {\n        menus: authorityIdList,\n        userId: this.currentRow.userId\n      };\n      await newUserMenu(params).then(res => {\n        this.$message({\n          message: '修改成功',\n          type: 'success'\n        });\n        this.$refs.header.initMenu();\n        this.getUserInfoAuthority(this.currentRow.userId);\n      }).catch(e => {\n        this.$message({\n          message: \"修改失败\",\n          type: \"error\"\n        });\n      });\n    },\n    // 退出权限管理\n    quitBtn() {\n      this.$confirm('是否退出当前用户权限管理?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$router.push({\n          name: 'MainPage'\n        });\n        this.$message({\n          type: 'success',\n          message: '退出成功!'\n        });\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserList", "getUserMenuInfoByUserId", "newUserMenu", "<PERSON><PERSON><PERSON><PERSON>", "name", "components", "data", "roleTableList", "currentRow", "AuthorityInfoByUserId", "multipleSelection", "mounted", "init", "destroyed", "methods", "then", "res", "extra", "$refs", "singleTable", "setCurrentRow", "getUserInfoAuthority", "userId", "handleCurrentChange", "val", "isAuthorityFlag", "for<PERSON>ach", "item", "index", "$nextTick", "checked", "multipleTable", "toggleRowSelection", "handleSelectionChange", "cancelChecked", "clearSelection", "initialData", "submitData", "authorityIdList", "push", "id", "params", "menus", "$message", "message", "type", "header", "initMenu", "catch", "e", "quitBtn", "$confirm", "confirmButtonText", "cancelButtonText", "$router"], "sources": ["src/views/mainPage/AuthorityManagement.vue"], "sourcesContent": ["<!-- 权限管理 -->\r\n<template>\r\n    <div class=\"manage\">\r\n        <mainHeader ref=\"header\"></mainHeader>\r\n        <el-container>\r\n            <el-header></el-header>\r\n            <el-container>\r\n                <el-aside width=\"35rem\">\r\n                    <div class=\"roleList\">\r\n                        <div class=\"title\">\r\n                            <div class=\"txt\">\r\n                                用户列表\r\n                            </div>\r\n                            <div class=\"sumData\">\r\n                                共有数据 {{ roleTableList.length }} 条\r\n                            </div>\r\n                        </div>\r\n                        <el-table ref=\"singleTable\" :data=\"roleTableList\" style=\"width: 95%\" height=\"80vh\"\r\n                            highlight-current-row @current-change=\"handleCurrentChange\">\r\n                            <el-table-column prop=\"userId\" label=\"编码\" width=\"90px\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"username\" label=\"用户名\" align=\"center\"></el-table-column>\r\n                            <el-table-column prop=\"company\" label=\"角色名称\" align=\"center\"></el-table-column>\r\n\r\n                        </el-table>\r\n                    </div>\r\n                </el-aside>\r\n                <el-main>\r\n                    <div class=\"title\">\r\n                        <div class=\"left\">\r\n                            <div class=\"txt\">\r\n                                权限管理\r\n                            </div>\r\n\r\n                        </div>\r\n\r\n                        <div class=\"rightBtn\">\r\n                            <div class=\"submit\">\r\n                                <el-button size=\"medium\" icon=\"el-icon-upload2\" @click=\"submitData\">提交</el-button>\r\n                            </div>\r\n                            <el-button type=\"primary\" size=\"medium\" icon=\"el-icon-refresh-left\"\r\n                                @click=\"initialData\">恢复初始值</el-button>\r\n                            <el-button type=\"warning\" size=\"medium\" icon=\"el-icon-circle-close\"\r\n                                @click=\"cancelChecked()\">取消</el-button>\r\n                            <div class=\"quit\" @click=\"quitBtn\">\r\n                                <i class=\"el-icon-right\"></i>\r\n                                退出权限管理\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"authorityList\">\r\n                        <el-table ref=\"multipleTable\" :data=\"AuthorityInfoByUserId\" tooltip-effect=\"dark\"\r\n                            style=\"width: 100%\" @selection-change=\"handleSelectionChange\" height=\"80vh\">\r\n                            <el-table-column type=\"selection\" width=\"155\">\r\n                            </el-table-column>\r\n                            <el-table-column prop=\"levelOneMenu\" label=\"一级菜单\">\r\n                            </el-table-column>\r\n                            <el-table-column prop=\"levelTwoMenu\" label=\"二级菜单\">\r\n                            </el-table-column>\r\n                            <el-table-column prop=\"levelThreeMenu\" label=\"三级菜单\" show-overflow-tooltip>\r\n                            </el-table-column>\r\n                        </el-table>\r\n                    </div>\r\n                </el-main>\r\n            </el-container>\r\n        </el-container>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getUserList, getUserMenuInfoByUserId, newUserMenu } from '@/api/userMenu'\r\nimport mainHeader from './components/mainHeader.vue'\r\nexport default {\r\n    name: 'AuthorityManagement',\r\n    components: {\r\n        mainHeader\r\n    },\r\n    data() {\r\n        return {\r\n            roleTableList: [],\r\n            currentRow: null, //用户列表中选中的一条\r\n            AuthorityInfoByUserId: [], //权限列表\r\n            multipleSelection: [], //权限列表选中\r\n        };\r\n    },\r\n\r\n    mounted() {\r\n        this.init();\r\n    },\r\n    destroyed() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        async init() {\r\n            // 所有用户\r\n            await getUserList().then(res => {\r\n                this.roleTableList = res.data.extra\r\n                // 用户列表默认选中第一行\r\n                this.currentRow = this.roleTableList[0]\r\n                this.$refs.singleTable.setCurrentRow(this.currentRow);\r\n                this.getUserInfoAuthority(this.currentRow.userId)\r\n\r\n            })\r\n\r\n        },\r\n        // 点击用户列表\r\n        handleCurrentChange(val) {\r\n            this.currentRow = val;\r\n            this.getUserInfoAuthority(this.currentRow.userId)\r\n        },\r\n        // 获取选中用户的权限\r\n        async getUserInfoAuthority(userId) {\r\n            let res = await getUserMenuInfoByUserId(userId)\r\n            this.AuthorityInfoByUserId = res.data.extra;\r\n            this.isAuthorityFlag()\r\n        },\r\n        // 默认权限初始值\r\n        isAuthorityFlag() {\r\n            this.AuthorityInfoByUserId.forEach((item, index) => {\r\n                this.$nextTick(() => {\r\n                    if (item.checked) {\r\n                        this.$refs.multipleTable.toggleRowSelection(this.AuthorityInfoByUserId[index], true)\r\n\r\n                    } else {\r\n                        this.$refs.multipleTable.toggleRowSelection(this.AuthorityInfoByUserId[index], false)\r\n\r\n                    }\r\n                })\r\n            })\r\n        },\r\n        // 权限列表选中\r\n        handleSelectionChange(val) {\r\n            this.multipleSelection = val;\r\n        },\r\n        // 取消按钮\r\n        cancelChecked() {\r\n            this.$refs.multipleTable.clearSelection();\r\n        },\r\n        // 恢复初始值\r\n        initialData() {\r\n            this.isAuthorityFlag()\r\n        },\r\n        // 提交按钮\r\n        async submitData() {\r\n            let authorityIdList = [];\r\n            this.multipleSelection.forEach((item) => {\r\n                authorityIdList.push(item.id);\r\n            })\r\n            let params = {\r\n                menus: authorityIdList,\r\n                userId: this.currentRow.userId\r\n            }\r\n            await newUserMenu(params).then(res => {\r\n                this.$message({\r\n                    message: '修改成功',\r\n                    type: 'success'\r\n                });\r\n                this.$refs.header.initMenu();\r\n                this.getUserInfoAuthority(this.currentRow.userId)\r\n            }).catch(e => {\r\n                this.$message({\r\n                    message: \"修改失败\",\r\n                    type: \"error\"\r\n                })\r\n            })\r\n        },\r\n\r\n        // 退出权限管理\r\n        quitBtn() {\r\n            this.$confirm('是否退出当前用户权限管理?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            })\r\n                .then(() => {\r\n                    this.$router.push({\r\n                        name: 'MainPage'\r\n                    })\r\n                    this.$message({\r\n                        type: 'success',\r\n                        message: '退出成功!'\r\n                    })\r\n\r\n                })\r\n        }\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.manage {\r\n    width: 100%;\r\n    height: 100vh;\r\n    background-color: #fff;\r\n\r\n    ::-webkit-scrollbar {\r\n        /*滚动条宽度*/\r\n        width: 3px;\r\n        height: 12px;\r\n    }\r\n\r\n    // 滚动条背景颜色\r\n    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {\r\n        background-color: rgba(26, 160, 250, .2);\r\n\r\n    }\r\n\r\n    // 滚动条颜色\r\n    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {\r\n        width: 5px;\r\n        border-radius: 15px;\r\n        background-color: rgba(71, 179, 251, 0.6);\r\n\r\n    }\r\n\r\n    .el-aside {\r\n        color: #333;\r\n        padding: 1rem 0 1rem 1rem;\r\n        box-sizing: border-box;\r\n        overflow: hidden;\r\n\r\n        .roleList {\r\n            width: 90%;\r\n            height: 90vh;\r\n            box-shadow: 0px 0px 15px #cccccc91;\r\n            z-index: 99;\r\n            border-radius: 5px;\r\n\r\n            .title {\r\n                display: flex;\r\n                align-items: center;\r\n                color: rgb(26, 160, 250);\r\n                width: 90%;\r\n                height: 2.4rem;\r\n                padding-left: 1rem;\r\n\r\n                background: linear-gradient(to right, rgba(43, 170, 253, .4) 10%, rgba(255, 255, 255, 1) 100%);\r\n\r\n                .txt {\r\n                    font-weight: bold;\r\n                    padding-right: .5rem;\r\n\r\n                }\r\n\r\n                .sumData {\r\n                    font-size: .6rem;\r\n                    color: rgba(26, 160, 250, .8);\r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n\r\n    .el-main {\r\n        height: 100vh;\r\n        overflow: hidden;\r\n\r\n        .title {\r\n            padding-bottom: .4rem;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            border-bottom: 3px solid #0995fd;\r\n\r\n            .txt {\r\n                color: #3FAEFF;\r\n                font-weight: bold;\r\n            }\r\n\r\n            .rightBtn {\r\n                width: 30rem;\r\n                display: flex;\r\n                justify-content: space-between;\r\n\r\n                .submit .el-button {\r\n                    background-color: #00b9d5;\r\n                    color: #fff;\r\n                }\r\n\r\n                .quit {\r\n                    color: #ff6b43;\r\n                    line-height: 2.2rem;\r\n                    font-size: .9rem;\r\n                    font-weight: bold;\r\n                    cursor: pointer;\r\n                }\r\n\r\n                .quit:active {\r\n                    color: #ff6c436e;\r\n                }\r\n            }\r\n        }\r\n\r\n        .levelThreeMenu {\r\n            height: 100vh;\r\n        }\r\n    }\r\n\r\n\r\n\r\n\r\n}\r\n</style>"], "mappings": "AAsEA,SAAAA,WAAA,EAAAC,uBAAA,EAAAC,WAAA;AACA,OAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,iBAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,UAAA,GAEA;EAEAC,OAAA;IACA,MAAAF,KAAA;MACA;MACA,MAAAZ,WAAA,GAAAe,IAAA,CAAAC,GAAA;QACA,KAAAT,aAAA,GAAAS,GAAA,CAAAV,IAAA,CAAAW,KAAA;QACA;QACA,KAAAT,UAAA,QAAAD,aAAA;QACA,KAAAW,KAAA,CAAAC,WAAA,CAAAC,aAAA,MAAAZ,UAAA;QACA,KAAAa,oBAAA,MAAAb,UAAA,CAAAc,MAAA;MAEA;IAEA;IACA;IACAC,oBAAAC,GAAA;MACA,KAAAhB,UAAA,GAAAgB,GAAA;MACA,KAAAH,oBAAA,MAAAb,UAAA,CAAAc,MAAA;IACA;IACA;IACA,MAAAD,qBAAAC,MAAA;MACA,IAAAN,GAAA,SAAAf,uBAAA,CAAAqB,MAAA;MACA,KAAAb,qBAAA,GAAAO,GAAA,CAAAV,IAAA,CAAAW,KAAA;MACA,KAAAQ,eAAA;IACA;IACA;IACAA,gBAAA;MACA,KAAAhB,qBAAA,CAAAiB,OAAA,EAAAC,IAAA,EAAAC,KAAA;QACA,KAAAC,SAAA;UACA,IAAAF,IAAA,CAAAG,OAAA;YACA,KAAAZ,KAAA,CAAAa,aAAA,CAAAC,kBAAA,MAAAvB,qBAAA,CAAAmB,KAAA;UAEA;YACA,KAAAV,KAAA,CAAAa,aAAA,CAAAC,kBAAA,MAAAvB,qBAAA,CAAAmB,KAAA;UAEA;QACA;MACA;IACA;IACA;IACAK,sBAAAT,GAAA;MACA,KAAAd,iBAAA,GAAAc,GAAA;IACA;IACA;IACAU,cAAA;MACA,KAAAhB,KAAA,CAAAa,aAAA,CAAAI,cAAA;IACA;IACA;IACAC,YAAA;MACA,KAAAX,eAAA;IACA;IACA;IACA,MAAAY,WAAA;MACA,IAAAC,eAAA;MACA,KAAA5B,iBAAA,CAAAgB,OAAA,CAAAC,IAAA;QACAW,eAAA,CAAAC,IAAA,CAAAZ,IAAA,CAAAa,EAAA;MACA;MACA,IAAAC,MAAA;QACAC,KAAA,EAAAJ,eAAA;QACAhB,MAAA,OAAAd,UAAA,CAAAc;MACA;MACA,MAAApB,WAAA,CAAAuC,MAAA,EAAA1B,IAAA,CAAAC,GAAA;QACA,KAAA2B,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,KAAA3B,KAAA,CAAA4B,MAAA,CAAAC,QAAA;QACA,KAAA1B,oBAAA,MAAAb,UAAA,CAAAc,MAAA;MACA,GAAA0B,KAAA,CAAAC,CAAA;QACA,KAAAN,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;IACA;IAEA;IACAK,QAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAR,IAAA;MACA,GACA9B,IAAA;QACA,KAAAuC,OAAA,CAAAf,IAAA;UACAnC,IAAA;QACA;QACA,KAAAuC,QAAA;UACAE,IAAA;UACAD,OAAA;QACA;MAEA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}