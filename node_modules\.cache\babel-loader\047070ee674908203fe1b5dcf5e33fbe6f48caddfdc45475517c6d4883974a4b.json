{"ast": null, "code": "import { LayersAdd, LayersEdit } from '@/api/userMenu';\nexport default {\n  name: \"layerDialog\",\n  components: {},\n  data() {\n    return {\n      title: \"\",\n      open: false,\n      form: {},\n      rules: {\n        name: [{\n          required: true,\n          message: \"请输入图层名称\",\n          trigger: \"blur\"\n        }],\n        description: [{\n          required: true,\n          message: \"请输入图层介绍\",\n          trigger: \"blur\"\n        }],\n        provideUnit: [{\n          required: true,\n          message: \"请输入所属单位\",\n          trigger: \"blur\"\n        }],\n        coverage: [{\n          required: true,\n          message: \"请输入数据范围\",\n          trigger: \"blur\"\n        }],\n        subject: [{\n          required: true,\n          message: \"请输入数据主题\",\n          trigger: \"blur\"\n        }],\n        key: [{\n          required: true,\n          message: \"请输入数据类型\",\n          trigger: \"blur\"\n        }],\n        time: [{\n          required: true,\n          message: \"请输入上线时间\",\n          trigger: \"blur\"\n        }],\n        thumbnail: [{\n          required: true,\n          message: \"请输入图片路径\",\n          trigger: \"blur\"\n        }],\n        url: [{\n          required: true,\n          message: \"请输入资源地址\",\n          trigger: \"blur\"\n        }],\n        locationExternalUrl: [{\n          required: true,\n          message: \"请输入对外地址\",\n          trigger: \"blur\"\n        }],\n        content: [{\n          required: true,\n          message: \"请输入资源内容\",\n          trigger: \"blur\"\n        }],\n        key: [{\n          required: true,\n          message: \"请输入资源类型\",\n          trigger: \"blur\"\n        }],\n        suitableLoadEngine: [{\n          required: true,\n          message: \"请输入适用范围\",\n          trigger: \"blur\"\n        }],\n        coordinateSystem: [{\n          required: true,\n          message: \"请输入坐标系统\",\n          trigger: \"blur\"\n        }],\n        loadtype: [{\n          required: true,\n          message: \"请输入加载方式\",\n          trigger: \"blur\"\n        }]\n      }\n    };\n  },\n  methods: {\n    openAciton(type, info) {\n      this.open = true;\n      this.form = info;\n      this.title = type;\n    },\n    //新增\n    LayersAddList() {\n      LayersAdd(this.form).then(res => {\n        if (res.data.code == 200) {\n          this.open = false;\n          this.$emit('resetList');\n        }\n        this.$message({\n          showClose: true,\n          message: res.data.msg,\n          type: res.data.code == 200 ? \"success\" : \"error\"\n        });\n      });\n    },\n    // 编辑\n    LayersEditList() {\n      LayersEdit(this.form).then(res => {\n        if (res.data.code == 200) {\n          this.open = false;\n          this.$emit('resetList');\n        }\n        this.$message({\n          showClose: true,\n          message: res.data.msg,\n          type: res.data.code == 200 ? \"success\" : \"error\"\n        });\n      });\n    },\n    submitCheck() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.title == 'add') {\n            this.LayersAddList();\n          } else {\n            this.LayersEditList();\n          }\n        } else {\n          return false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["LayersAdd", "LayersEdit", "name", "components", "data", "title", "open", "form", "rules", "required", "message", "trigger", "description", "provideUnit", "coverage", "subject", "key", "time", "thumbnail", "url", "locationExternalUrl", "content", "suitableLoadEngine", "coordinateSystem", "loadtype", "methods", "openAciton", "type", "info", "LayersAddList", "then", "res", "code", "$emit", "$message", "showClose", "msg", "LayersEditList", "submit<PERSON>heck", "$refs", "validate", "valid"], "sources": ["src/views/mainPage/components/resource-center/components/layerDialog.vue"], "sourcesContent": ["<template>\r\n    <el-dialog :title=\"title == 'add' ? '新增图层' : '编辑图层'\" :visible.sync=\"open\" width=\"900px\" top=\"10vh\" :modal=\"false\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\">\r\n            <el-form-item label=\"图层名称:\" prop=\"name\">\r\n                <el-input v-model.trim=\"form.name\" placeholder=\"请输入图层名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"图层介绍:\" prop=\"description\">\r\n                <el-input v-model.trim=\"form.description\" placeholder=\"请输入图层介绍\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属单位:\" prop=\"provideUnit\">\r\n                <el-input v-model.trim=\"form.provideUnit\" placeholder=\"请输入所属单位\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"数据范围:\" prop=\"coverage\">\r\n                <el-input v-model.trim=\"form.coverage\" placeholder=\"请输入数据范围\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"数据主题:\" prop=\"subject\">\r\n                <el-input v-model.trim=\"form.subject\" placeholder=\"请输入数据主题\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"上线时间:\" prop=\"time\">\r\n                <el-input v-model.trim=\"form.time\" placeholder=\"请输入上线时间\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"图片路径:\" prop=\"thumbnail\">\r\n                <el-input v-model.trim=\"form.thumbnail\" placeholder=\"请输入图片路径\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"加载方式:\" prop=\"loadtype\">\r\n                <el-input v-model.trim=\"form.loadtype\" placeholder=\"请输入加载方式\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"资源内容:\" prop=\"content\">\r\n                <el-input v-model.trim=\"form.content\" placeholder=\"请输入资源内容\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"资源类型:\" prop=\"key\">\r\n                <el-input v-model.trim=\"form.key\" placeholder=\"请输入资源类型\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"资源地址:\" prop=\"url\">\r\n                <el-input v-model.trim=\"form.url\" placeholder=\"请输入资源地址\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"对外地址:\" prop=\"locationExternalUrl\">\r\n                <el-input v-model.trim=\"form.locationExternalUrl\" placeholder=\"请输入对外地址\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"坐标系统:\" prop=\"coordinateSystem\">\r\n                <el-input v-model.trim=\"form.coordinateSystem\" placeholder=\"请输入坐标系统\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"适用范围:\" prop=\"suitableLoadEngine\">\r\n                <el-input v-model.trim=\"form.suitableLoadEngine\" placeholder=\"请输入适用范围\" />\r\n            </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"submitCheck()\" type=\"primary\">确 定</el-button>\r\n            <el-button @click=\"open = false\">取 消</el-button>\r\n        </div>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { LayersAdd, LayersEdit } from '@/api/userMenu'\r\nexport default {\r\n    name: \"layerDialog\",\r\n    components: {},\r\n    data() {\r\n        return {\r\n            title: \"\",\r\n            open: false,\r\n            form: {\r\n            },\r\n            rules: {\r\n                name: [\r\n                    { required: true, message: \"请输入图层名称\", trigger: \"blur\" },\r\n                ],\r\n                description: [\r\n                    { required: true, message: \"请输入图层介绍\", trigger: \"blur\" },\r\n                ],\r\n                provideUnit: [\r\n                    { required: true, message: \"请输入所属单位\", trigger: \"blur\" },\r\n                ],\r\n                coverage: [\r\n                    { required: true, message: \"请输入数据范围\", trigger: \"blur\" },\r\n                ],\r\n                subject: [\r\n                    { required: true, message: \"请输入数据主题\", trigger: \"blur\" },\r\n                ],\r\n                key: [\r\n                    { required: true, message: \"请输入数据类型\", trigger: \"blur\" },\r\n                ],\r\n                time: [\r\n                    { required: true, message: \"请输入上线时间\", trigger: \"blur\" },\r\n                ],\r\n                thumbnail: [\r\n                    { required: true, message: \"请输入图片路径\", trigger: \"blur\" },\r\n                ],\r\n                url: [\r\n                    { required: true, message: \"请输入资源地址\", trigger: \"blur\" },\r\n                ],\r\n                locationExternalUrl: [{ required: true, message: \"请输入对外地址\", trigger: \"blur\" },],\r\n                content: [{ required: true, message: \"请输入资源内容\", trigger: \"blur\" },],\r\n\r\n                key: [{ required: true, message: \"请输入资源类型\", trigger: \"blur\" },],\r\n\r\n                suitableLoadEngine: [{ required: true, message: \"请输入适用范围\", trigger: \"blur\" },],\r\n                coordinateSystem: [{ required: true, message: \"请输入坐标系统\", trigger: \"blur\" },],\r\n                loadtype: [{ required: true, message: \"请输入加载方式\", trigger: \"blur\" },],\r\n\r\n\r\n\r\n            }\r\n        };\r\n    },\r\n    methods: {\r\n        openAciton(type, info) {\r\n            this.open = true;\r\n            this.form = info;\r\n            this.title = type;\r\n        },\r\n        //新增\r\n        LayersAddList() {\r\n            LayersAdd(this.form).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.open = false;\r\n                    this.$emit('resetList');\r\n                }\r\n                this.$message({ showClose: true, message: res.data.msg, type: res.data.code == 200 ? \"success\" : \"error\" });\r\n            })\r\n        },\r\n        // 编辑\r\n        LayersEditList() {\r\n            LayersEdit(this.form).then(res => {\r\n                if (res.data.code == 200) {\r\n                    this.open = false;\r\n                    this.$emit('resetList');\r\n                }\r\n                this.$message({ showClose: true, message: res.data.msg, type: res.data.code == 200 ? \"success\" : \"error\" });\r\n            })\r\n        },\r\n        submitCheck() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                if (valid) {\r\n                    if (this.title == 'add') {\r\n                        this.LayersAddList();\r\n                    } else {\r\n                        this.LayersEditList();\r\n                    }\r\n\r\n                } else {\r\n                    return false;\r\n                }\r\n            });\r\n\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n::v-deep .el-dialog {\r\n    background: #124C6F;\r\n\r\n    .el-dialog__title {\r\n        color: #fff;\r\n    }\r\n\r\n    .el-form-item {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .el-form-item__label {\r\n            width: 120px;\r\n        }\r\n\r\n        .el-form-item__content {\r\n\r\n            margin-left: 40px;\r\n\r\n            .el-input {\r\n                width: 600px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.uploadInfo {\r\n    display: inline-block;\r\n    margin-top: 10px;\r\n    margin-left: 20px;\r\n}\r\n\r\n::v-deep .el-scrollbar__wrap {\r\n    max-height: 500px;\r\n}\r\n\r\n::v-deep .el-dialog__body {\r\n    padding: 24px;\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n    max-height: 500px;\r\n}\r\n\r\n::v-deep .dialog-footer {\r\n    margin-top: 10px;\r\n}\r\n\r\n::v-deep .el-form {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.maintain-radio {\r\n    ::v-deep .el-form-item__content {\r\n        display: flex;\r\n    }\r\n\r\n    ::v-deep .el-radio--medium.is-bordered {\r\n        margin-right: 18px;\r\n    }\r\n\r\n    ::v-deep .inner-input-radio.el-radio--medium.is-bordered {\r\n        padding: 3px 10px 0 10px;\r\n    }\r\n\r\n    .radio-input {\r\n        ::v-deep .el-input__inner {\r\n            width: 145px;\r\n            margin-left: 15px;\r\n            // padding: 0 10px;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAsDA,SAAAA,SAAA,EAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA,GACA;MACAC,KAAA;QACAN,IAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,WAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,QAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,OAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,GAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,IAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,SAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,GAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,mBAAA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAU,OAAA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEAK,GAAA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEAW,kBAAA;UAAAb,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAY,gBAAA;UAAAd,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAa,QAAA;UAAAf,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAIA;IACA;EACA;EACAc,OAAA;IACAC,WAAAC,IAAA,EAAAC,IAAA;MACA,KAAAtB,IAAA;MACA,KAAAC,IAAA,GAAAqB,IAAA;MACA,KAAAvB,KAAA,GAAAsB,IAAA;IACA;IACA;IACAE,cAAA;MACA7B,SAAA,MAAAO,IAAA,EAAAuB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;UACA,KAAA1B,IAAA;UACA,KAAA2B,KAAA;QACA;QACA,KAAAC,QAAA;UAAAC,SAAA;UAAAzB,OAAA,EAAAqB,GAAA,CAAA3B,IAAA,CAAAgC,GAAA;UAAAT,IAAA,EAAAI,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;QAAA;MACA;IACA;IACA;IACAK,eAAA;MACApC,UAAA,MAAAM,IAAA,EAAAuB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;UACA,KAAA1B,IAAA;UACA,KAAA2B,KAAA;QACA;QACA,KAAAC,QAAA;UAAAC,SAAA;UAAAzB,OAAA,EAAAqB,GAAA,CAAA3B,IAAA,CAAAgC,GAAA;UAAAT,IAAA,EAAAI,GAAA,CAAA3B,IAAA,CAAA4B,IAAA;QAAA;MACA;IACA;IACAM,YAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,SAAApC,KAAA;YACA,KAAAwB,aAAA;UACA;YACA,KAAAQ,cAAA;UACA;QAEA;UACA;QACA;MACA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}