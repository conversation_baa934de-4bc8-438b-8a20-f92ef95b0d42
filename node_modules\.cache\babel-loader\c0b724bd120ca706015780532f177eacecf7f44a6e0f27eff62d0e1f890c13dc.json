{"ast": null, "code": "import * as echarts from 'echarts';\nimport { CommunityPeopleCount } from '@/api/index.js';\nexport default {\n  name: 'PopTrend',\n  data() {\n    return {\n      mainCharts: null,\n      sideData: [],\n      dataArr: []\n    };\n  },\n  beforeDestroy() {\n    if (this.mainCharts) {\n      this.mainCharts.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.myChart();\n      }, 200);\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.mainCharts != null && this.mainCharts != \"\" && this.mainCharts != undefined) {\n          this.mainCharts.dispose();\n        }\n        this.mainCharts = echarts.init(this.$refs.mainCharts);\n        this.mainCharts.clear();\n        CommunityPeopleCount().then(res => {\n          let dataList = res.data.extra;\n          dataList.map(item => {\n            this.sideData.push(item.count);\n            this.dataArr.push(item.village);\n          });\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        color: ['red', 'black'],\n        grid: {\n          left: \"0\",\n          top: '10%',\n          bottom: '25%',\n          right: '0%',\n          containLabel: true\n        },\n        tooltip: {\n          // show: false,\n          trigger: 'axis',\n          formatter: function (params) {\n            return params[0].marker + params[0].name + '<br>' + '人数 : ' + params[0].value;\n          },\n          backgroundColor: \"rgba(13, 63, 103, .8)\",\n          borderWidth: \"1\",\n          //边框宽度设置1\n          borderColor: \"rgba(15, 124, 164, 1)\",\n          //设置边框颜色\n          textStyle: {\n            fontSize: 21,\n            color: \"#fff\"\n          }\n        },\n        calculable: true,\n        xAxis: [{\n          type: 'category',\n          splitLine: {\n            show: false\n          },\n          data: this.dataArr,\n          axisLabel: {\n            show: true,\n            color: \"#fff\",\n            //X轴文字颜色\n            fontSize: 20\n          }\n        }],\n        yAxis: [{\n          type: 'value',\n          interval: 10000,\n          nameTextStyle: {\n            color: '#fff',\n            padding: [0, 20, 5, 0],\n            //表示[上,右,下,左]的边距\n            fontSize: 20\n          },\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: 'rgba(45, 60, 92, 1)'\n            }\n          },\n          axisLabel: {\n            show: true,\n            color: \"#fff\",\n            //X轴文字颜色\n            fontSize: 20\n          },\n          splitLine: {\n            show: false\n          }\n        }],\n        dataZoom: [{\n          type: 'inside',\n          start: 0,\n          end: 30\n        }, {}],\n        series: [{\n          name: '常住',\n          tooltip: {\n            show: false\n          },\n          type: 'bar',\n          barGap: '0%',\n          barWidth: 15,\n          itemStyle: {\n            // normal: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: \"rgba(57, 206, 255, 1)\" // 0% 处的颜色\n            }, {\n              offset: 1,\n              color: \"rgba(45, 72, 173, 0.1)\" // 100% 处的颜色\n            }], false)\n            // }\n          },\n          data: this.sideData,\n          barGap: 0\n        }, {\n          name: '常住',\n          type: 'bar',\n          barWidth: 15,\n          itemStyle: {\n            // normal: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: \"rgba(36, 201, 255, 1)\" // 0% 处的颜色\n            }, {\n              offset: 1,\n              color: \"rgba(45, 72, 173, 0.1)\" // 100% 处的颜色\n            }], false)\n            // }\n          },\n          barGap: 0,\n          data: this.sideData,\n          label: {\n            show: true,\n            position: 'top',\n            color: 'white',\n            fontSize: 15\n          }\n        }, {\n          name: '常住',\n          tooltip: {\n            show: false\n          },\n          type: 'pictorialBar',\n          itemStyle: {\n            borderWidth: 1,\n            borderColor: '#0571D5',\n            color: 'rgba(0, 222, 255, 1)' // 控制顶部方形的颜色\n          },\n          symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',\n          symbolSize: ['30', '10'],\n          // 第一个值控制顶部方形大小\n          symbolOffset: ['0', '-6'],\n          // 控制顶部放行 左右和上下\n          symbolRotate: -16,\n          symbolPosition: 'end',\n          data: this.sideData,\n          z: 3\n        }]\n      };\n      if (option && typeof option == 'object') {\n        this.mainCharts.setOption(option);\n      }\n      window.onresize = this.mainCharts.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "CommunityPeopleCount", "name", "data", "<PERSON><PERSON><PERSON><PERSON>", "sideData", "dataArr", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "setTimeout", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "extra", "map", "item", "push", "count", "village", "setOption", "option", "color", "grid", "left", "top", "bottom", "right", "containLabel", "tooltip", "trigger", "formatter", "params", "marker", "value", "backgroundColor", "borderWidth", "borderColor", "textStyle", "fontSize", "calculable", "xAxis", "type", "splitLine", "show", "axisLabel", "yAxis", "interval", "nameTextStyle", "padding", "lineStyle", "dataZoom", "start", "end", "series", "barGap", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "graphic", "LinearGradient", "offset", "label", "position", "symbol", "symbolSize", "symbolOffset", "symbolRotate", "symbolPosition", "z", "window", "onresize", "resize"], "sources": ["src/components/comprehensive/PopTrend.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"mainCharts\" ref=\"mainCharts\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { CommunityPeopleCount } from '@/api/index.js'\r\nexport default {\r\n    name: 'PopTrend',\r\n\r\n    data() {\r\n        return {\r\n            mainCharts: null,\r\n            sideData: [],\r\n            dataArr: []\r\n\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if(this.mainCharts){\r\n            this.mainCharts.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            setTimeout(()=>{\r\n\r\n                this.myChart()\r\n            },200)\r\n\r\n        })\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.mainCharts != null && this.mainCharts != \"\" && this.mainCharts != undefined) {\r\n                    this.mainCharts.dispose();\r\n                }\r\n                this.mainCharts = echarts.init(this.$refs.mainCharts);\r\n                this.mainCharts.clear()\r\n                CommunityPeopleCount().then(res => {\r\n                    let dataList = res.data.extra;\r\n                    dataList.map(item => {\r\n                        this.sideData.push(item.count)\r\n                        this.dataArr.push(item.village)\r\n                    })\r\n                    this.setOption()\r\n                })\r\n\r\n\r\n            })\r\n        },\r\n        setOption() {\r\n\r\n            let option = {\r\n                color: ['red', 'black'],\r\n\r\n                grid: {\r\n                    left: \"0\",\r\n                    top: '10%',\r\n                    bottom: '25%',\r\n                    right: '0%',\r\n                    containLabel: true\r\n                },\r\n\r\n                tooltip: {\r\n                    // show: false,\r\n                    trigger: 'axis',\r\n                    formatter: function (params) {\r\n                        return params[0].marker + params[0].name + '<br>' + '人数 : ' + params[0].value;\r\n\r\n                    },\r\n                    backgroundColor: \"rgba(13, 63, 103, .8)\",\r\n                    borderWidth: \"1\", //边框宽度设置1\r\n                    borderColor: \"rgba(15, 124, 164, 1)\", //设置边框颜色\r\n                    textStyle: {\r\n                        fontSize: 21,\r\n                        color: \"#fff\"\r\n\r\n                    },\r\n                },\r\n                calculable: true,\r\n                xAxis: [\r\n                    {\r\n                        type: 'category',\r\n                        splitLine: {\r\n                            show: false\r\n                        },\r\n                        data: this.dataArr,\r\n                        axisLabel: {\r\n                            show: true,\r\n                            color: \"#fff\",//X轴文字颜色\r\n                            fontSize: 20,\r\n\r\n                        },\r\n                    }\r\n                ],\r\n                yAxis: [\r\n                    {\r\n                        type: 'value',\r\n                        interval: 10000,\r\n                        nameTextStyle: {\r\n                            color: '#fff',\r\n                            padding: [0, 20, 5, 0],\t\t//表示[上,右,下,左]的边距\r\n                            fontSize: 20,\r\n                        },\r\n                        splitLine: {\r\n                            show: true,\r\n                            lineStyle: {\r\n                                color: 'rgba(45, 60, 92, 1)'\r\n                            }\r\n                        },\r\n                        axisLabel: {\r\n                            show: true,\r\n                            color: \"#fff\", //X轴文字颜色\r\n                            fontSize: 20,\r\n                        },\r\n                        splitLine: {\r\n                            show: false,\r\n                        }\r\n                    }\r\n                ],\r\n                dataZoom: [\r\n                    {\r\n                        type: 'inside',\r\n                        start: 0,\r\n                        end: 30\r\n                    }, {}\r\n                ],\r\n                series: [\r\n                    {\r\n                        name: '常住',\r\n                        tooltip: {\r\n                            show: false\r\n                        },\r\n                        type: 'bar',\r\n                        barGap: '0%',\r\n                        barWidth: 15,\r\n                        itemStyle: {\r\n                            // normal: {\r\n                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\r\n                                offset: 0,\r\n                                color: \"rgba(57, 206, 255, 1)\" // 0% 处的颜色\r\n                            }, {\r\n                                offset: 1,\r\n                                color: \"rgba(45, 72, 173, 0.1)\" // 100% 处的颜色\r\n                            }], false)\r\n                            // }\r\n                        },\r\n\r\n                        data: this.sideData,\r\n                        barGap: 0,\r\n                    },\r\n                    {\r\n                        name: '常住',\r\n                        type: 'bar',\r\n                        barWidth: 15,\r\n                        itemStyle: {\r\n                            // normal: {\r\n                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\r\n                                offset: 0,\r\n                                color: \"rgba(36, 201, 255, 1)\" // 0% 处的颜色\r\n                            }, {\r\n                                offset: 1,\r\n                                color: \"rgba(45, 72, 173, 0.1)\" // 100% 处的颜色\r\n                            }], false)\r\n                            // }\r\n                        },\r\n                        barGap: 0,\r\n                        data: this.sideData,\r\n                        label: {\r\n                            show: true,\r\n                            position: 'top',\r\n                            color: 'white',\r\n                            fontSize: 15\r\n                        }\r\n                    },\r\n                    {\r\n                        name: '常住',\r\n                        tooltip: {\r\n                            show: false\r\n                        },\r\n                        type: 'pictorialBar',\r\n                        itemStyle: {\r\n                            borderWidth: 1,\r\n                            borderColor: '#0571D5',\r\n                            color: 'rgba(0, 222, 255, 1)' // 控制顶部方形的颜色\r\n                        },\r\n                        symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',\r\n                        symbolSize: ['30', '10'], // 第一个值控制顶部方形大小\r\n                        symbolOffset: ['0', '-6'], // 控制顶部放行 左右和上下\r\n                        symbolRotate: -16,\r\n                        symbolPosition: 'end',\r\n                        data: this.sideData,\r\n                        z: 3,\r\n                    }\r\n                ]\r\n            };\r\n\r\n\r\n            if (option && typeof option == 'object') {\r\n                this.mainCharts.setOption(option)\r\n            }\r\n\r\n            window.onresize = this.mainCharts.resize;\r\n        }\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.mainCharts {\r\n    width: 100%;\r\n    height: 280px;\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,oBAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MACAC,OAAA;IAEA;EACA;EACAC,cAAA;IACA,SAAAH,UAAA;MACA,KAAAA,UAAA,CAAAI,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACAC,UAAA;QAEA,KAAAC,OAAA;MACA;IAEA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAZ,UAAA,iBAAAA,UAAA,eAAAA,UAAA,IAAAa,SAAA;UACA,KAAAb,UAAA,CAAAc,OAAA;QACA;QACA,KAAAd,UAAA,GAAAJ,OAAA,CAAAmB,IAAA,MAAAC,KAAA,CAAAhB,UAAA;QACA,KAAAA,UAAA,CAAAI,KAAA;QACAP,oBAAA,GAAAe,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAlB,IAAA,CAAAoB,KAAA;UACAD,QAAA,CAAAE,GAAA,CAAAC,IAAA;YACA,KAAApB,QAAA,CAAAqB,IAAA,CAAAD,IAAA,CAAAE,KAAA;YACA,KAAArB,OAAA,CAAAoB,IAAA,CAAAD,IAAA,CAAAG,OAAA;UACA;UACA,KAAAC,SAAA;QACA;MAGA;IACA;IACAA,UAAA;MAEA,IAAAC,MAAA;QACAC,KAAA;QAEAC,IAAA;UACAC,IAAA;UACAC,GAAA;UACAC,MAAA;UACAC,KAAA;UACAC,YAAA;QACA;QAEAC,OAAA;UACA;UACAC,OAAA;UACAC,SAAA,WAAAA,CAAAC,MAAA;YACA,OAAAA,MAAA,IAAAC,MAAA,GAAAD,MAAA,IAAAvC,IAAA,sBAAAuC,MAAA,IAAAE,KAAA;UAEA;UACAC,eAAA;UACAC,WAAA;UAAA;UACAC,WAAA;UAAA;UACAC,SAAA;YACAC,QAAA;YACAjB,KAAA;UAEA;QACA;QACAkB,UAAA;QACAC,KAAA,GACA;UACAC,IAAA;UACAC,SAAA;YACAC,IAAA;UACA;UACAlD,IAAA,OAAAG,OAAA;UACAgD,SAAA;YACAD,IAAA;YACAtB,KAAA;YAAA;YACAiB,QAAA;UAEA;QACA,EACA;QACAO,KAAA,GACA;UACAJ,IAAA;UACAK,QAAA;UACAC,aAAA;YACA1B,KAAA;YACA2B,OAAA;YAAA;YACAV,QAAA;UACA;UACAI,SAAA;YACAC,IAAA;YACAM,SAAA;cACA5B,KAAA;YACA;UACA;UACAuB,SAAA;YACAD,IAAA;YACAtB,KAAA;YAAA;YACAiB,QAAA;UACA;UACAI,SAAA;YACAC,IAAA;UACA;QACA,EACA;QACAO,QAAA,GACA;UACAT,IAAA;UACAU,KAAA;UACAC,GAAA;QACA,MACA;QACAC,MAAA,GACA;UACA7D,IAAA;UACAoC,OAAA;YACAe,IAAA;UACA;UACAF,IAAA;UACAa,MAAA;UACAC,QAAA;UACAC,SAAA;YACA;YACAnC,KAAA,MAAA/B,OAAA,CAAAmE,OAAA,CAAAC,cAAA;cACAC,MAAA;cACAtC,KAAA;YACA;cACAsC,MAAA;cACAtC,KAAA;YACA;YACA;UACA;UAEA5B,IAAA,OAAAE,QAAA;UACA2D,MAAA;QACA,GACA;UACA9D,IAAA;UACAiD,IAAA;UACAc,QAAA;UACAC,SAAA;YACA;YACAnC,KAAA,MAAA/B,OAAA,CAAAmE,OAAA,CAAAC,cAAA;cACAC,MAAA;cACAtC,KAAA;YACA;cACAsC,MAAA;cACAtC,KAAA;YACA;YACA;UACA;UACAiC,MAAA;UACA7D,IAAA,OAAAE,QAAA;UACAiE,KAAA;YACAjB,IAAA;YACAkB,QAAA;YACAxC,KAAA;YACAiB,QAAA;UACA;QACA,GACA;UACA9C,IAAA;UACAoC,OAAA;YACAe,IAAA;UACA;UACAF,IAAA;UACAe,SAAA;YACArB,WAAA;YACAC,WAAA;YACAf,KAAA;UACA;UACAyC,MAAA;UACAC,UAAA;UAAA;UACAC,YAAA;UAAA;UACAC,YAAA;UACAC,cAAA;UACAzE,IAAA,OAAAE,QAAA;UACAwE,CAAA;QACA;MAEA;MAGA,IAAA/C,MAAA,WAAAA,MAAA;QACA,KAAA1B,UAAA,CAAAyB,SAAA,CAAAC,MAAA;MACA;MAEAgD,MAAA,CAAAC,QAAA,QAAA3E,UAAA,CAAA4E,MAAA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}