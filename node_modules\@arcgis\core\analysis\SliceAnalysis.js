/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"./Analysis.js";import s from"./SlicePlane.js";import o from"../core/Collection.js";import{castForReferenceSetter as i,referenceSetter as r}from"../core/collectionUtils.js";import l from"../core/Logger.js";import{isNone as a,isSome as n,applySome as p}from"../core/maybe.js";import{property as c}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as d}from"../core/accessorSupport/decorators/subclass.js";import{renderSRFromViewSR as u}from"../geometry/support/coordinateSystem.js";import{ViewingMode as y}from"../views/ViewingMode.js";import{shapeToExtent as h}from"../views/3d/interactive/analysisTools/slice/sliceToolUtils.js";import{RenderCoordsHelper as m}from"../views/3d/support/RenderCoordsHelper.js";import{isSpatialReferenceSupported as f}from"../views/support/spatialReferenceSupport.js";const g=l.getLogger("esri.analysis.Slice");let b=class extends t{constructor(e){super(e),this.type="slice",this.tiltEnabled=!1,this.shape=null,this.excludeGroundSurface=!1}get extent(){if(a(this.shape)||a(this.shape.position))return null;const e=this.shape.position.spatialReference;let t=null,s=null;if(f(e,y.Local)){const s=m.create(y.Local,u(!1,e));t=h(this.shape,s,{tiltEnabled:this.tiltEnabled})}if(f(e,y.Global)){const t=m.create(y.Global,u(!0,e));s=h(this.shape,t,{tiltEnabled:this.tiltEnabled})}return n(t)&&n(s)?t.union(s):a(t)&&a(s)?(g.warnOnce(`Extent of slice analysis (title: '${null!=(o=this.title)?o:"no title"}', id: '${null!=(i=this.id)?i:"no id"}') could not be computed as the spatial reference of the shape (wkid: '${null!=(r=e.wkid)?r:"no wkid"}') is not supported by the view`),null):a(t)?s:t;var o,i,r}get excludedLayers(){return this._get("excludedLayers")||new o}set excludedLayers(e){this._set("excludedLayers",r(e,this._get("excludedLayers")))}get requiredPropertiesForEditing(){return[p(this.shape,(e=>e.position))]}get nonEditableMessage(){return a(this.shape)?"Assign a slice shape to the analysis to allow editing.":"plane"===this.shape.type?"Assign a position to the analysis slice plane to allow editing.":void 0}};e([c({type:["slice"]})],b.prototype,"type",void 0),e([c({readOnly:!0})],b.prototype,"extent",null),e([c()],b.prototype,"tiltEnabled",void 0),e([c({types:{key:"type",base:null,typeMap:{plane:s},defaultKeyValue:"plane"}})],b.prototype,"shape",void 0),e([c({cast:i,clonable:e=>e.slice()})],b.prototype,"excludedLayers",null),e([c({type:Boolean,nonNullable:!0})],b.prototype,"excludeGroundSurface",void 0),e([c({readOnly:!0})],b.prototype,"requiredPropertiesForEditing",null),e([c({readOnly:!0})],b.prototype,"nonEditableMessage",null),b=e([d("esri.analysis.SliceAnalysis")],b);const j=b;export{j as default};
