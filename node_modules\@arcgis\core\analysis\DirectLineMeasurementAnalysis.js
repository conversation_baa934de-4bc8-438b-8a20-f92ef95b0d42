/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import e from"./Analysis.js";import{isNone as o,isSome as r}from"../core/maybe.js";import{measurementLengthUnits as s}from"../core/unitUtils.js";import{property as i}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as n}from"../core/accessorSupport/decorators/subclass.js";import p from"../geometry/Extent.js";import a from"../geometry/Point.js";let l=class extends e{constructor(t){super(t),this.type="direct-line-measurement",this.startPoint=null,this.endPoint=null,this.unit=null,this.nonEditableMessage="Assign start and end points to the analysis to allow editing."}get extent(){if(o(this.startPoint))return null;const t=p.fromPoint(this.startPoint);return r(this.endPoint)&&t.union(p.fromPoint(this.endPoint)),t}get requiredPropertiesForEditing(){return[this.startPoint,this.endPoint]}};t([i({type:["direct-line-measurement"]})],l.prototype,"type",void 0),t([i({type:a})],l.prototype,"startPoint",void 0),t([i({type:a})],l.prototype,"endPoint",void 0),t([i()],l.prototype,"extent",null),t([i({type:s,value:null})],l.prototype,"unit",void 0),t([i({readOnly:!0})],l.prototype,"requiredPropertiesForEditing",null),t([i({readOnly:!0})],l.prototype,"nonEditableMessage",void 0),l=t([n("esri.analysis.DirectLineMeasurementAnalysis")],l);const m=l;export{m as default};
