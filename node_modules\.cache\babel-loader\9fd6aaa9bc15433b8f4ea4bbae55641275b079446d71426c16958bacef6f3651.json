{"ast": null, "code": "import request from '@/utils/api/request.js';\nconst queryStringfn = queryParams => {\n  // 拼接 query 参数到 URL\n  return Object.keys(queryParams).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`).join('&');\n};\n// 充电桩\nexport function getCountStatistic() {\n  return request.get('/charge/countStatistic');\n}\nexport function getAlarmList(params) {\n  return request.get('/charge/alarmList', {\n    params\n  });\n}\nexport function getSiteList() {\n  return request.get('/charge/siteList');\n}\nexport function getSiteAlarmList(params) {\n  return request.get('/charge/siteAlarmList', {\n    params\n  });\n}\n//站点数据\nexport function getQuerySiteList(params) {\n  return request.get('/charge/querySiteList', {\n    params\n  });\n}\n//设备数据\nexport function getQueryDeviceList(params) {\n  return request.get('/charge/queryDeviceList', {\n    params\n  });\n}\n//插座数据\nexport function getQueryPortList(params) {\n  return request.get('/charge/queryPortList', {\n    params\n  });\n}\n// 站点饼图\nexport function getSiteStatistic() {\n  return request.get('/charge/siteStatistic');\n}\n// 插座饼图\nexport function getPortStatistic() {\n  return request.get('/charge/portStatistic');\n}\n// 设备饼图\nexport function getDeviceStatistic() {\n  return request.get('/charge/deviceStatistic');\n}\n// 预警饼图\nexport function getAlarmStatistic() {\n  return request.get('/charge/alarmStatistic');\n}\n// 实时警情信息\nexport function getFireAlarm(params) {\n  return request.post(`/fireApi/alarm-info-for-screen?${queryStringfn(params)}`);\n}\n//根据orgId获取坐标\nexport function getLatLng(params) {\n  return request.get(`/fireApi/getLatLng?${queryStringfn(params)}`);\n}\n// 警情统计\nexport function getFireStatus(params) {\n  return request.post(`/fireApi/alarm-status-count?${queryStringfn(params)}`);\n}\n// 实时隐患统计接口\nexport function getHiddenStatus(params) {\n  return request.post(`/fireApi/hidden-manager-statistics?${queryStringfn(params)}`);\n}\n// 获取接入单位，报警单位统计\nexport function getOrgAllCount(params) {\n  return request.post(`/fireApi/org-all-count?${queryStringfn(params)}`);\n}\n// 获取高危信号、重大问题\nexport function getCoreIndex(params) {\n  return request.post(`/fireApi/core-index?${queryStringfn(params)}`);\n}", "map": {"version": 3, "names": ["request", "queryStringfn", "queryParams", "Object", "keys", "map", "key", "encodeURIComponent", "join", "getCountStatistic", "get", "getAlarmList", "params", "getSiteList", "getSiteAlarmList", "getQuerySiteList", "getQueryDeviceList", "getQueryPortList", "getSiteStatistic", "getPortStatistic", "getDeviceStatistic", "getAlarmStatistic", "getFireAlarm", "post", "getLatLng", "getFireStatus", "getHiddenStatus", "getOrgAllCount", "getCoreIndex"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/chargingPiles.js"], "sourcesContent": ["import request from '@/utils/api/request.js'\r\nconst queryStringfn = (queryParams) => {// 拼接 query 参数到 URL\r\n  return Object.keys(queryParams)\r\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)\r\n    .join('&');\r\n}\r\n// 充电桩\r\nexport function getCountStatistic() {\r\n  return request.get('/charge/countStatistic')\r\n}\r\nexport function getAlarmList(params) {\r\n  return request.get('/charge/alarmList', { params })\r\n}\r\nexport function getSiteList() {\r\n  return request.get('/charge/siteList')\r\n}\r\nexport function getSiteAlarmList(params) {\r\n  return request.get('/charge/siteAlarmList', { params })\r\n}\r\n//站点数据\r\nexport function getQuerySiteList(params) {\r\n  return request.get('/charge/querySiteList', { params })\r\n}\r\n//设备数据\r\nexport function getQueryDeviceList(params) {\r\n  return request.get('/charge/queryDeviceList', { params })\r\n}\r\n//插座数据\r\nexport function getQueryPortList(params) {\r\n  return request.get('/charge/queryPortList', { params })\r\n}\r\n// 站点饼图\r\nexport function getSiteStatistic() {\r\n  return request.get('/charge/siteStatistic')\r\n}\r\n// 插座饼图\r\nexport function getPortStatistic() {\r\n  return request.get('/charge/portStatistic')\r\n}\r\n// 设备饼图\r\nexport function getDeviceStatistic() {\r\n  return request.get('/charge/deviceStatistic')\r\n}\r\n// 预警饼图\r\nexport function getAlarmStatistic() {\r\n  return request.get('/charge/alarmStatistic')\r\n}\r\n// 实时警情信息\r\nexport function getFireAlarm(params) {\r\n  return request.post(`/fireApi/alarm-info-for-screen?${queryStringfn(params)}`)\r\n}\r\n//根据orgId获取坐标\r\nexport function getLatLng(params) {\r\n  return request.get(`/fireApi/getLatLng?${queryStringfn(params)}`)\r\n}\r\n// 警情统计\r\nexport function getFireStatus(params) {\r\n  return request.post(`/fireApi/alarm-status-count?${queryStringfn(params)}`)\r\n}\r\n// 实时隐患统计接口\r\nexport function getHiddenStatus(params) {\r\n  return request.post(`/fireApi/hidden-manager-statistics?${queryStringfn(params)}`)\r\n}\r\n// 获取接入单位，报警单位统计\r\nexport function getOrgAllCount(params) {\r\n  return request.post(`/fireApi/org-all-count?${queryStringfn(params)}`)\r\n}\r\n// 获取高危信号、重大问题\r\nexport function getCoreIndex(params) {\r\n  return request.post(`/fireApi/core-index?${queryStringfn(params)}`)\r\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,wBAAwB;AAC5C,MAAMC,aAAa,GAAIC,WAAW,IAAK;EAAC;EACtC,OAAOC,MAAM,CAACC,IAAI,CAACF,WAAW,CAAC,CAC5BG,GAAG,CAACC,GAAG,IAAI,GAAGC,kBAAkB,CAACD,GAAG,CAAC,IAAIC,kBAAkB,CAACL,WAAW,CAACI,GAAG,CAAC,CAAC,EAAE,CAAC,CAChFE,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;AACD;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,OAAOT,OAAO,CAACU,GAAG,CAAC,wBAAwB,CAAC;AAC9C;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,OAAOZ,OAAO,CAACU,GAAG,CAAC,mBAAmB,EAAE;IAAEE;EAAO,CAAC,CAAC;AACrD;AACA,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,OAAOb,OAAO,CAACU,GAAG,CAAC,kBAAkB,CAAC;AACxC;AACA,OAAO,SAASI,gBAAgBA,CAACF,MAAM,EAAE;EACvC,OAAOZ,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAO,CAAC,CAAC;AACzD;AACA;AACA,OAAO,SAASG,gBAAgBA,CAACH,MAAM,EAAE;EACvC,OAAOZ,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAO,CAAC,CAAC;AACzD;AACA;AACA,OAAO,SAASI,kBAAkBA,CAACJ,MAAM,EAAE;EACzC,OAAOZ,OAAO,CAACU,GAAG,CAAC,yBAAyB,EAAE;IAAEE;EAAO,CAAC,CAAC;AAC3D;AACA;AACA,OAAO,SAASK,gBAAgBA,CAACL,MAAM,EAAE;EACvC,OAAOZ,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAO,CAAC,CAAC;AACzD;AACA;AACA,OAAO,SAASM,gBAAgBA,CAAA,EAAG;EACjC,OAAOlB,OAAO,CAACU,GAAG,CAAC,uBAAuB,CAAC;AAC7C;AACA;AACA,OAAO,SAASS,gBAAgBA,CAAA,EAAG;EACjC,OAAOnB,OAAO,CAACU,GAAG,CAAC,uBAAuB,CAAC;AAC7C;AACA;AACA,OAAO,SAASU,kBAAkBA,CAAA,EAAG;EACnC,OAAOpB,OAAO,CAACU,GAAG,CAAC,yBAAyB,CAAC;AAC/C;AACA;AACA,OAAO,SAASW,iBAAiBA,CAAA,EAAG;EAClC,OAAOrB,OAAO,CAACU,GAAG,CAAC,wBAAwB,CAAC;AAC9C;AACA;AACA,OAAO,SAASY,YAAYA,CAACV,MAAM,EAAE;EACnC,OAAOZ,OAAO,CAACuB,IAAI,CAAC,kCAAkCtB,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AAChF;AACA;AACA,OAAO,SAASY,SAASA,CAACZ,MAAM,EAAE;EAChC,OAAOZ,OAAO,CAACU,GAAG,CAAC,sBAAsBT,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AACnE;AACA;AACA,OAAO,SAASa,aAAaA,CAACb,MAAM,EAAE;EACpC,OAAOZ,OAAO,CAACuB,IAAI,CAAC,+BAA+BtB,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AAC7E;AACA;AACA,OAAO,SAASc,eAAeA,CAACd,MAAM,EAAE;EACtC,OAAOZ,OAAO,CAACuB,IAAI,CAAC,sCAAsCtB,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AACpF;AACA;AACA,OAAO,SAASe,cAAcA,CAACf,MAAM,EAAE;EACrC,OAAOZ,OAAO,CAACuB,IAAI,CAAC,0BAA0BtB,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AACxE;AACA;AACA,OAAO,SAASgB,YAAYA,CAAChB,MAAM,EAAE;EACnC,OAAOZ,OAAO,CAACuB,IAAI,CAAC,uBAAuBtB,aAAa,CAACW,MAAM,CAAC,EAAE,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}