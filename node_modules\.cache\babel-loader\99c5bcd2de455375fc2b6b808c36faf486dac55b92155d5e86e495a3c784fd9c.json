{"ast": null, "code": "export default {\n  name: 'rightView',\n  data() {\n    return {\n      // 预警统计\n      levelTitle: [{\n        name: \"一级预警\",\n        num: 15\n      }, {\n        name: \"二级预警\",\n        num: 49\n      }, {\n        name: \"三级预警\",\n        num: 62\n      }],\n      timer: '',\n      animate: false,\n      // 是否显示动画效果，默认为true。\n      levelData: [{\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }, {\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }, {\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }, {\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }, {\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }, {\n        title: \"水位预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\n        level: \"一级预警\"\n      }, {\n        title: \"进水口流量预警\",\n        text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\n        level: \"二级预警\"\n      }]\n    };\n  },\n  components: {},\n  watch: {},\n  computed: {},\n  mounted() {\n    // this.scroll();\n    // this.timer = setInterval(() => {\n    //     this.scroll()\n    // }, 1500);\n  },\n  updated() {},\n  destroyed() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    // 预警统计列表滚动\n    scroll() {\n      let that = this;\n      // let mainCon = document.querySelector(\".pushData .mainCon\")\n      // mainCon.style.top = '-9rem';\n      that.animate = !that.animate;\n      setTimeout(() => {\n        that.levelData.push(that.levelData[0]);\n        that.levelData.shift(that.levelData[0]);\n        // mainCon.style.top = '0';\n        that.animate = !that.animate;\n      }, 500);\n    },\n    // 预警统计数据鼠标经过\n    mEnter() {\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n    },\n    // 预警统计数据鼠标离开\n    mLeave() {\n      this.scroll();\n      this.timer = setInterval(() => {\n        this.scroll();\n      }, 1500);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "levelTitle", "num", "timer", "animate", "levelData", "title", "text", "level", "components", "watch", "computed", "mounted", "updated", "destroyed", "clearInterval", "methods", "scroll", "that", "setTimeout", "push", "shift", "mEnter", "mLeave", "setInterval"], "sources": ["src/views/smartWater/components/water-decision/right-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"rightView\">\r\n        <div class=\"title-box\">\r\n            <span>水务决策</span>\r\n        </div>\r\n        <div class=\"two-title\">预警统计</div>\r\n        <div class=\"earlyWarningArtical\">\r\n            <div class=\"levelTitle\">\r\n                <div class=\"item\" v-for=\"(item, index) of levelTitle\" :key=\"index\">\r\n                    <div class=\"num\">{{ item.num }}</div>\r\n                    <div class=\"txt\">{{ item.name }}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"pushData\">\r\n                <div class=\"mainCon\">\r\n                    <div class=\"artItem child\" v-for=\"(item, index) of levelData\" :key=\"index\">\r\n                        <div class=\"artical\">\r\n                            <div class=\"pic\">\r\n                                <img src=\"@/assets/images/right_slices/water/icon_2or.png\" alt=\"\" width=\"100%\">\r\n                            </div>\r\n                            <div class=\"title\">\r\n\r\n                                {{ item.title }}\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"level\" :class=\"item.level == '一级预警' ? 'border1' : 'border2'\">\r\n                            {{ item.level }}\r\n                        </div>\r\n                        <div class=\"text\">\r\n                            {{ item.text }}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n    name: 'rightView',\r\n    data() {\r\n        return {\r\n            // 预警统计\r\n            levelTitle: [\r\n                {\r\n                    name: \"一级预警\",\r\n                    num: 15\r\n                },\r\n                {\r\n                    name: \"二级预警\",\r\n                    num: 49\r\n                },\r\n                {\r\n                    name: \"三级预警\",\r\n                    num: 62\r\n                }\r\n            ],\r\n            timer: '',\r\n            animate: false, // 是否显示动画效果，默认为true。\r\n            levelData: [\r\n                {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                },\r\n                {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                }, {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                },\r\n                {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                }, {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                },\r\n                {\r\n                    title: \"水位预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.05,23\",\r\n                    level: \"一级预警\"\r\n                },\r\n                {\r\n                    title: \"进水口流量预警\",\r\n                    text: \"A区南侧水位过高, 报警设备LOO, 2023.02,12\",\r\n                    level: \"二级预警\"\r\n                },\r\n\r\n            ],\r\n        };\r\n    },\r\n    components: {\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        // this.scroll();\r\n        // this.timer = setInterval(() => {\r\n        //     this.scroll()\r\n        // }, 1500);\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n    destroyed() {\r\n        if (this.timer) {\r\n            clearInterval(this.timer);\r\n        }\r\n\r\n    },\r\n    methods: {\r\n        // 预警统计列表滚动\r\n        scroll() {\r\n            let that = this;\r\n            // let mainCon = document.querySelector(\".pushData .mainCon\")\r\n            // mainCon.style.top = '-9rem';\r\n            that.animate = !that.animate;\r\n            setTimeout(() => {\r\n                that.levelData.push(that.levelData[0]);\r\n                that.levelData.shift(that.levelData[0]);\r\n                // mainCon.style.top = '0';\r\n                that.animate = !that.animate;\r\n            }, 500)\r\n\r\n\r\n        },\r\n        // 预警统计数据鼠标经过\r\n        mEnter() {\r\n            if (this.timer) {\r\n                clearInterval(this.timer);\r\n            }\r\n        },\r\n        // 预警统计数据鼠标离开\r\n        mLeave() {\r\n            this.scroll()\r\n            this.timer = setInterval(() => {\r\n                this.scroll()\r\n            }, 1500)\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    width: 700px;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    margin-top: 20px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n}\r\n\r\n.rightView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .levelTitle {\r\n        margin-top: 30px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 60px;\r\n\r\n        >div {\r\n            width: 218px;\r\n            height: 169px;\r\n            text-align: center;\r\n            cursor: pointer;\r\n\r\n            .num {\r\n                margin-top: 30px;\r\n\r\n            }\r\n\r\n            .txt {\r\n                margin-top: 30px;\r\n            }\r\n\r\n            &:nth-child(1) {\r\n                background: url(\"@/assets/images/right_slices/water/boxIcon1.png\") no-repeat center;\r\n                background-size: 100% 100%;\r\n\r\n                .num {\r\n                    color: rgba(1, 196, 204, 1);\r\n                }\r\n            }\r\n\r\n            &:nth-child(2) {\r\n                background: url(\"@/assets/images/right_slices/water/boxIcon2.png\") no-repeat center;\r\n                background-size: 100% 100%;\r\n\r\n                .num {\r\n                    color: rgba(81, 147, 214, 1)\r\n                }\r\n            }\r\n\r\n            &:nth-child(3) {\r\n                background: url(\"@/assets/images/right_slices/water/boxIcon3.png\") no-repeat center;\r\n                background-size: 100% 100%;\r\n\r\n                .num {\r\n                    color: rgba(203, 132, 16, 1)\r\n                }\r\n            }\r\n\r\n            .num {\r\n                font-weight: bold;\r\n                font-size: 2.3rem;\r\n                padding-top: .5rem;\r\n            }\r\n\r\n            .txt {\r\n                font-size: 1.6rem;\r\n                line-height: 3rem;\r\n\r\n            }\r\n\r\n        }\r\n    }\r\n\r\n    .earlyWarningArtical {\r\n        width: 94%;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .pushData {\r\n        height: 690px;\r\n        overflow: hidden;\r\n\r\n        // .anim {\r\n        //     transition: all 1s ease;\r\n        // }\r\n\r\n        .mainCon {\r\n            position: relative;\r\n\r\n        }\r\n\r\n        .artItem {\r\n            position: relative;\r\n            padding: 1rem 0;\r\n            border-bottom: 2px solid rgb(204, 204, 204, .3);\r\n            cursor: pointer;\r\n\r\n            .artical {\r\n                margin-bottom: 1rem;\r\n                display: flex;\r\n                font-size: 1.7rem;\r\n                color: rgba(131, 212, 240, 1);\r\n\r\n                .title {\r\n                    margin-left: 1rem;\r\n                }\r\n\r\n                .pic {\r\n                    width: 1rem;\r\n                }\r\n            }\r\n\r\n            .level {\r\n                padding: 0 .7rem;\r\n                position: absolute;\r\n                right: .5rem;\r\n                top: 1.8rem;\r\n                text-align: right;\r\n                margin-right: 1rem;\r\n\r\n                font-size: 1.4rem;\r\n\r\n            }\r\n\r\n            .border1 {\r\n                color: rgba(8, 207, 203, 1);\r\n                border: 2px solid rgba(8, 207, 203, .7);\r\n            }\r\n\r\n            .border2 {\r\n                color: rgba(118, 174, 228, 1);\r\n                border: 2px solid rgba(118, 174, 228, 1);\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAwCA;EACAA,IAAA;EACAC,KAAA;IACA;MACA;MACAC,UAAA,GACA;QACAF,IAAA;QACAG,GAAA;MACA,GACA;QACAH,IAAA;QACAG,GAAA;MACA,GACA;QACAH,IAAA;QACAG,GAAA;MACA,EACA;MACAC,KAAA;MACAC,OAAA;MAAA;MACAC,SAAA,GACA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;IAGA;EACA;EACAC,UAAA,GACA;EACAC,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA;IACA;IACA;IACA;EAAA,CACA;EAEAC,QAAA,GAEA;EACAC,UAAA;IACA,SAAAX,KAAA;MACAY,aAAA,MAAAZ,KAAA;IACA;EAEA;EACAa,OAAA;IACA;IACAC,OAAA;MACA,IAAAC,IAAA;MACA;MACA;MACAA,IAAA,CAAAd,OAAA,IAAAc,IAAA,CAAAd,OAAA;MACAe,UAAA;QACAD,IAAA,CAAAb,SAAA,CAAAe,IAAA,CAAAF,IAAA,CAAAb,SAAA;QACAa,IAAA,CAAAb,SAAA,CAAAgB,KAAA,CAAAH,IAAA,CAAAb,SAAA;QACA;QACAa,IAAA,CAAAd,OAAA,IAAAc,IAAA,CAAAd,OAAA;MACA;IAGA;IACA;IACAkB,OAAA;MACA,SAAAnB,KAAA;QACAY,aAAA,MAAAZ,KAAA;MACA;IACA;IACA;IACAoB,OAAA;MACA,KAAAN,MAAA;MACA,KAAAd,KAAA,GAAAqB,WAAA;QACA,KAAAP,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}