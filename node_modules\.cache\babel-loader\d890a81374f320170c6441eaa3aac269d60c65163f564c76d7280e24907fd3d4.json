{"ast": null, "code": "import VoiceChat from '@/components/VoiceChat/index.vue';\nimport regionalIntroduction from '@/components/regionalIntroduction.vue';\nimport playUeShow from '@/components/play.vue';\nimport mainHeader from './components/mainHeader.vue';\nimport BaseLayer from './components/timeBaseLayer';\nimport allVideoStream from './components/allVideoStream';\nimport MapView from './components/MapView';\nimport mapEcharts from '@/views/mainPage/components/mapEcharts';\nimport videoPlayerList from '@/components/hlvJsVideo/videoPlayerList.vue';\nimport videoPlayerSteam from './components/videoPlayerSteam.vue';\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue';\n// import specificLayer from './components/resource-center/components/specificLayer.vue'\nimport resourceCenter from \"./components/resource-center/index.vue\";\n\n// import eventListProcessFlow from './components/eventListProcessFlow/eventList.vue'\n\nimport { getAddressList, getInfo, BuildingSummary, getHouseSummary } from '@/api/userMenu.js';\nimport { getOneVideoUrl } from '@/api/index.js';\nimport rslPlayer from \"@/components/rslPlayer/components/new-dialog.vue\";\nimport { getDispatchDyList } from '@/api/video.js';\nimport DiaLogPdf from '@/components/DiaLogPdf.vue';\nimport WaterQualityDetection from '@/components/smartWater/WaterQualityDetection.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'MainPage',\n  components: {\n    playUeShow,\n    mainHeader,\n    BaseLayer,\n    allVideoStream,\n    rslPlayer,\n    MapView,\n    mapEcharts,\n    DiaLogPdf,\n    WaterQualityDetection,\n    videoPlayerList,\n    videoPlayerSteam,\n    singleHlsVideo,\n    resourceCenter,\n    VoiceChat,\n    regionalIntroduction\n    // specificLayer\n    // eventListProcessFlow\n  },\n  data() {\n    return {\n      buildData: '',\n      allStyle: {\n        width: '46rem',\n        height: '15rem'\n      },\n      sizeStyle2: {\n        transform: 'translate(-10%, 50%) scale(2.1)',\n        left: '45%',\n        top: '20%'\n      },\n      minWidth: '380px',\n      videomBox: false,\n      width: 3840,\n      height: 2160,\n      isShowBaseLayer: false,\n      isShowFLVPlayer: false,\n      isShowAreaSearch: false,\n      isShowBox: false,\n      loading: true,\n      loading2: false,\n      isCheckMore: false,\n      //点击查看更多数据\n      restaurants: [],\n      //关键词列表\n      matchList: [],\n      //地名地址列表\n      address: '',\n      //选中的地名地址\n      searchValue: '',\n      pageNum: 1,\n      pageSize: 10,\n      total: 0,\n      headerFlag: true,\n      orgChildTabs: '',\n      playerSingleTitle: \"\",\n      singleCameras: [],\n      bottomList: [],\n      rslOverlay: false,\n      state1: '',\n      restaurants2: [],\n      devicesList: [],\n      rslOverlay: false,\n      showVideo: false,\n      // 图层坐标\n      layerCoordinateInfo: null,\n      isShowPdf: false,\n      pdfList: [],\n      changePdfArr: [{\n        name: '花桥经济开发区城市信息模型基本术语标准',\n        pdf: '/pdf/1.1cityInfo.pdf'\n      }, {\n        name: '花桥经济开发区标准地名地址库标准',\n        pdf: '/pdf/1.2addressName.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型分级分类规范',\n        pdf: '/pdf/1.3modelList.pdf'\n      }, {\n        name: '花桥经济开发区房屋建筑和市政设施编码标准',\n        pdf: '/pdf/2.1houseBuilding.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型基础平台数据资源目录标准',\n        pdf: '/pdf/3.1baseModel.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型基础平台数据汇交标准',\n        pdf: '/pdf/3.2webData.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型简单模型数据加工标准',\n        pdf: '/pdf/3.3dataFinishing.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型标准模型数据加工标准',\n        pdf: '/pdf/3.4modelStandardDataProcessing.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型精细模型数据加工标准',\n        pdf: '/pdf/3.5informationModelFineDataProcessing.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型功能级模型数据加工标准',\n        pdf: '/pdf/3.6functionLevelModelData.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型“一标多实”数据融合标准',\n        pdf: '/pdf/3.7oneStandardAndMultiReality.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型基础平台建设规范',\n        pdf: '/pdf/4.1foundationPlatformSpecification.pdf'\n      }, {\n        name: '花桥经济开发区城市信息模型基础平台应用规范',\n        pdf: '/pdf/4.2applicationSpecification.pdf'\n      }],\n      InterfaceList: [],\n      changeInterfaceArr: [{\n        name: '多发时间预警',\n        url: '/za/getFrequentEventWarningsList'\n      }, {\n        name: '社区小区办件量',\n        url: '/za/getCommunityVolumeList'\n      }, {\n        name: '智慧停车指数',\n        url: '/za/getSmartParkingList'\n      }, {\n        name: '物联设备',\n        url: '/za/getDevicesAll'\n      }, {\n        name: '网管液位点位告警及处理状态表',\n        url: '/za/getPipeNetworkAlarmProcessingList'\n      }, {\n        name: '半年投诉事件趋势',\n        url: '/za/getJurisDictionalAppealsList'\n      }, {\n        name: '管网液位预警',\n        url: '/za/getGuanwangLevelWarningList'\n      }, {\n        name: '部门办件量',\n        url: '/za/getSectorVolumeList'\n      }, {\n        name: '办件指数',\n        url: '/za/getAdminApprovalTakeamountnumslist'\n      }, {\n        name: '水质检测',\n        url: '/za/getWaterQualityMonitoringList'\n      }, {\n        name: '小区隐患上报',\n        url: '/za/getFireAnalysisHiddenDangerCommunityList'\n      }, {\n        name: '隐患上报-统计分析',\n        url: '/za/getFireAnalysisHiddenDangerCountList'\n      }, {\n        name: '水质事件管理',\n        url: '/watereventmanagement/groupByLocation'\n      }, {\n        name: '水质事件管理详情',\n        url: '/watereventmanagement/list'\n      }, {\n        name: '液位差实时报警',\n        url: '/waterywalarmhistory/groupByYwstatus'\n      }, {\n        name: '重大问题时间走热',\n        url: '/api/problemtimetrend/getQuestionCountByMonth'\n      }, {\n        name: '产业变化情况趋势',\n        url: '/api/industrialchangetrend/list'\n      }, {\n        name: '经济发展指数',\n        url: '/api/economicdevelopment/list'\n      }],\n      pdfDialogFlag: false,\n      isShowInterface: false,\n      PdfUrl: null,\n      AuthorityValue: '',\n      hzVideoSHow: '',\n      AuthorityValueList: ['标准规范', '数据图层', '地址搜索', '视频播放', '接口规范'],\n      peopleHouseInfoPop: false,\n      peopleInfoList: {},\n      houseTitle: '',\n      peopleOneHouseInfoPop: false,\n      peopleOneHouseInfoPopList: [],\n      subwayInformation: {\n        '地铁站级别': 'c级',\n        '客流分布': '单向锋型',\n        '站台形式': '地下岛式',\n        '车辆类型': '6节编组B型列车',\n        '设置座位': '232个',\n        '设计容纳人数': '2098人',\n        '应急预案': `1.已发生或出现可预见大客流、火灾、冒水等应急事件时行车值班员立即汇报控制中心、站长、中心站长,中心站长向领导汇报; \\n2.控制中心值班主任向公司应急指挥机构领导汇报;\\n3.值班主任向线网车站、相关部门发布博览中心站站启动Ⅲ级大客流预案；`,\n        '行值处置方案': 'a)车站发生大客流、火灾、冒水等应急事件,立即向行调、地铁公安、站长汇报；\\nb)通过CCTV监控车站客流变化情况,做好信息续报。做好乘客广播工作：“各位乘客请注意,由于本站客流较大,请配合车站工作人员安排,有序进站乘车”； \\nc)根据值站指示,安排站务人员关闭半边铁栏杆,减缓乘客进入本站速度；',\n        '客值处置方案': 'a)根据值站指示,关闭部分或全部进站闸机,限制进站客流,并做好乘客服务和解释工作；',\n        '站务员处置方案': 'a)根据值站指示,在出站闸机处引导乘客快速出站； \\nb)做好乘客服务和解释工作；',\n        '车站安保保洁等人员处置方案': '保安：站台保安根据站长指示,做好站台乘客的引导,并关闭站台至站厅电扶梯（确认轿厢内无人后),防止踩踏事情的发生； \\n安检员：安检员根据值站指示,严格控制进站,当进站闸机部分关闭时,做好乘客服务和解释工作。'\n      },\n      subwayInformation2: {},\n      interfaceName: '',\n      searchPdfName: '',\n      areaTitInfo: {},\n      hlsSingleData: {},\n      //撒点点击的单个视频\n      companyNum: 0,\n      currentPage: 1,\n      videoOptionClone: {},\n      videoOption: {},\n      videoDialogVisible: false,\n      voiceChat: true\n    };\n  },\n  watch: {\n    isShowPlay: {\n      handler(nv) {\n\n        // this.AuthorityValue = ''\n      },\n      immediate: true\n    },\n    dataChannelText(nv) {\n      // console.log('main',nv)\n      if (nv?.typeName == '人房信息') {\n        this.houseTitle = nv.name;\n        this.getBuildingSummary(nv.name);\n      } else if (nv?.typename == '监控') {\n        this.videoDialogVisible = true;\n        this.videoOptionClone = nv;\n      } else if (nv?.typename == '数据图层') {\n        this.$store.commit(\"action/getIsShowData\", true);\n        // this.isShowData=true\n        this.buildData = nv.data;\n      } else if (nv?.typename == 'data') {\n        setTimeout(() => {\n          if (nv.ape_id != '') {\n            this.$store.commit(\"action/getVideoPlayerList\", nv);\n            this.$store.commit(\"action/getVideoPlayerBox\", true);\n            this.$store.commit(\"action/getIsShowNumInitChange\", true);\n          } else {\n            this.$message.error('暂无视频点位');\n          }\n        }, 1500);\n      } else if (nv?.typename == 'videom') {\n        // this.videomBox = true\n        // nv.ape_id = nv.install_position\n        // nv.channalName = nv.device_name\n        // const { install_position, device_name, ...data } = nv\n        // nv = data\n\n        // if (nv.ape_id != '') {\n        //     this.$store.commit(\"action/getVideoPlayerList\", nv)\n        //     this.$store.commit(\"action/getVideoPlayerBox\", true)\n        //     this.$store.commit(\"action/getIsShowNumInitChange\", true)\n        // } else {\n        //     this.$message.error('暂无视频点位')\n        // }\n      } else if (nv?.typename == 'closevm') {\n        this.videomBox = false;\n        this.$store.commit(\"action/getVideoPlayerList\", null);\n        this.$store.commit(\"action/getVideoPlayerBox\", false);\n      } else if (nv?.typeName == 'GetVideoUrl') {\n        getOneVideoUrl(nv.Name).then(res => {\n          if (res.status == 200) {\n            let params = {\n              name: nv.Name,\n              url: res.data\n            };\n            this.$eventBus.$emit('senTxtToUe', params);\n          }\n        });\n      }\n    },\n    videoDialogVisible(nv) {\n      if (nv) {\n        this.videoOption = this.videoOptionClone;\n      }\n    }\n  },\n  computed: {\n    ...mapState(['dataChannelText', 'userVideoToken', 'isShowPlay', \"videoPlayerBox\"]),\n    ...mapState({\n      isShowData: state => state.action.isShowData\n    })\n  },\n  created() {},\n  beforeDestroy() {},\n  mounted() {\n    this.loadAll();\n    this.InterfaceList = this.changeInterfaceArr;\n    this.pdfList = this.changePdfArr;\n    this.setScale();\n    window.addEventListener('resize', this.debounce(this.setScale));\n  },\n  destroyed() {},\n  methods: {\n    handleStyle(newStyle) {\n      this.allStyle = {\n        ...this.allStyle,\n        ...newStyle\n      };\n    },\n    closeData() {\n      this.$store.commit(\"action/getIsShowData\", false);\n      // this.isShowData=false\n      this.buildData = '';\n    },\n    changeVideomBox(newValue) {\n      this.videomBox = newValue;\n    },\n    // 按比例缩放\n    getScale() {\n      const {\n        width,\n        height\n      } = this;\n      const wh = window.innerHeight / height;\n      const ww = window.innerWidth / width;\n      return {\n        scaleX: ww,\n        scaleY: wh\n      };\n    },\n    setScale() {\n      const {\n        scaleY,\n        scaleX\n      } = this.getScale();\n      if (this.$refs.main_page) {\n        this.$refs.main_page.style.setProperty('--scaleX', scaleX);\n        this.$refs.main_page.style.setProperty('--scaleY', scaleY);\n      }\n    },\n    debounce(fn, delay) {\n      const delays = delay || 200;\n      let timer;\n      return function () {\n        const th = this;\n        const args = arguments;\n        if (timer) {\n          clearTimeout(timer);\n        }\n        timer = setTimeout(function () {\n          timer = null;\n          fn.apply(th, args);\n        }, delays);\n      };\n    },\n    // 关闭弹窗\n    closeFun(nv) {\n      this.videoDialogVisible = false;\n    },\n    //播放内部视频\n    baseVideo(code) {\n      const selectVideo = this.devicesList.find(item => item.cameraCode === code);\n      selectVideo.deviceCode = selectVideo.cameraCode;\n      this.singleCameras.push(selectVideo);\n      this.playerSingleTitle = selectVideo.cameraName;\n      this.$refs.video1.open();\n      this.rslOverlay = true;\n    },\n    //关闭内部视频\n    closeBaseVideo() {\n      this.rslOverlay = false;\n      this.singleCameras = [];\n    },\n    loadAll() {\n      var dataList = [];\n      getDispatchDyList().then(res => {\n        dataList = res.data.data;\n        this.devicesList = dataList;\n      }).catch(e => {});\n    },\n    async initSearchData() {\n      let params = {\n        keyword: this.searchValue,\n        pageNum: this.pageNum,\n        pageSize: this.pageSize\n      };\n      await getAddressList(params).then(res => {\n        this.loading = false;\n        this.loading2 = false;\n        this.isCheckMore = false;\n        if (this.pageNum == 1) {\n          this.matchList = res.data.extra.records;\n          this.total = res.data.extra.total;\n        } else {\n          this.matchList.push(...res.data.extra.records);\n        }\n      }).catch(err => console.log(err));\n    },\n    getInput(val) {\n      this.isShowBox = false;\n    },\n    getCurrentDataBtn() {\n      this.loading = true;\n      this.isShowBox = true;\n      this.pageNum = 1;\n      this.total = 0;\n      this.matchList = [];\n      this.initSearchData();\n    },\n    // 查看更多数据\n    checkMoreBtn() {\n      this.isCheckMore = true;\n      this.loading2 = true;\n      this.pageNum++;\n      this.initSearchData();\n      this.total -= this.matchList.length;\n    },\n    async selectMatch(val) {\n      this.address = val.dzQdz;\n      await getInfo(this.address).then(res => {\n        let building = res.data.extra.building[0];\n        if (building == [] || building == null || building == undefined) {\n          this.$message({\n            message: '该小区暂无点位',\n            type: 'warning'\n          });\n        } else {\n          // this.$refs.play.larksr.sendTextToDataChannel(JSON.stringify(building))\n          this.$eventBus.$emit('senTxtToUe', building);\n        }\n      });\n    },\n    // 楼栋信息\n    async getBuildingSummary(val) {\n      let str = val;\n      str = val.replace(/栋/g, '幢');\n      // 截取市后面的字符串\n      str = str.split('市')[1];\n      await BuildingSummary(str).then(res => {\n        this.peopleInfoList = res.data.extra;\n        if (!this.peopleInfoList.houses.length) {\n          this.peopleHouseInfoPop = false;\n        } else {\n          this.peopleOneHouseInfoPop = false;\n          this.peopleHouseInfoPop = true;\n        }\n      });\n    },\n    // 权限数据ue交互\n\n    getHeaderData(val, topFlag) {\n      let isFlag = this.AuthorityValueList.some(i => {\n        return i == val;\n      });\n      if (!isFlag) {\n        this.$eventBus.$emit('senTxtToUe', val);\n      }\n      if (!topFlag) {\n        this.AuthorityValue = '';\n      } else {\n        this.AuthorityValue = val;\n        switch (val) {\n          case \"标准规范\":\n            this.isShowPdf = true;\n            break;\n          case \"接口管理\":\n            this.isShowInterface = true;\n            break;\n          case \"视频播放\":\n            // this.getDeviceListInfo()\n            break;\n          case \"专题图层\":\n            this.voiceChat = false;\n            break;\n          case \"首页\":\n            this.voiceChat = true;\n            break;\n          default:\n            break;\n        }\n      }\n    },\n    // 数据图层ue交互\n    getDataLayer(val) {\n      if (this.isShowPlay) {\n        this.$eventBus.$emit('senTxtToUe', val);\n        // console.log('val', val)\n      }\n    },\n    // 2d数据撒点\n    changeData2DLayer(val) {\n      this.layerCoordinateInfo = val;\n    },\n    // 文件预览\n    handlePosition(index, row) {\n      // window.open(row.pdf);\n      this.pdfDialogFlag = true;\n      this.PdfUrl = row.pdf;\n    },\n    // 关闭pdf弹窗\n    pdfPopWindow(nv) {\n      this.pdfDialogFlag = nv;\n    },\n    // 文件下载\n    downPdf(index, row) {\n      var a = document.createElement(\"a\");\n      a.href = row.pdf;\n      a.download = row.name;\n      a.style.display = \"none\";\n      document.body.appendChild(a);\n      a.click();\n      a.remove();\n    },\n    closePdfDialog() {\n      this.isShowPdf = false;\n    },\n    closeInterface() {\n      this.isShowInterface = false;\n    },\n    // 关闭弹窗\n    closeBox() {\n      this.AuthorityValue = '';\n    },\n    async oneHouseInfo_show(index, row) {\n      this.peopleHouseInfoPop = false;\n      let {\n        qfwm\n      } = row;\n      await getHouseSummary(qfwm).then(res => {\n        this.peopleOneHouseInfoPopList = res.data.extra;\n      });\n      this.peopleOneHouseInfoPop = true;\n    },\n    closeOneHouseInfoPop() {\n      this.peopleOneHouseInfoPop = false;\n      this.peopleHouseInfoPop = true;\n    },\n    changeInterfaceList(val) {\n      this.InterfaceList = [];\n      this.changeInterfaceArr.find((item, index) => {\n        let i = item.name.indexOf(val) != -1;\n        if (i) {\n          this.InterfaceList.push(item);\n        }\n      });\n    },\n    changePdfList(val) {\n      this.pdfList = [];\n      this.changePdfArr.find(item => {\n        let i = item.name.indexOf(val) != -1;\n        if (i) {\n          this.pdfList.push(item);\n        }\n      });\n    },\n    // 获取设备列表\n    async getDeviceListInfo(nv = 1) {\n      let params = {\n        page_num: nv,\n        page_size: 100\n      };\n      // await getVideoListInfo(params).then(res => {\n      //     if (res.data.message == \"success\") {\n\n      //         let params = {\n      //             type: \"区域监控\",\n      //             data: res.data.data.data,\n      //             flag: true\n      //         }\n      //         this.$eventBus.$emit('senTxtToUe', params)\n\n      //     }\n\n      // }).catch(err => {\n      //     console.log(err);\n      // })\n    },\n    getSelectPageSize(nv) {\n      this.currentPage = nv;\n    }\n  }\n};", "map": {"version": 3, "names": ["VoiceChat", "regionalIntroduction", "playUeShow", "<PERSON><PERSON><PERSON><PERSON>", "Base<PERSON><PERSON>er", "allVideoStream", "MapView", "mapEcharts", "videoPlayerList", "videoPlayerSteam", "singleHlsVideo", "resourceCenter", "getAddressList", "getInfo", "BuildingSummary", "getHouseSummary", "getOneVideoUrl", "rslPlayer", "getDispatchDyList", "DiaLogPdf", "WaterQualityDetection", "mapState", "name", "components", "data", "buildData", "allStyle", "width", "height", "sizeStyle2", "transform", "left", "top", "min<PERSON><PERSON><PERSON>", "videomBox", "isShowBaseLayer", "isShowFLVPlayer", "isShowAreaSearch", "isShowBox", "loading", "loading2", "isCheckMore", "restaurants", "matchList", "address", "searchValue", "pageNum", "pageSize", "total", "headerFlag", "orgChildTabs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleCameras", "bottomList", "rslOverlay", "state1", "restaurants2", "devicesList", "showVideo", "layerCoordinateInfo", "isShowPdf", "pdfList", "changePdfArr", "pdf", "InterfaceList", "changeInterfaceArr", "url", "pdfDialogFlag", "isShowInterface", "PdfUrl", "AuthorityValue", "hzVideoSHow", "AuthorityValueList", "peopleHouseInfoPop", "peopleInfoList", "houseTitle", "peopleOneHouseInfoPop", "peopleOneHouseInfoPopList", "subwayInformation", "subwayInformation2", "interfaceName", "searchPdfName", "areaTitInfo", "hlsSingleData", "companyNum", "currentPage", "videoOptionClone", "videoOption", "videoDialogVisible", "voiceChat", "watch", "isShowPlay", "handler", "nv", "immediate", "dataChannelText", "typeName", "getBuildingSummary", "typename", "$store", "commit", "setTimeout", "ape_id", "$message", "error", "Name", "then", "res", "status", "params", "$eventBus", "$emit", "computed", "isShowData", "state", "action", "created", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "loadAll", "setScale", "window", "addEventListener", "debounce", "destroyed", "methods", "handleStyle", "newStyle", "closeData", "changeVideomBox", "newValue", "getScale", "wh", "innerHeight", "ww", "innerWidth", "scaleX", "scaleY", "$refs", "main_page", "style", "setProperty", "fn", "delay", "delays", "timer", "th", "args", "arguments", "clearTimeout", "apply", "closeFun", "baseVideo", "code", "selectVideo", "find", "item", "cameraCode", "deviceCode", "push", "cameraName", "video1", "open", "closeBaseVideo", "dataList", "catch", "e", "initSearchData", "keyword", "extra", "records", "err", "console", "log", "getInput", "val", "getCurrentDataBtn", "checkMoreBtn", "length", "selectMatch", "dzQdz", "building", "undefined", "message", "type", "str", "replace", "split", "houses", "getHeaderData", "topFlag", "isFlag", "some", "i", "get<PERSON>ata<PERSON><PERSON><PERSON>", "changeData2DLayer", "handlePosition", "index", "row", "pdfPopWindow", "downPdf", "a", "document", "createElement", "href", "download", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "closePdfDialog", "closeInterface", "closeBox", "oneHouseInfo_show", "qfwm", "closeOneHouseInfoPop", "changeInterfaceList", "indexOf", "changePdfList", "getDeviceListInfo", "page_num", "page_size", "getSelectPageSize"], "sources": ["src/views/mainPage/MainPage.vue"], "sourcesContent": ["<template>\n    <div class=\"main-page\" ref=\"main_page\">\n        <mainHeader ref=\"header\" @changerHeaderData='getHeaderData' :class=\"rslOverlay ? 'noClick' : ''\"></mainHeader>\n        <!-- 标准规范 -->\n        <!-- <div class=\"pdfShow\" v-if=\"AuthorityValue == '标准规范'\">\n            <el-dialog class=\"DialogType_Box\" :title=\"AuthorityValue\" :visible.sync=\"isShowPdf\" width=\"40%\"\n                :fullscreen=\"false\" :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\"\n                :before-close=\"closePdfDialog\" append-to-body>\n\n                <div class=\"inputTxt\" style=\"width: 30%;padding-left: 100px;\">\n                    <el-input placeholder=\"请输入内容\" v-model=\"searchPdfName\" @change=\"changePdfList\" clearable>\n                    </el-input>\n                </div>\n\n                <el-table :data=\"pdfList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"400\" :header-cell-style=\"{\n                    color: '#fff',\n                    fontWeight: '700',\n                    backgroundColor: 'rgba(18, 76, 111, .45)',\n\n                }\">\n                    <el-table-column align=\"center\" prop=\"name\" label=\"名称\" :show-overflow-tooltip=\"true\">\n                    </el-table-column>\n                    <el-table-column align=\"center\" label=\"文件\" :show-overflow-tooltip=\"true\">\n                        <template slot-scope=\"scope\">\n                            <el-button @click.native.prevent=\"handlePosition(scope.$index, scope.row)\" type=\"text\"\n                                size=\"small\">\n                                文件预览\n                            </el-button>\n\n                        </template>\n</el-table-column>\n<el-table-column fixed=\"right\" label=\"操作\" width=\"100\">\n    <template slot-scope=\"scope\">\n\n                            <el-button @click.native.prevent=\"downPdf(scope.$index, scope.row)\" type=\"text\"\n                                size=\"small\">\n                                下载\n                            </el-button>\n                        </template>\n</el-table-column>\n\n</el-table>\n</el-dialog>\n\n<DiaLogPdf v-if=\"pdfDialogFlag\" ref=\"diaLogPdf\" :url=\"PdfUrl\" :dialogVisible=\"pdfDialogFlag\" @updateFun=\"pdfPopWindow\">\n</DiaLogPdf>\n</div> -->\n        <!-- 接口规范 -->\n        <!-- <div class=\"pdfShow\" v-if=\"AuthorityValue == '接口管理'\">\n            <el-dialog class=\"DialogType_Box\" title=\"接口规范\" :visible.sync=\"isShowInterface\" width=\"40%\"\n                :fullscreen=\"false\" :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\"\n                :before-close=\"closeInterface\" append-to-body>\n\n                <div class=\"inputTxt\" style=\"width: 30%;padding-left: 100px;\">\n                    <el-input placeholder=\"请输入内容\" v-model=\"interfaceName\" @change=\"changeInterfaceList\" clearable>\n                    </el-input>\n                </div>\n\n\n                <el-table :data=\"InterfaceList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"400\" :header-cell-style=\"{\n                    color: '#fff',\n                    fontWeight: '700',\n                    backgroundColor: 'rgba(18, 76, 111, .45)',\n\n                }\">\n                    <el-table-column align=\"center\" prop=\"name\" label=\"名称\" :show-overflow-tooltip=\"true\">\n                    </el-table-column>\n                    <el-table-column align=\"center\" prop=\"url\" label=\"接口\" :show-overflow-tooltip=\"true\">\n                    </el-table-column>\n\n                </el-table>\n            </el-dialog>\n        </div> -->\n        <!-- 人防搜索 -->\n        <div class=\"areaSearch\" v-if=\"AuthorityValue == '地址搜索' && isShowPlay\">\n            <el-row class=\"demo-autocomplete addressSearch\">\n                <el-col>\n                    <el-input class=\"areaInputTxt\" v-model=\"searchValue\" placeholder=\"请输入内容\" style=\"width: 250px;\"\n                        @focus=\"getInput\"></el-input>\n                    <el-button @click=\"getCurrentDataBtn\" type=\"primary\" icon=\"el-icon-search\"></el-button>\n                </el-col>\n\n            </el-row>\n            <div class=\"littleBox\" v-if=\"isShowBox\">\n                <transition name=\"moveBox\" appear>\n                    <div class=\"inputDataBox\">\n                        <ul v-loading=\"loading\" element-loading-spinner=\"el-icon-loading\">\n                            <li v-for=\"(item, index) of matchList\" :key=\"index\"\n                                :class=\"item.dzQdz == address ? 'avtiveItem' : ''\" @click=\"selectMatch(item)\">\n                                <div class=\"itemList\">\n                                    <div class=\"placeName\">\n                                        {{ item.dzJlxmc }}\n                                    </div>\n                                    <div class=\"placeNameInfo\">\n                                        <a href=\"javascript:;\" :title=\"item.dzQdz\">\n                                            {{ item.dzQdz }}\n                                        </a>\n                                    </div>\n\n                                </div>\n                            </li>\n                            <li class=\"loadMore\" v-if=\"pageNum * pageSize < total && total > 0\">\n                                <div class=\"txt\" v-show=\"!loading2\" @click=\"checkMoreBtn\">查看全部结果{{ total }}条结果</div>\n                                <div class=\"txt\" v-loading=\"loading2\" element-loading-spinner=\"el-icon-loading\"></div>\n                            </li>\n                            <li class=\"loadMore\" v-if=\"!loading && total == 0\">\n                                <div class=\"txt\">没有找到相关内容</div>\n                            </li>\n                        </ul>\n                    </div>\n\n                </transition>\n\n            </div>\n\n        </div>\n        <div class=\"screenBox\">\n            <!-- <playUeShow ref=\"play\" v-show=\"isShowPlay\"></playUeShow> -->\n            <MapView ref=\"MapView\" v-if=\"isShowPlay === '2d'\" :layerCoordinateInfo=\"layerCoordinateInfo\"></MapView>\n            <resourceCenter v-if=\"!isShowPlay\" :AuthorityValue=\"AuthorityValue\" />\n        </div>\n\n\n        <!-- 人房信息弹窗展示 -->\n        <div class=\"peopleHouseInfo\">\n            <el-card class=\"box-card\" v-show=\"peopleHouseInfoPop\">\n                <div slot=\"header\" class=\"clearfix\" style=\"transform: translateY(30px); \">\n                    <span> {{ houseTitle.substring(houseTitle.indexOf('号') + 1) }}</span>\n                    <el-button icon=\"el-icon-close\" style=\"float: right; padding: 3px 10px; font-size: 20px;\"\n                        type=\"text\" @click=\"peopleHouseInfoPop = false\"></el-button>\n                </div>\n                <div class=\"peoplePerShow\">\n                    <div class=\"totalHouses\">\n                        住户: {{ peopleInfoList.houses?.length }} 户\n                    </div>\n                    <WaterQualityDetection :width=\"'300px'\" :height=\"'112px'\" :fontSize=\"12\"\n                        :peopleCount=\"peopleInfoList\">\n                    </WaterQualityDetection>\n                </div>\n                <el-table :data=\"peopleInfoList.houses\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"270\"\n                    :header-cell-style=\"{\n                        color: '#000',\n                        fontWeight: '700',\n                        backgroundColor: 'rgba(18, 76, 111, .45)',\n\n                    }\" class=\"houseTable\">\n                    <el-table-column align=\"center\" type=\"index\" label=\"序号\" width=\"50\"> </el-table-column>\n                    <el-table-column align=\"center\" prop=\"hz.bzaddress\" label=\"门牌号\" :show-overflow-tooltip=\"true\">\n                        <template slot-scope=\"scope\">\n\n                            <span>\n                                {{ scope.row?.qfwm.substring(scope.row.qfwm.indexOf('幢') + 1) }}\n                            </span>\n                        </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\" prop=\"hz.name\" label=\"户主\" :show-overflow-tooltip=\"true\">\n                        <template slot-scope=\"scope\">\n                            <span>\n                                {{ scope.row?.hz.name.substring(0, 1) + '*' + scope.row?.hz.name.substring(2) }}\n                            </span>\n                        </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\" prop=\"hz.phone\" label=\"联系方式\" :show-overflow-tooltip=\"true\">\n                    </el-table-column>\n                    <el-table-column fixed=\"right\" label=\"操作\" width=\"100\">\n                        <template slot-scope=\"scope\">\n\n                            <el-button @click.native.prevent=\"oneHouseInfo_show(scope.$index, scope.row)\" type=\"text\"\n                                size=\"small\">\n                                详情\n                            </el-button>\n                        </template>\n                    </el-table-column>\n\n                </el-table>\n            </el-card>\n\n            <el-card class=\"box-card\" v-show=\"peopleOneHouseInfoPop\">\n                <div slot=\"header\" class=\"clearfix\" style=\"transform: translateY(30px); \">\n                    <span>{{ houseTitle.substring(houseTitle.indexOf('号') + 1) }}</span>\n                    <el-button icon=\"el-icon-close\" style=\"float: right; padding: 3px 10px; font-size: 20px;\"\n                        type=\"text\" @click=\"closeOneHouseInfoPop\"></el-button>\n                </div>\n                <el-table :data=\"peopleOneHouseInfoPopList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"350\"\n                    :header-cell-style=\"{\n                        color: '#000',\n                        fontWeight: '700',\n                        backgroundColor: 'rgba(18, 76, 111, .7)',\n\n                    }\" class=\"houseTable\">\n                    <el-table-column align=\"center\" type=\"index\" label=\"序号\" width=\"50\"> </el-table-column>\n                    <el-table-column align=\"center\" prop=\"qdzShort\" label=\"门牌号\" :show-overflow-tooltip=\"true\">\n                        <template slot-scope=\"scope\">\n\n                            <span>\n                                {{ scope.row?.qdzShort.substring(scope.row.qdzShort.indexOf('幢') + 1) }}\n                            </span>\n                        </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\" prop=\"name\" label=\"户主\" :show-overflow-tooltip=\"true\">\n                        <template slot-scope=\"scope\">\n                            <span>\n                                {{ scope.row?.name.substring(0, 1) + '*' + scope.row?.name.substring(2) }}\n                            </span>\n                        </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\" prop=\"phone\" label=\"联系方式\" :show-overflow-tooltip=\"true\">\n                    </el-table-column>\n\n                </el-table>\n            </el-card>\n\n        </div>\n\n        <!-- 人员疏散 -->\n        <div class=\"PersonnelEvacuation\" v-if=\"AuthorityValue == '人员疏散' && isShowPlay\">\n            <div class=\"box-card boxBgStyle\">\n                <div slot=\"header\" class=\"clearfix el-card__header\">\n                    <span :style=\"{ fontSize: '35px' }\">博览中心站</span>\n                    <el-button icon=\"el-icon-close\" style=\"float: right; padding: 3px 10px; font-size: 30px;\"\n                        type=\"text\" @click=\"AuthorityValue = ''\"></el-button>\n                </div>\n                <div class=\"con_box_mainShow EvacuationStyles el-card__body\">\n                    <div class=\"widthSize text item\">\n                        <div class=\"item\" v-for=\"(v, key, i) in subwayInformation\" :key=\"i\" v-if=\"i < 6\"\n                            :style=\"{ 'display': 'flex' }\">\n                            <div class=\"keyTxt\">{{ key }} :</div>\n                            <div class=\"valTxt\">{{ v }}</div>\n                        </div>\n                    </div>\n                    <div class=\"items\">\n                        <div class=\"item\" v-for=\"(v, key, i) in subwayInformation\" :key=\"i\" v-if=\"i > 5\"\n                            :style=\"{ 'display': 'flex' }\">\n                            <div class=\"keyTxt\">{{ key }} :</div>\n                            <div class=\"valTxt\">{{ v }}</div>\n                        </div>\n                    </div>\n\n                </div>\n            </div>\n        </div>\n\n        <!-- 数据图层 -->\n        <div class=\"baseLayer\" v-if=\"AuthorityValue == '数据融合'\">\n            <BaseLayer @changeDataLayer='getDataLayer'></BaseLayer>\n            <div class=\"dataBox\" v-if=\"isShowData\">\n                <div class=\"delete\" @click=\"closeData\">\n                    <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\n                </div>\n\n                <ul>\n                    <li>\n                        <span class=\"dataT\">名称:</span>\n                        <span>{{ buildData.name }}</span>\n                    </li>\n                    <li>\n                        <span class=\"dataT\">地址:</span>\n                        <span>{{ buildData.address }}</span>\n                    </li>\n                    <li>\n                        <span class=\"dataT\">所属派出所:</span>\n                        <span>{{ buildData.local }}</span>\n                    </li>\n                    <li>\n                        <span class=\"dataT\">社区:</span>\n                        <span>{{ buildData.society }}</span>\n                    </li>\n                </ul>\n            </div>\n        </div>\n\n\n        <!-- 监控列表（水务、高空,区域) -->\n        <!-- <div class=\"videoShow\" v-if=\"AuthorityValue == '视频融合'\"> -->\n\n        <!-- <allVideoStream :devicesList=\"devicesList\" ref=\"flvPlayer\" :hlsSingleVideo=\"hlsSingleData\"\n                :currentPage=\"currentPage\" @changePageSize=\"getSelectPageSize\"></allVideoStream> -->\n        <!-- <videoPlayerSteam ref=\"flvPlayer\"></videoPlayerSteam>\n        </div> -->\n\n        <!-- 图表 -->\n        <div class=\"yearGDP\">\n            <!-- <mapEcharts v-if=\"isShowPlay\"></mapEcharts> -->\n            <mapEcharts v-if=\"false\"></mapEcharts>\n        </div>\n\n        <!-- 场景漫游弹窗 -->\n        <div class=\"uePlayerDialog\" v-if=\"videoDialogVisible\">\n            <!-- <videoPlayerList :videoOption=\"videoOption\"></videoPlayerList> -->\n        </div>\n\n        <!-- 事件列表 -->\n        <div class=\"eventListProcessFl\">\n            <!-- <eventListProcessFlow></eventListProcessFlow> -->\n        </div>\n        <div class=\"eventListProcessFl\" v-if=\"videomBox\">\n            <singleHlsVideo :allStyle=\"allStyle\" :sizeStyle=\"sizeStyle2\" :videomBox=\"videomBox\"\n                @changeVideomBox=\"changeVideomBox\" @updateStyle=\"handleStyle\" :minWidth=\"minWidth\">\n            </singleHlsVideo>\n        </div>\n        <!-- ai对话框 -->\n        <VoiceChat v-if=\"voiceChat\" />\n        <!-- 地址搜索 -->\n        <!-- <addressSearch /> -->\n        <!-- 区域介绍 -->\n        <regionalIntroduction />\n    </div>\n</template>\n\n<script>\nimport VoiceChat from '@/components/VoiceChat/index.vue'\nimport regionalIntroduction from '@/components/regionalIntroduction.vue'\nimport playUeShow from '@/components/play.vue'\nimport mainHeader from './components/mainHeader.vue'\nimport BaseLayer from './components/timeBaseLayer'\nimport allVideoStream from './components/allVideoStream'\nimport MapView from './components/MapView'\nimport mapEcharts from '@/views/mainPage/components/mapEcharts'\nimport videoPlayerList from '@/components/hlvJsVideo/videoPlayerList.vue'\nimport videoPlayerSteam from './components/videoPlayerSteam.vue'\nimport singleHlsVideo from '@/components/hlvJsVideo/singleHlsVideo.vue'\n// import specificLayer from './components/resource-center/components/specificLayer.vue'\nimport resourceCenter from \"./components/resource-center/index.vue\"\n\n// import eventListProcessFlow from './components/eventListProcessFlow/eventList.vue'\n\nimport { getAddressList, getInfo, BuildingSummary, getHouseSummary } from '@/api/userMenu.js'\nimport { getOneVideoUrl } from '@/api/index.js'\nimport rslPlayer from \"@/components/rslPlayer/components/new-dialog.vue\";\nimport { getDispatchDyList } from '@/api/video.js'\n\nimport DiaLogPdf from '@/components/DiaLogPdf.vue'\nimport WaterQualityDetection from '@/components/smartWater/WaterQualityDetection.vue'\n\nimport { mapState } from 'vuex'\n\n\nexport default {\n    name: 'MainPage',\n    components: {\n        playUeShow,\n        mainHeader,\n        BaseLayer,\n        allVideoStream,\n        rslPlayer,\n        MapView,\n        mapEcharts,\n        DiaLogPdf,\n        WaterQualityDetection,\n        videoPlayerList,\n        videoPlayerSteam,\n        singleHlsVideo,\n        resourceCenter,\n        VoiceChat,\n        regionalIntroduction\n        // specificLayer\n        // eventListProcessFlow\n    },\n    data() {\n        return {\n            buildData: '',\n            allStyle: {\n                width: '46rem',\n                height: '15rem',\n            },\n            sizeStyle2: {\n                transform: 'translate(-10%, 50%) scale(2.1)',\n                left: '45%',\n                top: '20%'\n            },\n            minWidth: '380px',\n            videomBox: false,\n            width: 3840,\n            height: 2160,\n            isShowBaseLayer: false,\n            isShowFLVPlayer: false,\n            isShowAreaSearch: false,\n            isShowBox: false,\n            loading: true,\n            loading2: false,\n            isCheckMore: false,//点击查看更多数据\n            restaurants: [],//关键词列表\n            matchList: [],//地名地址列表\n            address: '',//选中的地名地址\n            searchValue: '',\n            pageNum: 1,\n            pageSize: 10,\n            total: 0,\n\n            headerFlag: true,\n            orgChildTabs: '',\n            playerSingleTitle: \"\",\n            singleCameras: [],\n            bottomList: [],\n            rslOverlay: false,\n\n            state1: '',\n            restaurants2: [],\n            devicesList: [],\n            rslOverlay: false,\n            showVideo: false,\n            // 图层坐标\n            layerCoordinateInfo: null,\n            isShowPdf: false,\n            pdfList: [],\n            changePdfArr: [\n                { name: '花桥经济开发区城市信息模型基本术语标准', pdf: '/pdf/1.1cityInfo.pdf' },\n                { name: '花桥经济开发区标准地名地址库标准', pdf: '/pdf/1.2addressName.pdf' },\n                { name: '花桥经济开发区城市信息模型分级分类规范', pdf: '/pdf/1.3modelList.pdf' },\n                { name: '花桥经济开发区房屋建筑和市政设施编码标准', pdf: '/pdf/2.1houseBuilding.pdf' },\n                { name: '花桥经济开发区城市信息模型基础平台数据资源目录标准', pdf: '/pdf/3.1baseModel.pdf' },\n                { name: '花桥经济开发区城市信息模型基础平台数据汇交标准', pdf: '/pdf/3.2webData.pdf' },\n                { name: '花桥经济开发区城市信息模型简单模型数据加工标准', pdf: '/pdf/3.3dataFinishing.pdf' },\n                { name: '花桥经济开发区城市信息模型标准模型数据加工标准', pdf: '/pdf/3.4modelStandardDataProcessing.pdf' },\n                { name: '花桥经济开发区城市信息模型精细模型数据加工标准', pdf: '/pdf/3.5informationModelFineDataProcessing.pdf' },\n                { name: '花桥经济开发区城市信息模型功能级模型数据加工标准', pdf: '/pdf/3.6functionLevelModelData.pdf' },\n                { name: '花桥经济开发区城市信息模型“一标多实”数据融合标准', pdf: '/pdf/3.7oneStandardAndMultiReality.pdf' },\n                { name: '花桥经济开发区城市信息模型基础平台建设规范', pdf: '/pdf/4.1foundationPlatformSpecification.pdf' },\n                { name: '花桥经济开发区城市信息模型基础平台应用规范', pdf: '/pdf/4.2applicationSpecification.pdf' }\n            ],\n            InterfaceList: [],\n            changeInterfaceArr: [\n                { name: '多发时间预警', url: '/za/getFrequentEventWarningsList' },\n                { name: '社区小区办件量', url: '/za/getCommunityVolumeList' },\n                { name: '智慧停车指数', url: '/za/getSmartParkingList' },\n                { name: '物联设备', url: '/za/getDevicesAll' },\n                { name: '网管液位点位告警及处理状态表', url: '/za/getPipeNetworkAlarmProcessingList' },\n                { name: '半年投诉事件趋势', url: '/za/getJurisDictionalAppealsList' },\n                { name: '管网液位预警', url: '/za/getGuanwangLevelWarningList' },\n                { name: '部门办件量', url: '/za/getSectorVolumeList' },\n                { name: '办件指数', url: '/za/getAdminApprovalTakeamountnumslist' },\n                { name: '水质检测', url: '/za/getWaterQualityMonitoringList' },\n                { name: '小区隐患上报', url: '/za/getFireAnalysisHiddenDangerCommunityList' },\n                { name: '隐患上报-统计分析', url: '/za/getFireAnalysisHiddenDangerCountList' },\n                { name: '水质事件管理', url: '/watereventmanagement/groupByLocation' },\n                { name: '水质事件管理详情', url: '/watereventmanagement/list' },\n                { name: '液位差实时报警', url: '/waterywalarmhistory/groupByYwstatus' },\n                { name: '重大问题时间走热', url: '/api/problemtimetrend/getQuestionCountByMonth' },\n                { name: '产业变化情况趋势', url: '/api/industrialchangetrend/list' },\n                { name: '经济发展指数', url: '/api/economicdevelopment/list' },\n            ],\n            pdfDialogFlag: false,\n            isShowInterface: false,\n            PdfUrl: null,\n            AuthorityValue: '',\n            hzVideoSHow: '',\n            AuthorityValueList: ['标准规范', '数据图层', '地址搜索', '视频播放', '接口规范'],\n            peopleHouseInfoPop: false,\n            peopleInfoList: {},\n            houseTitle: '',\n            peopleOneHouseInfoPop: false,\n            peopleOneHouseInfoPopList: [],\n            subwayInformation: {\n                '地铁站级别': 'c级',\n                '客流分布': '单向锋型',\n                '站台形式': '地下岛式',\n                '车辆类型': '6节编组B型列车',\n                '设置座位': '232个',\n                '设计容纳人数': '2098人',\n                '应急预案': `1.已发生或出现可预见大客流、火灾、冒水等应急事件时行车值班员立即汇报控制中心、站长、中心站长,中心站长向领导汇报; \\n2.控制中心值班主任向公司应急指挥机构领导汇报;\\n3.值班主任向线网车站、相关部门发布博览中心站站启动Ⅲ级大客流预案；`,\n                '行值处置方案': 'a)车站发生大客流、火灾、冒水等应急事件,立即向行调、地铁公安、站长汇报；\\nb)通过CCTV监控车站客流变化情况,做好信息续报。做好乘客广播工作：“各位乘客请注意,由于本站客流较大,请配合车站工作人员安排,有序进站乘车”； \\nc)根据值站指示,安排站务人员关闭半边铁栏杆,减缓乘客进入本站速度；',\n                '客值处置方案': 'a)根据值站指示,关闭部分或全部进站闸机,限制进站客流,并做好乘客服务和解释工作；',\n                '站务员处置方案': 'a)根据值站指示,在出站闸机处引导乘客快速出站； \\nb)做好乘客服务和解释工作；',\n                '车站安保保洁等人员处置方案': '保安：站台保安根据站长指示,做好站台乘客的引导,并关闭站台至站厅电扶梯（确认轿厢内无人后),防止踩踏事情的发生； \\n安检员：安检员根据值站指示,严格控制进站,当进站闸机部分关闭时,做好乘客服务和解释工作。'\n            },\n            subwayInformation2: {},\n            interfaceName: '',\n            searchPdfName: '',\n            areaTitInfo: {},\n            hlsSingleData: {},//撒点点击的单个视频\n            companyNum: 0,\n            currentPage: 1,\n            videoOptionClone: {},\n            videoOption: {},\n            videoDialogVisible: false,\n            voiceChat: true\n        };\n    },\n    watch: {\n        isShowPlay: {\n            handler(nv) {\n\n                // this.AuthorityValue = ''\n            },\n            immediate: true\n\n        },\n\n        dataChannelText(nv) {\n            // console.log('main',nv)\n            if (nv?.typeName == '人房信息') {\n                this.houseTitle = nv.name\n                this.getBuildingSummary(nv.name)\n            } else if (nv?.typename == '监控') {\n                this.videoDialogVisible = true\n                this.videoOptionClone = nv;\n            }\n            else if (nv?.typename == '数据图层') {\n                this.$store.commit(\"action/getIsShowData\", true)\n                // this.isShowData=true\n                this.buildData = nv.data\n            }\n            else if (nv?.typename == 'data') {\n                setTimeout(() => {\n                    if (nv.ape_id != '') {\n                        this.$store.commit(\"action/getVideoPlayerList\", nv)\n                        this.$store.commit(\"action/getVideoPlayerBox\", true)\n                        this.$store.commit(\"action/getIsShowNumInitChange\", true)\n                    } else {\n                        this.$message.error('暂无视频点位')\n                    }\n                }, 1500)\n            }\n            else if (nv?.typename == 'videom') {\n                // this.videomBox = true\n                // nv.ape_id = nv.install_position\n                // nv.channalName = nv.device_name\n                // const { install_position, device_name, ...data } = nv\n                // nv = data\n\n                // if (nv.ape_id != '') {\n                //     this.$store.commit(\"action/getVideoPlayerList\", nv)\n                //     this.$store.commit(\"action/getVideoPlayerBox\", true)\n                //     this.$store.commit(\"action/getIsShowNumInitChange\", true)\n                // } else {\n                //     this.$message.error('暂无视频点位')\n                // }\n            }\n            else if (nv?.typename == 'closevm') {\n                this.videomBox = false\n                this.$store.commit(\"action/getVideoPlayerList\", null)\n                this.$store.commit(\"action/getVideoPlayerBox\", false)\n            }\n            else if (nv?.typeName == 'GetVideoUrl') {\n                getOneVideoUrl(nv.Name).then(res => {\n                    if (res.status == 200) {\n                        let params = {\n                            name: nv.Name,\n                            url: res.data\n                        }\n                        this.$eventBus.$emit('senTxtToUe', params)\n                    }\n                })\n            }\n        },\n        videoDialogVisible(nv) {\n            if (nv) {\n                this.videoOption = this.videoOptionClone\n            }\n        },\n\n\n    },\n    computed: {\n\n        ...mapState(['dataChannelText', 'userVideoToken', 'isShowPlay', \"videoPlayerBox\",]),\n        ...mapState({\n            isShowData: state => state.action.isShowData,\n        }),\n    },\n    created() {\n\n    },\n    beforeDestroy() {\n\n    },\n    mounted() {\n\n        this.loadAll();\n        this.InterfaceList = this.changeInterfaceArr\n        this.pdfList = this.changePdfArr\n\n        this.setScale()\n        window.addEventListener('resize', this.debounce(this.setScale))\n    },\n    destroyed() {\n\n    },\n\n\n    methods: {\n        handleStyle(newStyle) {\n            this.allStyle = { ...this.allStyle, ...newStyle };\n        },\n        closeData() {\n            this.$store.commit(\"action/getIsShowData\", false)\n            // this.isShowData=false\n            this.buildData = ''\n        },\n        changeVideomBox(newValue) {\n            this.videomBox = newValue\n        },\n        // 按比例缩放\n        getScale() {\n            const { width, height } = this\n            const wh = window.innerHeight / height\n            const ww = window.innerWidth / width\n            return { scaleX: ww, scaleY: wh }\n        },\n        setScale() {\n            const { scaleY, scaleX } = this.getScale()\n            if (this.$refs.main_page) {\n                this.$refs.main_page.style.setProperty('--scaleX', scaleX)\n                this.$refs.main_page.style.setProperty('--scaleY', scaleY)\n            }\n        },\n\n\n        debounce(fn, delay) {\n            const delays = delay || 200\n            let timer\n            return function () {\n                const th = this\n                const args = arguments\n                if (timer) {\n                    clearTimeout(timer)\n                }\n                timer = setTimeout(function () {\n                    timer = null\n                    fn.apply(th, args)\n                }, delays)\n            }\n        },\n\n\n        // 关闭弹窗\n        closeFun(nv) {\n            this.videoDialogVisible = false\n        },\n        //播放内部视频\n        baseVideo(code) {\n            const selectVideo = this.devicesList.find(item => item.cameraCode === code);\n            selectVideo.deviceCode = selectVideo.cameraCode;\n            this.singleCameras.push(selectVideo);\n            this.playerSingleTitle = selectVideo.cameraName;\n            this.$refs.video1.open();\n            this.rslOverlay = true;\n        },\n        //关闭内部视频\n        closeBaseVideo() {\n            this.rslOverlay = false;\n            this.singleCameras = [];\n        },\n        loadAll() {\n            var dataList = [];\n            getDispatchDyList().then(res => {\n                dataList = res.data.data;\n                this.devicesList = dataList\n            }).catch(e => {\n\n            })\n\n        },\n\n        async initSearchData() {\n            let params = {\n                keyword: this.searchValue,\n                pageNum: this.pageNum,\n                pageSize: this.pageSize\n            }\n            await getAddressList(params).then(res => {\n                this.loading = false;\n                this.loading2 = false;\n                this.isCheckMore = false;\n                if (this.pageNum == 1) {\n                    this.matchList = res.data.extra.records;\n                    this.total = res.data.extra.total;\n                } else {\n                    this.matchList.push(...res.data.extra.records);\n                }\n            }).catch(err => console.log(err))\n        },\n        getInput(val) {\n            this.isShowBox = false\n        },\n\n        getCurrentDataBtn() {\n            this.loading = true\n            this.isShowBox = true\n            this.pageNum = 1;\n            this.total = 0\n            this.matchList = [];\n            this.initSearchData()\n        },\n        // 查看更多数据\n        checkMoreBtn() {\n            this.isCheckMore = true;\n            this.loading2 = true;\n            this.pageNum++;\n            this.initSearchData()\n            this.total -= this.matchList.length\n        },\n\n        async selectMatch(val) {\n            this.address = val.dzQdz;\n            await getInfo(this.address).then(res => {\n                let building = res.data.extra.building[0];\n\n                if (building == [] || building == null || building == undefined) {\n                    this.$message({\n                        message: '该小区暂无点位',\n                        type: 'warning'\n                    })\n                } else {\n                    // this.$refs.play.larksr.sendTextToDataChannel(JSON.stringify(building))\n                    this.$eventBus.$emit('senTxtToUe', building)\n                }\n\n            })\n        },\n        // 楼栋信息\n        async getBuildingSummary(val) {\n\n            let str = val;\n            str = val.replace(/栋/g, '幢');\n            // 截取市后面的字符串\n            str = str.split('市')[1];\n            await BuildingSummary(str).then(res => {\n                this.peopleInfoList = res.data.extra;\n                if (!this.peopleInfoList.houses.length) {\n                    this.peopleHouseInfoPop = false;\n                } else {\n                    this.peopleOneHouseInfoPop = false\n                    this.peopleHouseInfoPop = true;\n                }\n\n            })\n        },\n\n        // 权限数据ue交互\n\n        getHeaderData(val, topFlag) {\n            let isFlag = this.AuthorityValueList.some((i) => {\n                return i == val\n            })\n            if (!isFlag) {\n                this.$eventBus.$emit('senTxtToUe', val)\n            }\n\n            if (!topFlag) {\n                this.AuthorityValue = ''\n            } else {\n                this.AuthorityValue = val\n\n                switch (val) {\n                    case \"标准规范\":\n                        this.isShowPdf = true;\n                        break;\n                    case \"接口管理\":\n                        this.isShowInterface = true;\n                        break;\n                    case \"视频播放\":\n                        // this.getDeviceListInfo()\n                        break;\n                    case \"专题图层\":\n                        this.voiceChat = false\n                        break;\n                    case \"首页\":\n                        this.voiceChat = true\n                        break;\n                    default:\n                        break;\n                }\n\n            }\n\n        },\n        // 数据图层ue交互\n        getDataLayer(val) {\n            if (this.isShowPlay) {\n                this.$eventBus.$emit('senTxtToUe', val)\n                // console.log('val', val)\n            }\n        },\n        // 2d数据撒点\n        changeData2DLayer(val) {\n            this.layerCoordinateInfo = val\n        },\n        // 文件预览\n        handlePosition(index, row) {\n            // window.open(row.pdf);\n            this.pdfDialogFlag = true\n            this.PdfUrl = row.pdf\n        },\n        // 关闭pdf弹窗\n        pdfPopWindow(nv) {\n            this.pdfDialogFlag = nv\n        },\n        // 文件下载\n        downPdf(index, row) {\n            var a = document.createElement(\"a\");\n            a.href = row.pdf;\n            a.download = row.name;\n            a.style.display = \"none\";\n            document.body.appendChild(a);\n            a.click();\n            a.remove();\n        },\n        closePdfDialog() {\n            this.isShowPdf = false\n        },\n        closeInterface() {\n            this.isShowInterface = false\n        },\n        // 关闭弹窗\n        closeBox() {\n            this.AuthorityValue = ''\n        },\n        async oneHouseInfo_show(index, row) {\n            this.peopleHouseInfoPop = false\n            let { qfwm } = row;\n            await getHouseSummary(qfwm).then(res => {\n                this.peopleOneHouseInfoPopList = res.data.extra\n            })\n\n            this.peopleOneHouseInfoPop = true\n        },\n        closeOneHouseInfoPop() {\n            this.peopleOneHouseInfoPop = false\n            this.peopleHouseInfoPop = true\n        },\n        changeInterfaceList(val) {\n            this.InterfaceList = []\n            this.changeInterfaceArr.find((item, index) => {\n                let i = item.name.indexOf(val) != -1\n                if (i) {\n                    this.InterfaceList.push(item)\n                }\n            })\n        },\n        changePdfList(val) {\n            this.pdfList = []\n            this.changePdfArr.find(item => {\n                let i = item.name.indexOf(val) != -1;\n                if (i) {\n                    this.pdfList.push(item)\n                }\n            })\n        },\n        // 获取设备列表\n        async getDeviceListInfo(nv = 1) {\n            let params = {\n                page_num: nv,\n                page_size: 100,\n            }\n            // await getVideoListInfo(params).then(res => {\n            //     if (res.data.message == \"success\") {\n\n            //         let params = {\n            //             type: \"区域监控\",\n            //             data: res.data.data.data,\n            //             flag: true\n            //         }\n            //         this.$eventBus.$emit('senTxtToUe', params)\n\n            //     }\n\n            // }).catch(err => {\n            //     console.log(err);\n            // })\n        },\n        getSelectPageSize(nv) {\n            this.currentPage = nv\n        }\n\n    },\n\n};\n</script>\n\n<style lang=\"less\" scoped>\n.dataBox {\n    width: 260px;\n    height: 220px;\n    position: absolute;\n    top: 200px;\n    left: 52%;\n    background-color: rgba(46, 101, 167, 0.5);\n\n    .delete {\n        float: right;\n        margin-right: 20px;\n        margin-top: 10px;\n    }\n\n    li {\n        margin-top: 28px;\n        margin-left: 5px;\n        width: 230px;\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n    }\n\n    .dataT {\n        font-size: 20px;\n        margin-right: 5px;\n    }\n\n}\n\n.main-page {\n    position: relative;\n}\n\n.peopleHouseInfo {\n\n    position: absolute;\n    top: 500px;\n    transform: translate(-50%, -50%);\n\n    /deep/ .el-card__header {\n        border: none;\n        // padding: 30px 40px 15px;\n    }\n\n    :deep(.el-card) {\n        background: url('@/assets/images/mainPics/popBg.png') no-repeat center;\n        background-size: 100% 100%;\n        border: none;\n        color: #fff;\n\n\n    }\n\n    /deep/ .el-table,\n    /deep/ .el-table tr,\n    /deep/ .el-table td,\n    /deep/ .el-table th {\n        color: #fff !important;\n        background-color: transparent !important;\n        border: 0;\n    }\n\n    /deep/ .el-table__cell.is-leaf {\n        border: 0;\n\n    }\n\n    /deep/ .el-table::before {\n        height: 0; // 将高度修改为0\n    }\n\n    .text {\n        font-size: 12px;\n\n    }\n\n    .item {\n        margin-bottom: 14px;\n    }\n\n    .clearfix:before,\n    .clearfix:after {\n        display: table;\n        content: \"\";\n    }\n\n    .clearfix:after {\n        clear: both\n    }\n\n\n\n}\n\n.PersonnelEvacuation {\n    position: absolute;\n    left: 0;\n    top: 10vh;\n    margin-left: 60%;\n    transform-origin: 0 0;\n    transform: scale(var(--scaleX), var(--scaleY));\n    transition: 0.3s;\n\n    .box-card {\n        width: 1200px;\n        color: #fff;\n        text-shadow: 0 0 5px rgb(102, 177, 255), 0 0 15px #fff;\n        // background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\n        // background-size: 100% 100%;\n        // border: 0;\n\n        .el-card__header {\n            // border: none;\n            padding: 30px 40px 15px;\n        }\n\n        .widthSize {\n            // width: 100%;\n            font-size: 30px;\n            display: flex;\n            flex-wrap: wrap;\n\n            >div {\n                width: 33%;\n            }\n        }\n\n        .item {\n            line-height: 50px;\n        }\n\n        .items {\n            line-height: 40px;\n\n            .keyTxt {\n                width: 36%;\n            }\n\n            .valTxt {\n                width: 80%;\n                white-space: pre-wrap;\n            }\n        }\n\n\n        .el-card__body {\n            font-size: 25px;\n            height: 680px;\n            overflow: auto;\n            padding-bottom: 20px;\n        }\n\n        .peoplePerShow {\n            display: flex;\n\n            .totalHouses {\n                margin-right: 20px;\n                line-height: 50px;\n            }\n        }\n    }\n\n}\n\n.videoShow {\n    z-index: 1;\n}\n\n.peopleHouseInfo {\n    left: 85%;\n}\n\n\n\n.yearGDP {\n    position: fixed;\n    top: 4rem;\n}\n\n.noClick {\n    pointer-events: none;\n    cursor: not-allowed;\n}\n\n.videoSearch {\n    position: relative;\n    top: 5rem;\n}\n\n.screenBox {\n    width: 100%;\n    // height: 100vh;\n}\n\n.boxVideo {\n    width: 500px;\n    height: 400px;\n    position: fixed;\n    top: 30%;\n    left: 20%;\n}\n\n.DialogType_Box,\n.peopleHouseInfo {\n    z-index: 1;\n\n    .widthSize {\n        // width: 100%;\n        font-size: 20px;\n        display: flex;\n        flex-wrap: wrap;\n\n        >div {\n            width: 33%;\n        }\n    }\n\n    .items {\n        .keyTxt {\n            width: 36%;\n        }\n\n        .valTxt {\n            width: 80%;\n            white-space: pre-wrap;\n        }\n    }\n\n\n    /deep/ .el-dialog__header {\n        padding: 5rem;\n    }\n\n    /deep/.el-input__inner {\n        background-color: transparent !important;\n        border: 1px solid #1296db;\n    }\n\n    ::v-deep .el-table__fixed-right-patch {\n        background-color: transparent !important;\n    }\n\n    ::v-deep .el-table,\n    ::v-deep .el-table tr,\n    ::v-deep .el-table td,\n    ::v-deep .el-table th {\n        background-color: transparent !important;\n        border: 0;\n    }\n\n    ::v-deep .el-table__cell.is-leaf {\n        border: 0;\n\n    }\n\n    ::v-deep .el-table tr {\n        color: #fff;\n    }\n\n    ::v-deep .el-table.houseTable tr {\n        color: #000;\n    }\n\n    ::v-deep .warning.el-table tr:nth-of-type(even) {\n        color: rgb(91, 170, 243);\n    }\n\n    ::v-deep .el-table::before {\n        height: 0; // 将高度修改为0\n    }\n\n    ::v-deep .el-dialog {\n        /* background-color: transparent; */\n        background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\n        background-size: 100% 100%;\n\n        .el-input__inner {\n            color: #fff;\n        }\n\n        .el-dialog__header {\n            padding: 3rem 4rem 0;\n\n            .el-dialog__title {\n                color: #fff;\n                font-size: 1.4rem;\n\n            }\n\n            .el-dialog__close {\n                color: #fff;\n                padding: 1.6rem 1rem 0;\n            }\n        }\n    }\n\n}\n\n.areaSearch {\n\n    width: 20rem;\n    position: absolute;\n    top: 10vh;\n    left: 2rem;\n    z-index: 22;\n\n    ::v-deep .el-input__inner {\n        background: transparent;\n        color: #fff;\n    }\n\n    .littleBox {\n        overflow: hidden;\n        margin-left: 0;\n\n    }\n\n    .addressSearch {\n        ::v-deep .el-input__inner {\n            border: 2px solid #1977d2;\n\n        }\n\n        .el-input {\n            width: 100px;\n            background-color: rgba(52, 80, 120);\n\n        }\n    }\n\n    .inputDataBox {\n        /* margin-top: 1rem; */\n        /* color: #fff; */\n        width: 95%;\n        height: 14rem;\n        overflow: auto;\n        border: 1px solid #1977d2;\n        border-top: none;\n        background-color: rgba(52, 80, 120, .6);\n\n        ::v-deep .el-loading-mask {\n            background-color: rgba(52, 80, 120, .6);\n        }\n\n        .avtiveItem {\n            background-color: rgba(52, 80, 121);\n        }\n\n        ul {\n            height: 16rem;\n        }\n\n        li {\n            padding: 10px 8px;\n            cursor: pointer;\n            border-bottom: 1px solid rgba(52, 80, 120, .3);\n\n            .placeName {\n                font-size: .9rem;\n                color: #fff;\n            }\n\n            .placeNameInfo {\n                width: 100%;\n                font-size: .7rem;\n\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n\n                a {\n                    text-decoration: none;\n                    color: rgba(154, 179, 203);\n\n                }\n            }\n\n        }\n\n        .loadMore {\n            font-size: .7rem;\n            color: #77adff;\n            text-align: center;\n            user-select: none;\n            padding: .5rem 0;\n\n            ::v-deep .el-loading-spinner {\n                margin: top -13px;\n            }\n        }\n\n        .loadMore:hover {\n            color: #377ee9;\n        }\n\n    }\n\n    .moveBox-enter-active {\n        animation: move .7s;\n    }\n\n    .moveBox-leave-active {\n        animation: move .2s reverse;\n    }\n\n    @keyframes move {\n        from {\n            transform: translateY(-100%);\n        }\n\n        to {\n            transform: translate(0);\n        }\n    }\n}\n</style>"], "mappings": "AAsTA,OAAAA,SAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,eAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,cAAA;AACA;AACA,OAAAC,cAAA;;AAEA;;AAEA,SAAAC,cAAA,EAAAC,OAAA,EAAAC,eAAA,EAAAC,eAAA;AACA,SAAAC,cAAA;AACA,OAAAC,SAAA;AACA,SAAAC,iBAAA;AAEA,OAAAC,SAAA;AACA,OAAAC,qBAAA;AAEA,SAAAC,QAAA;AAGA;EACAC,IAAA;EACAC,UAAA;IACArB,UAAA;IACAC,UAAA;IACAC,SAAA;IACAC,cAAA;IACAY,SAAA;IACAX,OAAA;IACAC,UAAA;IACAY,SAAA;IACAC,qBAAA;IACAZ,eAAA;IACAC,gBAAA;IACAC,cAAA;IACAC,cAAA;IACAX,SAAA;IACAC;IACA;IACA;EACA;EACAuB,KAAA;IACA;MACAC,SAAA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,UAAA;QACAC,SAAA;QACAC,IAAA;QACAC,GAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAP,KAAA;MACAC,MAAA;MACAO,eAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,SAAA;MACAC,OAAA;MACAC,QAAA;MACAC,WAAA;MAAA;MACAC,WAAA;MAAA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,WAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MAEAC,UAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,UAAA;MACAC,UAAA;MAEAC,MAAA;MACAC,YAAA;MACAC,WAAA;MACAH,UAAA;MACAI,SAAA;MACA;MACAC,mBAAA;MACAC,SAAA;MACAC,OAAA;MACAC,YAAA,GACA;QAAAxC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,GACA;QAAAzC,IAAA;QAAAyC,GAAA;MAAA,EACA;MACAC,aAAA;MACAC,kBAAA,GACA;QAAA3C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,GACA;QAAA5C,IAAA;QAAA4C,GAAA;MAAA,EACA;MACAC,aAAA;MACAC,eAAA;MACAC,MAAA;MACAC,cAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,UAAA;MACAC,qBAAA;MACAC,yBAAA;MACAC,iBAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,kBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,UAAA;MACAC,QAAAC,EAAA;;QAEA;MAAA,CACA;MACAC,SAAA;IAEA;IAEAC,gBAAAF,EAAA;MACA;MACA,IAAAA,EAAA,EAAAG,QAAA;QACA,KAAArB,UAAA,GAAAkB,EAAA,CAAAvE,IAAA;QACA,KAAA2E,kBAAA,CAAAJ,EAAA,CAAAvE,IAAA;MACA,WAAAuE,EAAA,EAAAK,QAAA;QACA,KAAAV,kBAAA;QACA,KAAAF,gBAAA,GAAAO,EAAA;MACA,OACA,IAAAA,EAAA,EAAAK,QAAA;QACA,KAAAC,MAAA,CAAAC,MAAA;QACA;QACA,KAAA3E,SAAA,GAAAoE,EAAA,CAAArE,IAAA;MACA,OACA,IAAAqE,EAAA,EAAAK,QAAA;QACAG,UAAA;UACA,IAAAR,EAAA,CAAAS,MAAA;YACA,KAAAH,MAAA,CAAAC,MAAA,8BAAAP,EAAA;YACA,KAAAM,MAAA,CAAAC,MAAA;YACA,KAAAD,MAAA,CAAAC,MAAA;UACA;YACA,KAAAG,QAAA,CAAAC,KAAA;UACA;QACA;MACA,OACA,IAAAX,EAAA,EAAAK,QAAA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA,MACA,IAAAL,EAAA,EAAAK,QAAA;QACA,KAAAhE,SAAA;QACA,KAAAiE,MAAA,CAAAC,MAAA;QACA,KAAAD,MAAA,CAAAC,MAAA;MACA,OACA,IAAAP,EAAA,EAAAG,QAAA;QACAhF,cAAA,CAAA6E,EAAA,CAAAY,IAAA,EAAAC,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,MAAA;YACA,IAAAC,MAAA;cACAvF,IAAA,EAAAuE,EAAA,CAAAY,IAAA;cACAvC,GAAA,EAAAyC,GAAA,CAAAnF;YACA;YACA,KAAAsF,SAAA,CAAAC,KAAA,eAAAF,MAAA;UACA;QACA;MACA;IACA;IACArB,mBAAAK,EAAA;MACA,IAAAA,EAAA;QACA,KAAAN,WAAA,QAAAD,gBAAA;MACA;IACA;EAGA;EACA0B,QAAA;IAEA,GAAA3F,QAAA;IACA,GAAAA,QAAA;MACA4F,UAAA,EAAAC,KAAA,IAAAA,KAAA,CAAAC,MAAA,CAAAF;IACA;EACA;EACAG,QAAA,GAEA;EACAC,cAAA,GAEA;EACAC,QAAA;IAEA,KAAAC,OAAA;IACA,KAAAvD,aAAA,QAAAC,kBAAA;IACA,KAAAJ,OAAA,QAAAC,YAAA;IAEA,KAAA0D,QAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,QAAA,MAAAH,QAAA;EACA;EACAI,UAAA,GAEA;EAGAC,OAAA;IACAC,YAAAC,QAAA;MACA,KAAArG,QAAA;QAAA,QAAAA,QAAA;QAAA,GAAAqG;MAAA;IACA;IACAC,UAAA;MACA,KAAA7B,MAAA,CAAAC,MAAA;MACA;MACA,KAAA3E,SAAA;IACA;IACAwG,gBAAAC,QAAA;MACA,KAAAhG,SAAA,GAAAgG,QAAA;IACA;IACA;IACAC,SAAA;MACA;QAAAxG,KAAA;QAAAC;MAAA;MACA,MAAAwG,EAAA,GAAAX,MAAA,CAAAY,WAAA,GAAAzG,MAAA;MACA,MAAA0G,EAAA,GAAAb,MAAA,CAAAc,UAAA,GAAA5G,KAAA;MACA;QAAA6G,MAAA,EAAAF,EAAA;QAAAG,MAAA,EAAAL;MAAA;IACA;IACAZ,SAAA;MACA;QAAAiB,MAAA;QAAAD;MAAA,SAAAL,QAAA;MACA,SAAAO,KAAA,CAAAC,SAAA;QACA,KAAAD,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,WAAA,aAAAL,MAAA;QACA,KAAAE,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,WAAA,aAAAJ,MAAA;MACA;IACA;IAGAd,SAAAmB,EAAA,EAAAC,KAAA;MACA,MAAAC,MAAA,GAAAD,KAAA;MACA,IAAAE,KAAA;MACA;QACA,MAAAC,EAAA;QACA,MAAAC,IAAA,GAAAC,SAAA;QACA,IAAAH,KAAA;UACAI,YAAA,CAAAJ,KAAA;QACA;QACAA,KAAA,GAAA5C,UAAA;UACA4C,KAAA;UACAH,EAAA,CAAAQ,KAAA,CAAAJ,EAAA,EAAAC,IAAA;QACA,GAAAH,MAAA;MACA;IACA;IAGA;IACAO,SAAA1D,EAAA;MACA,KAAAL,kBAAA;IACA;IACA;IACAgE,UAAAC,IAAA;MACA,MAAAC,WAAA,QAAAjG,WAAA,CAAAkG,IAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,UAAA,KAAAJ,IAAA;MACAC,WAAA,CAAAI,UAAA,GAAAJ,WAAA,CAAAG,UAAA;MACA,KAAAzG,aAAA,CAAA2G,IAAA,CAAAL,WAAA;MACA,KAAAvG,iBAAA,GAAAuG,WAAA,CAAAM,UAAA;MACA,KAAAtB,KAAA,CAAAuB,MAAA,CAAAC,IAAA;MACA,KAAA5G,UAAA;IACA;IACA;IACA6G,eAAA;MACA,KAAA7G,UAAA;MACA,KAAAF,aAAA;IACA;IACAmE,QAAA;MACA,IAAA6C,QAAA;MACAlJ,iBAAA,GAAAwF,IAAA,CAAAC,GAAA;QACAyD,QAAA,GAAAzD,GAAA,CAAAnF,IAAA,CAAAA,IAAA;QACA,KAAAiC,WAAA,GAAA2G,QAAA;MACA,GAAAC,KAAA,CAAAC,CAAA,KAEA;IAEA;IAEA,MAAAC,eAAA;MACA,IAAA1D,MAAA;QACA2D,OAAA,OAAA3H,WAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA;MACA,MAAAnC,cAAA,CAAAiG,MAAA,EAAAH,IAAA,CAAAC,GAAA;QACA,KAAApE,OAAA;QACA,KAAAC,QAAA;QACA,KAAAC,WAAA;QACA,SAAAK,OAAA;UACA,KAAAH,SAAA,GAAAgE,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA,CAAAC,OAAA;UACA,KAAA1H,KAAA,GAAA2D,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA,CAAAzH,KAAA;QACA;UACA,KAAAL,SAAA,CAAAoH,IAAA,IAAApD,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA,CAAAC,OAAA;QACA;MACA,GAAAL,KAAA,CAAAM,GAAA,IAAAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;IACA;IACAG,SAAAC,GAAA;MACA,KAAAzI,SAAA;IACA;IAEA0I,kBAAA;MACA,KAAAzI,OAAA;MACA,KAAAD,SAAA;MACA,KAAAQ,OAAA;MACA,KAAAE,KAAA;MACA,KAAAL,SAAA;MACA,KAAA4H,cAAA;IACA;IACA;IACAU,aAAA;MACA,KAAAxI,WAAA;MACA,KAAAD,QAAA;MACA,KAAAM,OAAA;MACA,KAAAyH,cAAA;MACA,KAAAvH,KAAA,SAAAL,SAAA,CAAAuI,MAAA;IACA;IAEA,MAAAC,YAAAJ,GAAA;MACA,KAAAnI,OAAA,GAAAmI,GAAA,CAAAK,KAAA;MACA,MAAAvK,OAAA,MAAA+B,OAAA,EAAA8D,IAAA,CAAAC,GAAA;QACA,IAAA0E,QAAA,GAAA1E,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA,CAAAY,QAAA;QAEA,IAAAA,QAAA,UAAAA,QAAA,YAAAA,QAAA,IAAAC,SAAA;UACA,KAAA/E,QAAA;YACAgF,OAAA;YACAC,IAAA;UACA;QACA;UACA;UACA,KAAA1E,SAAA,CAAAC,KAAA,eAAAsE,QAAA;QACA;MAEA;IACA;IACA;IACA,MAAApF,mBAAA8E,GAAA;MAEA,IAAAU,GAAA,GAAAV,GAAA;MACAU,GAAA,GAAAV,GAAA,CAAAW,OAAA;MACA;MACAD,GAAA,GAAAA,GAAA,CAAAE,KAAA;MACA,MAAA7K,eAAA,CAAA2K,GAAA,EAAA/E,IAAA,CAAAC,GAAA;QACA,KAAAjC,cAAA,GAAAiC,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA;QACA,UAAA/F,cAAA,CAAAkH,MAAA,CAAAV,MAAA;UACA,KAAAzG,kBAAA;QACA;UACA,KAAAG,qBAAA;UACA,KAAAH,kBAAA;QACA;MAEA;IACA;IAEA;;IAEAoH,cAAAd,GAAA,EAAAe,OAAA;MACA,IAAAC,MAAA,QAAAvH,kBAAA,CAAAwH,IAAA,CAAAC,CAAA;QACA,OAAAA,CAAA,IAAAlB,GAAA;MACA;MACA,KAAAgB,MAAA;QACA,KAAAjF,SAAA,CAAAC,KAAA,eAAAgE,GAAA;MACA;MAEA,KAAAe,OAAA;QACA,KAAAxH,cAAA;MACA;QACA,KAAAA,cAAA,GAAAyG,GAAA;QAEA,QAAAA,GAAA;UACA;YACA,KAAAnH,SAAA;YACA;UACA;YACA,KAAAQ,eAAA;YACA;UACA;YACA;YACA;UACA;YACA,KAAAqB,SAAA;YACA;UACA;YACA,KAAAA,SAAA;YACA;UACA;YACA;QACA;MAEA;IAEA;IACA;IACAyG,aAAAnB,GAAA;MACA,SAAApF,UAAA;QACA,KAAAmB,SAAA,CAAAC,KAAA,eAAAgE,GAAA;QACA;MACA;IACA;IACA;IACAoB,kBAAApB,GAAA;MACA,KAAApH,mBAAA,GAAAoH,GAAA;IACA;IACA;IACAqB,eAAAC,KAAA,EAAAC,GAAA;MACA;MACA,KAAAnI,aAAA;MACA,KAAAE,MAAA,GAAAiI,GAAA,CAAAvI,GAAA;IACA;IACA;IACAwI,aAAA1G,EAAA;MACA,KAAA1B,aAAA,GAAA0B,EAAA;IACA;IACA;IACA2G,QAAAH,KAAA,EAAAC,GAAA;MACA,IAAAG,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA,CAAAvI,GAAA;MACA0I,CAAA,CAAAI,QAAA,GAAAP,GAAA,CAAAhL,IAAA;MACAmL,CAAA,CAAA7D,KAAA,CAAAkE,OAAA;MACAJ,QAAA,CAAAK,IAAA,CAAAC,WAAA,CAAAP,CAAA;MACAA,CAAA,CAAAQ,KAAA;MACAR,CAAA,CAAAS,MAAA;IACA;IACAC,eAAA;MACA,KAAAvJ,SAAA;IACA;IACAwJ,eAAA;MACA,KAAAhJ,eAAA;IACA;IACA;IACAiJ,SAAA;MACA,KAAA/I,cAAA;IACA;IACA,MAAAgJ,kBAAAjB,KAAA,EAAAC,GAAA;MACA,KAAA7H,kBAAA;MACA;QAAA8I;MAAA,IAAAjB,GAAA;MACA,MAAAvL,eAAA,CAAAwM,IAAA,EAAA7G,IAAA,CAAAC,GAAA;QACA,KAAA9B,yBAAA,GAAA8B,GAAA,CAAAnF,IAAA,CAAAiJ,KAAA;MACA;MAEA,KAAA7F,qBAAA;IACA;IACA4I,qBAAA;MACA,KAAA5I,qBAAA;MACA,KAAAH,kBAAA;IACA;IACAgJ,oBAAA1C,GAAA;MACA,KAAA/G,aAAA;MACA,KAAAC,kBAAA,CAAA0F,IAAA,EAAAC,IAAA,EAAAyC,KAAA;QACA,IAAAJ,CAAA,GAAArC,IAAA,CAAAtI,IAAA,CAAAoM,OAAA,CAAA3C,GAAA;QACA,IAAAkB,CAAA;UACA,KAAAjI,aAAA,CAAA+F,IAAA,CAAAH,IAAA;QACA;MACA;IACA;IACA+D,cAAA5C,GAAA;MACA,KAAAlH,OAAA;MACA,KAAAC,YAAA,CAAA6F,IAAA,CAAAC,IAAA;QACA,IAAAqC,CAAA,GAAArC,IAAA,CAAAtI,IAAA,CAAAoM,OAAA,CAAA3C,GAAA;QACA,IAAAkB,CAAA;UACA,KAAApI,OAAA,CAAAkG,IAAA,CAAAH,IAAA;QACA;MACA;IACA;IACA;IACA,MAAAgE,kBAAA/H,EAAA;MACA,IAAAgB,MAAA;QACAgH,QAAA,EAAAhI,EAAA;QACAiI,SAAA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;;MAEA;MACA;MACA;IACA;IACAC,kBAAAlI,EAAA;MACA,KAAAR,WAAA,GAAAQ,EAAA;IACA;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}