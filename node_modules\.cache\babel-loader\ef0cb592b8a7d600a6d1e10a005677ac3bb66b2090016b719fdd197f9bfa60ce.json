{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getSectorVolumeList } from '@/api/index.js';\nexport default {\n  name: 'CaseDistribution',\n  data() {\n    return {\n      case: null,\n      xData: [],\n      data: []\n    };\n  },\n  beforeDestroy() {\n    if (this.case) {\n      this.case.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.case != null && this.case != undefined && this.case != '') {\n          this.case.dispose();\n        }\n        ;\n        this.case = new echarts.init(this.$refs.case);\n        getSectorVolumeList().then(res => {\n          let dataList = res.data.data;\n          for (let i of dataList) {\n            this.data.push({\n              name: i.deptName,\n              value: i.total * 1\n            });\n            this.xData.push(i.deptName);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let max;\n      let temp;\n      for (var i of this.data) {\n        max = i.value;\n        if (i.value > max) {\n          max = i.value;\n        }\n      }\n      const option = {\n        grid: {\n          left: '5%',\n          right: '5%',\n          bottom: '45%',\n          top: '15%'\n          // containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: this.xData,\n          axisLine: {\n            show: false\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 24,\n            formatter: params => {\n              let rowMax = 4;\n              let overValue = '';\n              for (let i = 0; i < params.length; i++) {\n                if (i % rowMax == 0 && i != 0) {\n                  overValue += params[i] + '\\n';\n                } else {\n                  overValue += params[i];\n                }\n              }\n              return overValue;\n            }\n          },\n          axisTick: {\n            show: false\n          }\n        },\n        yAxis: {\n          type: 'value',\n          splitLine: {\n            show: false\n          },\n          axisLabel: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          }\n        },\n        dataZoom: [{\n          type: 'inside',\n          start: 0,\n          end: 30\n        }, {}],\n        series: [{\n          data: this.data,\n          type: 'pictorialBar',\n          // 象形柱图\n          symbol: 'triangle',\n          // 三角形\n          barWidth: '100%',\n          itemStyle: {\n            // normal: {\n            color: v => {\n              let color = ['rgba(248, 219, 139,1)', 'rgba(248, 219, 139, .3)'];\n              if (v.value == max) {\n                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n                  offset: 0,\n                  color: color[0]\n                }, {\n                  offset: .4,\n                  color: color[1]\n                }, {\n                  offset: 1,\n                  color: 'rgba(0,0,0,.1)'\n                }]);\n              }\n              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n                offset: 0,\n                color: 'rgba(3, 251, 226, 1)'\n              }, {\n                offset: .4,\n                color: 'rgba(3, 251, 226, .3)'\n              }, {\n                offset: 1,\n                color: 'rgba(0,0,0,.1)'\n              }]);\n            }\n            // }\n          },\n          label: {\n            show: true,\n            position: 'top',\n            color: '#fff',\n            fontWeight: 'bold',\n            fontSize: 24\n          }\n        }]\n      };\n      if (option && typeof option == 'object') {\n        this.case.setOption(option);\n      }\n      window.onresize = this.case.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getSectorVolumeList", "name", "data", "case", "xData", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "i", "push", "deptName", "value", "total", "setOption", "max", "temp", "option", "grid", "left", "right", "bottom", "top", "xAxis", "type", "axisLine", "show", "axisLabel", "color", "fontSize", "formatter", "params", "rowMax", "overValue", "length", "axisTick", "yAxis", "splitLine", "dataZoom", "start", "end", "series", "symbol", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "v", "graphic", "LinearGradient", "offset", "label", "position", "fontWeight", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/CaseDistribution.vue"], "sourcesContent": ["<template>\r\n    <div ref=\"case\" id=\"case\">\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport { getSectorVolumeList } from '@/api/index.js'\r\n\r\nexport default {\r\n    name: 'CaseDistribution',\r\n\r\n    data() {\r\n        return {\r\n            case: null,\r\n            xData: [],\r\n            data: []\r\n        };\r\n    },\r\n    beforeDestroy() {\r\n        if (this.case) {\r\n            this.case.clear()\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.myChart()\r\n        });\r\n    },\r\n\r\n    methods: {\r\n        myChart() {\r\n            new Promise((resolve) => {\r\n                resolve()\r\n            }).then(() => {\r\n                if (this.case != null && this.case != undefined && this.case != '') {\r\n                    this.case.dispose()\r\n                };\r\n                this.case = new echarts.init(this.$refs.case)\r\n\r\n\r\n                getSectorVolumeList().then(res => {\r\n                    let dataList = res.data.data;\r\n                    for (let i of dataList) {\r\n\r\n                        this.data.push({\r\n                            name: i.deptName,\r\n                            value: i.total * 1\r\n                        })\r\n                        this.xData.push(i.deptName)\r\n                    }\r\n                    this.setOption()\r\n                })\r\n            })\r\n        },\r\n        setOption() {\r\n            let max;\r\n            let temp;\r\n            for (var i of this.data) {\r\n                max = i.value;\r\n                if (i.value > max) {\r\n                    max = i.value\r\n                }\r\n            }\r\n            const option = {\r\n                grid: {\r\n                    left: '5%',\r\n                    right: '5%',\r\n                    bottom: '45%',\r\n                    top: '15%',\r\n                    // containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'category',\r\n                    data: this.xData,\r\n\r\n                    axisLine: {\r\n                        show: false,\r\n\r\n                    },\r\n                    axisLabel: {\r\n                        color: \"#fff\",\r\n                        fontSize: 24,\r\n                        formatter: (params) => {\r\n                            let rowMax = 4;\r\n                            let overValue = ''\r\n                            for (let i = 0; i < params.length; i++) {\r\n                                if (i % rowMax == 0 && i != 0) {\r\n                                    overValue += params[i] + '\\n'\r\n                                }\r\n                                else {\r\n                                    overValue += params[i]\r\n                                }\r\n                            }\r\n                            return overValue;\r\n                        }\r\n                    },\r\n                    axisTick: {\r\n                        show: false\r\n                    },\r\n                },\r\n                yAxis: {\r\n                    type: 'value',\r\n                    splitLine: {\r\n                        show: false\r\n                    },\r\n                    axisLabel: {\r\n                        show: false\r\n                    },\r\n                    axisTick: {\r\n                        show: false\r\n                    },\r\n                    axisLine: {\r\n                        show: false,\r\n\r\n                    },\r\n                },\r\n                dataZoom: [\r\n                    {\r\n                        type: 'inside',\r\n                        start: 0,\r\n                        end: 30\r\n                    }, {}\r\n                ],\r\n                series: [\r\n                    {\r\n                        data: this.data,\r\n                        type: 'pictorialBar', // 象形柱图\r\n                        symbol: 'triangle', // 三角形\r\n                        barWidth: '100%',\r\n                        itemStyle: {\r\n                            // normal: {\r\n                            color: (v) => {\r\n                                let color = ['rgba(248, 219, 139,1)', 'rgba(248, 219, 139, .3)']\r\n                                if (v.value == max) {\r\n                                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                        {\r\n                                            offset: 0, color: color[0]\r\n                                        }, {\r\n                                            offset: .4, color: color[1]\r\n                                        }, {\r\n                                            offset: 1, color: 'rgba(0,0,0,.1)'\r\n                                        }\r\n                                    ])\r\n                                }\r\n                                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                    {\r\n                                        offset: 0, color: 'rgba(3, 251, 226, 1)'\r\n                                    }, {\r\n                                        offset: .4, color: 'rgba(3, 251, 226, .3)'\r\n                                    }, {\r\n                                        offset: 1, color: 'rgba(0,0,0,.1)'\r\n                                    }\r\n                                ])\r\n                            },\r\n                            // }\r\n                        },\r\n\r\n\r\n                        label: {\r\n                            show: true,\r\n                            position: 'top',\r\n                            color: '#fff',\r\n                            fontWeight: 'bold',\r\n                            fontSize: 24\r\n\r\n                        },\r\n                    }\r\n                ]\r\n\r\n            };\r\n            if (option && typeof option == 'object') {\r\n                this.case.setOption(option);\r\n\r\n            }\r\n\r\n            window.onresize = this.case.resize\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#case {\r\n    width: 94%;\r\n    height: 300px;\r\n}\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,mBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,IAAA;MACAC,KAAA;MACAF,IAAA;IACA;EACA;EACAG,cAAA;IACA,SAAAF,IAAA;MACA,KAAAA,IAAA,CAAAG,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,KAAAC,OAAA;IACA;EACA;EAEAC,OAAA;IACAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAV,IAAA,iBAAAA,IAAA,IAAAW,SAAA,SAAAX,IAAA;UACA,KAAAA,IAAA,CAAAY,OAAA;QACA;QAAA;QACA,KAAAZ,IAAA,OAAAJ,OAAA,CAAAiB,IAAA,MAAAC,KAAA,CAAAd,IAAA;QAGAH,mBAAA,GAAAa,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAhB,IAAA,CAAAA,IAAA;UACA,SAAAkB,CAAA,IAAAD,QAAA;YAEA,KAAAjB,IAAA,CAAAmB,IAAA;cACApB,IAAA,EAAAmB,CAAA,CAAAE,QAAA;cACAC,KAAA,EAAAH,CAAA,CAAAI,KAAA;YACA;YACA,KAAApB,KAAA,CAAAiB,IAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;UACA,KAAAG,SAAA;QACA;MACA;IACA;IACAA,UAAA;MACA,IAAAC,GAAA;MACA,IAAAC,IAAA;MACA,SAAAP,CAAA,SAAAlB,IAAA;QACAwB,GAAA,GAAAN,CAAA,CAAAG,KAAA;QACA,IAAAH,CAAA,CAAAG,KAAA,GAAAG,GAAA;UACAA,GAAA,GAAAN,CAAA,CAAAG,KAAA;QACA;MACA;MACA,MAAAK,MAAA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACA;QACA;QACAC,KAAA;UACAC,IAAA;UACAjC,IAAA,OAAAE,KAAA;UAEAgC,QAAA;YACAC,IAAA;UAEA;UACAC,SAAA;YACAC,KAAA;YACAC,QAAA;YACAC,SAAA,EAAAC,MAAA;cACA,IAAAC,MAAA;cACA,IAAAC,SAAA;cACA,SAAAxB,CAAA,MAAAA,CAAA,GAAAsB,MAAA,CAAAG,MAAA,EAAAzB,CAAA;gBACA,IAAAA,CAAA,GAAAuB,MAAA,SAAAvB,CAAA;kBACAwB,SAAA,IAAAF,MAAA,CAAAtB,CAAA;gBACA,OACA;kBACAwB,SAAA,IAAAF,MAAA,CAAAtB,CAAA;gBACA;cACA;cACA,OAAAwB,SAAA;YACA;UACA;UACAE,QAAA;YACAT,IAAA;UACA;QACA;QACAU,KAAA;UACAZ,IAAA;UACAa,SAAA;YACAX,IAAA;UACA;UACAC,SAAA;YACAD,IAAA;UACA;UACAS,QAAA;YACAT,IAAA;UACA;UACAD,QAAA;YACAC,IAAA;UAEA;QACA;QACAY,QAAA,GACA;UACAd,IAAA;UACAe,KAAA;UACAC,GAAA;QACA,MACA;QACAC,MAAA,GACA;UACAlD,IAAA,OAAAA,IAAA;UACAiC,IAAA;UAAA;UACAkB,MAAA;UAAA;UACAC,QAAA;UACAC,SAAA;YACA;YACAhB,KAAA,EAAAiB,CAAA;cACA,IAAAjB,KAAA;cACA,IAAAiB,CAAA,CAAAjC,KAAA,IAAAG,GAAA;gBACA,WAAA3B,OAAA,CAAA0D,OAAA,CAAAC,cAAA,cACA;kBACAC,MAAA;kBAAApB,KAAA,EAAAA,KAAA;gBACA;kBACAoB,MAAA;kBAAApB,KAAA,EAAAA,KAAA;gBACA;kBACAoB,MAAA;kBAAApB,KAAA;gBACA,EACA;cACA;cACA,WAAAxC,OAAA,CAAA0D,OAAA,CAAAC,cAAA,cACA;gBACAC,MAAA;gBAAApB,KAAA;cACA;gBACAoB,MAAA;gBAAApB,KAAA;cACA;gBACAoB,MAAA;gBAAApB,KAAA;cACA,EACA;YACA;YACA;UACA;UAGAqB,KAAA;YACAvB,IAAA;YACAwB,QAAA;YACAtB,KAAA;YACAuB,UAAA;YACAtB,QAAA;UAEA;QACA;MAGA;MACA,IAAAZ,MAAA,WAAAA,MAAA;QACA,KAAAzB,IAAA,CAAAsB,SAAA,CAAAG,MAAA;MAEA;MAEAmC,MAAA,CAAAC,QAAA,QAAA7D,IAAA,CAAA8D,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}