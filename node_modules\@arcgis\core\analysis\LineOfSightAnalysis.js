/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import e from"./Analysis.js";import r from"./LineOfSightAnalysisObserver.js";import o from"./LineOfSightAnalysisTarget.js";import s from"../core/Collection.js";import{castForReferenceSetter as i,referenceSetter as n}from"../core/collectionUtils.js";import{isNone as p,isSome as a}from"../core/maybe.js";import{property as l}from"../core/accessorSupport/decorators/property.js";import"../core/arrayUtils.js";import"../core/has.js";import"../core/accessorSupport/ensureType.js";import{subclass as c}from"../core/accessorSupport/decorators/subclass.js";import m from"../geometry/Extent.js";import{fromValues as y,expand as g}from"../geometry/support/aaBoundingRect.js";const u=s.ofType(o);let f=class extends e{constructor(t){super(t),this.type="line-of-sight",this.observer=null,this.nonEditableMessage="Assign an observer location to the analysis to allow editing."}get targets(){return this._get("targets")||new u}set targets(t){this._set("targets",n(t,this.targets,u))}get extent(){if(p(this.observer)||p(this.observer.position))return null;const t=this.observer.position,e=y(t.x,t.y,t.x,t.y);for(const r of this.targets)a(r.position)&&g(e,r.position,e);return m.fromBounds(e,t.spatialReference)}get requiredPropertiesForEditing(){return[this.observer]}};t([l({type:["line-of-sight"]})],f.prototype,"type",void 0),t([l({type:r})],f.prototype,"observer",void 0),t([l({cast:i,type:u,nonNullable:!0})],f.prototype,"targets",null),t([l({value:null})],f.prototype,"extent",null),t([l({readOnly:!0})],f.prototype,"requiredPropertiesForEditing",null),t([l({readOnly:!0})],f.prototype,"nonEditableMessage",void 0),f=t([c("esri.analysis.LineOfSightAnalysis")],f);const h=f;export{h as default};
