{"ast": null, "code": "import { getVideoListInfo, getVideoUrl } from '@/api/hzVideo.js';\nimport hlsItem from './hlsItem.vue';\nimport Hls from \"hls.js\";\nimport \"video.js/dist/video-js.css\";\nimport { mapState } from 'vuex';\nexport default {\n  name: 'hlsCreated',\n  components: {\n    hlsItem\n  },\n  data() {\n    return {\n      hlsVideo: null,\n      url: null,\n      count: 0,\n      playerList: [],\n      totalNum: 0,\n      totalVideoList: [],\n      currentActiveIndex: 0,\n      gridTemplateColumns: 'repeat(1,1fr)',\n      gridTemplateRows: 'repeat(1,1fr)',\n      ueTest: true\n    };\n  },\n  props: {\n    areaTitInfo: {\n      type: Object,\n      default: () => {}\n    },\n    winPlayer: {\n      type: Number,\n      default: 1\n    },\n    isAction: {\n      type: Boolean,\n      default: false\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    idName: {\n      type: String,\n      default: \"document\"\n    }\n  },\n  created() {},\n  updated() {},\n  computed: {\n    isActionWin() {\n      return this.isAction;\n    },\n    ...mapState('action', ['videoPlayerList'])\n  },\n  watch: {\n    // 获取监控名称\n    areaTitInfo: {\n      handler(nv, old) {\n        if (JSON.stringify(nv) != JSON.stringify(old)) {\n          if (nv) {\n            if (nv?.ape_id) {\n              this.getVideoUrl(nv.ape_id, 3);\n            } else if (nv?.channalCode) {\n              this.getVideoUrl(nv.channalCode, 3);\n            }\n            if (!this.isActionWin) {\n              this.count++;\n              this.count > this.winPlayer ? this.count = 1 : this.count;\n            }\n          }\n        }\n      },\n      immediate: true\n    },\n    winPlayer: {\n      handler(nv, old) {\n        if (this.winPlayer > 1) {\n          // 拿字符串中的数字\n          let num = this.height.match(/\\d+/g)[0];\n          this.currentWinHeight = num / 3.5 + 'px';\n          // this.getDeviceListInfo()\n        } else {\n          this.currentWinHeight = this.height;\n        }\n        if (nv != old) this.getVideoSize();\n      },\n      immediate: true\n    }\n  },\n  beforeDestroy() {\n    if (this.playerList.length > 0) {\n      this.playerList.forEach((item, index) => {\n        item.destroy();\n      });\n      this.playerList = [];\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.getVideoSize();\n    });\n  },\n  methods: {\n    shiftPlayerList() {\n      if (this.playerList.length > this.winPlayer) {\n        this.playerList[this.count - 1].destroy();\n        this.playerList.shift();\n      }\n    },\n    // 获取设备列表\n    async getDeviceListInfo(nv = 43) {\n      let params = {\n        page_num: nv\n      };\n      await getVideoListInfo(params).then(res => {\n        if (res.data.message == \"success\") {\n          if (this.winPlayer > 0) {\n            this.$emit('selectAreaPlayer', res.data.data);\n            let params = {\n              type: \"区域监控\",\n              data: res.data.data.data,\n              flag: true\n            };\n            this.$eventBus.$emit('senTxtToUe', params);\n            this.totalNum = res.data.data.paging.total_num;\n          }\n        }\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n    // 获取设备视频流\n    async getVideoUrl(channel_code, stream_mode) {\n      await getVideoUrl(channel_code, stream_mode).then(res => {\n        if (res.data.error_code = '0000000000') {\n          this.url = res.data.data;\n          this.initVideo(res.data.data, this.areaTitInfo.channalName);\n        }\n      });\n    },\n    // 创建视频\n    initVideo(url, name) {\n      console.log('111', this.count);\n      let id;\n      if (this.isActionWin) {\n        this.currentActiveIndex++;\n        id = 'dplayer' + this.idName + this.currentActiveIndex;\n      } else {\n        id = 'dplayer' + this.idName + this.count;\n      }\n      let dom = this.$refs[id][0];\n      if (dom) {\n        dom.createVideo(url, name);\n      }\n    },\n    //获取播放器\n    getPlayerList(val, count) {\n      val.uid = count;\n      val.urlPlayer = val.url;\n      if (this.isActionWin) {\n        this.closeVideo(count);\n        this.playerList[count] = val;\n        this.$emit('sendIsAction', false);\n      } else {\n        this.shiftPlayerList();\n        this.playerList[count] = val;\n      }\n    },\n    // 点击指定的监控\n    getVideoOne(val) {\n      let {\n        uid\n      } = val;\n      this.currentActiveIndex = uid;\n      this.$emit('sendIsAction', true);\n    },\n    // 关闭视频\n    closeVideo(val) {\n      this.playerList[val].destroy();\n    },\n    // 播放视频\n    openVideo(val) {\n      this.playerList[val].play();\n      console.log('111');\n    },\n    // 获取监控尺寸\n    getVideoSize() {\n      let videoShowMany = document.querySelector('.videoShowMany');\n      let col;\n      let row;\n      if (this.winPlayer == 4) {\n        col = 2;\n        row = this.winPlayer / 2;\n      } else if (this.winPlayer == 9) {\n        col = 3;\n        row = this.winPlayer / 3;\n      } else if (this.winPlayer == 6) {\n        col = 3;\n        row = this.winPlayer / 3;\n      } else {\n        col = this.winPlayer;\n        row = 1;\n      }\n      this.gridTemplateColumns = `repeat(${col},1fr)`;\n      this.gridTemplateRows = `repeat(${row},1fr)`;\n\n      // console.log('style=====',videoShowMany.style.gridTemplateColumns)\n    }\n  }\n};", "map": {"version": 3, "names": ["getVideoListInfo", "getVideoUrl", "hlsItem", "Hls", "mapState", "name", "components", "data", "hlsVideo", "url", "count", "playerList", "totalNum", "totalVideoList", "currentActiveIndex", "gridTemplateColumns", "gridTemplateRows", "ueTest", "props", "areaTitInfo", "type", "Object", "default", "winPlayer", "Number", "isAction", "Boolean", "height", "String", "idName", "created", "updated", "computed", "isActionWin", "watch", "handler", "nv", "old", "JSON", "stringify", "ape_id", "channalCode", "immediate", "num", "match", "currentWinHeight", "getVideoSize", "<PERSON><PERSON><PERSON><PERSON>", "length", "for<PERSON>ach", "item", "index", "destroy", "mounted", "$nextTick", "methods", "shiftPlayerList", "shift", "getDeviceListInfo", "params", "page_num", "then", "res", "message", "$emit", "flag", "$eventBus", "paging", "total_num", "catch", "err", "console", "log", "channel_code", "stream_mode", "error_code", "initVideo", "channal<PERSON>ame", "id", "dom", "$refs", "createVideo", "getPlayerList", "val", "uid", "urlPlayer", "closeVideo", "getVideoOne", "openVideo", "play", "videoShowMany", "document", "querySelector", "col", "row"], "sources": ["src/components/hlvJsVideo/hlsCreated.vue"], "sourcesContent": ["<template>\r\n    <div class=\"videoShowMany\"\r\n        :style=\"{ height: height, gridTemplateColumns: gridTemplateColumns, gridTemplateRows: gridTemplateRows }\">\r\n        <div class=\"videos\" v-for=\"( i, index ) of  this.winPlayer \">\r\n            <hlsItem :ref=\"'dplayer' + idName + i\" :id=\"'dplayer' + idName + i\" :currentCount='i-1'\r\n                :playerList=\"playerList\" @sendPlayerList=getPlayerList :height=\"currentWinHeight\">\r\n            </hlsItem>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { getVideoListInfo, getVideoUrl } from '@/api/hzVideo.js';\r\nimport hlsItem from './hlsItem.vue';\r\nimport Hls from \"hls.js\";\r\nimport \"video.js/dist/video-js.css\";\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'hlsCreated',\r\n    components: {\r\n        hlsItem\r\n    },\r\n    data() {\r\n        return {\r\n            hlsVideo: null,\r\n            url: null,\r\n            count: 0,\r\n            playerList: [],\r\n            totalNum: 0,\r\n            totalVideoList: [],\r\n            currentActiveIndex: 0,\r\n            gridTemplateColumns: 'repeat(1,1fr)',\r\n            gridTemplateRows: 'repeat(1,1fr)',\r\n            ueTest: true\r\n        }\r\n    },\r\n    props: {\r\n        areaTitInfo: {\r\n            type: Object,\r\n            default: () => { }\r\n        },\r\n        winPlayer: {\r\n            type: Number,\r\n            default: 1\r\n        },\r\n        isAction: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        height: {\r\n            type: String,\r\n            default: '400px'\r\n        },\r\n        idName: {\r\n            type: String,\r\n            default: \"document\"\r\n        }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    updated() {\r\n\r\n    },\r\n    computed: {\r\n        isActionWin() {\r\n            return this.isAction;\r\n        },\r\n        ...mapState('action', ['videoPlayerList']),\r\n    },\r\n    watch: {\r\n        // 获取监控名称\r\n        areaTitInfo: {\r\n            handler(nv, old) {\r\n               if(JSON.stringify(nv)!=JSON.stringify(old)){\r\n                if (nv) {\r\n                    if (nv?.ape_id) {\r\n                        this.getVideoUrl(nv.ape_id, 3)\r\n                    } else if (nv?.channalCode) {\r\n                        this.getVideoUrl(nv.channalCode, 3)\r\n\r\n                    }\r\n                    if (!this.isActionWin) {\r\n                        this.count++;\r\n                        this.count > this.winPlayer ? this.count = 1 : this.count;\r\n                    }\r\n                }\r\n               } \r\n            },\r\n            immediate: true\r\n\r\n        },\r\n        winPlayer: {\r\n            handler(nv, old) {\r\n                if (this.winPlayer > 1) {\r\n                    // 拿字符串中的数字\r\n                    let num = this.height.match(/\\d+/g)[0];\r\n                    this.currentWinHeight = num / 3.5 + 'px'\r\n                    // this.getDeviceListInfo()\r\n                } else {\r\n                    this.currentWinHeight = this.height\r\n                }\r\n                if (nv != old) this.getVideoSize()\r\n            },\r\n            immediate: true\r\n        }\r\n        \r\n    },\r\n    beforeDestroy() {\r\n        if (this.playerList.length > 0) {\r\n            this.playerList.forEach((item, index) => {\r\n\r\n                item.destroy()\r\n            })\r\n            this.playerList = []\r\n        }\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.getVideoSize()\r\n        })\r\n\r\n    },\r\n    methods: {\r\n\r\n        shiftPlayerList() {\r\n            if (this.playerList.length > this.winPlayer) {\r\n                this.playerList[this.count - 1].destroy()\r\n                this.playerList.shift();\r\n            }\r\n        },\r\n        // 获取设备列表\r\n        async getDeviceListInfo(nv = 43) {\r\n            let params = {\r\n                page_num: nv\r\n            }\r\n            await getVideoListInfo(params).then(res => {\r\n                if (res.data.message == \"success\") {\r\n                    if (this.winPlayer > 0) {\r\n                        this.$emit('selectAreaPlayer', res.data.data)\r\n                        let params = {\r\n                            type: \"区域监控\",\r\n                            data: res.data.data.data,\r\n                            flag: true\r\n                        }\r\n                        this.$eventBus.$emit('senTxtToUe', params)\r\n                        this.totalNum = res.data.data.paging.total_num\r\n\r\n                    }\r\n\r\n                }\r\n\r\n            }).catch(err => {\r\n                console.log(err);\r\n            })\r\n        },\r\n        // 获取设备视频流\r\n        async getVideoUrl(channel_code, stream_mode) {\r\n            await getVideoUrl(channel_code, stream_mode).then(res => {\r\n                if (res.data.error_code = '0000000000') {\r\n                    this.url = res.data.data\r\n                    this.initVideo(res.data.data, this.areaTitInfo.channalName)\r\n                }\r\n            })\r\n        },\r\n\r\n        // 创建视频\r\n        initVideo(url, name) {\r\n            console.log('111',this.count)\r\n            let id;\r\n            if (this.isActionWin) {\r\n                this.currentActiveIndex++;\r\n                id = 'dplayer' + this.idName + this.currentActiveIndex\r\n            } else {\r\n                id = 'dplayer' + this.idName + this.count\r\n            }\r\n            let dom = this.$refs[id][0];\r\n            if (dom) {\r\n                dom.createVideo(url, name)\r\n            }\r\n\r\n        },\r\n        //获取播放器\r\n        getPlayerList(val, count) {\r\n            val.uid = count;\r\n            val.urlPlayer = val.url;\r\n            if (this.isActionWin) {\r\n                this.closeVideo(count)\r\n                this.playerList[count] = val\r\n                this.$emit('sendIsAction', false)\r\n            } else {\r\n                this.shiftPlayerList()\r\n                this.playerList[count] = val\r\n            }\r\n            \r\n            \r\n        },\r\n\r\n        // 点击指定的监控\r\n        getVideoOne(val) {\r\n            let { uid } = val;\r\n            this.currentActiveIndex = uid;\r\n            this.$emit('sendIsAction', true)\r\n        },\r\n        // 关闭视频\r\n        closeVideo(val) {\r\n            this.playerList[val].destroy()\r\n        },\r\n        // 播放视频\r\n        openVideo(val) {\r\n            this.playerList[val].play()\r\n            console.log('111')\r\n        },\r\n        // 获取监控尺寸\r\n        getVideoSize() {\r\n            let videoShowMany = document.querySelector('.videoShowMany');\r\n\r\n            let col;\r\n            let row;\r\n            if (this.winPlayer == 4) {\r\n                col = 2;\r\n                row = this.winPlayer / 2\r\n            } else if (this.winPlayer == 9) {\r\n                col = 3;\r\n                row = this.winPlayer / 3\r\n            } else if (this.winPlayer == 6) {\r\n                col = 3;\r\n                row = this.winPlayer / 3\r\n            } else {\r\n                col = this.winPlayer;\r\n                row = 1;\r\n            }\r\n\r\n            this.gridTemplateColumns = `repeat(${col},1fr)`;\r\n            this.gridTemplateRows = `repeat(${row},1fr)`;\r\n\r\n            // console.log('style=====',videoShowMany.style.gridTemplateColumns)\r\n\r\n        },\r\n\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.videoShowMany {\r\n    width: 100% !important;\r\n\r\n    display: grid;\r\n    grid-gap: 15px;\r\n\r\n\r\n    .videos {\r\n        position: relative;\r\n\r\n        video {\r\n            // object-fit: cover;\r\n        }\r\n\r\n        .errorTxt {\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 50%;\r\n            transform: translate(-50%, -50%);\r\n            font-size: 15px;\r\n            z-index: 99;\r\n            color: #f3f2f2;\r\n\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAaA,SAAAA,gBAAA,EAAAC,WAAA;AACA,OAAAC,OAAA;AACA,OAAAC,GAAA;AACA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAJ;EACA;EACAK,KAAA;IACA;MACAC,QAAA;MACAC,GAAA;MACAC,KAAA;MACAC,UAAA;MACAC,QAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;IACAK,MAAA;MACAP,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;IACAO,MAAA;MACAT,IAAA,EAAAQ,MAAA;MACAN,OAAA;IACA;EACA;EACAQ,QAAA,GAEA;EACAC,QAAA,GAEA;EACAC,QAAA;IACAC,YAAA;MACA,YAAAR,QAAA;IACA;IACA,GAAArB,QAAA;EACA;EACA8B,KAAA;IACA;IACAf,WAAA;MACAgB,QAAAC,EAAA,EAAAC,GAAA;QACA,IAAAC,IAAA,CAAAC,SAAA,CAAAH,EAAA,KAAAE,IAAA,CAAAC,SAAA,CAAAF,GAAA;UACA,IAAAD,EAAA;YACA,IAAAA,EAAA,EAAAI,MAAA;cACA,KAAAvC,WAAA,CAAAmC,EAAA,CAAAI,MAAA;YACA,WAAAJ,EAAA,EAAAK,WAAA;cACA,KAAAxC,WAAA,CAAAmC,EAAA,CAAAK,WAAA;YAEA;YACA,UAAAR,WAAA;cACA,KAAAvB,KAAA;cACA,KAAAA,KAAA,QAAAa,SAAA,QAAAb,KAAA,YAAAA,KAAA;YACA;UACA;QACA;MACA;MACAgC,SAAA;IAEA;IACAnB,SAAA;MACAY,QAAAC,EAAA,EAAAC,GAAA;QACA,SAAAd,SAAA;UACA;UACA,IAAAoB,GAAA,QAAAhB,MAAA,CAAAiB,KAAA;UACA,KAAAC,gBAAA,GAAAF,GAAA;UACA;QACA;UACA,KAAAE,gBAAA,QAAAlB,MAAA;QACA;QACA,IAAAS,EAAA,IAAAC,GAAA,OAAAS,YAAA;MACA;MACAJ,SAAA;IACA;EAEA;EACAK,cAAA;IACA,SAAApC,UAAA,CAAAqC,MAAA;MACA,KAAArC,UAAA,CAAAsC,OAAA,EAAAC,IAAA,EAAAC,KAAA;QAEAD,IAAA,CAAAE,OAAA;MACA;MACA,KAAAzC,UAAA;IACA;EACA;EACA0C,QAAA;IACA,KAAAC,SAAA;MACA,KAAAR,YAAA;IACA;EAEA;EACAS,OAAA;IAEAC,gBAAA;MACA,SAAA7C,UAAA,CAAAqC,MAAA,QAAAzB,SAAA;QACA,KAAAZ,UAAA,MAAAD,KAAA,MAAA0C,OAAA;QACA,KAAAzC,UAAA,CAAA8C,KAAA;MACA;IACA;IACA;IACA,MAAAC,kBAAAtB,EAAA;MACA,IAAAuB,MAAA;QACAC,QAAA,EAAAxB;MACA;MACA,MAAApC,gBAAA,CAAA2D,MAAA,EAAAE,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAvD,IAAA,CAAAwD,OAAA;UACA,SAAAxC,SAAA;YACA,KAAAyC,KAAA,qBAAAF,GAAA,CAAAvD,IAAA,CAAAA,IAAA;YACA,IAAAoD,MAAA;cACAvC,IAAA;cACAb,IAAA,EAAAuD,GAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAA,IAAA;cACA0D,IAAA;YACA;YACA,KAAAC,SAAA,CAAAF,KAAA,eAAAL,MAAA;YACA,KAAA/C,QAAA,GAAAkD,GAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAA4D,MAAA,CAAAC,SAAA;UAEA;QAEA;MAEA,GAAAC,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;IACA;IACA,MAAArE,YAAAwE,YAAA,EAAAC,WAAA;MACA,MAAAzE,WAAA,CAAAwE,YAAA,EAAAC,WAAA,EAAAb,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAvD,IAAA,CAAAoE,UAAA;UACA,KAAAlE,GAAA,GAAAqD,GAAA,CAAAvD,IAAA,CAAAA,IAAA;UACA,KAAAqE,SAAA,CAAAd,GAAA,CAAAvD,IAAA,CAAAA,IAAA,OAAAY,WAAA,CAAA0D,WAAA;QACA;MACA;IACA;IAEA;IACAD,UAAAnE,GAAA,EAAAJ,IAAA;MACAkE,OAAA,CAAAC,GAAA,aAAA9D,KAAA;MACA,IAAAoE,EAAA;MACA,SAAA7C,WAAA;QACA,KAAAnB,kBAAA;QACAgE,EAAA,oBAAAjD,MAAA,QAAAf,kBAAA;MACA;QACAgE,EAAA,oBAAAjD,MAAA,QAAAnB,KAAA;MACA;MACA,IAAAqE,GAAA,QAAAC,KAAA,CAAAF,EAAA;MACA,IAAAC,GAAA;QACAA,GAAA,CAAAE,WAAA,CAAAxE,GAAA,EAAAJ,IAAA;MACA;IAEA;IACA;IACA6E,cAAAC,GAAA,EAAAzE,KAAA;MACAyE,GAAA,CAAAC,GAAA,GAAA1E,KAAA;MACAyE,GAAA,CAAAE,SAAA,GAAAF,GAAA,CAAA1E,GAAA;MACA,SAAAwB,WAAA;QACA,KAAAqD,UAAA,CAAA5E,KAAA;QACA,KAAAC,UAAA,CAAAD,KAAA,IAAAyE,GAAA;QACA,KAAAnB,KAAA;MACA;QACA,KAAAR,eAAA;QACA,KAAA7C,UAAA,CAAAD,KAAA,IAAAyE,GAAA;MACA;IAGA;IAEA;IACAI,YAAAJ,GAAA;MACA;QAAAC;MAAA,IAAAD,GAAA;MACA,KAAArE,kBAAA,GAAAsE,GAAA;MACA,KAAApB,KAAA;IACA;IACA;IACAsB,WAAAH,GAAA;MACA,KAAAxE,UAAA,CAAAwE,GAAA,EAAA/B,OAAA;IACA;IACA;IACAoC,UAAAL,GAAA;MACA,KAAAxE,UAAA,CAAAwE,GAAA,EAAAM,IAAA;MACAlB,OAAA,CAAAC,GAAA;IACA;IACA;IACA1B,aAAA;MACA,IAAA4C,aAAA,GAAAC,QAAA,CAAAC,aAAA;MAEA,IAAAC,GAAA;MACA,IAAAC,GAAA;MACA,SAAAvE,SAAA;QACAsE,GAAA;QACAC,GAAA,QAAAvE,SAAA;MACA,gBAAAA,SAAA;QACAsE,GAAA;QACAC,GAAA,QAAAvE,SAAA;MACA,gBAAAA,SAAA;QACAsE,GAAA;QACAC,GAAA,QAAAvE,SAAA;MACA;QACAsE,GAAA,QAAAtE,SAAA;QACAuE,GAAA;MACA;MAEA,KAAA/E,mBAAA,aAAA8E,GAAA;MACA,KAAA7E,gBAAA,aAAA8E,GAAA;;MAEA;IAEA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}