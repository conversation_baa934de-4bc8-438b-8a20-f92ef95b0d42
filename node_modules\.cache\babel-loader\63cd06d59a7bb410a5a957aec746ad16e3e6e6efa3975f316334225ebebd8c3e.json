{"ast": null, "code": "export default {\n  name: 'topTitle',\n  data() {\n    return {\n      timer: '',\n      date: '',\n      week: '',\n      currentTime: ''\n      // isScreenShow:true\n    };\n  },\n  computed: {\n    isScreenShow() {\n      // 获取在action模块中的state\n      return this.$store.state.isScreenShow;\n    }\n  },\n  mounted() {\n    this.TodaysDat();\n    this.$nextTick(() => {\n      this.$store.commit('action/getIsScreenShow', true);\n    });\n  },\n  destroyed() {\n    clearInterval(this.timer);\n  },\n  methods: {\n    TodaysDat() {\n      var _this = this;\n      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');\n      var now = new Date();\n      var day = now.getDay();\n      _this.week = weeks[day]; // 设置星期几\n      _this.date = new Date().getFullYear() + ' - ' + _this.appendZero(new Date().getMonth() + 1) + ' - ' + _this.appendZero(new Date().getDate()); // 设置年月日\n      _this.timer = setInterval(function () {\n        _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes()); //修改数据date\n      }, 1000);\n    },\n    appendZero(num) {\n      if (num < 10) {\n        return '0' + num;\n      } else {\n        return num;\n      }\n    },\n    // 点击返回主页\n    returnMainBtn() {\n      this.$router.push('/');\n    },\n    isScreenFun() {\n      this.$store.commit('action/getIsScreenShow', null);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "timer", "date", "week", "currentTime", "computed", "isScreenShow", "$store", "state", "mounted", "TodaysDat", "$nextTick", "commit", "destroyed", "clearInterval", "methods", "_this", "weeks", "Array", "now", "Date", "day", "getDay", "getFullYear", "appendZero", "getMonth", "getDate", "setInterval", "getHours", "getMinutes", "num", "returnMainBtn", "$router", "push", "isScreenFun"], "sources": ["src/views/chargingPilesScreen/header/topTitle.vue"], "sourcesContent": ["<template>\r\n    <!-- 头部 -->\r\n    <div class=\"top\" ref=\"top\">\r\n        <div class=\"leftTop\">\r\n            <div class=\"weater\">\r\n                <div class=\"txt\">\r\n\r\n                    天气晴\r\n                </div>\r\n            </div> \r\n            <div class=\"rainNum fz28\">\r\n                温度 15-23℃ \r\n            </div> \r\n\r\n        </div>\r\n        <div class=\"titTop\">\r\n           <img src=\"@/assets/images/chongdian/fontSize.png\" alt=\"\" width=\"100%\">\r\n        </div>\r\n        <div class=\"rightTop\">\r\n            <div class=\"time\">\r\n                {{ week }} \r\n            </div>\r\n            <div class=\"data\">\r\n                {{ date }}\r\n            </div>\r\n            <div class=\"day\">\r\n                 {{ currentTime }}\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n    name: 'topTitle',\r\n\r\n    data() {\r\n        return {\r\n            timer: '',\r\n            date: '',\r\n            week: '',\r\n            currentTime: '',\r\n            // isScreenShow:true\r\n        };\r\n    },\r\n    computed: {\r\n        isScreenShow() {\r\n            // 获取在action模块中的state\r\n            return this.$store.state.isScreenShow;\r\n        }\r\n    },\r\n    mounted() {\r\n        this.TodaysDat();\r\n        this.$nextTick(()=>{\r\n            this.$store.commit('action/getIsScreenShow', true)\r\n\r\n        })\r\n    },\r\n    destroyed() {\r\n        clearInterval(this.timer)\r\n    },\r\n\r\n    methods: {\r\n        TodaysDat() {\r\n            var _this = this\r\n            var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')\r\n            var now = new Date()\r\n            var day = now.getDay()\r\n            _this.week = weeks[day] // 设置星期几\r\n            _this.date = new Date().getFullYear() + ' - ' + _this.appendZero(new Date().getMonth() + 1) + ' - ' + _this.appendZero(new Date().getDate()); // 设置年月日\r\n            _this.timer = setInterval(function () {\r\n                _this.currentTime = _this.appendZero(new Date().getHours()) + ':' + _this.appendZero(new Date().getMinutes())  //修改数据date\r\n            }, 1000);\r\n        },\r\n\r\n        appendZero(num) {\r\n            if (num < 10) {\r\n                return '0' + num\r\n            } else {\r\n                return num;\r\n            }\r\n        }, \r\n        // 点击返回主页\r\n        returnMainBtn() {\r\n            this.$router.push('/')\r\n        },\r\n        isScreenFun() {\r\n            this.$store.commit('action/getIsScreenShow', null)\r\n        },\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n \r\n.top {\r\n    pointer-events: stroke;\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 144px;\r\n    background: url(\"@/assets/images/ChongDianZhuang/titleBg.png\") no-repeat;\r\n    background-size: 100%;\r\n    font-size: 1.5rem;\r\n    box-sizing: border-box;\r\n    font-size: 28px;\r\n    z-index: 1;\r\n\r\n    /* border-image: linear-gradient(270deg, rgba(3, 207, 230, 0.16), rgba(13, 237, 255, 1), rgba(8, 199, 255, 0.16)) 1 1; */\r\n    .leftTop {\r\n        position: absolute;\r\n        left: 130px;\r\n        height: 105px;\r\n        display: flex;\r\n        align-items: flex-end;\r\n        .weater{\r\n            display: flex; \r\n            color: #D8D8D8;\r\n            img{\r\n                width: 60px;\r\n            }\r\n            .txt{\r\n                margin-top: 30px;\r\n            }\r\n        } \r\n        .temperature{\r\n            font-weight: 400;\r\n            font-size: 65px;\r\n            font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif; \r\n        } \r\n        .rainNum{\r\n            margin-left: 28px;\r\n            color: #D8D8D8;\r\n        }\r\n\r\n    }\r\n\r\n    .titTop {\r\n        margin: 0 auto;\r\n        img{\r\n            width: 1300px;\r\n        }\r\n    \r\n    }\r\n    .rightTop {\r\n        position: absolute;\r\n        right: 120px;\r\n\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: flex-end;\r\n\r\n        width: 400px;\r\n        height: 104px;\r\n        font-size: 28px;\r\n        /* border: 1px solid pink; */\r\n    }\r\n}\r\n</style>"], "mappings": "AAkCA;EACAA,IAAA;EAEAC,KAAA;IACA;MACAC,KAAA;MACAC,IAAA;MACAC,IAAA;MACAC,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAF,YAAA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,SAAA;MACA,KAAAJ,MAAA,CAAAK,MAAA;IAEA;EACA;EACAC,UAAA;IACAC,aAAA,MAAAb,KAAA;EACA;EAEAc,OAAA;IACAL,UAAA;MACA,IAAAM,KAAA;MACA,IAAAC,KAAA,OAAAC,KAAA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,IAAAC,GAAA,GAAAF,GAAA,CAAAG,MAAA;MACAN,KAAA,CAAAb,IAAA,GAAAc,KAAA,CAAAI,GAAA;MACAL,KAAA,CAAAd,IAAA,OAAAkB,IAAA,GAAAG,WAAA,aAAAP,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAK,QAAA,kBAAAT,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAM,OAAA;MACAV,KAAA,CAAAf,KAAA,GAAA0B,WAAA;QACAX,KAAA,CAAAZ,WAAA,GAAAY,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAQ,QAAA,YAAAZ,KAAA,CAAAQ,UAAA,KAAAJ,IAAA,GAAAS,UAAA;MACA;IACA;IAEAL,WAAAM,GAAA;MACA,IAAAA,GAAA;QACA,aAAAA,GAAA;MACA;QACA,OAAAA,GAAA;MACA;IACA;IACA;IACAC,cAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,YAAA;MACA,KAAA3B,MAAA,CAAAK,MAAA;IACA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}