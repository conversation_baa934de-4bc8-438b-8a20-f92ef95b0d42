/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"./FeatureSetIterator.js";import t from"./IdSet.js";import{FeatureServiceDatabaseType as r,layerGeometryEsriConstants as n,IdState as s,convertSquareUnitsToCode as i,convertLinearUnitsToCode as a,esriFieldToJson as l,layerGeometryEsriRestConstants as h}from"./shared.js";import u from"./cache.js";import{max as o,min as c,sum as d,variance as _,stdev as p,mean as f,distinct as g,count as m}from"./stats.js";import{create as y,resolve as b,reject as F,isPromiseLike as S}from"../../../core/promiseUtils.js";import{WhereClause as I}from"../../../core/sql/WhereClause.js";import{union as T,geodesicArea as P,planarArea as w,geodesicLength as D,planarLength as k}from"../../../geometry/geometryEngineAsync.js";import v from"../../../geometry/SpatialReference.js";import x from"../../../layers/support/FieldsIndex.js";class E{constructor(e){this.recentlyUsedQueries=null,this.featureSetQueryInterceptor=null,this._idstates=[],this._parent=null,this._wset=null,this._mainSetInUse=null,this._maxProcessing=200,this._maxQuery=500,this._totalCount=-1,this._databaseType=r.NotEvaluated,this._databaseTypeProbed=null,this.declaredRootClass="esri.arcade.featureset.support.FeatureSet",this._featureCache=[],this.types=null,this.fields=null,this.geometryType="",this.objectIdField="",this.globalIdField="",this.spatialReference=null,this.hasM=!1,this.hasZ=!1,this._transparent=!1,this.loaded=!1,this._loadPromise=null,this._fieldsIndex=null,e&&e.lrucache&&(this.recentlyUsedQueries=e.lrucache),e&&e.interceptor&&(this.featureSetQueryInterceptor=e.interceptor)}optimisePagingFeatureQueries(e){this._parent&&this._parent.optimisePagingFeatureQueries(e)}_hasMemorySource(){return!0}prop(e,t){return void 0===t?this[e]:(void 0!==this[e]&&(this[e]=t),this)}end(){return null!==this._parent&&!0===this._parent._transparent?this._parent.end():this._parent}_ensureLoaded(){return this.load()}load(){return null===this._loadPromise&&(this._loadPromise=y(((e,t)=>{if(!0===this._parent.loaded)return this._initialiseFeatureSet(),void e(this);this._parent.load().then((()=>{try{this._initialiseFeatureSet(),e(this)}catch(r){t(r)}}),t)}))),this._loadPromise}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new v({wkid:4326}),this.geometryType=n.point)}getField(e,t){let r;return(t=t||this.fields)&&(e=e.toLowerCase(),t.some((t=>(t&&t.name.toLowerCase()===e&&(r=t),!!r)))),r}getFieldsIndex(){return null===this._fieldsIndex&&(this._fieldsIndex=new x(this.fields)),this._fieldsIndex}_maxProcessingRate(){return null!==this._parent?Math.min(this._maxProcessing,this._parent._maxProcessingRate()):Math.min(this._maxProcessing,this._maxQueryRate())}_maxQueryRate(){return null!==this._parent?Math.max(this._maxQuery,this._parent._maxQueryRate()):this._maxQuery}_checkCancelled(e){if(null!==e&&e.aborted)throw new Error("Operation has been cancelled.")}nativeCapabilities(){return this._parent.nativeCapabilities()}_canDoAggregates(e,t,r,n,s){return null===this._parent?b(!1):this._parent._canDoAggregates(e,t,r,n,s)}_getAggregatePagesDataSourceDefinition(e,t,r,n,s,i,a){return null===this._parent?F(new Error("Should never be called")):this._parent._getAggregatePagesDataSourceDefinition(e,t,r,n,s,i,a)}_getAgregagtePhysicalPage(e,t,r){return null===this._parent?F(new Error("Should never be called")):this._parent._getAgregagtePhysicalPage(e,t,r)}databaseType(){if(this._databaseType===r.NotEvaluated){if(null!==u.applicationCache){const e=u.applicationCache.getDatabaseType(this._cacheableFeatureSetSourceKey());if(null!==e)return e}if(null!==this._databaseTypeProbed)return this._databaseTypeProbed;const e=[{thetype:r.SqlServer,testwhere:"(CAST( '2015-01-01' as DATETIME) = CAST( '2015-01-01' as DATETIME)) AND OBJECTID<0"},{thetype:r.Oracle,testwhere:"(TO_DATE('2003-11-18','YYYY-MM-DD') = TO_DATE('2003-11-18','YYYY-MM-DD')) AND OBJECTID<0"},{thetype:r.StandardisedNoInterval,testwhere:"(date '2015-01-01 10:10:10' = date '2015-01-01 10:10:10') AND OBJECTID<0"}];let t=y(((t,r)=>{this._getDatabaseTypeImpl(e,0).then((e=>{this._databaseType=e,t(this._databaseType)}),(e=>{r(e)}))}));return null!==u.applicationCache&&(u.applicationCache.setDatabaseType(this._cacheableFeatureSetSourceKey(),t),t=t.catch((e=>{throw u.applicationCache.clearDatabaseType(this._cacheableFeatureSetSourceKey()),e}))),this._databaseTypeProbed=t,this._databaseTypeProbed}return b(this._databaseType)}_cacheableFeatureSetSourceKey(){return"MUSTBESET"}_getDatabaseTypeImpl(e,t){return t>=e.length?b(r.StandardisedNoInterval):this._runDatabaseProbe(e[t].testwhere).then((r=>!0===r?e[t].thetype:this._getDatabaseTypeImpl(e,t+1)))}_runDatabaseProbe(e){return null!==this._parent?this._parent._runDatabaseProbe(e):F(new Error("Not Implemented"))}isTable(){return this._parent.isTable()}_featureFromCache(e){if(void 0!==this._featureCache[e])return this._featureCache[e]}_isInFeatureSet(e){return s.Unknown}_getSet(e){throw new Error("Not implemented in abstract class")}_getFeature(e,t,r){try{return this._checkCancelled(r),void 0!==this._featureFromCache(t)?b(this._featureFromCache(t)):this._getFeatures(e,t,this._maxProcessingRate(),r).then((()=>(this._checkCancelled(r),void 0!==this._featureFromCache(t)?this._featureFromCache(t):F(new Error("Feature Not Found")))))}catch(n){return F(n)}}_getFeatureBatch(e,r){try{this._checkCancelled(r);const n=new t([],e,!1,null),s=[];return this._getFeatures(n,-1,e.length,r).then((()=>{this._checkCancelled(r);for(const t of e)void 0!==this._featureFromCache(t)&&s.push(this._featureFromCache(t));return s}))}catch(n){return F(n)}}_getFeatures(e,t,r,n){return b("success")}_getFilteredSet(e,t,r,n,s){throw new Error("Not implemented in abstract class")}_refineSetBlock(e,t,r){try{if(!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQueryRate()))return this._expandPagedSet(e,this._maxQueryRate(),0,0,r).then((()=>this._refineSetBlock(e,t,r)));this._checkCancelled(r);const n=e._candidates.length;this._refineKnowns(e,t);let s=n-e._candidates.length;return 0===e._candidates.length||s>=t?b(e):this._refineIfParentKnown(e,t-s,r).then((()=>{if(this._checkCancelled(r),this._refineKnowns(e,t-s),s=n-e._candidates.length,s<t&&e._candidates.length>0){const n=t-s,i=this._prepareFetchAndRefineSet(e._candidates);return this._fetchAndRefineFeatures(i,i.length>n?n:e._candidates.length,r).then((()=>(this._checkCancelled(r),this._refineKnowns(e,t-s),e)))}return e}))}catch(n){return F(n)}}_fetchAndRefineFeatures(e,t,r){return null}_prepareFetchAndRefineSet(e){const t=[];for(let r=0;r<e.length;r++)this._isPhysicalFeature(e[r])&&t.push(e[r]);return t}_isPhysicalFeature(e){return null===this._parent||this._parent._isPhysicalFeature(e)}_refineKnowns(e,t){let r=0,n=null;const i=[];t=this._maxQueryRate();for(let a=0;a<e._candidates.length&&"GETPAGES"!==e._candidates[a];a++){let l=!1;const h=this._candidateIdTransform(e._candidates[a]);h!==e._candidates[a]&&(l=!0);const u=this._isInFeatureSet(h);if(u===s.InFeatureSet)!0===l?e._known.indexOf(h)<0&&(e._known.push(h),r+=1):(e._known.push(e._candidates[a]),r+=1),null===n?n={start:a,end:a}:n.end===a-1?n.end=a:(i.push(n),n={start:a,end:a});else if(u===s.NotInFeatureSet)null===n?n={start:a,end:a}:n.end===a-1?n.end=a:(i.push(n),n={start:a,end:a}),r+=1;else if(u===s.Unknown&&(r+=1,!0===e._ordered))break;if(r>=t)break}null!==n&&i.push(n);for(let s=i.length-1;s>=0;s--)e._candidates.splice(i[s].start,i[s].end-i[s].start+1)}_refineIfParentKnown(e,r,n){const s=new t([],[],e._ordered,null);return s._candidates=e._candidates.slice(0),this._parent._refineSetBlock(s,r,n)}_candidateIdTransform(e){return this._parent._candidateIdTransform(e)}_checkIfNeedToExpandKnownPage(e,t){if(null===e.pagesDefinition)return!1;let r=0;for(let n=e._lastFetchedIndex;n<e._known.length;n++){if("GETPAGES"===e._known[n])return!0;if(void 0===this._featureCache[e._known[n]]&&(r+=1,r>=t))break}return!1}_checkIfNeedToExpandCandidatePage(e,t){if(null===e.pagesDefinition)return!1;let r=0;for(let n=0;n<e._candidates.length;n++){if("GETPAGES"===e._candidates[n])return!0;if(r+=1,r>=t)break}return!1}_expandPagedSet(e,t,r,n,s){return null===this._parent?F(new Error("Parent Paging not implemented")):this._parent._expandPagedSet(e,t,r,n,s)}_expandPagedSetFeatureSet(e,t,r,n,s){return e._known.length>0&&"GETPAGES"===e._known[e._known.length-1]&&(n=1),0===n&&e._candidates.length>0&&"GETPAGES"===e._candidates[e._candidates.length-1]&&(n=2),0===n?b("finished"):this._getPage(e,n,s).then((n=>r+n<t?this._expandPagedSet(e,t,r+n,0,s):"success"))}_getPage(e,t,r){const n=1===t?e._known:e._candidates;if(e.pagesDefinition.internal.set.length>e.pagesDefinition.resultOffset||!0===e.pagesDefinition.internal.fullyResolved){n.length=n.length-1;let t=0;for(let s=0;s<e.pagesDefinition.resultRecordCount&&!(e.pagesDefinition.resultOffset+s>=e.pagesDefinition.internal.set.length);s++)n[n.length]=e.pagesDefinition.internal.set[e.pagesDefinition.resultOffset+s],t++;e.pagesDefinition.resultOffset+=t;let r=!1;return!0===e.pagesDefinition.internal.fullyResolved&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset&&(r=!0),!1===r&&n.push("GETPAGES"),b(t)}return this._getPhysicalPage(e,t,r).then((()=>this._getPage(e,t,r)))}_getPhysicalPage(e,t,r){return null}_clonePageDefinition(e){return null===this._parent?null:this._parent._clonePageDefinition(e)}_first(e){return this.iterator(e).next()}first(e){return this._first(e)}calculateStatistic(e,t,r,n){return this._ensureLoaded().then((()=>this._stat(e,t,"",null,null,r,n).then((s=>!1===s.calculated?this._manualStat(e,t,r,n).then((e=>e.result)):s.result))))}_manualStat(e,t,r,n){switch(e.toLowerCase()){case"count":return m(this,n).then((e=>({calculated:!0,result:e})));case"distinct":return g(this,t,r).then((e=>({calculated:!0,result:e})));case"avg":case"mean":return f(this,t,n).then((e=>({calculated:!0,result:e})));case"stdev":return p(this,t,n).then((e=>({calculated:!0,result:e})));case"variance":return _(this,t,n).then((e=>({calculated:!0,result:e})));case"sum":return d(this,t,n).then((e=>({calculated:!0,result:e})));case"min":return c(this,t,n).then((e=>({calculated:!0,result:e})));case"max":return o(this,t,n).then((e=>({calculated:!0,result:e})));default:return b({calculated:!0,result:0})}}_stat(e,t,r,n,s,i,a){return this._parent._stat(e,t,r,n,s,i,a).then((l=>!1===l.calculated?null===s&&""===r&&null===n?this._manualStat(e,t,i,a):{calculated:!1}:l))}_unionAllGeomSelf(e){const t=this.iterator(this._defaultTracker(e)),r=[];return y(((e,n)=>{this._unionShapeInBatches(r,t,e,n)}))}_unionAllGeom(e){return y(((t,r)=>{const n=this.iterator(this._defaultTracker(e)),s=[];this._unionShapeInBatches(s,n,t,r)}))}_unionShapeInBatches(e,t,r,n){t.next().then((s=>{try{null!==s&&null!==s.geometry&&e.push(s.geometry),e.length>30||null===s&&e.length>1?T(e).then((i=>{try{null===s?r(i):(e=[i],this._unionShapeInBatches(e,t,r,n))}catch(a){n(a)}}),n):null===s?1===e.length?r(e[0]):r(null):this._unionShapeInBatches(e,t,r,n)}catch(i){n(i)}}),n)}iterator(t){return new e(this,t)}intersection(e,t=!1){return E._featuresetFunctions.intersection.bind(this)(e,t)}difference(e,t=!1,r=!0){return E._featuresetFunctions.difference.bind(this)(e,t,r)}symmetricDifference(e,t=!1,r=!0){return E._featuresetFunctions.symmetricDifference.bind(this)(e,t,r)}morphShape(e,t,r="unknown",n=null){return E._featuresetFunctions.morphShape.bind(this)(e,t,r,n)}morphShapeAndAttributes(e,t,r="unknown"){return E._featuresetFunctions.morphShapeAndAttributes.bind(this)(e,t,r)}union(e,t=!1){return E._featuresetFunctions.union.bind(this)(e,t)}intersects(e){return E._featuresetFunctions.intersects.bind(this)(e)}envelopeIntersects(e){return E._featuresetFunctions.envelopeIntersects.bind(this)(e)}contains(e){return E._featuresetFunctions.contains.bind(this)(e)}overlaps(e){return E._featuresetFunctions.overlaps.bind(this)(e)}relate(e,t){return E._featuresetFunctions.relate.bind(this)(e,t)}within(e){return E._featuresetFunctions.within.bind(this)(e)}touches(e){return E._featuresetFunctions.touches.bind(this)(e)}top(e){return E._featuresetFunctions.top.bind(this)(e)}crosses(e){return E._featuresetFunctions.crosses.bind(this)(e)}buffer(e,t,r,n=!0){return E._featuresetFunctions.buffer.bind(this)(e,t,r,n)}filter(e,t=null){return E._featuresetFunctions.filter.bind(this)(e,t)}orderBy(e){return E._featuresetFunctions.orderBy.bind(this)(e)}dissolve(e,t){return E._featuresetFunctions.dissolve.bind(this)(e,t)}groupby(e,t){return E._featuresetFunctions.groupby.bind(this)(e,t)}reduce(e,t=null,r){return y(((n,s)=>{this._reduceImpl(this.iterator(this._defaultTracker(r)),e,t,0,n,s,0)}))}_reduceImpl(e,t,r,n,s,i,a){try{if(++a>1e3)return void setTimeout((()=>{a=0,this._reduceImpl(e,t,r,n,s,i,a)}));e.next().then((l=>{try{if(null===l)s(r);else{const h=t(r,l,n,this);S(h)?h.then((r=>{this._reduceImpl(e,t,r,n+1,s,i,a)}),i):this._reduceImpl(e,t,h,n+1,s,i,a)}}catch(h){i(h)}}),i)}catch(l){i(l)}}removeField(e){return E._featuresetFunctions.removeField.bind(this)(e)}addField(e,t,r=null){return E._featuresetFunctions.addField.bind(this)(e,t,r)}sumArea(e,t=!1,r){const n=i(e);return this.reduce(((e,r)=>null===r.geometry?0:t?P(r.geometry,n).then((t=>e+t)):w(r.geometry,n).then((t=>e+t))),0,r)}sumLength(e,t=!1,r){const n=a(e);return this.reduce(((e,r)=>null===r.geometry?0:t?D(r.geometry,n).then((t=>e+t)):k(r.geometry,n).then((t=>e+t))),0,r)}_substituteVars(e,t){if(null!==t){const r={};for(const e in t)r[e.toLowerCase()]=t[e];e.parameters=r}}distinct(e,t=1e3,r=null,n){return this.load().then((()=>{const s=I.create(e,this.getFieldsIndex());return this._substituteVars(s,r),this.calculateStatistic("distinct",s,t,this._defaultTracker(n))}))}min(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("min",n,-1,this._defaultTracker(r))}))}max(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("max",n,-1,this._defaultTracker(r))}))}avg(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("avg",n,-1,this._defaultTracker(r))}))}sum(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("sum",n,-1,this._defaultTracker(r))}))}stdev(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("stdev",n,-1,this._defaultTracker(r))}))}variance(e,t=null,r){return this.load().then((()=>{const n=I.create(e,this.getFieldsIndex());return this._substituteVars(n,t),this.calculateStatistic("variance",n,-1,this._defaultTracker(r))}))}count(e){return this.load().then((()=>this.calculateStatistic("count",I.create("1",this.getFieldsIndex()),-1,this._defaultTracker(e))))}_defaultTracker(e){return e||{aborted:!1}}forEach(e,t){return y(((r,n)=>{this._forEachImpl(this.iterator(this._defaultTracker(t)),e,this,r,n,0)}))}_forEachImpl(e,t,r,n,s,i){try{if(++i>1e3)return void setTimeout((()=>{i=0,this._forEachImpl(e,t,r,n,s,i)}),0);e.next().then((a=>{try{if(null===a)n(r);else{const l=t(a);null==l?this._forEachImpl(e,t,r,n,s,i):S(l)?l.then((()=>{try{this._forEachImpl(e,t,r,n,s,i)}catch(a){s(a)}}),s):this._forEachImpl(e,t,r,n,s,i)}}catch(l){s(l)}}),s)}catch(a){s(a)}}convertToJSON(e){const t={layerDefinition:{geometryType:this.geometryType,fields:[]},featureSet:{features:[],geometryType:this.geometryType}};for(let r=0;r<this.fields.length;r++)t.layerDefinition.fields.push(l(this.fields[r]));return this.reduce(((e,r)=>{const n={geometry:r.geometry&&r.geometry.toJSON(),attributes:{}};for(const t in r.attributes)n.attributes[t]=r.attributes[t];return t.featureSet.features.push(n),1}),0,e).then((()=>t))}castToText(){return"object, FeatureSet"}queryAttachments(e,t,r,n,s){return this._parent.queryAttachments(e,t,r,n,s)}serviceUrl(){return this._parent.serviceUrl()}subtypes(){return this.typeIdField?{subtypeField:this.typeIdField,subtypes:this.types?this.types.map((e=>({name:e.name,code:e.id}))):[]}:null}relationshipMetaData(){return this._parent.relationshipMetaData()}get gdbVersion(){return this._parent?this._parent.gdbVersion:""}schema(){const e=[];for(const t of this.fields)e.push(l(t));return{objectIdField:this.objectIdField,globalIdField:this.globalIdField,geometryType:void 0===h[this.geometryType]?"":h[this.geometryType],fields:e}}convertToText(e,t){return"schema"===e?this._ensureLoaded().then((()=>JSON.stringify(this.schema()))):"featureset"===e?this._ensureLoaded().then((()=>{const e=[];return this.reduce(((t,r)=>{const n={geometry:r.geometry?r.geometry.toJSON():null,attributes:r.attributes};return null!==n.geometry&&n.geometry.spatialReference&&delete n.geometry.spatialReference,e.push(n),1}),0,t).then((()=>{const t=this.schema();return t.features=e,t.spatialReference=this.spatialReference.toJSON(),JSON.stringify(t)}))})):b(this.castToText())}getFeatureByObjectId(e,t){return this._parent.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._parent.getOwningSystemUrl()}getIdentityUser(){return this._parent.getIdentityUser()}getRootFeatureSet(){return null!==this._parent?this._parent.getRootFeatureSet():this}getDataSourceFeatureSet(){return null!==this._parent?this._parent.getDataSourceFeatureSet():this}castAsJson(e=null){return"keeptype"===(null==e?void 0:e.featureset)?this:"none"===(null==e?void 0:e.featureset)?null:{type:"FeatureSet"}}castAsJsonAsync(e=null,t=null){return"keeptype"===(null==t?void 0:t.featureset)?b(this):"schema"===(null==t?void 0:t.featureset)?this._ensureLoaded().then((()=>JSON.parse(JSON.stringify(this.schema())))):"none"===(null==t?void 0:t.featureset)?b(null):this._ensureLoaded().then((()=>{const r=[];return this.reduce(((e,n)=>{const s={geometry:n.geometry?!0===(null==t?void 0:t.keepGeometryType)?n.geometry:n.geometry.toJSON():null,attributes:n.attributes};return null!==s.geometry&&s.geometry.spatialReference&&!0!==(null==t?void 0:t.keepGeometryType)&&delete s.geometry.spatialReference,r.push(s),1}),0,e).then((()=>{const e=this.schema();return e.features=r,e.spatialReference=!0===(null==t?void 0:t.keepGeometryType)?this.spatialReference:this.spatialReference.toJSON(),e}))}))}}E._featuresetFunctions={};export{E as default};
