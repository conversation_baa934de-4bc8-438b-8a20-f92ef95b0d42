{"ast": null, "code": "import popTitle from '@/components/popTitle/popTitle.vue';\nimport { getSiteAlarmList } from '@/api/chargingPiles.js';\nimport RotationData from '@/components/RotationData/RotationData.vue';\nexport default {\n  name: 'SiteAlarmList',\n  components: {\n    popTitle,\n    RotationData\n  },\n  data() {\n    return {\n      pageNum: 1,\n      pageSize: 10,\n      numPage: 1,\n      total: 0,\n      scrollList: [],\n      headerList: ['标题', '社区名称', '预警类型', '时间', '发生位置', '预警类型'],\n      styleAll: {\n        width: '100%',\n        height: '25rem'\n      },\n      isShowDiagLog: false\n    };\n  },\n  props: {\n    AlarmList: {\n      type: Object,\n      default: () => {\n        return {};\n      }\n    }\n  },\n  watch: {\n    AlarmList: {\n      handler(nv) {\n        if (nv.siteCode) {\n          this.pageNum = 1;\n          this.scrollList = [];\n          let {\n            siteCode,\n            count\n          } = nv;\n          if (count) {\n            this.SiteAlarmListFun(siteCode, this.pageNum, this.pageSize);\n          } else {\n            this.isShowDiagLog = false;\n            this.$message({\n              message: '暂无数据',\n              type: 'warning'\n            });\n          }\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    SiteAlarmListFun(siteCode, pageNum, pageSize = 10) {\n      this.scrollList = [];\n      let params = {\n        siteCode,\n        pageNum,\n        pageSize\n      };\n      getSiteAlarmList(params).then(res => {\n        if (res.status == '200') {\n          let {\n            records,\n            total,\n            pageNum\n          } = res.data.extra;\n          this.total = total;\n          this.numPage = total / pageNum;\n          this.isShowDiagLog = true;\n          records.forEach((item, index) => {\n            let {\n              id,\n              address,\n              community,\n              handleResult,\n              happenTime,\n              level,\n              title\n            } = item;\n            this.scrollList.push([title, community, handleResult, happenTime, address, level]);\n          });\n\n          // console.log(records, total)\n        }\n      });\n    },\n    prePage() {\n      if (this.pageNum > 1) {\n        this.pageNum--;\n        this.SiteAlarmListFun(this.AlarmList.siteCode, this.pageNum);\n      }\n    },\n    nextPage() {\n      let sumPage = Math.ceil(this.total / this.pageSize);\n      if (this.pageNum < sumPage) {\n        this.pageNum++;\n        this.SiteAlarmListFun(this.AlarmList.siteCode, this.pageNum);\n      }\n    },\n    closeFun() {\n      this.isShowDiagLog = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["popTitle", "getSiteAlarmList", "RotationData", "name", "components", "data", "pageNum", "pageSize", "numPage", "total", "scrollList", "headerList", "styleAll", "width", "height", "isShowDiagLog", "props", "AlarmList", "type", "Object", "default", "watch", "handler", "nv", "siteCode", "count", "SiteAlarmListFun", "$message", "message", "immediate", "mounted", "methods", "params", "then", "res", "status", "records", "extra", "for<PERSON>ach", "item", "index", "id", "address", "community", "handleResult", "happenTime", "level", "title", "push", "prePage", "nextPage", "sumPage", "Math", "ceil", "closeFun"], "sources": ["src/views/chargingPilesScreen/SiteAlarmList/SiteAlarmList.vue"], "sourcesContent": ["<template>\r\n    <div id=\"SiteAlarmList\" class=\"boxBgStyle\" v-if=\"isShowDiagLog\">\r\n        <popTitle :title=\"AlarmList?.siteName\"></popTitle>\r\n        <div class=\"main\">\r\n            <RotationData :data=\"scrollList\" :header=\"headerList\" :styleAll=\"styleAll\">\r\n            </RotationData>\r\n            <!-- 上下一页按钮 -->\r\n            <div class=\"pdf-preview-btn\">\r\n                <div class=\"txt\">\r\n                    <span>{{ pageNum }}/{{ Math.ceil(total / pageSize) }}</span>\r\n                </div>\r\n                <div class=\"btns\">\r\n                    <el-button size=\"medium\" @click=\"prePage\">上一页</el-button>\r\n                    <el-button size=\"medium\" @click=\"nextPage\">下一页</el-button>\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport popTitle from '@/components/popTitle/popTitle.vue'\r\nimport { getSiteAlarmList } from '@/api/chargingPiles.js'\r\nimport RotationData from '@/components/RotationData/RotationData.vue'\r\n\r\nexport default {\r\n    name: 'SiteAlarmList',\r\n\r\n    components: {\r\n        popTitle,\r\n        RotationData\r\n    },\r\n    data() {\r\n        return {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n            numPage: 1,\r\n            total: 0,\r\n            scrollList: [],\r\n            headerList: ['标题', '社区名称', '预警类型', '时间', '发生位置', '预警类型'],\r\n            styleAll: {\r\n                width: '100%',\r\n                height: '25rem'\r\n            },\r\n            isShowDiagLog: false,\r\n        }\r\n    },\r\n    props: {\r\n        AlarmList: {\r\n            type: Object,\r\n            default: () => {\r\n                return { \r\n                }\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        AlarmList: {\r\n            handler(nv) {\r\n                if(nv.siteCode){\r\n                    this.pageNum = 1;\r\n                    this.scrollList = [];\r\n                    let { siteCode,count } = nv\r\n                    if(count) {\r\n                        this.SiteAlarmListFun(siteCode, this.pageNum, this.pageSize)\r\n                    }else{\r\n                        this.isShowDiagLog = false\r\n                        this.$message({\r\n                            message: '暂无数据',\r\n                            type: 'warning'\r\n                        })\r\n                    }\r\n\r\n                }\r\n\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n\r\n    },\r\n    methods: {\r\n        SiteAlarmListFun(siteCode, pageNum, pageSize = 10) {\r\n            this.scrollList = []\r\n            let params = {\r\n                siteCode,\r\n                pageNum,\r\n                pageSize\r\n            }\r\n\r\n            getSiteAlarmList(params).then(res => {\r\n                if (res.status == '200') {\r\n                    let { records, total, pageNum } = res.data.extra\r\n                    this.total = total;\r\n                    this.numPage = total / pageNum \r\n                        this.isShowDiagLog = true\r\n                        records.forEach((item, index) => {\r\n                            let { id, address, community, handleResult, happenTime, level, title } = item\r\n                            this.scrollList.push([title, community, handleResult, happenTime, address, level])\r\n                        })\r\n\r\n                  \r\n\r\n                    // console.log(records, total)\r\n                }\r\n\r\n            })\r\n\r\n\r\n        },\r\n        prePage() {\r\n            if (this.pageNum > 1) {\r\n                this.pageNum--;\r\n                this.SiteAlarmListFun(this.AlarmList.siteCode, this.pageNum)\r\n\r\n            }\r\n        },\r\n        nextPage() {\r\n            let sumPage = Math.ceil(this.total / this.pageSize)\r\n            if (this.pageNum < sumPage) {\r\n                this.pageNum++;\r\n                this.SiteAlarmListFun(this.AlarmList.siteCode, this.pageNum)\r\n            }\r\n        },\r\n        closeFun() {\r\n            this.isShowDiagLog = false\r\n        }\r\n    }\r\n}\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#SiteAlarmList {\r\n    position: absolute;\r\n    width: 1000px;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, 0);\r\n    pointer-events: stroke;\r\n\r\n    .main {\r\n        margin-top: 40px;\r\n\r\n        .pdf-preview-btn {\r\n\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            ;\r\n            margin-top: 30px;\r\n            font-size: 28px;\r\n            ;\r\n\r\n            .btns {\r\n                margin-left: 50px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAqBA,OAAAA,QAAA;AACA,SAAAC,gBAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EAEAC,UAAA;IACAJ,QAAA;IACAE;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,KAAA;MACAC,UAAA;MACAC,UAAA;MACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA,QACA;MACA;IACA;EACA;EACAC,KAAA;IACAJ,SAAA;MACAK,QAAAC,EAAA;QACA,IAAAA,EAAA,CAAAC,QAAA;UACA,KAAAlB,OAAA;UACA,KAAAI,UAAA;UACA;YAAAc,QAAA;YAAAC;UAAA,IAAAF,EAAA;UACA,IAAAE,KAAA;YACA,KAAAC,gBAAA,CAAAF,QAAA,OAAAlB,OAAA,OAAAC,QAAA;UACA;YACA,KAAAQ,aAAA;YACA,KAAAY,QAAA;cACAC,OAAA;cACAV,IAAA;YACA;UACA;QAEA;MAEA;MACAW,SAAA;IACA;EACA;EAEAC,QAAA,GAEA;EACAC,OAAA;IACAL,iBAAAF,QAAA,EAAAlB,OAAA,EAAAC,QAAA;MACA,KAAAG,UAAA;MACA,IAAAsB,MAAA;QACAR,QAAA;QACAlB,OAAA;QACAC;MACA;MAEAN,gBAAA,CAAA+B,MAAA,EAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,MAAA;UACA;YAAAC,OAAA;YAAA3B,KAAA;YAAAH;UAAA,IAAA4B,GAAA,CAAA7B,IAAA,CAAAgC,KAAA;UACA,KAAA5B,KAAA,GAAAA,KAAA;UACA,KAAAD,OAAA,GAAAC,KAAA,GAAAH,OAAA;UACA,KAAAS,aAAA;UACAqB,OAAA,CAAAE,OAAA,EAAAC,IAAA,EAAAC,KAAA;YACA;cAAAC,EAAA;cAAAC,OAAA;cAAAC,SAAA;cAAAC,YAAA;cAAAC,UAAA;cAAAC,KAAA;cAAAC;YAAA,IAAAR,IAAA;YACA,KAAA7B,UAAA,CAAAsC,IAAA,EAAAD,KAAA,EAAAJ,SAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAH,OAAA,EAAAI,KAAA;UACA;;UAIA;QACA;MAEA;IAGA;IACAG,QAAA;MACA,SAAA3C,OAAA;QACA,KAAAA,OAAA;QACA,KAAAoB,gBAAA,MAAAT,SAAA,CAAAO,QAAA,OAAAlB,OAAA;MAEA;IACA;IACA4C,SAAA;MACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,IAAA,MAAA5C,KAAA,QAAAF,QAAA;MACA,SAAAD,OAAA,GAAA6C,OAAA;QACA,KAAA7C,OAAA;QACA,KAAAoB,gBAAA,MAAAT,SAAA,CAAAO,QAAA,OAAAlB,OAAA;MACA;IACA;IACAgD,SAAA;MACA,KAAAvC,aAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}