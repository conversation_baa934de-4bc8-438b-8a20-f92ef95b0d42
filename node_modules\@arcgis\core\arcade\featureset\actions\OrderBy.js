/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{X as e}from"../../../chunks/languageUtils.js";import t from"../support/FeatureSet.js";import r from"../support/IdSet.js";import n from"../support/OrderbyClause.js";import{resolve as s,reject as i}from"../../../core/promiseUtils.js";class a extends t{constructor(e){super(e),this._orderbyclause=null,this.declaredClass="esri.arcade.featureset.actions.OrderBy",this._maxProcessing=100,this._orderbyclause=e.orderbyclause,this._parent=e.parentfeatureset}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._getFilteredSet("",null,null,this._orderbyclause,e))).then((t=>(this._checkCancelled(e),this._wset=t,this._wset))):s(this._wset)}manualOrderSet(e,t){return this.getIdColumnDictionary(e,[],-1,t).then((e=>{this._orderbyclause.order(e);const t=new r([],[],!0,null);for(let r=0;r<e.length;r++)t._known.push(e[r].id);return t}))}getIdColumnDictionary(t,r,n,i){if(n<t._known.length-1){const s=this._maxQueryRate();if("GETPAGES"===t._known[n+1])return e(this._parent._expandPagedSet(t,s,0,0,i)).then((()=>this.getIdColumnDictionary(t,r,n,i)));let a=n+1;const o=[];for(;a<t._known.length&&"GETPAGES"!==t._known[a];)o.push(t._known[a]),a++;return n+=o.length,e(this._parent._getFeatureBatch(o,i)).then((e=>{this._checkCancelled(i);for(const t of e)r.push({id:t.attributes[this.objectIdField],feature:t});return this.getIdColumnDictionary(t,r,n,i)}))}return t._candidates.length>0?e(this._refineSetBlock(t,this._maxProcessingRate(),i)).then((()=>(this._checkCancelled(i),this.getIdColumnDictionary(t,r,n,i)))):s(r)}_isInFeatureSet(e){return this._parent._isInFeatureSet(e)}_getFeatures(e,t,r,n){return this._parent._getFeatures(e,t,r,n)}_featureFromCache(e){if(void 0===this._featureCache[e]){const t=this._parent._featureFromCache(e);if(void 0===t)return;return null===t?null:(this._featureCache[e]=t,t)}return this._featureCache[e]}_fetchAndRefineFeatures(){return i(new Error("Fetch and Refine should not be called in this featureset"))}_getFilteredSet(e,t,n,s,i){return this._ensureLoaded().then((()=>this._parent._getFilteredSet(e,t,n,null===s?this._orderbyclause:s,i))).then((e=>{this._checkCancelled(i);const s=new r(e._candidates.slice(0),e._known.slice(0),e._ordered,this._clonePageDefinition(e.pagesDefinition));let a=!0;return e._candidates.length>0&&(a=!1),!1===s._ordered?this.manualOrderSet(s,i).then((e=>(!1===a&&(null===t&&null===n||(e=new r(e._candidates.slice(0).concat(e._known.slice(0)),[],e._ordered,this._clonePageDefinition(e.pagesDefinition)))),e))):s}))}static registerAction(){t._featuresetFunctions.orderBy=function(e){return""===e?this:new a({parentfeatureset:this,orderbyclause:new n(e)})}}}export{a as default};
