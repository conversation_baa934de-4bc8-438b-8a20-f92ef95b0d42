{"ast": null, "code": "import canvasPlayerItem from './canvasPlayerItem.vue';\nexport default {\n  name: \"canvas-player-list\",\n  components: {\n    canvasPlayerItem\n  },\n  props: {\n    nameFieldDisplay: {\n      type: String,\n      default: '0'\n    },\n    // 是否隐藏底部功能按钮 \n    hideAllFunc: {\n      type: <PERSON>olean,\n      default() {\n        return false;\n      }\n    },\n    /*\n        播放器的样式\n    */\n    'playerListStyle': {\n      type: Object,\n      default() {\n        return {};\n      }\n    },\n    /*\n        播放器的播放参数列表\n    */\n    'playerArray': {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    // 初始化几路播放器\n    'initNum': {\n      type: Number,\n      default() {\n        return 4;\n      }\n    },\n    // \n    'prefix': {\n      type: String,\n      default() {\n        return '_canvasPlayer';\n      }\n    },\n    // 播放区域的宽度\n    'width': {\n      type: String,\n      default() {\n        return '0px';\n      }\n    },\n    // 播放区域的高度\n    'height': {\n      type: String,\n      default() {\n        return '0px';\n      }\n    },\n    // 播放器底部的按钮组\n    \"bottomList\": {\n      type: Array,\n      default() {\n        return [];\n      }\n    },\n    // 标记当前调用组件的状态;\n    \"current\": {\n      type: String,\n      default() {\n        return '';\n      }\n    },\n    // 当前的包裹组件v-dialog 是否是全屏状态;\n    \"isFull\": {\n      type: Boolean,\n      default() {\n        return false;\n      }\n    },\n    // 批注按钮显示，默认为true\n    isAnnotation: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      markValue1: 100,\n      markValue2: 14,\n      // 当前命中的播放器\n      currentActiveIndex: 0,\n      ways: this.initNum,\n      gridStyle: {\n        playerWidth: '640px',\n        //整个播放区域的宽度,单路播放640px, 4路播放 单个播放器的宽度 = (playerWidth - columnGap) /2\n        playerHeight: '480px',\n        // 整个播放区域的高度,单路播放480px, 4路播放 单个播放器的高度 = (playerHeight - rowGap) /2\n        rowGap: '10px',\n        // grid 行间距 单路播放时此参数不生效;\n        columnGap: '10px',\n        // grid 列间距 单路播放时此参数不生效\n        oneFull: true // 当需要多路播放,但当前状态下只有一路在播放,是否让这一路铺满这个播放区域 true 铺满 false 不铺满,单路播放此参数不生效\n        // 单个播放器的边框\n      },\n      cameras: []\n    };\n  },\n  watch: {\n    isFull(nv) {\n      let dom = document.querySelector('.player-list-container');\n      if (nv) {\n        dom.classList.add('addList');\n      } else {\n        dom.classList.remove('addList');\n      }\n    }\n  },\n  computed: {\n    // 每个播放器是否显示边框\n    bordered() {\n      return this.playerArray.length > 1 && this.playerListStyle.itemBorder;\n    },\n    itemStyle() {\n      let grid = 1;\n      if (this.ways == 4) grid = 2;else if (this.ways % 3 == 0) grid = 3;\n      return {\n        width: parseInt(this.width) / grid - (this.playerListStyle.itemBorder ? 4 : 0),\n        height: parseInt(this.height) / grid - (this.playerListStyle.itemBorder ? 4 : 0)\n      };\n    }\n  },\n  watch: {},\n  methods: {\n    // getWatermarkOptions() {\n    //     getWaterMarkSet().then((res) => {\n    //         this.markOptions = res.data\n    //         this.markOptions.forEach(item => {\n    //             if (item.dictLabel == 'font_transparence') {\n    //                 this.markValue1 = Number(item.dictValue)\n    //             } else if (item.dictLabel == 'font_size') {\n    //                 this.markValue2 = Number(item.dictValue)\n    //             }\n    //         })\n    //     });\n    // },\n    resetWatermark() {\n      // await this.getWatermarkOptions()\n      this.playerArray.forEach(element => {\n        let {\n          id\n        } = element;\n        this.$refs[id][0].resetWatermark(element);\n      });\n    },\n    init() {\n      this.cameras = this.playerArray;\n      // 初始化playerArray 数组;并且默认播放前n路;\n      for (let i = 0; i < this.initNum; i++) {\n        if (this.playerArray[i]) {\n          this.playerArray[i].id = `way1_${i}`;\n          this.playerArray[i].type = true;\n        }\n      }\n      ;\n    },\n    // 开始播放;\n    play() {\n      // await this.getWatermarkOptions()\n      this.playerArray.forEach(element => {\n        let {\n          id\n        } = element;\n        this.$refs[id][0].resetWatermark(element);\n        this.$refs[id][0].play(element);\n      });\n    },\n    // 打开弹窗\n    open() {\n      this.init();\n      this.$nextTick(() => {\n        setTimeout(() => {\n          this.play();\n        }, 100);\n      });\n    },\n    // 关闭弹窗;\n    // close() {\n    //     if (this.isFull) {\n    //         // this.$set(this, 'isFull', false);\n    //         this.isFull = false;\n    //         exitFullscreen();\n    //     }\n    //     this.stop();\n    //     // this.playerArray = []\n    //     this.$emit('close', true);\n    // },\n    // 停止播放;\n    stop() {\n      this.playerArray.forEach(element => {\n        let {\n          id\n        } = element;\n        this.$refs[id][0].close();\n      });\n      // this.$set(this, 'currentActiveIndex', 0);\n      this.currentActiveIndex = 0;\n    },\n    stopLoop(id) {\n      this.$refs[id][0].close();\n      this.currentActiveIndex = 0;\n    },\n    // 选中窗口的切换;\n    handleChangeIndex(index) {\n      // this.$set(this, 'currentActiveIndex', index);\n      this.currentActiveIndex = index;\n      this.$emit('activeIndex', index, this.playerArray[index]);\n    },\n    // 1路和4路切换,当需要此功能时需要将props:playerListStyle的itemBorder设置成true(主要是为了视觉效果);\n    handleChangeWin(num) {\n      // 当前选中的窗口\n      let id = this.playerArray[this.currentActiveIndex]['id'];\n      this.$refs[id][0].resize();\n      // this.$set(this, 'ways', num);\n      this.ways = num;\n    },\n    // 替换指定窗口的播放器;索引值;\n    replacePlayer(index) {\n      let {\n        id\n      } = this.playerArray[index];\n      let oldItem = JSON.parse(JSON.stringify(this.playerArray[index]));\n      this.$refs[id][0].close().then(res => {\n        this.$refs[id][0].play(oldItem);\n      });\n    },\n    // 获取当前选中的是哪一个窗口;\n    getCurrentActiveIndex() {\n      return this.currentActiveIndex;\n    },\n    // 窗口大小切换;\n    resize() {\n      this.playerArray.forEach(element => {\n        let {\n          id\n        } = element;\n        this.$refs[id][0].resize();\n      });\n    },\n    // 当前的播放状态\n    handlePlayType(cameraCode, type) {\n      this.$emit('playType', cameraCode, type);\n    },\n    // 自定义按钮组的点击事件\n    handleBtnClick(player, index) {\n      this.$emit('handleBtnClick', player, index);\n    },\n    close(item) {\n      if (item && item.cameraCode) {\n        this.$emit('closeItem', item);\n      }\n    },\n    handleDblclick() {\n      this.handleChangeWin(1);\n      this.$emit('handleDblclick', 1);\n    }\n  }\n};", "map": {"version": 3, "names": ["canvasPlayerItem", "name", "components", "props", "nameFieldDisplay", "type", "String", "default", "hideAllFunc", "Boolean", "Object", "Array", "Number", "isAnnotation", "data", "markValue1", "markValue2", "currentActiveIndex", "ways", "initNum", "gridStyle", "player<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rowGap", "columnGap", "oneFull", "cameras", "watch", "isFull", "nv", "dom", "document", "querySelector", "classList", "add", "remove", "computed", "bordered", "<PERSON><PERSON><PERSON><PERSON>", "length", "player<PERSON>ist<PERSON><PERSON><PERSON>", "itemBorder", "itemStyle", "grid", "width", "parseInt", "height", "methods", "resetWatermark", "for<PERSON>ach", "element", "id", "$refs", "init", "i", "play", "open", "$nextTick", "setTimeout", "stop", "close", "stopLoop", "handleChangeIndex", "index", "$emit", "handleChangeWin", "num", "resize", "replacePlayer", "oldItem", "JSON", "parse", "stringify", "then", "res", "getCurrentActiveIndex", "handlePlayType", "cameraCode", "handleBtnClick", "player", "item", "handleDblclick"], "sources": ["src/components/rslPlayer/components/canvasPlayerList.vue"], "sourcesContent": ["<template>\n    <div class=\"player-list-container\" id=\"player-list-container\"\n        :style=\"{ 'width': width + 'px', 'height': height + 'px' }\">\n        <div :class=\"['player-list', 'player-list-' + ways]\">\n            <div :class=\"['player-item', bordered ? 'player-item-border' : '', currentActiveIndex == index ? 'active' : '']\"\n                v-for=\"(player, index) in playerArray\" :key=\"index\" @click=\"handleChangeIndex(index)\"\n                @dblclick=\"handleDblclick\">\n                <canvas-player-item :isFull=\"isFull\" :current=\"current\" :id=\"player.id\" :ref=\"player.id\"\n                    :width=\"itemStyle.width\" :prefix=\"prefix + '_' + index\" :height=\"itemStyle.height\"\n                    :show-close=\"playerListStyle.showClose\" :show-full=\"playerListStyle.showFull\"\n                    :is-annotation=\"isAnnotation\" :top-hover-color=\"playerListStyle.topHoverColor\"\n                    :bottom-hover-color=\"playerListStyle.bottomHoverColor\" :bottom-list=\"bottomList\"\n                    @playType=\"handlePlayType\" :name-field-display=\"nameFieldDisplay\" @close=\"close\"\n                    @handleBtnClick=\"(btn, i) => { handleBtnClick(player, index) }\" :hideAllFunc=\"hideAllFunc\">\n                </canvas-player-item>\n                <!--边框-->\n            </div>\n        </div>\n    </div>\n</template>\n<script>\nimport canvasPlayerItem from './canvasPlayerItem.vue'\nexport default {\n    name: \"canvas-player-list\",\n    components: {\n        canvasPlayerItem\n    },\n    props: {\n        nameFieldDisplay: {\n            type: String,\n            default: '0'\n        },\n        // 是否隐藏底部功能按钮 \n        hideAllFunc: {\n            type: Boolean,\n            default() {\n                return false\n            }\n        },\n        /*\n            播放器的样式\n        */\n        'playerListStyle': {\n            type: Object,\n            default() {\n                return {}\n            }\n        },\n        /*\n            播放器的播放参数列表\n        */\n        'playerArray': {\n            type: Array,\n            default() {\n                return [];\n            }\n        },\n        // 初始化几路播放器\n        'initNum': {\n            type: Number,\n            default() {\n                return 4\n            }\n        },\n        // \n        'prefix': {\n            type: String,\n            default() {\n                return '_canvasPlayer'\n            }\n        },\n        // 播放区域的宽度\n        'width': {\n            type: String,\n            default() {\n                return '0px'\n            }\n        },\n        // 播放区域的高度\n        'height': {\n            type: String,\n            default() {\n                return '0px'\n            }\n        },\n        // 播放器底部的按钮组\n        \"bottomList\": {\n            type: Array,\n            default() {\n                return []\n            }\n        },\n        // 标记当前调用组件的状态;\n        \"current\": {\n            type: String,\n            default() {\n                return '';\n            }\n        },\n        // 当前的包裹组件v-dialog 是否是全屏状态;\n        \"isFull\": {\n            type: Boolean,\n            default() {\n                return false;\n            }\n        },\n        // 批注按钮显示，默认为true\n        isAnnotation: {\n            type: Boolean,\n            default: true\n        }\n    },\n    data() {\n        return {\n            markValue1: 100,\n            markValue2: 14,\n            // 当前命中的播放器\n            currentActiveIndex: 0,\n            ways: this.initNum,\n            gridStyle: {\n                playerWidth: '640px',//整个播放区域的宽度,单路播放640px, 4路播放 单个播放器的宽度 = (playerWidth - columnGap) /2\n                playerHeight: '480px',// 整个播放区域的高度,单路播放480px, 4路播放 单个播放器的高度 = (playerHeight - rowGap) /2\n                rowGap: '10px',// grid 行间距 单路播放时此参数不生效;\n                columnGap: '10px', // grid 列间距 单路播放时此参数不生效\n                oneFull: true,// 当需要多路播放,但当前状态下只有一路在播放,是否让这一路铺满这个播放区域 true 铺满 false 不铺满,单路播放此参数不生效\n                // 单个播放器的边框\n            },\n            cameras: [],\n        };\n    },\n    watch: {\n        isFull(nv) {\n            let dom = document.querySelector('.player-list-container');\n            if (nv) {\n                dom.classList.add('addList')\n            } else {\n                dom.classList.remove('addList')\n            }\n        }\n    },\n    computed: {\n        // 每个播放器是否显示边框\n        bordered() {\n            return this.playerArray.length > 1 && this.playerListStyle.itemBorder\n        },\n        itemStyle() {\n            let grid = 1\n            if (this.ways == 4)  grid = 2;\n            else if (this.ways % 3 == 0)  grid = 3;\n            return {\n                width: parseInt(this.width) / grid - (this.playerListStyle.itemBorder ? 4 : 0),\n                height: parseInt(this.height) / grid - (this.playerListStyle.itemBorder ? 4 : 0)\n            }\n        },\n\n    },\n    watch: {\n\n    },\n    methods: {\n        // getWatermarkOptions() {\n        //     getWaterMarkSet().then((res) => {\n        //         this.markOptions = res.data\n        //         this.markOptions.forEach(item => {\n        //             if (item.dictLabel == 'font_transparence') {\n        //                 this.markValue1 = Number(item.dictValue)\n        //             } else if (item.dictLabel == 'font_size') {\n        //                 this.markValue2 = Number(item.dictValue)\n        //             }\n        //         })\n        //     });\n        // },\n        resetWatermark() {\n            // await this.getWatermarkOptions()\n            this.playerArray.forEach((element) => {\n                let { id } = element;\n                this.$refs[id][0].resetWatermark(element);\n            });\n        },\n        init() {\n            this.cameras = this.playerArray\n            // 初始化playerArray 数组;并且默认播放前n路;\n            for (let i = 0; i < this.initNum; i++) {\n                if (this.playerArray[i]) {\n                    this.playerArray[i].id = `way1_${i}`;\n                    this.playerArray[i].type = true;\n\n                }\n            };\n\n\n        },\n        // 开始播放;\n        play() {\n            // await this.getWatermarkOptions()\n            this.playerArray.forEach((element) => {\n                let { id } = element;\n                this.$refs[id][0].resetWatermark(element);\n                this.$refs[id][0].play(element);\n            });\n        },\n        // 打开弹窗\n        open() {\n            this.init();\n            this.$nextTick(() => {\n                setTimeout(() => {\n                    this.play();\n                }, 100)\n            })\n\n        },\n        // 关闭弹窗;\n        // close() {\n        //     if (this.isFull) {\n        //         // this.$set(this, 'isFull', false);\n        //         this.isFull = false;\n        //         exitFullscreen();\n        //     }\n        //     this.stop();\n        //     // this.playerArray = []\n        //     this.$emit('close', true);\n        // },\n        // 停止播放;\n        stop() {\n            this.playerArray.forEach((element) => {\n                let { id } = element\n                this.$refs[id][0].close();\n            });\n            // this.$set(this, 'currentActiveIndex', 0);\n            this.currentActiveIndex = 0;\n        },\n        stopLoop(id) {\n            this.$refs[id][0].close();\n            this.currentActiveIndex = 0;\n        },\n        // 选中窗口的切换;\n        handleChangeIndex(index) {\n            // this.$set(this, 'currentActiveIndex', index);\n            this.currentActiveIndex = index;\n            this.$emit('activeIndex', index, this.playerArray[index]);\n        },\n        // 1路和4路切换,当需要此功能时需要将props:playerListStyle的itemBorder设置成true(主要是为了视觉效果);\n        handleChangeWin(num) {\n            // 当前选中的窗口\n            let id = this.playerArray[this.currentActiveIndex]['id'];\n            this.$refs[id][0].resize();\n            // this.$set(this, 'ways', num);\n            this.ways = num;\n        },\n        // 替换指定窗口的播放器;索引值;\n        replacePlayer(index) {\n            let { id } = this.playerArray[index];\n            let oldItem = JSON.parse(JSON.stringify(this.playerArray[index]));\n            this.$refs[id][0].close().then(res => {\n                this.$refs[id][0].play(oldItem);\n            })\n        },\n        // 获取当前选中的是哪一个窗口;\n        getCurrentActiveIndex() {\n            return this.currentActiveIndex;\n        },\n        // 窗口大小切换;\n        resize() {\n            this.playerArray.forEach((element) => {\n                let { id } = element;\n                this.$refs[id][0].resize();\n            });\n        },\n        // 当前的播放状态\n        handlePlayType(cameraCode, type) {\n            this.$emit('playType', cameraCode, type);\n        },\n        // 自定义按钮组的点击事件\n        handleBtnClick(player, index) {\n            this.$emit('handleBtnClick', player, index);\n        },\n        close(item) {\n            if (item && item.cameraCode) {\n                this.$emit('closeItem', item);\n            }\n        },\n        handleDblclick() {\n            this.handleChangeWin(1);\n            this.$emit('handleDblclick', 1)\n        }\n    }\n};\n</script>\n\n<style scoped lang=\"less\">\n.addList {\n    position: fixed;\n    top: 0;\n    left: 0;\n}\n\n.player-list-container {\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n\n    .player-list {\n        width: 100%;\n        height: 100%;\n        display: grid;\n    }\n\n    .player-list-1 {\n        grid-template-rows: repeat(1, 100%);\n        grid-template-columns: repeat(1, 100%);\n\n        .player-item {\n            display: none;\n\n            &.active {\n                display: block;\n            }\n        }\n    }\n\n    .player-list-4 {\n        grid-template-rows: repeat(2, 50%);\n        grid-template-columns: repeat(2, 50%);\n\n        .player-item-border {\n            border-width: 2px;\n            border-style: solid;\n            border-color: #dcdee0;\n\n            &.active {\n                border-color: #4b87ff;\n            }\n        }\n    }\n    .player-list-9 {\n        grid-template-rows: repeat(3, 33.33%);\n        grid-template-columns: repeat(3, 33.33%);\n\n        .player-item-border {\n            border-width: 2px;\n            border-style: solid;\n            border-color: #dcdee0;\n\n            &.active {\n                border-color: #4b87ff;\n            }\n        }\n    }\n}\n</style>\n"], "mappings": "AAqBA,OAAAA,gBAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACAC,gBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACA;MACAF,IAAA,EAAAK,MAAA;MACAH,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACA;MACAF,IAAA,EAAAM,KAAA;MACAJ,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAO,MAAA;MACAL,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAM,KAAA;MACAJ,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAC,MAAA;MACAC,QAAA;QACA;MACA;IACA;IACA;IACA;MACAF,IAAA,EAAAI,OAAA;MACAF,QAAA;QACA;MACA;IACA;IACA;IACAM,YAAA;MACAR,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAO,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;MACA;MACAC,kBAAA;MACAC,IAAA,OAAAC,OAAA;MACAC,SAAA;QACAC,WAAA;QAAA;QACAC,YAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,OAAA;QACA;MACA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAAC,EAAA;MACA,IAAAC,GAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAH,EAAA;QACAC,GAAA,CAAAG,SAAA,CAAAC,GAAA;MACA;QACAJ,GAAA,CAAAG,SAAA,CAAAE,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,SAAA;MACA,YAAAC,WAAA,CAAAC,MAAA,aAAAC,eAAA,CAAAC,UAAA;IACA;IACAC,UAAA;MACA,IAAAC,IAAA;MACA,SAAAzB,IAAA,OAAAyB,IAAA,UACA,SAAAzB,IAAA,WAAAyB,IAAA;MACA;QACAC,KAAA,EAAAC,QAAA,MAAAD,KAAA,IAAAD,IAAA,SAAAH,eAAA,CAAAC,UAAA;QACAK,MAAA,EAAAD,QAAA,MAAAC,MAAA,IAAAH,IAAA,SAAAH,eAAA,CAAAC,UAAA;MACA;IACA;EAEA;EACAd,KAAA,GAEA;EACAoB,OAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,eAAA;MACA;MACA,KAAAV,WAAA,CAAAW,OAAA,CAAAC,OAAA;QACA;UAAAC;QAAA,IAAAD,OAAA;QACA,KAAAE,KAAA,CAAAD,EAAA,KAAAH,cAAA,CAAAE,OAAA;MACA;IACA;IACAG,KAAA;MACA,KAAA3B,OAAA,QAAAY,WAAA;MACA;MACA,SAAAgB,CAAA,MAAAA,CAAA,QAAAnC,OAAA,EAAAmC,CAAA;QACA,SAAAhB,WAAA,CAAAgB,CAAA;UACA,KAAAhB,WAAA,CAAAgB,CAAA,EAAAH,EAAA,WAAAG,CAAA;UACA,KAAAhB,WAAA,CAAAgB,CAAA,EAAAjD,IAAA;QAEA;MACA;MAAA;IAGA;IACA;IACAkD,KAAA;MACA;MACA,KAAAjB,WAAA,CAAAW,OAAA,CAAAC,OAAA;QACA;UAAAC;QAAA,IAAAD,OAAA;QACA,KAAAE,KAAA,CAAAD,EAAA,KAAAH,cAAA,CAAAE,OAAA;QACA,KAAAE,KAAA,CAAAD,EAAA,KAAAI,IAAA,CAAAL,OAAA;MACA;IACA;IACA;IACAM,KAAA;MACA,KAAAH,IAAA;MACA,KAAAI,SAAA;QACAC,UAAA;UACA,KAAAH,IAAA;QACA;MACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAI,KAAA;MACA,KAAArB,WAAA,CAAAW,OAAA,CAAAC,OAAA;QACA;UAAAC;QAAA,IAAAD,OAAA;QACA,KAAAE,KAAA,CAAAD,EAAA,KAAAS,KAAA;MACA;MACA;MACA,KAAA3C,kBAAA;IACA;IACA4C,SAAAV,EAAA;MACA,KAAAC,KAAA,CAAAD,EAAA,KAAAS,KAAA;MACA,KAAA3C,kBAAA;IACA;IACA;IACA6C,kBAAAC,KAAA;MACA;MACA,KAAA9C,kBAAA,GAAA8C,KAAA;MACA,KAAAC,KAAA,gBAAAD,KAAA,OAAAzB,WAAA,CAAAyB,KAAA;IACA;IACA;IACAE,gBAAAC,GAAA;MACA;MACA,IAAAf,EAAA,QAAAb,WAAA,MAAArB,kBAAA;MACA,KAAAmC,KAAA,CAAAD,EAAA,KAAAgB,MAAA;MACA;MACA,KAAAjD,IAAA,GAAAgD,GAAA;IACA;IACA;IACAE,cAAAL,KAAA;MACA;QAAAZ;MAAA,SAAAb,WAAA,CAAAyB,KAAA;MACA,IAAAM,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAlC,WAAA,CAAAyB,KAAA;MACA,KAAAX,KAAA,CAAAD,EAAA,KAAAS,KAAA,GAAAa,IAAA,CAAAC,GAAA;QACA,KAAAtB,KAAA,CAAAD,EAAA,KAAAI,IAAA,CAAAc,OAAA;MACA;IACA;IACA;IACAM,sBAAA;MACA,YAAA1D,kBAAA;IACA;IACA;IACAkD,OAAA;MACA,KAAA7B,WAAA,CAAAW,OAAA,CAAAC,OAAA;QACA;UAAAC;QAAA,IAAAD,OAAA;QACA,KAAAE,KAAA,CAAAD,EAAA,KAAAgB,MAAA;MACA;IACA;IACA;IACAS,eAAAC,UAAA,EAAAxE,IAAA;MACA,KAAA2D,KAAA,aAAAa,UAAA,EAAAxE,IAAA;IACA;IACA;IACAyE,eAAAC,MAAA,EAAAhB,KAAA;MACA,KAAAC,KAAA,mBAAAe,MAAA,EAAAhB,KAAA;IACA;IACAH,MAAAoB,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAH,UAAA;QACA,KAAAb,KAAA,cAAAgB,IAAA;MACA;IACA;IACAC,eAAA;MACA,KAAAhB,eAAA;MACA,KAAAD,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}