/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{cloneGeometry as t}from"../../kernel.js";import r from"../support/FeatureSet.js";import s from"../support/IdSet.js";import{cloneField as a,FeatureServiceDatabaseType as i,layerGeometryEsriConstants as l}from"../support/shared.js";import{reformulateWithoutField as n,toWhereClause as o,translateFunctionToDatabaseSpecific as h,makeToday as u,makeDateString as d,convertIntervalToSql as c,combine as p,scanForField as f}from"../support/sqlUtils.js";import{resolve as g,reject as _}from"../../../core/promiseUtils.js";import{WhereClause as N}from"../../../core/sql/WhereClause.js";import S from"../../../geometry/SpatialReference.js";class m{constructor(e){this.field=e,this.sqlRewritable=!1}postInitialization(e,t){}}class w extends m{constructor(e){super(e),this.sqlRewritable=!0}extractValue(e){return e.attributes[this.field.name]}rewriteSql(e){return{rewritten:this.sqlRewritable,where:e}}}class v extends m{constructor(e,t,r){super(a(e)),this.originalField=e,this.sqlRewritable=!0,this.field.name=t,this.field.alias=r}rewriteSql(e,t){return{rewritten:this.sqlRewritable,where:n(e,this.field.name,this.originalField.name,t.getFieldsIndex())}}extractValue(e){return e.attributes[this.originalField.name]}}class C extends m{constructor(e,t,r){super(e),this.codefield=t,this.lkp=r,this.reverseLkp={};for(const s in r)this.reverseLkp[r[s]]=s;this.sqlRewritable=!0}rewriteSql(e,t){const r=this.evaluateNodeToWhereClause(e.parseTree,i.Standardised,this.field.name,this.codefield instanceof N?o(this.codefield,i.Standardised):this.codefield,e.parameters);return r.indexOf(C.BADNESS)>=0?{rewritten:!1,where:e}:{rewritten:this.sqlRewritable,where:N.create(r,t._parent.getFieldsIndex())}}evaluateNodeToWhereClause(e,t,r=null,s=null,a){let i,l,n,o;switch(e.type){case"interval":return c(this.evaluateNodeToWhereClause(e.value,t,r,s,a),e.qualifier,e.op);case"case_expression":{let s=" CASE ";"simple"===e.format&&(s+=this.evaluateNodeToWhereClause(e.operand,t,r,C.BADNESS,a));for(let i=0;i<e.clauses.length;i++)s+=" WHEN "+this.evaluateNodeToWhereClause(e.clauses[i].operand,t,r,C.BADNESS,a)+" THEN "+this.evaluateNodeToWhereClause(e.clauses[i].value,t,r,C.BADNESS,a);return null!==e.else&&(s+=" ELSE "+this.evaluateNodeToWhereClause(e.else,t,r,C.BADNESS,a)),s+=" END ",s}case"param":{const r=a[e.value.toLowerCase()];if("string"==typeof r){return"'"+a[e.value.toLowerCase()].toString().replace(/'/g,"''")+"'"}if(r instanceof Date)return d(r,t);if(r instanceof Array){const e=[];for(let s=0;s<r.length;s++)"string"==typeof r[s]?e.push("'"+r[s].toString().replace(/'/g,"''")+"'"):r[s]instanceof Date?e.push(d(r[s],t)):e.push(r[s].toString());return e}return r.toString()}case"expr_list":l=[];for(const i of e.value)l.push(this.evaluateNodeToWhereClause(i,t,r,s,a));return l;case"unary_expr":return" ( NOT "+this.evaluateNodeToWhereClause(e.expr,t,r,C.BADNESS,a)+" ) ";case"binary_expr":switch(e.operator){case"AND":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" AND "+this.evaluateNodeToWhereClause(e.right,t,r,s,a)+") ";case"OR":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" OR "+this.evaluateNodeToWhereClause(e.right,t,r,s,a)+") ";case"IS":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IS NULL )";case"ISNOT":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IS NOT NULL )";case"IN":if(i=[],"expr_list"===e.right.type){if("column_ref"===e.left.type&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const i=[];let l=!0;for(const t of e.right.value){if("string"!==t.type){l=!1;break}if(void 0===this.lkp[t.value]){l=!1;break}i.push(this.lkp[t.value].toString())}if(l)return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IN ("+i.join(",")+")) "}return i=this.evaluateNodeToWhereClause(e.right,t,r,s,a)," ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IN ("+i.join(",")+")) "}return o=this.evaluateNodeToWhereClause(e.right,t,r,s,a),o instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IN ("+o.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" IN ("+o+")) ";case"NOT IN":if(i=[],"expr_list"===e.right.type){if("column_ref"===e.left.type&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const i=[];let l=!0;for(const t of e.right.value){if("string"!==t.type){l=!1;break}if(void 0===this.lkp[t.value]){l=!1;break}i.push(this.lkp[t.value].toString())}if(l)return" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" NOT IN ("+i.join(",")+")) "}return i=this.evaluateNodeToWhereClause(e.right,t,r,s,a)," ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" NOT IN ("+i.join(",")+")) "}return o=this.evaluateNodeToWhereClause(e.right,t,r,s,a),o instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" NOT IN ("+o.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,s,a)+" NOT IN ("+o+")) ";case"BETWEEN":return n=this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)," ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" BETWEEN "+n[0]+" AND "+n[1]+" ) ";case"NOTBETWEEN":return n=this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)," ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" NOT BETWEEN "+n[0]+" AND "+n[1]+" ) ";case"LIKE":return""!==e.escape?" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+") ";case"NOT LIKE":return""!==e.escape?" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+") ";case"<>":case"=":if("column_ref"===e.left.type&&"string"===e.right.type){if(e.left.column.toUpperCase()===this.field.name.toUpperCase()&&void 0!==this.lkp[e.right.value.toString()])return" ("+s+" "+e.operator+" "+this.lkp[e.right.value.toString()].toString()+") "}else if("column_ref"===e.right.type&&"string"===e.left.type&&e.right.column.toUpperCase()===this.field.name.toUpperCase())return" ("+this.lkp[e.right.value.toString()].toString()+" "+e.operator+" "+s+") ";return" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+") ";case"<":case">":case">=":case"<=":case"*":case"-":case"+":case"/":return" ("+this.evaluateNodeToWhereClause(e.left,t,r,C.BADNESS,a)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,r,C.BADNESS,a)+") "}throw new Error("Not Supported Operator "+e.operator);case"null":return"null";case"bool":return!0===e.value?"1":"0";case"string":return"'"+e.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return d(e.value,t);case"number":return e.value.toString();case"current_time":return u("date"===e.mode,t);case"column_ref":return r&&r.toLowerCase()===e.column.toLowerCase()?"("+s+")":e.column;case"function":{const s=this.evaluateNodeToWhereClause(e.args,t,r,C.BADNESS,a);return h(e.name,s,t)}}throw new Error("Unsupported sql syntax "+e.type)}extractValue(e){return this.codefield instanceof N?this.reverseLkp[this.codefield.calculateValueCompiled(e)]:this.reverseLkp[e.attributes[this.codefield]]}}C.BADNESS="_!!!_BAD_LKP_!!!!";class T extends m{constructor(e,t){super(e),this.sql=t}rewriteSql(e,t){return{rewritten:!0,where:n(e,this.field.name,o(this.sql,i.Standardised),t.getFieldsIndex())}}extractValue(e){return this.sql.calculateValueCompiled(e)}}class E extends r{constructor(e){super(e),this._calcFunc=null,this.declaredClass="esri.arcade.featureset.actions.Adapted",this.adaptedFields=null,this._extraFilter=null,this._extraFilter=e.extraFilter,this._parent=e.parentfeatureset,this._maxProcessing=30,this.adaptedFields=e.adaptedFields}static findField(e,t){for(const r of e)if(r.name.toLowerCase()===t.toString().toLowerCase())return r;return null}_initialiseFeatureSet(){null!==this._parent?(this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.spatialReference=new S({wkid:4326}),this.objectIdField="",this.globalIdField="",this.geometryType=l.point,this.typeIdField="",this.types=null),this.fields=[];for(const e of this.adaptedFields)e.postInitialization(this,this._parent),this.fields.push(e.field)}_getSet(e){return null===this._wset?this._ensureLoaded().then((()=>this._extraFilter?this._getFilteredSet("",null,null,null,e):this._parent._getSet(e))).then((t=>(this._checkCancelled(e),this._wset=new s(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset))):g(this._wset)}_isInFeatureSet(e){return this._parent._isInFeatureSet(e)}_getFeatures(r,a,i,l){const n=[];-1!==a&&void 0===this._featureCache[a]&&n.push(a);const o=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(r,o))return this._expandPagedSet(r,o,0,0,l).then((()=>this._getFeatures(r,a,i,l)));let h=0;for(let e=r._lastFetchedIndex;e<r._known.length&&(h++,h<=i&&(r._lastFetchedIndex+=1),!(void 0===this._featureCache[r._known[e]]&&(r._known[e]!==a&&n.push(r._known[e]),n.length>=o)));e++);if(0===n.length)return g("success");r=new s([],n,r._ordered,null);const u=Math.min(n.length,i);return this._parent._getFeatures(r,-1,u,l).then((()=>{this._checkCancelled(l);const r=[];for(let e=0;e<u;e++){const t=this._parent._featureFromCache(n[e]);void 0!==t&&r.push({geometry:t.geometry,attributes:t.attributes,id:n[e]})}for(const s of r){const r=[];for(const e of this.adaptedFields)r[e.field.name]=e.extractValue(s);this._featureCache[s.id]=new e({attributes:r,geometry:t(s.geometry)})}return"success"}))}_fetchAndRefineFeatures(){return _(new Error("Fetch and Refine should not be called in this featureset"))}_getFilteredSet(e,t,r,a,i){let l=!1;const n=this._reformulateWithoutAdaptions(r);l=n.cannot,r=n.where;let o=!1;if(null!==a){o=!0;const e=[];for(const t of this.adaptedFields)if(!(t instanceof w)&&!0===a.scanForField(t.field.name)){if(!(t instanceof v)){a=null,o=!1;break}e.push({field:t.field.name,newfield:t.originalField.name})}a&&e.length>0&&(a=a.replaceFields(e))}return null!==r?null!==this._extraFilter&&(r=p(this._extraFilter,r)):r=this._extraFilter,this._ensureLoaded().then((()=>this._parent._getFilteredSet(e,t,r,a,i))).then((e=>{let t;return this._checkCancelled(i),t=!0===l?new s(e._candidates.slice(0).concat(e._known.slice(0)),[],!0===o&&e._ordered,this._clonePageDefinition(e.pagesDefinition)):new s(e._candidates.slice(0),e._known.slice(0),!0===o&&e._ordered,this._clonePageDefinition(e.pagesDefinition)),t}))}_reformulateWithoutAdaptions(e){const t={cannot:!1,where:e};if(null!==e)for(const r of this.adaptedFields)if(!0===f(e,r.field.name)){const s=r.rewriteSql(e,this);if(!0!==s.rewritten){t.cannot=!0,t.where=null;break}t.where=s.where}return t}_stat(e,t,r,s,a,i,l){let n=!1,o=this._reformulateWithoutAdaptions(t);return n=o.cannot,t=o.where,o=this._reformulateWithoutAdaptions(a),n=n||o.cannot,null!==(a=o.where)?null!==this._extraFilter&&(a=p(this._extraFilter,a)):a=this._extraFilter,!0===n?null===a&&""===r&&null===s?this._manualStat(e,t,i,l):g({calculated:!1}):this._parent._stat(e,t,r,s,a,i,l).then((n=>!1===n.calculated?null===a&&""===r&&null===s?this._manualStat(e,t,i,l):{calculated:!1}:n))}_canDoAggregates(e,t,r,s,a){if(null===this._parent)return g(!1);for(let n=0;n<e.length;n++)for(const t of this.adaptedFields)if(e[n].toLowerCase()===t.field.name.toLowerCase()&&!(t instanceof w))return g(!1);const i=[];for(let n=0;n<t.length;n++){const e=t[n];if(null!==e.workingexpr){const t=this._reformulateWithoutAdaptions(e.workingexpr);if(t.cannot)return g(!1);const r=e.clone();r.workingexpr=t.where,i.push(r)}else i.push(e)}const l=this._reformulateWithoutAdaptions(a);return l.cannot?g(!1):(null!==(a=l.where)?null!==this._extraFilter&&(a=p(this._extraFilter,a)):a=this._extraFilter,this._parent._canDoAggregates(e,i,r,s,a))}_getAggregatePagesDataSourceDefinition(e,t,r,s,a,i,l){if(null===this._parent)return _(new Error("Should never be called"));const n=[];for(let h=0;h<t.length;h++){const e=t[h];if(null!==e.workingexpr){const t=this._reformulateWithoutAdaptions(e.workingexpr);if(t.cannot)return _(new Error("Should never be called"));const r=e.clone();r.workingexpr=t.where,n.push(r)}else n.push(e)}const o=this._reformulateWithoutAdaptions(a);return o.cannot?_(new Error("Should never be called")):(null!==(a=o.where)?null!==this._extraFilter&&(a=p(this._extraFilter,a)):a=this._extraFilter,this._parent._getAggregatePagesDataSourceDefinition(e,n,r,s,a,i,l))}}export{E as AdaptedFeatureSet,m as AdaptedField,v as FieldRename,w as OriginalField,T as SqlExpressionAdapted,C as StringToCodeAdapted};
