{"ast": null, "code": "import { getSubScriptions, getSubList, getImgUrl } from '@/api/hzVideo.js';\nimport { getQueryBySubscribed } from \"@/api/userMenu.js\";\nimport { timestampToTime } from '@/utils/timeStamp.js';\nexport default {\n  name: 'eventSubscription',\n  data() {\n    return {\n      eventSubscriptionInfoPop: true,\n      typeSelectList: [],\n      typeList: [{\n        typeName: '人员聚合',\n        eventType: \"99999991\"\n      }, {\n        typeName: '区域入侵',\n        eventType: \"99999992\"\n      }],\n      ActionTypeList: [],\n      tableData: [],\n      timer: null,\n      picUrlList: []\n    };\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.subScriptions();\n      // 看板关闭\n      this.$store.commit('action/getIsScreenShow', false);\n    });\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    // 订阅\n    async subScriptions() {\n      this.getSubLisFun();\n      let event_type_list = [];\n      this.typeList.map(item => {\n        if (!item.isSub) {\n          event_type_list.push(item.eventType);\n        }\n      });\n      if (!event_type_list.length) {\n        let params = {\n          mode: 0,\n          event_type_list,\n          accept_url: 'http://***********:81/api/alarm/receive'\n        };\n        await getSubScriptions(params).then(res => {}).catch(err => {\n          console.log(err);\n        });\n      }\n      this.getWarningList();\n      this.timer = setInterval(() => {\n        this.getWarningList();\n      }, 300000);\n    },\n    // 查询已订阅\n    async getSubLisFun() {\n      await getSubList().then(res => {\n        if (res.status == 200) {\n          let list = res.data.data.event_type_list;\n          list.map(item => {\n            this.typeList.map(item1 => {\n              if (item1.eventType == item) {\n                // 已訂閱\n                item1.isSub = 1;\n              }\n            });\n          });\n        }\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n    // 获取已订阅告警信息\n    getWarningList() {\n      let eventTypes = [];\n      this.typeList.map(item => {\n        eventTypes.push(item.eventType);\n      });\n      getQueryBySubscribed(eventTypes).then(res => {\n        if (res.status == 200) {\n          this.$eventBus.$emit('senTxtToUe', '清除');\n          this.tableData = res.data.data;\n          this.tableData.forEach(item => {\n            item.typename = '事件推送';\n            item.time = timestampToTime(item.alarmTime);\n            if (item.pictureUrl.substring(0, 9) != '/file/mg/') {\n              getImgUrl([item.pictureUrl]).then(res => {\n                if (res.status == 200) {\n                  item.pictureUrl = res.data.data[0];\n                }\n              });\n            } else {\n              item.pictureUrl = 'http://2.36.239.130:11125/' + item.pictureUrl;\n            }\n          });\n          let params = {\n            name: '事件告警',\n            flag: true,\n            array: this.tableData\n          };\n          this.$eventBus.$emit('senTxtToUe', params);\n        }\n      });\n    },\n    handleClick(node) {\n      node.typename = '事件推送';\n      let params = {\n        name: '事件告警',\n        flag: true,\n        array: [node]\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n      this.closeFun();\n    },\n    closeFun() {\n      // 关闭事件推送弹窗\n      this.$store.commit('action/getEventSubBoxFlag', false);\n    }\n  },\n  created() {\n    document.cookie = \"usercode=01\";\n  },\n  computed: {},\n  watch: {}\n};", "map": {"version": 3, "names": ["getSubScriptions", "getSubList", "getImgUrl", "getQueryBySubscribed", "timestampToTime", "name", "data", "eventSubscriptionInfoPop", "typeSelectList", "typeList", "typeName", "eventType", "ActionTypeList", "tableData", "timer", "picUrlList", "mounted", "$nextTick", "subScriptions", "$store", "commit", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "getSubLisFun", "event_type_list", "map", "item", "isSub", "push", "length", "params", "mode", "accept_url", "then", "res", "catch", "err", "console", "log", "getWarningList", "setInterval", "status", "list", "item1", "eventTypes", "$eventBus", "$emit", "for<PERSON>ach", "typename", "time", "alarmTime", "pictureUrl", "substring", "flag", "array", "handleClick", "node", "closeFun", "created", "document", "cookie", "computed", "watch"], "sources": ["src/components/eventSubscription.vue"], "sourcesContent": ["<template>\r\n    <div class=\"eventSubscriptionCon boxBgStyle\">\r\n        <div class=\"box-card\">\r\n            <div class=\"topMain\">\r\n                <div class=\"titleTxt\">\r\n                    <div class=\"txt\">事件推送</div>\r\n                </div>\r\n                <div class=\"delete\" @click=\"closeFun\">\r\n                    <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n                </div>\r\n              \r\n            </div>\r\n            <div class=\"TypeShow\">\r\n                \r\n            </div>\r\n            <div class=\"tableStyle\">\r\n\r\n                <div class=\"item_one\" v-for=\"item of tableData\" :key=\"item.id\">\r\n                    <div class=\"itemTit flex\">\r\n                        <div class=\"left flex\">\r\n                            <div class=\"pic\">\r\n                                <img :src=\"item.pictureUrl\" alt=\"\" width=\"100%\">\r\n                            </div>\r\n                            <div class=\"txt\">\r\n                                {{ item.alarmLevelName }}\r\n                            </div>\r\n\r\n                        </div>\r\n                        <div class=\"right\" @click=\"handleClick(item)\">\r\n                            <img src=\"@/assets/images/mainPics/i_points.png\" alt=\"\" width=\"100%\">\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"item_content\">\r\n                        <div class=\"item\">\r\n                            <span>报警时间： </span>\r\n                            <span> {{ item.time }}</span>\r\n                        </div>\r\n                        <div class=\"item\">\r\n                            <span>报警类型： </span>\r\n                            <span> {{ item.alarmTypeName }}</span>\r\n                        </div>\r\n\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n        </div>\r\n\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nimport { getSubScriptions, getSubList, getImgUrl } from '@/api/hzVideo.js'\r\nimport { getQueryBySubscribed } from \"@/api/userMenu.js\"\r\nimport { timestampToTime } from '@/utils/timeStamp.js'\r\nexport default {\r\n    name: 'eventSubscription',\r\n    data() {\r\n        return {\r\n            eventSubscriptionInfoPop: true,\r\n            typeSelectList: [],\r\n            typeList: [{\r\n                typeName: '人员聚合',\r\n                eventType: \"99999991\"\r\n            }, {\r\n                typeName: '区域入侵',\r\n                eventType: \"99999992\"\r\n            }],\r\n            ActionTypeList: [],\r\n            tableData: [],\r\n            timer: null,\r\n            picUrlList: [],\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n\r\n        this.$nextTick(() => {\r\n            this.subScriptions()\r\n            // 看板关闭\r\n            this.$store.commit('action/getIsScreenShow', false)\r\n        })\r\n    },\r\n    beforeDestroy() {\r\n        if (this.timer) {\r\n            clearInterval(this.timer)\r\n\r\n        }\r\n    },\r\n    methods: {\r\n\r\n        // 订阅\r\n        async subScriptions() {\r\n            this.getSubLisFun()\r\n            let event_type_list = []\r\n            this.typeList.map(item => {\r\n                if (!item.isSub) {\r\n                    event_type_list.push(item.eventType)\r\n                }\r\n            })\r\n            if (!event_type_list.length) {\r\n                let params = {\r\n                    mode: 0,\r\n                    event_type_list,\r\n                    accept_url: 'http://***********:81/api/alarm/receive'\r\n                }\r\n                await getSubScriptions(params).then(res => {\r\n                }).catch(err => {\r\n                    console.log(err)\r\n                })\r\n            }\r\n            this.getWarningList()\r\n            this.timer = setInterval(() => {\r\n                this.getWarningList()\r\n            }, 300000)\r\n\r\n        },\r\n        // 查询已订阅\r\n        async getSubLisFun() {\r\n            await getSubList().then(res => {\r\n                if (res.status == 200) {\r\n                    let list = res.data.data.event_type_list\r\n                    list.map(item => {\r\n                        this.typeList.map(item1 => {\r\n                            if (item1.eventType == item) {\r\n                                // 已訂閱\r\n                                item1.isSub = 1\r\n\r\n                            }\r\n                        })\r\n                    })\r\n                }\r\n            }).catch(err => {\r\n                console.log(err)\r\n            })\r\n        },\r\n\r\n        // 获取已订阅告警信息\r\n        getWarningList() {\r\n            let eventTypes = [];\r\n            this.typeList.map(item => {\r\n                eventTypes.push(item.eventType)\r\n            })\r\n            getQueryBySubscribed(eventTypes).then(res => {\r\n                if (res.status == 200) {\r\n                    this.$eventBus.$emit('senTxtToUe', '清除')\r\n                    this.tableData = res.data.data\r\n                    this.tableData.forEach(item => {\r\n                        item.typename = '事件推送'\r\n                        item.time = timestampToTime(item.alarmTime)\r\n                        if (item.pictureUrl.substring(0, 9) != '/file/mg/') {\r\n                            getImgUrl([item.pictureUrl]).then(res => {\r\n                                if (res.status == 200) {\r\n                                    item.pictureUrl = res.data.data[0]\r\n                                }\r\n                            })\r\n                        } else {\r\n                            item.pictureUrl = 'http://2.36.239.130:11125/' + item.pictureUrl\r\n                        }\r\n\r\n                    })\r\n                    let params = {\r\n                        name: '事件告警',\r\n                        flag: true,\r\n                        array: this.tableData\r\n                    }\r\n                    this.$eventBus.$emit('senTxtToUe', params)\r\n                }\r\n            })\r\n        },\r\n        handleClick(node) {\r\n            node.typename = '事件推送'\r\n            let params = {\r\n                name: '事件告警',\r\n                flag: true,\r\n                array: [node],\r\n            }\r\n\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n\r\n            this.closeFun()\r\n        },\r\n        closeFun() {\r\n\r\n            // 关闭事件推送弹窗\r\n            this.$store.commit('action/getEventSubBoxFlag', false)\r\n\r\n        },\r\n\r\n    },\r\n    created() {\r\n        document.cookie = \"usercode=01\"\r\n    },\r\n    computed: {\r\n\r\n    },\r\n    watch: {\r\n\r\n    },\r\n}\r\n\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.eventSubscriptionCon {\r\n    pointer-events: stroke;\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 100;\r\n\r\n    .topMain {\r\n        .titleTxt {\r\n            width: 300px;\r\n            position: relative;\r\n            left: -60px;\r\n            text-align: right;\r\n            height: 100px;\r\n            line-height: 100px;\r\n            font-size: 38px;\r\n            background: url('@/assets/images/mainPics/i_title.png') no-repeat 100%;\r\n            background-size: 100% 100%;\r\n            text-align: center;\r\n\r\n        }\r\n\r\n        .delete {\r\n            width: 30px;\r\n\r\n            img {\r\n                width: 100%;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.flex {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n\r\n.tableStyle {\r\n    width: 700px;\r\n    max-height: 1200px;\r\n\r\n    font-size: 25px;\r\n    padding: 0 20px;\r\n    overflow-y: auto;\r\n\r\n    .item_one {\r\n        padding: 10px 0;\r\n        border-bottom: 2px solid #ccc;\r\n    }\r\n\r\n    .itemTit {\r\n        font-size: 35px;\r\n        margin: 15px 0;\r\n\r\n        .pic {\r\n            width: 250px;\r\n            margin-right: 20px;\r\n        }\r\n\r\n        .right {\r\n            width: 40px;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n\r\n    .item_content {\r\n\r\n        .item {\r\n            padding: 10px 0;\r\n\r\n            span:nth-child(1) {\r\n                color: #ccc;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\n.box-card {\r\n    color: #fff;\r\n    width: fit-content;\r\n    // background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n    // background-size: 100% 100%;\r\n    border: 0;\r\n\r\n\r\n    .peoplePerShow {\r\n        display: flex;\r\n\r\n        .totalHouses {\r\n            margin-right: 20px;\r\n            line-height: 50px;\r\n        }\r\n    }\r\n\r\n    .TypeShow {\r\n        display: flex;\r\n\r\n        .TypeItem {\r\n            margin: 0 5px;\r\n            cursor: pointer;\r\n            padding: 5px 10px;\r\n            // box-shadow: 0 0 5px rgb(102, 177, 255), 0 0 5px #fff;\r\n            user-select: none;\r\n        }\r\n\r\n        .typeSelect_list {\r\n            display: flex;\r\n        }\r\n\r\n        .TypeItemAction {\r\n            color: #fff;\r\n            // border: 2px solid rgb(116, 164, 216);\r\n            padding: 30px 0;\r\n            font-size: 25px;\r\n            text-shadow: 0 0 5px rgb(46, 116, 192), 0 0 5px rgb(46, 116, 192);\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAqDA,SAAAA,gBAAA,EAAAC,UAAA,EAAAC,SAAA;AACA,SAAAC,oBAAA;AACA,SAAAC,eAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,wBAAA;MACAC,cAAA;MACAC,QAAA;QACAC,QAAA;QACAC,SAAA;MACA;QACAD,QAAA;QACAC,SAAA;MACA;MACAC,cAAA;MACAC,SAAA;MACAC,KAAA;MACAC,UAAA;IACA;EACA;EAEAC,QAAA;IAEA,KAAAC,SAAA;MACA,KAAAC,aAAA;MACA;MACA,KAAAC,MAAA,CAAAC,MAAA;IACA;EACA;EACAC,cAAA;IACA,SAAAP,KAAA;MACAQ,aAAA,MAAAR,KAAA;IAEA;EACA;EACAS,OAAA;IAEA;IACA,MAAAL,cAAA;MACA,KAAAM,YAAA;MACA,IAAAC,eAAA;MACA,KAAAhB,QAAA,CAAAiB,GAAA,CAAAC,IAAA;QACA,KAAAA,IAAA,CAAAC,KAAA;UACAH,eAAA,CAAAI,IAAA,CAAAF,IAAA,CAAAhB,SAAA;QACA;MACA;MACA,KAAAc,eAAA,CAAAK,MAAA;QACA,IAAAC,MAAA;UACAC,IAAA;UACAP,eAAA;UACAQ,UAAA;QACA;QACA,MAAAjC,gBAAA,CAAA+B,MAAA,EAAAG,IAAA,CAAAC,GAAA,KACA,GAAAC,KAAA,CAAAC,GAAA;UACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA;MACA;MACA,KAAAG,cAAA;MACA,KAAA1B,KAAA,GAAA2B,WAAA;QACA,KAAAD,cAAA;MACA;IAEA;IACA;IACA,MAAAhB,aAAA;MACA,MAAAvB,UAAA,GAAAiC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAO,MAAA;UACA,IAAAC,IAAA,GAAAR,GAAA,CAAA7B,IAAA,CAAAA,IAAA,CAAAmB,eAAA;UACAkB,IAAA,CAAAjB,GAAA,CAAAC,IAAA;YACA,KAAAlB,QAAA,CAAAiB,GAAA,CAAAkB,KAAA;cACA,IAAAA,KAAA,CAAAjC,SAAA,IAAAgB,IAAA;gBACA;gBACAiB,KAAA,CAAAhB,KAAA;cAEA;YACA;UACA;QACA;MACA,GAAAQ,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;IAEA;IACAG,eAAA;MACA,IAAAK,UAAA;MACA,KAAApC,QAAA,CAAAiB,GAAA,CAAAC,IAAA;QACAkB,UAAA,CAAAhB,IAAA,CAAAF,IAAA,CAAAhB,SAAA;MACA;MACAR,oBAAA,CAAA0C,UAAA,EAAAX,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAO,MAAA;UACA,KAAAI,SAAA,CAAAC,KAAA;UACA,KAAAlC,SAAA,GAAAsB,GAAA,CAAA7B,IAAA,CAAAA,IAAA;UACA,KAAAO,SAAA,CAAAmC,OAAA,CAAArB,IAAA;YACAA,IAAA,CAAAsB,QAAA;YACAtB,IAAA,CAAAuB,IAAA,GAAA9C,eAAA,CAAAuB,IAAA,CAAAwB,SAAA;YACA,IAAAxB,IAAA,CAAAyB,UAAA,CAAAC,SAAA;cACAnD,SAAA,EAAAyB,IAAA,CAAAyB,UAAA,GAAAlB,IAAA,CAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAO,MAAA;kBACAf,IAAA,CAAAyB,UAAA,GAAAjB,GAAA,CAAA7B,IAAA,CAAAA,IAAA;gBACA;cACA;YACA;cACAqB,IAAA,CAAAyB,UAAA,kCAAAzB,IAAA,CAAAyB,UAAA;YACA;UAEA;UACA,IAAArB,MAAA;YACA1B,IAAA;YACAiD,IAAA;YACAC,KAAA,OAAA1C;UACA;UACA,KAAAiC,SAAA,CAAAC,KAAA,eAAAhB,MAAA;QACA;MACA;IACA;IACAyB,YAAAC,IAAA;MACAA,IAAA,CAAAR,QAAA;MACA,IAAAlB,MAAA;QACA1B,IAAA;QACAiD,IAAA;QACAC,KAAA,GAAAE,IAAA;MACA;MAEA,KAAAX,SAAA,CAAAC,KAAA,eAAAhB,MAAA;MAEA,KAAA2B,QAAA;IACA;IACAA,SAAA;MAEA;MACA,KAAAvC,MAAA,CAAAC,MAAA;IAEA;EAEA;EACAuC,QAAA;IACAC,QAAA,CAAAC,MAAA;EACA;EACAC,QAAA,GAEA;EACAC,KAAA,GAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}