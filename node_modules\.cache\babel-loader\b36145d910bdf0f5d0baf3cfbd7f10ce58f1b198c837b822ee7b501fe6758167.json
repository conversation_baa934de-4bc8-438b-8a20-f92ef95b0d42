{"ast": null, "code": "// 实时诉求\nimport peopleDataRealLeft from \"./components/people-realtime/left-realtime.vue\";\n// 数据分析\nimport peopleDataLeft from \"./components/people-data/left-datapanel.vue\";\nimport peopleDataRealRight from \"./components/people-data/right-analyze.vue\";\nimport { mapState } from 'vuex';\nexport default {\n  name: '',\n  data() {\n    return {\n      leftPanel: true,\n      rightPanel: false,\n      rightText: '',\n      leftView: peopleDataRealLeft,\n      rightView: null\n    };\n  },\n  components: {\n    peopleDataRealRight\n  },\n  watch: {\n    // 监听tabs切换组件\n    footerTbsItem: {\n      handler(nv) {\n        this.leftPanel = true;\n        this.rightPanel = false;\n        this.$store.commit('action/getEventSubBoxFlag', false);\n        switch (nv.item) {\n          case \"民生诉求\":\n            this.leftView = peopleDataRealLeft;\n            this.rightView = null;\n            this.rightText = '';\n            break;\n          case \"实时诉求\":\n            this.leftView = peopleDataRealLeft;\n            this.rightView = null;\n            this.rightText = '';\n            break;\n          case \"数据分析\":\n            this.leftView = peopleDataLeft;\n            this.rightView = peopleDataRealRight;\n            this.rightText = '诉求分析';\n            break;\n        }\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    ...mapState({\n      footerTbsItem: state => state.footerTbsItem\n    })\n  },\n  mounted() {},\n  updated() {},\n  methods: {\n    isShowView(type) {\n      if (type == 1) {\n        this.$store.commit('action/getEventSubBoxFlag', false);\n        this.leftPanel = !this.leftPanel;\n      } else {\n        this.rightPanel = !this.rightPanel;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["peopleDataRealLeft", "peopleDataLeft", "peopleDataRealRight", "mapState", "name", "data", "leftPanel", "rightPanel", "rightText", "leftView", "<PERSON><PERSON><PERSON><PERSON>", "components", "watch", "footerTbsItem", "handler", "nv", "$store", "commit", "item", "immediate", "computed", "state", "mounted", "updated", "methods", "isShowView", "type"], "sources": ["src/views/peopleLivelihood/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <div class=\"left-box\" :style=\"{ left: leftPanel ? '20px' : '-750px' }\">\r\n            <component :is=\"leftView\"></component>\r\n            <img v-if=\"leftPanel\" @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/left-btn.png\"\r\n                alt=\"\">\r\n            <img v-else @click=\"isShowView(1)\" src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n        </div>\r\n        <div class=\"right-box\" v-if=\"rightView\" :style=\"{ right: rightPanel ? '20px' : ' -750px' }\">\r\n            <component :is=\"rightView\"></component>\r\n            <p v-if=\"rightPanel\" @click=\"isShowView(2)\">\r\n                <!-- <span>{{ rightText }}</span> -->\r\n                <img src=\"@/assets/images/comprehensiveSituation/right-btn.png\" alt=\"\">\r\n            </p>\r\n            <p v-else @click=\"isShowView(2)\">\r\n                <span>{{ rightText }}</span>\r\n                <img src=\"@/assets/images/comprehensiveSituation/long-left-btn.png\" alt=\"\">\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n// 实时诉求\r\nimport peopleDataRealLeft from \"./components/people-realtime/left-realtime.vue\"\r\n// 数据分析\r\nimport peopleDataLeft from \"./components/people-data/left-datapanel.vue\"\r\nimport peopleDataRealRight from \"./components/people-data/right-analyze.vue\"\r\n\r\n\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: '',\r\n    data() {\r\n        return {\r\n            leftPanel: true,\r\n            rightPanel: false,\r\n            rightText: '',\r\n            leftView: peopleDataRealLeft,\r\n            rightView: null,\r\n        };\r\n    },\r\n\r\n    components: {\r\n        peopleDataRealRight\r\n    },\r\n    watch: {\r\n        // 监听tabs切换组件\r\n        footerTbsItem: {\r\n            handler(nv) {\r\n                this.leftPanel = true;\r\n                this.rightPanel = false;\r\n                this.$store.commit('action/getEventSubBoxFlag', false)\r\n                switch (nv.item) {\r\n                    case \"民生诉求\":\r\n                        this.leftView = peopleDataRealLeft;\r\n                        this.rightView = null;\r\n                        this.rightText = ''\r\n                        break;\r\n                    case \"实时诉求\":\r\n                        this.leftView = peopleDataRealLeft;\r\n                        this.rightView = null;\r\n                        this.rightText = ''\r\n                        break;\r\n                    case \"数据分析\":\r\n                        this.leftView = peopleDataLeft;\r\n                        this.rightView = peopleDataRealRight;\r\n                        this.rightText = '诉求分析'\r\n                        break;\r\n                }\r\n            },\r\n            immediate: true\r\n\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            footerTbsItem: state => state.footerTbsItem,\r\n        }),\r\n    },\r\n\r\n    mounted() {\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        isShowView(type) {\r\n            if (type == 1) {\r\n                this.$store.commit('action/getEventSubBoxFlag', false)\r\n                this.leftPanel = !this.leftPanel;\r\n            } else {\r\n                this.rightPanel = !this.rightPanel;\r\n\r\n            }\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.container {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n\r\n    .left-box {\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        width: 750px;\r\n        height: 1960px;\r\n        position: fixed;\r\n        background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        left: 20px;\r\n\r\n        >img {\r\n            cursor: pointer;\r\n            width: 103px;\r\n            height: 93px;\r\n            position: absolute;\r\n            left: 750px;\r\n            top: calc(50% - 46.5px);\r\n\r\n        }\r\n    }\r\n\r\n    .right-box {\r\n        // margin-right: 20px;\r\n        width: 750px;\r\n        height: 1960px;\r\n        transition: all 0.5s;\r\n        pointer-events: stroke;\r\n        position: fixed;\r\n        background-color: rgba(18, 76, 111, .7);\r\n        box-sizing: border-box;\r\n        right: 20px;\r\n\r\n        >p {\r\n            width: 103px;\r\n            height: 338px;\r\n            position: absolute;\r\n            right: 750px;\r\n            top: calc(50% - 169px);\r\n            cursor: pointer;\r\n\r\n            >span {\r\n                width: 40px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 48px;\r\n                color: #FFFFFF;\r\n                line-height: 56px;\r\n                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\r\n                position: absolute;\r\n                top: 96px;\r\n                left: 28px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAwBA;AACA,OAAAA,kBAAA;AACA;AACA,OAAAC,cAAA;AACA,OAAAC,mBAAA;AAGA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA,EAAAT,kBAAA;MACAU,SAAA;IACA;EACA;EAEAC,UAAA;IACAT;EACA;EACAU,KAAA;IACA;IACAC,aAAA;MACAC,QAAAC,EAAA;QACA,KAAAT,SAAA;QACA,KAAAC,UAAA;QACA,KAAAS,MAAA,CAAAC,MAAA;QACA,QAAAF,EAAA,CAAAG,IAAA;UACA;YACA,KAAAT,QAAA,GAAAT,kBAAA;YACA,KAAAU,SAAA;YACA,KAAAF,SAAA;YACA;UACA;YACA,KAAAC,QAAA,GAAAT,kBAAA;YACA,KAAAU,SAAA;YACA,KAAAF,SAAA;YACA;UACA;YACA,KAAAC,QAAA,GAAAR,cAAA;YACA,KAAAS,SAAA,GAAAR,mBAAA;YACA,KAAAM,SAAA;YACA;QACA;MACA;MACAW,SAAA;IAEA;EACA;EACAC,QAAA;IACA,GAAAjB,QAAA;MACAU,aAAA,EAAAQ,KAAA,IAAAA,KAAA,CAAAR;IACA;EACA;EAEAS,QAAA,GACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,WAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAV,MAAA,CAAAC,MAAA;QACA,KAAAX,SAAA,SAAAA,SAAA;MACA;QACA,KAAAC,UAAA,SAAAA,UAAA;MAEA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}