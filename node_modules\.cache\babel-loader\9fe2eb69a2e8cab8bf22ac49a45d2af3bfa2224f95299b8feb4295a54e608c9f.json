{"ast": null, "code": "import request from '@/utils/api/getApi.js';\n// // 多发时间预警\n// export function getEventWarnings() {\n//     return request.post('/sanTi/getMultipleEventsList', {\n//         appId: '445d748d-91fd-48a5-a1s5-6dbr14b63fb9',\n//         aseKey: 'NhaI3LjyMOGLXfLu3wFLKxDPr1vkgQ4Wze-ewZD3pPmVA-lHizFyjpMa666WWB0BwG5aq8W1nEtGjhpzQxJMhA_9EWWvEZDiiXUXHHFnbh3o9ALqB4w95ETuRk8X5K4FLf-',\n//         body: '8e7decb9b81f08e136212e946424c57b',\n//         sign: 'DtRbhg6RrME7tSu25a+PwcfQJB+TikYN5gPOwiXZB9d0wyusKIpyiT9JEEh',\n//         timestamp: '1686295492909'\n//     })\n// }\n\n// 小区排行推送\nexport function getCommunityVolume() {\n  return request.post('/za/getCommunityVolumeList', {});\n}\n// 智慧停车指数\nexport function getSmartParking() {\n  return request.post('/za/getSmartParkingList', {});\n}\n// 物联设备\nexport function getDevicesAll() {\n  return request.post('/za/getDevicesAll', {\n    \"appId\": \"4299740b47a84e8fb820a4c5e7ba9a0b\"\n  });\n}\n// 网管液位点位告警及处理状态表\nexport function AlarmProcessingList() {\n  return request.post('/za/getPipeNetworkAlarmProcessingList', {\n    \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\n    \"aseKey\": \"AE3Cug8aXuvfN406Dw_kqQMwCft3KKitmEu-dE0hcHAIokD0xA6Tv-iWaWtFE2YBSzqvhbD5AsHNLG4T1gjLogPwHCdkghaAkZ9hNP2CGniFYh8ONr7-2PwIV7hFyMflijKca42R6IXmbFvjskuBN-gPN-i1gVXSSPyD2ItBrWE5dBCZ0wKnaC4yPrgPqHkOrYSrsKNg-39jxZqLb86QGUugXXIwEm4Lldz8_PIhP0zQQXGhnTYviyy-n2_VDKptzlAUKSdZZHjLSsRfHO-ESfbDVuY7N4DJZhX7E_iHppIRMbmjeaU3vHAIvDNf0H8m_bzKIER2OC6FmUD2sq-Oig\",\n    \"body\": \"9229f01e6f2ce25120051124d398663d\",\n    \"sign\": \"Qjhzggbf3RTVXYitnvtwiTS1mKr90+7uWn/MCKlOCTWnX02MhFrUJRPc17ezsp0CkyGxA8AOt4ltCFwbHHs3cL+9xH1pyoSJF+wGPKt6tKxTywVxw73HC0sEIhCjioAxn/SNvSwTHlmbZ7OE17KLpKMnDHNK6GUBqqDVllGQvOgbeIoIrB5iqng1bLLoex/xnFMzvZ5HUDo+YeNGy6J03vSoK8AQ7iY0I8YH8x27kxe93DGti6nyTKm8GM24g31cSKaDbYRhyE/6eRVZWxom5iaZvXw1dfSbfQG2Xsyn5/90n7chdxkoP8RRrABMN00Qh4GMgkb1N9249nPE9X8pBQ==\",\n    \"timestamp\": 1686281554676\n  });\n}\n// // 辖区诉求量\n// export function getAppealsList() {\n//     return request.post('/sanTi/getCommunityProcessingList', {})\n// }\n\n//半年投诉事件趋势\nexport function getHalfComplaintList() {\n  return request.post('/za/getComplaints', {});\n}\n// 管网液位预警\nexport function getWaterLevelWarning() {\n  // return request.post('/za/getGuanwangLevelWarningList', {})\n  return request.post('/za/getWaterSewageHistoryList', {});\n}\n// // 部门办件量\n// export function getSectorVolumeList() {\n//     return request.post('/sanTi/getSectorVolume', {\n//         \"appId\": \"64b33cd45fc8428cb9c37a1aadf934c4\",\n//         \"body\": \"{\\\"year\\\":\\\"2022\\\"}\"\n//     })\n// }\n\n// 办件指数\nexport function getTakeAmountNum() {\n  return request.post('/za/getAdminApprovalTakeamountnumslist', {});\n}\n// // 办件前五部门\n// export function getTakeAmountDepartment() {\n//     return request.post('/sanTi/getSectorVolume', {})\n// }\n\n// 水质检测\nexport function getWaterQualityMonitoringList() {\n  return request.post('/za/getWaterQualityMonitoringList', {\n    \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\n    \"aseKey\": \"L50yntbMA7-j7ouTE-mgRnlV1SQF1LXX3vzToY97L2hnD64twdFuBA0j1pXNFWXvbXi1wv6BYKexjgQpB73RGskOTsAbeBP0GJ-_rULtnWqajfAglqaYGHGadtgZY1JV4bGApjmRwb7vlaipAUZu0Yd8QkUIPdxI6KAR_XEl9wssB9ITY-ZikXZNbcRFSbPVZGrHkFmmp369DtBMxlILDB__kLLj1zRiMaKdOzeW8QtswG85Ag1aU9J6JEVXMeMWfDn_ZgPShY6YvX1Cr2DDl4tltfotyAYDTi_piYaOAU7m688Bx2bCPs6TF3pa6BN7CQEld6kPGKthuSXyNrOfCg\",\n    \"body\": \"22e4a3ade651661cab4050c8b5269165\",\n    \"sign\": \"JJZ0uAv+WbD2v0/7ptE5blMhI4qWe3oKffyzsD+wrP6KN6TTNmJ75nWzph1Wx2HeDkyVmdRBgsquHc7TTvHjf2lzODe+6d2dIH4bF7m70FCJ9oFNu38IqZ+SF+Jpuv53PQqc57EsopIrg2PzbwBd9zekvStvNTQo8izTCycPzElC2RXnIfX7IU3nTR5uBQkhryqpTuY+Xv2jjrhrfUSGKvhdWc4Io992zE6b57Iqt3A6Jou/tzN0T7Iv7c/EwYMBHCRWWi2pJC0gyIufEzIEM+DKaeGiA6XHdvsfAhR7ZC5cTk26KcbWbKuea7XlXM81+ksOumbgWXZ2UaMKNdUaIg==\",\n    \"timestamp\": 1686298102199\n  });\n}\n// 每日诉求量\nexport function getDailyDemand() {\n  return request.post('/za/getDailyDemandList', {\n    \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\n    \"aseKey\": \"iiynFKRRcMKS_i3FHDIG6wWus8Go-SZFNm6y0GZ4sx_ImIj8AYcQC9_b_2ztdlatEHrNVztfRcg-ciR-LLDYdY5dkG17uQzpq_wXOsgXGHT1UNqRpjaR80SlrDEcUBud5Cajj31NOD7A1TtPvGhQo52DL7-RjVTCJHzzMcMUBtetUEFdtfoPSjx8rIyFQcWo2UchzvBFHVOL5Slye1TcrHISp303iksTPEWdt0THPX3wMr8YEk6M1xUQY_N_q62AIuWIMaKywqJz6NjZ7NQd9qz69MmeOwp4aXBMMGDIxHVYfz3vtVEDSgYRA-4l3cJlvxZTRBUJ1E2uNaLgbalTAw\",\n    \"body\": \"d9d4c738aefc33ed2bd41b9275c6f5b1\",\n    \"sign\": \"BG9FbbDMTJUzVl7BFerHo/2dF2timDCp//l+yijFM9/LnYbSyYVNlvfSpI5I7fkipICiJvqp0K0cvN3h8WbxST93iKgHTzi0zppMRiyjAlMbXl5mL62NQvPhUmN85OvBLIlzmYS7mFcqtr4Ys2GFTLPIzuMUaM44ykzjMWU2+mH3aqZtX3ma24KN2q1v+Cs4LBBBF6com8cfC8tGNPAe2DFoVosGuV+OIPlBAi5XDLs9M8ElTMXcy6WAVKnSn4aZATgt1zcR78Mf2oiT0cLFU14Fgwp5URxJ8eOeyuxSWrhs9iIZGlqXvK4xnkE1YbCddlNYONxKHOjPNs5HU3bIPg==\",\n    \"timestamp\": 1685413859380\n  });\n}\n// 小区隐患上报\nexport function getHiddenDangerCommunity() {\n  return request.post('/za/getFireAnalysisHiddenDangerCommunityList', {});\n}\n// 隐患上报-统计分析\nexport function getHiddenDangerCountList() {\n  return request.post('/za/getFireAnalysisHiddenDangerCountList', {});\n}\n// 采集数据\nexport function getMonitorCount() {\n  return request.post('/za/getMonitorCount', {});\n}\n// 地区生产值实际情况\nexport function getGdpCompletionList() {\n  return request.post('/za/getGdpCompletionList', {});\n}\n// 设备故障与维修情况\nexport function getFireFacilityCountList() {\n  return request.post('/za/getFireAnalysisFacilityFailReplayCountList', {});\n}\nexport function getPmScoreList() {\n  return request.post('/za/getPmScoreList', {});\n}\n\n// 33行动\nexport function getWaterThreeBigDataActionList() {\n  return request.post('/za/getWaterThreeBigDataActionList', {});\n}\n\n// 民生诉求工单列表\nexport function getWorkOrderList() {\n  return request.post('/za/getWorkOrderAll', {\n    \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\n    \"aseKey\": \"LGP_ES_GHwgDQFg3X2WsjAu4rMjzLWiqutu1MkpoLtKneDLEJns4aod2r4gUwm3fsRd3yI6G7LxExi4i17HCct_48N8prS2ZFdD8W_-vxI5eLhLDft4GtLNHqJik9YZCteQItTWnUjy9jAEZywN_08CfahzRZijnAqV0G8zuxOB-hE0FeLe0wHqzq0EcZQhDmupK8QeZhWu9GnGF12MFn3oRKzykxr4MICVEGLU6oI3BilX9CJ9CkhhzYkABd2CDX86bsyBDsDqXdQIpT0KvPYeVILpfM2_vkb5nn5Zg1Y3mh1yizXQePIguBzHjOEao6xOiERXSVVcoE7PECat62w\",\n    \"body\": \"9bae2bed020e6ef46f6225d1b9b1a945\",\n    \"sign\": \"QgjGF87rTPyv2Gowqv/qE0Hon9zGmD2U4+zquV26Vv4BugLqgU3ECN7vbsaITjS9JuRk0wLN4bGxeBZOQD3+JZiKzB+qXlZfiS2n0kg/ApZd5G9XI9hcTfhilyglEHMnDgNhTxXNuX2h42OOfAJnIiEch9iMA9Lf86Jq3Ta5Js6h+lR8c8hqUo60XYOPkyw6EvWjWbm4REH2IQ4GpqDNS3spkfZTjp4VJ8OSRJfJlq+9W3ow0odJZT+PlATNQPZKsXHOk4aUQr7mKaHIACkUXs5e9G+RdOnZ0BiPRE5BpOJy+/Q8DhbBlFvC+Jv6aPX4aLboEYS1BHIa1uorzKqoJw==\",\n    \"timestamp\": 1686289547004\n  });\n}\n\n// 一体化智能杆\nexport function IntegratedIntelligentRod() {\n  return request.post('/za/getAllInOneSmartPoleList', {\n    \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\n    \"aseKey\": \"gSzHTK1mXEBu5coXur-tmI6dcLuwQAdDX4PO5JEJ6osvoIOyJByoy1xHjjO-QqIVts3fUndJfUoNarVQCEfF_GtqtcxNFv-7VV-XxBb0Nl_-4oth6zk1bnZCmYQqe9K-759qJZP4ZcNiL7F7fs65Fd0t34o3RXhGBdW6rayoRSab_X3nuHgvSmowdZl0KG2WjsiiH48GffQYMaFEc6nrXRVXhLY9UnnEsJfOgsM-9FnvHxss0-O7zCu0fv8J1ROf25aRQyfV6U8i0UINvV2UQutAYleWk9k7YB-Oe_Y_KHg-kb46vvZEnwMgImzJ83UfnAngdO9j_QstbI77VJlEjw\",\n    \"body\": \"c50f7684aa4336abde4417d1cce72743\",\n    \"sign\": \"UWy/dV3+a16j7AkahmwJRqgLXHUnqgJ94JqU45RU3C79Z24AdW+QLrV70o91J7zIzDXDKAsLtISqe4BhPhtIPXRnr3DqGSePnfcI+d7+o/CelBKgilv3azs1Bj/9cMAXFrXdqJdhi8bFJsW3BuTqkiCeCxn/XE2jTH0UEwXZgzSJfUDV4x3k+dmE1kkYSIXRSo569XeL4D6AskEEBCHacrU1/RUWwW6eJvr4TWyNFb6+2+RKe8e9yy+eq63JsiwfWqC+BW04vVT2nHIzLwbViFSsnVA1J4bsYKtWBKxFhh3f93e/JX0+Fcqs6/Yw4uHaC48SeFRGha+uf+TMeuTrNw==\",\n    \"timestamp\": 1686534294351\n  });\n}\n// // 数字城管网格化\n// export function getDigitalCityRidsData() {\n//     return request.post('/sanTi/getResidentialQuarterVolumeList', {})\n// }\n\n// 烟感设备\nexport function getDevices() {\n  return request.post('/za/getDevices', {});\n}", "map": {"version": 3, "names": ["request", "getCommunityVolume", "post", "getSmartParking", "getDevicesAll", "AlarmProcessingList", "getHalfComplaintList", "getWaterLevelWarning", "getTakeAmountNum", "getWaterQualityMonitoringList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getHiddenDangerCommunity", "getHiddenDangerCountList", "getMonitorCount", "getGdpCompletionList", "getFireFacilityCountList", "getPmScoreList", "getWaterThreeBigDataActionList", "getWorkOrderList", "IntegratedIntelligentRod", "getDevices"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/bigScreen.js"], "sourcesContent": ["import request from '@/utils/api/getApi.js'\r\n// // 多发时间预警\r\n// export function getEventWarnings() {\r\n//     return request.post('/sanTi/getMultipleEventsList', {\r\n//         appId: '445d748d-91fd-48a5-a1s5-6dbr14b63fb9',\r\n//         aseKey: 'NhaI3LjyMOGLXfLu3wFLKxDPr1vkgQ4Wze-ewZD3pPmVA-lHizFyjpMa666WWB0BwG5aq8W1nEtGjhpzQxJMhA_9EWWvEZDiiXUXHHFnbh3o9ALqB4w95ETuRk8X5K4FLf-',\r\n//         body: '8e7decb9b81f08e136212e946424c57b',\r\n//         sign: 'DtRbhg6RrME7tSu25a+PwcfQJB+TikYN5gPOwiXZB9d0wyusKIpyiT9JEEh',\r\n//         timestamp: '1686295492909'\r\n//     })\r\n// }\r\n\r\n// 小区排行推送\r\nexport function getCommunityVolume() {\r\n    return request.post('/za/getCommunityVolumeList', {})\r\n}\r\n// 智慧停车指数\r\nexport function getSmartParking() {\r\n    return request.post('/za/getSmartParkingList', {})\r\n}\r\n// 物联设备\r\nexport function getDevicesAll() {\r\n    return request.post('/za/getDevicesAll', {\r\n        \"appId\": \"4299740b47a84e8fb820a4c5e7ba9a0b\"\r\n    })\r\n}\r\n// 网管液位点位告警及处理状态表\r\nexport function AlarmProcessingList() {\r\n    return request.post('/za/getPipeNetworkAlarmProcessingList', {\r\n        \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\r\n        \"aseKey\": \"AE3Cug8aXuvfN406Dw_kqQMwCft3KKitmEu-dE0hcHAIokD0xA6Tv-iWaWtFE2YBSzqvhbD5AsHNLG4T1gjLogPwHCdkghaAkZ9hNP2CGniFYh8ONr7-2PwIV7hFyMflijKca42R6IXmbFvjskuBN-gPN-i1gVXSSPyD2ItBrWE5dBCZ0wKnaC4yPrgPqHkOrYSrsKNg-39jxZqLb86QGUugXXIwEm4Lldz8_PIhP0zQQXGhnTYviyy-n2_VDKptzlAUKSdZZHjLSsRfHO-ESfbDVuY7N4DJZhX7E_iHppIRMbmjeaU3vHAIvDNf0H8m_bzKIER2OC6FmUD2sq-Oig\",\r\n        \"body\": \"9229f01e6f2ce25120051124d398663d\",\r\n        \"sign\": \"Qjhzggbf3RTVXYitnvtwiTS1mKr90+7uWn/MCKlOCTWnX02MhFrUJRPc17ezsp0CkyGxA8AOt4ltCFwbHHs3cL+9xH1pyoSJF+wGPKt6tKxTywVxw73HC0sEIhCjioAxn/SNvSwTHlmbZ7OE17KLpKMnDHNK6GUBqqDVllGQvOgbeIoIrB5iqng1bLLoex/xnFMzvZ5HUDo+YeNGy6J03vSoK8AQ7iY0I8YH8x27kxe93DGti6nyTKm8GM24g31cSKaDbYRhyE/6eRVZWxom5iaZvXw1dfSbfQG2Xsyn5/90n7chdxkoP8RRrABMN00Qh4GMgkb1N9249nPE9X8pBQ==\",\r\n        \"timestamp\": 1686281554676\r\n    })\r\n}\r\n// // 辖区诉求量\r\n// export function getAppealsList() {\r\n//     return request.post('/sanTi/getCommunityProcessingList', {})\r\n// }\r\n\r\n//半年投诉事件趋势\r\nexport function getHalfComplaintList() {\r\n    return request.post('/za/getComplaints', {})\r\n}\r\n// 管网液位预警\r\nexport function getWaterLevelWarning() {\r\n    // return request.post('/za/getGuanwangLevelWarningList', {})\r\n    return request.post('/za/getWaterSewageHistoryList', {})\r\n}\r\n// // 部门办件量\r\n// export function getSectorVolumeList() {\r\n//     return request.post('/sanTi/getSectorVolume', {\r\n//         \"appId\": \"64b33cd45fc8428cb9c37a1aadf934c4\",\r\n//         \"body\": \"{\\\"year\\\":\\\"2022\\\"}\"\r\n//     })\r\n// }\r\n\r\n// 办件指数\r\nexport function getTakeAmountNum() {\r\n    return request.post('/za/getAdminApprovalTakeamountnumslist', {})\r\n}\r\n// // 办件前五部门\r\n// export function getTakeAmountDepartment() {\r\n//     return request.post('/sanTi/getSectorVolume', {})\r\n// }\r\n\r\n// 水质检测\r\nexport function getWaterQualityMonitoringList() {\r\n    return request.post('/za/getWaterQualityMonitoringList', {\r\n        \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\r\n        \"aseKey\": \"L50yntbMA7-j7ouTE-mgRnlV1SQF1LXX3vzToY97L2hnD64twdFuBA0j1pXNFWXvbXi1wv6BYKexjgQpB73RGskOTsAbeBP0GJ-_rULtnWqajfAglqaYGHGadtgZY1JV4bGApjmRwb7vlaipAUZu0Yd8QkUIPdxI6KAR_XEl9wssB9ITY-ZikXZNbcRFSbPVZGrHkFmmp369DtBMxlILDB__kLLj1zRiMaKdOzeW8QtswG85Ag1aU9J6JEVXMeMWfDn_ZgPShY6YvX1Cr2DDl4tltfotyAYDTi_piYaOAU7m688Bx2bCPs6TF3pa6BN7CQEld6kPGKthuSXyNrOfCg\",\r\n        \"body\": \"22e4a3ade651661cab4050c8b5269165\",\r\n        \"sign\": \"JJZ0uAv+WbD2v0/7ptE5blMhI4qWe3oKffyzsD+wrP6KN6TTNmJ75nWzph1Wx2HeDkyVmdRBgsquHc7TTvHjf2lzODe+6d2dIH4bF7m70FCJ9oFNu38IqZ+SF+Jpuv53PQqc57EsopIrg2PzbwBd9zekvStvNTQo8izTCycPzElC2RXnIfX7IU3nTR5uBQkhryqpTuY+Xv2jjrhrfUSGKvhdWc4Io992zE6b57Iqt3A6Jou/tzN0T7Iv7c/EwYMBHCRWWi2pJC0gyIufEzIEM+DKaeGiA6XHdvsfAhR7ZC5cTk26KcbWbKuea7XlXM81+ksOumbgWXZ2UaMKNdUaIg==\",\r\n        \"timestamp\": 1686298102199\r\n    })\r\n}\r\n// 每日诉求量\r\nexport function getDailyDemand() {\r\n    return request.post('/za/getDailyDemandList', {\r\n        \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\r\n        \"aseKey\": \"iiynFKRRcMKS_i3FHDIG6wWus8Go-SZFNm6y0GZ4sx_ImIj8AYcQC9_b_2ztdlatEHrNVztfRcg-ciR-LLDYdY5dkG17uQzpq_wXOsgXGHT1UNqRpjaR80SlrDEcUBud5Cajj31NOD7A1TtPvGhQo52DL7-RjVTCJHzzMcMUBtetUEFdtfoPSjx8rIyFQcWo2UchzvBFHVOL5Slye1TcrHISp303iksTPEWdt0THPX3wMr8YEk6M1xUQY_N_q62AIuWIMaKywqJz6NjZ7NQd9qz69MmeOwp4aXBMMGDIxHVYfz3vtVEDSgYRA-4l3cJlvxZTRBUJ1E2uNaLgbalTAw\",\r\n        \"body\": \"d9d4c738aefc33ed2bd41b9275c6f5b1\",\r\n        \"sign\": \"BG9FbbDMTJUzVl7BFerHo/2dF2timDCp//l+yijFM9/LnYbSyYVNlvfSpI5I7fkipICiJvqp0K0cvN3h8WbxST93iKgHTzi0zppMRiyjAlMbXl5mL62NQvPhUmN85OvBLIlzmYS7mFcqtr4Ys2GFTLPIzuMUaM44ykzjMWU2+mH3aqZtX3ma24KN2q1v+Cs4LBBBF6com8cfC8tGNPAe2DFoVosGuV+OIPlBAi5XDLs9M8ElTMXcy6WAVKnSn4aZATgt1zcR78Mf2oiT0cLFU14Fgwp5URxJ8eOeyuxSWrhs9iIZGlqXvK4xnkE1YbCddlNYONxKHOjPNs5HU3bIPg==\",\r\n        \"timestamp\": 1685413859380\r\n    })\r\n}\r\n// 小区隐患上报\r\nexport function getHiddenDangerCommunity() {\r\n    return request.post('/za/getFireAnalysisHiddenDangerCommunityList', {})\r\n}\r\n// 隐患上报-统计分析\r\nexport function getHiddenDangerCountList() {\r\n    return request.post('/za/getFireAnalysisHiddenDangerCountList', {})\r\n}\r\n// 采集数据\r\nexport function getMonitorCount() {\r\n    return request.post('/za/getMonitorCount', {})\r\n}\r\n// 地区生产值实际情况\r\nexport function getGdpCompletionList() {\r\n    return request.post('/za/getGdpCompletionList', {})\r\n}\r\n// 设备故障与维修情况\r\nexport function getFireFacilityCountList() {\r\n    return request.post('/za/getFireAnalysisFacilityFailReplayCountList', {})\r\n}\r\nexport function getPmScoreList() {\r\n    return request.post('/za/getPmScoreList', {})\r\n}\r\n\r\n// 33行动\r\nexport function getWaterThreeBigDataActionList() {\r\n    return request.post('/za/getWaterThreeBigDataActionList', {})\r\n}\r\n\r\n// 民生诉求工单列表\r\nexport function getWorkOrderList() {\r\n    return request.post('/za/getWorkOrderAll', {\r\n        \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\r\n        \"aseKey\": \"LGP_ES_GHwgDQFg3X2WsjAu4rMjzLWiqutu1MkpoLtKneDLEJns4aod2r4gUwm3fsRd3yI6G7LxExi4i17HCct_48N8prS2ZFdD8W_-vxI5eLhLDft4GtLNHqJik9YZCteQItTWnUjy9jAEZywN_08CfahzRZijnAqV0G8zuxOB-hE0FeLe0wHqzq0EcZQhDmupK8QeZhWu9GnGF12MFn3oRKzykxr4MICVEGLU6oI3BilX9CJ9CkhhzYkABd2CDX86bsyBDsDqXdQIpT0KvPYeVILpfM2_vkb5nn5Zg1Y3mh1yizXQePIguBzHjOEao6xOiERXSVVcoE7PECat62w\",\r\n        \"body\": \"9bae2bed020e6ef46f6225d1b9b1a945\",\r\n        \"sign\": \"QgjGF87rTPyv2Gowqv/qE0Hon9zGmD2U4+zquV26Vv4BugLqgU3ECN7vbsaITjS9JuRk0wLN4bGxeBZOQD3+JZiKzB+qXlZfiS2n0kg/ApZd5G9XI9hcTfhilyglEHMnDgNhTxXNuX2h42OOfAJnIiEch9iMA9Lf86Jq3Ta5Js6h+lR8c8hqUo60XYOPkyw6EvWjWbm4REH2IQ4GpqDNS3spkfZTjp4VJ8OSRJfJlq+9W3ow0odJZT+PlATNQPZKsXHOk4aUQr7mKaHIACkUXs5e9G+RdOnZ0BiPRE5BpOJy+/Q8DhbBlFvC+Jv6aPX4aLboEYS1BHIa1uorzKqoJw==\",\r\n        \"timestamp\": 1686289547004\r\n    })\r\n}\r\n\r\n// 一体化智能杆\r\nexport function IntegratedIntelligentRod() {\r\n    return request.post('/za/getAllInOneSmartPoleList', {\r\n        \"appId\": \"445d748d-91fd-48a5-a1s5-6dbr14b63fb9\",\r\n        \"aseKey\": \"gSzHTK1mXEBu5coXur-tmI6dcLuwQAdDX4PO5JEJ6osvoIOyJByoy1xHjjO-QqIVts3fUndJfUoNarVQCEfF_GtqtcxNFv-7VV-XxBb0Nl_-4oth6zk1bnZCmYQqe9K-759qJZP4ZcNiL7F7fs65Fd0t34o3RXhGBdW6rayoRSab_X3nuHgvSmowdZl0KG2WjsiiH48GffQYMaFEc6nrXRVXhLY9UnnEsJfOgsM-9FnvHxss0-O7zCu0fv8J1ROf25aRQyfV6U8i0UINvV2UQutAYleWk9k7YB-Oe_Y_KHg-kb46vvZEnwMgImzJ83UfnAngdO9j_QstbI77VJlEjw\",\r\n        \"body\": \"c50f7684aa4336abde4417d1cce72743\",\r\n        \"sign\": \"UWy/dV3+a16j7AkahmwJRqgLXHUnqgJ94JqU45RU3C79Z24AdW+QLrV70o91J7zIzDXDKAsLtISqe4BhPhtIPXRnr3DqGSePnfcI+d7+o/CelBKgilv3azs1Bj/9cMAXFrXdqJdhi8bFJsW3BuTqkiCeCxn/XE2jTH0UEwXZgzSJfUDV4x3k+dmE1kkYSIXRSo569XeL4D6AskEEBCHacrU1/RUWwW6eJvr4TWyNFb6+2+RKe8e9yy+eq63JsiwfWqC+BW04vVT2nHIzLwbViFSsnVA1J4bsYKtWBKxFhh3f93e/JX0+Fcqs6/Yw4uHaC48SeFRGha+uf+TMeuTrNw==\",\r\n        \"timestamp\": 1686534294351\r\n    })\r\n}\r\n// // 数字城管网格化\r\n// export function getDigitalCityRidsData() {\r\n//     return request.post('/sanTi/getResidentialQuarterVolumeList', {})\r\n// }\r\n\r\n// 烟感设备\r\nexport function getDevices() {\r\n    return request.post('/za/getDevices', {})\r\n}\r\n\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACjC,OAAOD,OAAO,CAACE,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAC9B,OAAOH,OAAO,CAACE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AACtD;AACA;AACA,OAAO,SAASE,aAAaA,CAAA,EAAG;EAC5B,OAAOJ,OAAO,CAACE,IAAI,CAAC,mBAAmB,EAAE;IACrC,OAAO,EAAE;EACb,CAAC,CAAC;AACN;AACA;AACA,OAAO,SAASG,mBAAmBA,CAAA,EAAG;EAClC,OAAOL,OAAO,CAACE,IAAI,CAAC,uCAAuC,EAAE;IACzD,OAAO,EAAE,sCAAsC;IAC/C,QAAQ,EAAE,wVAAwV;IAClW,MAAM,EAAE,kCAAkC;IAC1C,MAAM,EAAE,0VAA0V;IAClW,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASI,oBAAoBA,CAAA,EAAG;EACnC,OAAON,OAAO,CAACE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;AAChD;AACA;AACA,OAAO,SAASK,oBAAoBA,CAAA,EAAG;EACnC;EACA,OAAOP,OAAO,CAACE,IAAI,CAAC,+BAA+B,EAAE,CAAC,CAAC,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASM,gBAAgBA,CAAA,EAAG;EAC/B,OAAOR,OAAO,CAACE,IAAI,CAAC,wCAAwC,EAAE,CAAC,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASO,6BAA6BA,CAAA,EAAG;EAC5C,OAAOT,OAAO,CAACE,IAAI,CAAC,mCAAmC,EAAE;IACrD,OAAO,EAAE,sCAAsC;IAC/C,QAAQ,EAAE,wVAAwV;IAClW,MAAM,EAAE,kCAAkC;IAC1C,MAAM,EAAE,0VAA0V;IAClW,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AACA;AACA,OAAO,SAASQ,cAAcA,CAAA,EAAG;EAC7B,OAAOV,OAAO,CAACE,IAAI,CAAC,wBAAwB,EAAE;IAC1C,OAAO,EAAE,sCAAsC;IAC/C,QAAQ,EAAE,wVAAwV;IAClW,MAAM,EAAE,kCAAkC;IAC1C,MAAM,EAAE,0VAA0V;IAClW,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AACA;AACA,OAAO,SAASS,wBAAwBA,CAAA,EAAG;EACvC,OAAOX,OAAO,CAACE,IAAI,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;AAC3E;AACA;AACA,OAAO,SAASU,wBAAwBA,CAAA,EAAG;EACvC,OAAOZ,OAAO,CAACE,IAAI,CAAC,0CAA0C,EAAE,CAAC,CAAC,CAAC;AACvE;AACA;AACA,OAAO,SAASW,eAAeA,CAAA,EAAG;EAC9B,OAAOb,OAAO,CAACE,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;AAClD;AACA;AACA,OAAO,SAASY,oBAAoBA,CAAA,EAAG;EACnC,OAAOd,OAAO,CAACE,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;AACvD;AACA;AACA,OAAO,SAASa,wBAAwBA,CAAA,EAAG;EACvC,OAAOf,OAAO,CAACE,IAAI,CAAC,gDAAgD,EAAE,CAAC,CAAC,CAAC;AAC7E;AACA,OAAO,SAASc,cAAcA,CAAA,EAAG;EAC7B,OAAOhB,OAAO,CAACE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AACjD;;AAEA;AACA,OAAO,SAASe,8BAA8BA,CAAA,EAAG;EAC7C,OAAOjB,OAAO,CAACE,IAAI,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC;AACjE;;AAEA;AACA,OAAO,SAASgB,gBAAgBA,CAAA,EAAG;EAC/B,OAAOlB,OAAO,CAACE,IAAI,CAAC,qBAAqB,EAAE;IACvC,OAAO,EAAE,sCAAsC;IAC/C,QAAQ,EAAE,wVAAwV;IAClW,MAAM,EAAE,kCAAkC;IAC1C,MAAM,EAAE,0VAA0V;IAClW,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;;AAEA;AACA,OAAO,SAASiB,wBAAwBA,CAAA,EAAG;EACvC,OAAOnB,OAAO,CAACE,IAAI,CAAC,8BAA8B,EAAE;IAChD,OAAO,EAAE,sCAAsC;IAC/C,QAAQ,EAAE,wVAAwV;IAClW,MAAM,EAAE,kCAAkC;IAC1C,MAAM,EAAE,0VAA0V;IAClW,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASkB,UAAUA,CAAA,EAAG;EACzB,OAAOpB,OAAO,CAACE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}