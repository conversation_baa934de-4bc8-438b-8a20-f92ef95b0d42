{"ast": null, "code": "import { getWaterThreeBigDataActionList } from '@/api/bigScreen';\nimport { WaterMonitor, WaterMonitorList, forThirdPartyInfo, forThirdPartyInfoList, getkeyArea } from '@/api/index.js';\nimport WaterHistory from '@/components/smartWater/WaterHistory.vue';\nimport EquipmentTrend from '@/components/smartWater/EquipmentTrend.vue';\nimport AreaPro from '@/components/comprehensive/AreaPro.vue';\nexport default {\n  name: 'leftView',\n  data() {\n    return {\n      rankTxt: '33行动分析',\n      ThreeBigData: [{\n        \"id\": 809,\n        \"threeTime\": \"2023-12-22 00:00:04\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1094.4000000003725\",\n        \"threshold\": \"0.13680000000004658\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-22 00:00:04\",\n        \"totalFlowRate\": 9771812.6,\n        \"testFlow\": \"2000000001,8021054.512;2000000003,1004829.900;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 808,\n        \"threeTime\": \"2023-12-21 00:00:09\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1123.800000000745\",\n        \"threshold\": \"0.14047500000009314\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-21 00:00:09\",\n        \"totalFlowRate\": 9770718.2,\n        \"testFlow\": \"2000000003,1003736.100;2000000001,8021053.912;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 807,\n        \"threeTime\": \"2023-12-20 00:00:02\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1116.2999999988824\",\n        \"threshold\": \"0.13953749999986031\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-20 00:00:02\",\n        \"totalFlowRate\": 9769594.399999999,\n        \"testFlow\": \"2000000001,8021054.512;2000000003,1002611.700;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 806,\n        \"threeTime\": \"2023-12-19 00:00:36\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1108.5\",\n        \"threshold\": \"0.1385625\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-19 00:00:36\",\n        \"totalFlowRate\": 9768478.1,\n        \"testFlow\": \"2000000006,260869.313;2000000001,8021054.512;2000000003,1001495.400;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 805,\n        \"threeTime\": \"2023-12-18 00:00:08\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1136.0999999996275\",\n        \"threshold\": \"0.14201249999995344\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-18 00:00:08\",\n        \"totalFlowRate\": 9767369.6,\n        \"testFlow\": \"2000000003,1000387.500;2000000002,485058.875;2000000006,260869.313;2000000001,8021053.912;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 804,\n        \"threeTime\": \"2023-12-17 00:00:41\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1107.6000000014901\",\n        \"threshold\": \"0.13845000000018626\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-17 00:00:41\",\n        \"totalFlowRate\": 9766233.5,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,999255.300;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 803,\n        \"threeTime\": \"2023-12-16 00:00:40\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1121.3999999985099\",\n        \"threshold\": \"0.14017499999981373\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-16 00:00:40\",\n        \"totalFlowRate\": 9765125.899999999,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,998147.700;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 802,\n        \"threeTime\": \"2023-12-15 00:00:34\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1122.9000000003725\",\n        \"threshold\": \"0.14036250000004658\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-15 00:00:34\",\n        \"totalFlowRate\": 9764004.5,\n        \"testFlow\": \"2000000006,260869.313;2000000002,485058.875;2000000001,8021053.912;2000000003,997022.400;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 801,\n        \"threeTime\": \"2023-12-14 00:00:39\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1103.0999999996275\",\n        \"threshold\": \"0.13788749999995342\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-14 00:00:39\",\n        \"totalFlowRate\": 9762881.6,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,995903.400;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 800,\n        \"threeTime\": \"2023-12-13 00:00:38\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1121.7000000011176\",\n        \"threshold\": \"0.1402125000001397\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-13 00:00:38\",\n        \"totalFlowRate\": 9761778.5,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,994800.300;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 799,\n        \"threeTime\": \"2023-12-12 00:00:00\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1118.0999999996275\",\n        \"threshold\": \"0.13976249999995344\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-12 00:00:00\",\n        \"totalFlowRate\": 9760656.799999999,\n        \"testFlow\": \"2000000001,8021053.912;2000000003,993674.700;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 798,\n        \"threeTime\": \"2023-12-11 00:00:32\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1116.300000000745\",\n        \"threshold\": \"0.13953750000009313\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-11 00:00:32\",\n        \"totalFlowRate\": 9759538.7,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,992560.500;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 797,\n        \"threeTime\": \"2023-12-10 00:00:43\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1118.3999999985099\",\n        \"threshold\": \"0.13979999999981374\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-10 00:00:43\",\n        \"totalFlowRate\": 9758422.399999999,\n        \"testFlow\": \"2000000002,485058.875;2000000001,8021050.012;2000000006,260869.313;2000000003,991444.200;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 796,\n        \"threeTime\": \"2023-12-09 00:00:32\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1106.4000000003725\",\n        \"threshold\": \"0.13830000000004655\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-09 00:00:32\",\n        \"totalFlowRate\": 9757304.0,\n        \"testFlow\": \"2000000002,485058.875;2000000001,8021050.012;2000000006,260869.313;2000000003,990325.800;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 795,\n        \"threeTime\": \"2023-12-08 00:00:00\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1132.800000000745\",\n        \"threshold\": \"0.14160000000009312\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-08 00:00:00\",\n        \"totalFlowRate\": 9756197.6,\n        \"testFlow\": \"2000000003,989216.100;2000000001,8021053.312;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 794,\n        \"threeTime\": \"2023-12-07 00:00:01\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1117.5\",\n        \"threshold\": \"0.1396875\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-07 00:00:01\",\n        \"totalFlowRate\": 9755064.799999999,\n        \"testFlow\": \"2000000003,988082.700;2000000001,8021053.912;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 793,\n        \"threeTime\": \"2023-12-06 00:00:39\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1105.199999999255\",\n        \"threshold\": \"0.13814999999990688\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-06 00:00:39\",\n        \"totalFlowRate\": 9753947.299999999,\n        \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,986969.100;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 792,\n        \"threeTime\": \"2023-12-05 00:00:10\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1137.5999999996275\",\n        \"threshold\": \"0.14219999999995345\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-05 00:00:10\",\n        \"totalFlowRate\": 9752842.1,\n        \"testFlow\": \"2000000001,8021053.612;2000000003,985860.300;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 791,\n        \"threeTime\": \"2023-12-04 00:00:00\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1127.4000000003725\",\n        \"threshold\": \"0.14092500000004657\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-04 00:00:00\",\n        \"totalFlowRate\": 9751704.5,\n        \"testFlow\": \"2000000001,8021054.212;2000000003,984722.100;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }, {\n        \"id\": 790,\n        \"threeTime\": \"2023-12-03 00:00:00\",\n        \"collectionArea\": \"东达标区\",\n        \"dailyWaterSupply\": \"8000\",\n        \"dailySewageSupply\": \"1133.1000000014901\",\n        \"threshold\": \"0.14163750000018627\",\n        \"isStandard\": 0,\n        \"responsePersonId\": null,\n        \"responsePersonName\": null,\n        \"telephone\": null,\n        \"threeOperating\": null,\n        \"createTime\": \"2023-12-03 00:00:00\",\n        \"totalFlowRate\": 9750577.1,\n        \"testFlow\": \"2000000003,983594.400;2000000001,8021054.512;2000000006,260869.313;2000000002,485058.875;\",\n        \"noticeTime\": null\n      }],\n      //33行动\n      sewageData: {\n        title: '',\n        style: {\n          width: \"94%\",\n          height: \"400px\"\n        },\n        data: []\n      },\n      tabsList: [\"水务设施分析\", \"水环境分析\"],\n      tabsIndex: 0,\n      equipmentData: [],\n      perIndex: null,\n      //设备种类\n      equipmentPer: [\n      // {\n      //     name: \"单兵执法仪\",\n      //     num: 8,\n      // },\n      {\n        name: \"物联设备\",\n        num: 382,\n        per: '42%'\n      }, {\n        name: \"视频监控设备\",\n        num: 294,\n        per: '40%'\n      }],\n      waterQualityTYpe: {\n        name: '水质检测',\n        data: [{\n          prop: 'siinName',\n          label: '监测名称'\n        }, {\n          prop: 'para',\n          label: \"监测类别\"\n        }, {\n          prop: 'evaluation',\n          label: '监测级别'\n        }, {\n          prop: 'valueUnit',\n          label: '监测单位'\n        }]\n      },\n      // 水质检测数据\n      WaterMonitorData: [],\n      isFalse: true,\n      qualityItems: [{\n        name: \"浊度\",\n        per: '26%'\n      }, {\n        name: '高锰酸盐',\n        per: '20%'\n      }, {\n        name: \"总磷\",\n        per: '19%'\n      }],\n      config: {\n        //工单\n        header: ['监测名称', '监测类别', '监测级别', '办结结果'],\n        data: [],\n        headerBGC: \"rgba(18, 76, 111, .45)\",\n        evenRowBGC: 'rgba(0,0,0,0)'\n      },\n      tableData: [],\n      MonitorName: '',\n      dialogVisible: false,\n      dialogType: {},\n      wgList: [],\n      siinTypeList: [],\n      showSiinType: false\n    };\n  },\n  components: {\n    WaterHistory,\n    EquipmentTrend,\n    AreaPro\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    // 天福湿地监测点\n    this.TFPartyInfoList();\n    this.WaterMonitor();\n    // this.ThreeBigDataActionList()\n  },\n  updated() {},\n  methods: {\n    // 33行动分析数据\n    async ThreeBigDataActionList() {\n      await getWaterThreeBigDataActionList().then(res => {\n        let list = res.data.data;\n        this.ThreeBigData = list;\n      });\n    },\n    toggleDatePanel() {\n      this.rankTxt == '33行动分析' ? this.rankTxt = \"排污溯源\" : this.rankTxt = \"33行动分析\";\n    },\n    isShowFn() {\n      this.$store.commit('warningType/getIsShowAccumulationArr', true);\n      this.Accumulation = {\n        name: '防汛值班表',\n        data: ' '\n      };\n      this.$store.commit('warningType/getAccumulationData', this.Accumulation);\n    },\n    // 天福湿地监测点\n    async TFPartyInfoList() {\n      await forThirdPartyInfo().then(res => {\n        this.equipmentData = res.data.extra;\n      });\n    },\n    //删除点位\n    deleteFn() {\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    async ChangeSiinType(val) {\n      this.siinType = val;\n      await forThirdPartyInfoList({\n        siinType: val\n      }).then(res => {\n        this.siinTypeList = res.data.extra;\n        this.showSiinType = true;\n      });\n    },\n    closeSiinType() {\n      this.showSiinType = false;\n    },\n    sendUeFn(name) {\n      this.$eventBus.$emit('senTxtToUe', name);\n    },\n    formatInteger(row, column, cellValue) {\n      return Math.round(cellValue); // 使用 Math.round 进行四舍五入取整\n    },\n    //查询点位并撒点\n    perEventFn(item, index) {\n      this.deleteFn();\n      if (this.perIndex == index) {\n        this.perIndex = null;\n      } else {\n        this.perIndex = index;\n        getkeyArea({\n          name: item.name\n        }).then(res => {\n          if (res.data.code == 200) {\n            this.wgList = res.data.extra.data;\n            let params = {\n              \"mode\": \"add\",\n              \"sources\": JSON.parse(this.wgList[0].points)\n            };\n            this.$eventBus.$emit('senTxtToUe', params); //撒点\n            this.$eventBus.$emit('senTxtToUe', this.wgList[0].angle); //聚焦到当前位置\n          }\n        });\n      }\n    },\n    setUrl(name, index) {\n      return require(`@/assets/images/smartWater/${name}${index + 1}${this.perIndex == index && name == 'per-bg' ? '-active' : ''}.png`);\n    },\n    tabsFn(index) {\n      this.tabsIndex = index;\n    },\n    // 水质检测弹出框\n    closeDialog() {\n      //弹框的关闭方法\n      this.dialogVisible = false;\n    },\n    // 水质监测类型列表弹出框\n    ChangeWaterMonitorName(val) {\n      this.MonitorName = val;\n      if (this.MonitorName) {\n        this.isFalse = false;\n        this.dialogVisible = true;\n        let params = {\n          siinName: val\n        };\n        this.dialogType = this.waterQualityTYpe;\n        this.getWaterMonitorList(params);\n      }\n    },\n    // 水质检测\n    async WaterMonitor() {\n      await WaterMonitor().then(res => {\n        this.WaterMonitorData = res.data.extra;\n        this.MonitorName = this.WaterMonitorData[0].siinName;\n        this.getWaterMonitorList({\n          siinName: this.MonitorName\n        });\n        let newDataList = [];\n        let current = 0;\n        if (this.WaterMonitorData && this.WaterMonitorData?.length > 0) {\n          for (let i = 0; i <= this.WaterMonitorData.length - 1; i++) {\n            if (i % 3 !== 0 || i === 0) {\n              if (!newDataList[current]) {\n                newDataList.push([this.WaterMonitorData[i]]);\n              } else {\n                newDataList[current].push(this.WaterMonitorData[i]);\n              }\n            } else {\n              current++;\n              newDataList.push([this.WaterMonitorData[i]]);\n            }\n          }\n        }\n        this.WaterMonitorData = [...newDataList];\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n    async getWaterMonitorList(params) {\n      await WaterMonitorList(params).then(res => {\n        let list = res.data.extra;\n        this.tableData = list;\n        let configList = {\n          header: ['监测名称', '监测类别', '监测级别', '办结结果'],\n          data: [],\n          headerBGC: \"rgba(18, 76, 111, .45)\",\n          evenRowBGC: 'rgba(0,0,0, 0)',\n          oddRowBGC: 'rgba(0,0,0, 0)'\n        };\n        list.forEach((item, index) => {\n          if (index < 10) configList.data.push([item.siinName, item.para, item.evaluation, item.valueUnit]);\n        });\n        this.config = configList;\n      }).catch(err => {\n        console.log(err);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["getWaterThreeBigDataActionList", "WaterMonitor", "WaterMonitorList", "forThirdPartyInfo", "forThirdPartyInfoList", "getkeyArea", "WaterHistory", "EquipmentTrend", "AreaPro", "name", "data", "rankTxt", "ThreeBigData", "sewageData", "title", "style", "width", "height", "tabsList", "tabsIndex", "equipmentData", "perIndex", "equipmentPer", "num", "per", "waterQualityTYpe", "prop", "label", "WaterMonitorData", "isFalse", "qualityItems", "config", "header", "headerBGC", "evenRowBGC", "tableData", "MonitorName", "dialogVisible", "dialogType", "wgList", "siinTypeList", "showSiinType", "components", "watch", "computed", "mounted", "TFPartyInfoList", "updated", "methods", "ThreeBigDataActionList", "then", "res", "list", "toggleDatePanel", "isShowFn", "$store", "commit", "Accumulation", "extra", "deleteFn", "params", "$eventBus", "$emit", "ChangeSiinType", "val", "siinType", "closeSiinType", "sendUeFn", "formatInteger", "row", "column", "cellValue", "Math", "round", "perEventFn", "item", "index", "code", "JSON", "parse", "points", "angle", "setUrl", "require", "tabsFn", "closeDialog", "ChangeWaterMonitorName", "siin<PERSON>ame", "getWaterMonitorList", "newDataList", "current", "length", "i", "push", "catch", "err", "console", "log", "configList", "oddRowBGC", "for<PERSON>ach", "para", "evaluation", "valueUnit"], "sources": ["src/views/smartWater/components/water-awareness/left-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leftView\">\r\n        <div class=\"title-box\">\r\n            <span>智慧水务</span>\r\n        </div>\r\n        <div class=\"wg-box\">\r\n            <div class=\"two-title\">水务监测\r\n                <!-- <img @click=\"sendUeFn('污水管网')\" src=\"@/assets/images/comprehensiveSituation/gd.png\" alt=\"\"> -->\r\n                <img @click=\"sendUeFn('水污染')\" src=\"@/assets/images/comprehensiveSituation/swr.png\" alt=\"\">\r\n                <i class=\"el-icon-date\" @click=\"isShowFn\"></i>\r\n                <img @click=\"sendUeFn('水淹模拟')\" src=\"@/assets/images/comprehensiveSituation/sw.png\" alt=\"\">\r\n            </div>\r\n            <div class=\"wg-types\">\r\n                <p v-for=\"(item, index) in equipmentData\" @click=\"ChangeSiinType(item.siinType)\">\r\n                    <span>{{ item.siinType ? item.siinType : '其他' }}</span>\r\n                    <span>{{ item.siinTypeCount }}</span>\r\n                    <img ref=\"wgImg\" :src=\"setUrl('wg-bg', index)\" alt=\"\">\r\n                </p>\r\n            </div>\r\n            <div class=\"per-box\">\r\n                <p v-for=\"(item, index) in equipmentPer\" @click=\"perEventFn(item, index)\">\r\n                    <span>{{ item.num }}</span>\r\n                    <span>{{ item.name }}</span>\r\n                    <img ref=\"preImg\" :src=\"setUrl('per-bg', index)\" alt=\"\">\r\n                </p>\r\n            </div>\r\n        </div>\r\n        <div class=\"tabs\">\r\n            <p @click=\"tabsFn(index)\" v-for=\"(item, index) in tabsList\">\r\n                <img v-if=\"tabsIndex == index\" src=\"@/assets/images/comprehensiveSituation/bg-active.png\" alt=\"\">\r\n                <img v-else src=\"@/assets/images/comprehensiveSituation/bg.png\" alt=\"\">\r\n                <span>{{ item }}</span>\r\n            </p>\r\n        </div>\r\n        <div class=\"tabs-main\" v-if=\"tabsIndex == 0\">\r\n            <div class=\"two-title\">液位差实时报警</div>\r\n            <WaterHistory></WaterHistory>\r\n            <div class=\"two-title\">排污溯源</div>\r\n            <AreaPro ref=\"areaPro\" :areaData=\"sewageData\"></AreaPro>\r\n            <!-- <EquipmentTrend></EquipmentTrend> -->\r\n\r\n        </div>\r\n        <div class=\"tabs-main\" v-else>\r\n            <div class=\"two-title\">水质检测</div>\r\n            <div class=\"qualityItems\">\r\n                <div class=\"carouselBox\">\r\n                    <el-carousel :loop=\"true\" :autoplay=\"isFalse\" class=\"carousel\">\r\n                        <el-carousel-item class=\"el-car-item\" v-for=\"(list, index) in WaterMonitorData\" :key=\"index\">\r\n                            <div v-for=\"(list1, index1) in list\" :key=\"index1\" class=\"divSrc\"\r\n                                @click=\"ChangeWaterMonitorName(list1.siinName)\">\r\n                                <div class=\"num\">{{ list1.siinNameCount }}</div>\r\n                                <div class=\"title\">{{ list1.siinName }}</div>\r\n                                <div class=\"line\" v-show=\"MonitorName == list1.siinName\"></div>\r\n                            </div>\r\n                        </el-carousel-item>\r\n                    </el-carousel>\r\n                </div>\r\n                <div class=\"carousel_lis\">\r\n                    <dv-scroll-board :config=\"config\" style=\"width:100%;height:400px\" />\r\n                </div>\r\n\r\n            </div>\r\n            <div class=\"two-title\" @click=\"toggleDatePanel\">“333”行动分析数据</div>\r\n            <div class=\"threeAction\">\r\n                <el-table :data=\"ThreeBigData\" height=\"400\" style=\"width: 94%\" ref=\"scroll_table\"\r\n                    :row-class-name=\"getRowClassName\">\r\n\r\n                    <el-table-column prop=\"collectionArea\" label=\"名称\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dailySewageSupply\" label=\"污水日供给\" :formatter=\"formatInteger\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dailyWaterSupply\" label=\"每日水供给\">\r\n                    </el-table-column>\r\n                    <!-- <el-table-column prop=\"three_time\" label=\"日期\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div class=\"time\">{{ scope.row.three_time.slice(0,\r\n                                scope.row.three_time.indexOf('T'))\r\n                                }}</div>\r\n                        </template>\r\n</el-table-column> -->\r\n\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"rank-bg\" @click=\"toggleDatePanel\">{{ rankTxt }}</div> -->\r\n\r\n\r\n        </div>\r\n        <!-- 水质监测弹出框 -->\r\n        <el-dialog class=\"waterDaLog\" :title=\"dialogType?.name\" :visible.sync=\"dialogVisible\" width=\"30%\"\r\n            :fullscreen=\"false\" :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\"\r\n            :before-close=\"closeDialog\" append-to-body>\r\n\r\n            <el-table :data=\"tableData\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"300\" :header-cell-style=\"{\r\n                color: '#fff',\r\n                fontWeight: '700',\r\n                backgroundColor: 'rgba(18, 76, 111, .45)',\r\n\r\n            }\">\r\n                <el-table-column v-for=\"(item, index) of dialogType?.data\" :key=\"index\" :prop=\"item.prop\"\r\n                    :label=\"item.label\" :show-overflow-tooltip=\"true\">\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n        </el-dialog>\r\n        <el-dialog class=\"siinTypeList\" title=\"监测点\" :visible.sync=\"showSiinType\" width=\"45%\" :fullscreen=\"false\"\r\n            :close-on-press-escape=\"false\" show-close :close-on-click-modal=\"false\" :before-close=\"closeSiinType\"\r\n            append-to-body>\r\n\r\n            <el-table :data=\"siinTypeList\" :stripe=\"true\" style=\"width: 100%\" :max-height=\"400\" :header-cell-style=\"{}\">\r\n                <!-- <el-table-column prop=\"siinWetlandname\" label=\"设备名称\" width=\"250\" :show-overflow-tooltip=\"true\">\r\n                </el-table-column> -->\r\n                <el-table-column prop=\"siinName\" width=\"250\" label=\"设备监测点\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"siinType\" label=\"设备类型\">\r\n                </el-table-column>\r\n                <el-table-column prop=\"siinWorkinfo\" label=\"设备情况\">\r\n                </el-table-column>\r\n                <!-- <el-table-column prop=\"siinPictures\" label=\"图片\">\r\n                    <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.siinPictures.length > 5\" :src=\"JSON.parse(scope.row.siinPictures)[0]\"\r\n                            width=\"100%\" height=\"100px\" />\r\n                        <p v-else>无</p>\r\n                    </template>\r\n</el-table-column> -->\r\n\r\n            </el-table>\r\n\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWaterThreeBigDataActionList } from '@/api/bigScreen'\r\nimport { WaterMonitor, WaterMonitorList, forThirdPartyInfo, forThirdPartyInfoList, getkeyArea } from '@/api/index.js'\r\nimport WaterHistory from '@/components/smartWater/WaterHistory.vue'\r\nimport EquipmentTrend from '@/components/smartWater/EquipmentTrend.vue'\r\nimport AreaPro from '@/components/comprehensive/AreaPro.vue'\r\nexport default {\r\n    name: 'leftView',\r\n    data() {\r\n        return {\r\n            rankTxt: '33行动分析',\r\n            ThreeBigData: [\r\n                {\r\n                    \"id\": 809,\r\n                    \"threeTime\": \"2023-12-22 00:00:04\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1094.4000000003725\",\r\n                    \"threshold\": \"0.13680000000004658\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-22 00:00:04\",\r\n                    \"totalFlowRate\": 9771812.6,\r\n                    \"testFlow\": \"2000000001,8021054.512;2000000003,1004829.900;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 808,\r\n                    \"threeTime\": \"2023-12-21 00:00:09\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1123.800000000745\",\r\n                    \"threshold\": \"0.14047500000009314\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-21 00:00:09\",\r\n                    \"totalFlowRate\": 9770718.2,\r\n                    \"testFlow\": \"2000000003,1003736.100;2000000001,8021053.912;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 807,\r\n                    \"threeTime\": \"2023-12-20 00:00:02\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1116.2999999988824\",\r\n                    \"threshold\": \"0.13953749999986031\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-20 00:00:02\",\r\n                    \"totalFlowRate\": 9769594.399999999,\r\n                    \"testFlow\": \"2000000001,8021054.512;2000000003,1002611.700;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 806,\r\n                    \"threeTime\": \"2023-12-19 00:00:36\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1108.5\",\r\n                    \"threshold\": \"0.1385625\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-19 00:00:36\",\r\n                    \"totalFlowRate\": 9768478.1,\r\n                    \"testFlow\": \"2000000006,260869.313;2000000001,8021054.512;2000000003,1001495.400;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 805,\r\n                    \"threeTime\": \"2023-12-18 00:00:08\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1136.0999999996275\",\r\n                    \"threshold\": \"0.14201249999995344\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-18 00:00:08\",\r\n                    \"totalFlowRate\": 9767369.6,\r\n                    \"testFlow\": \"2000000003,1000387.500;2000000002,485058.875;2000000006,260869.313;2000000001,8021053.912;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 804,\r\n                    \"threeTime\": \"2023-12-17 00:00:41\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1107.6000000014901\",\r\n                    \"threshold\": \"0.13845000000018626\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-17 00:00:41\",\r\n                    \"totalFlowRate\": 9766233.5,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,999255.300;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 803,\r\n                    \"threeTime\": \"2023-12-16 00:00:40\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1121.3999999985099\",\r\n                    \"threshold\": \"0.14017499999981373\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-16 00:00:40\",\r\n                    \"totalFlowRate\": 9765125.899999999,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,998147.700;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 802,\r\n                    \"threeTime\": \"2023-12-15 00:00:34\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1122.9000000003725\",\r\n                    \"threshold\": \"0.14036250000004658\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-15 00:00:34\",\r\n                    \"totalFlowRate\": 9764004.5,\r\n                    \"testFlow\": \"2000000006,260869.313;2000000002,485058.875;2000000001,8021053.912;2000000003,997022.400;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 801,\r\n                    \"threeTime\": \"2023-12-14 00:00:39\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1103.0999999996275\",\r\n                    \"threshold\": \"0.13788749999995342\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-14 00:00:39\",\r\n                    \"totalFlowRate\": 9762881.6,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,995903.400;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 800,\r\n                    \"threeTime\": \"2023-12-13 00:00:38\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1121.7000000011176\",\r\n                    \"threshold\": \"0.1402125000001397\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-13 00:00:38\",\r\n                    \"totalFlowRate\": 9761778.5,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,994800.300;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 799,\r\n                    \"threeTime\": \"2023-12-12 00:00:00\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1118.0999999996275\",\r\n                    \"threshold\": \"0.13976249999995344\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-12 00:00:00\",\r\n                    \"totalFlowRate\": 9760656.799999999,\r\n                    \"testFlow\": \"2000000001,8021053.912;2000000003,993674.700;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 798,\r\n                    \"threeTime\": \"2023-12-11 00:00:32\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1116.300000000745\",\r\n                    \"threshold\": \"0.13953750000009313\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-11 00:00:32\",\r\n                    \"totalFlowRate\": 9759538.7,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,992560.500;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 797,\r\n                    \"threeTime\": \"2023-12-10 00:00:43\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1118.3999999985099\",\r\n                    \"threshold\": \"0.13979999999981374\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-10 00:00:43\",\r\n                    \"totalFlowRate\": 9758422.399999999,\r\n                    \"testFlow\": \"2000000002,485058.875;2000000001,8021050.012;2000000006,260869.313;2000000003,991444.200;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 796,\r\n                    \"threeTime\": \"2023-12-09 00:00:32\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1106.4000000003725\",\r\n                    \"threshold\": \"0.13830000000004655\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-09 00:00:32\",\r\n                    \"totalFlowRate\": 9757304.0,\r\n                    \"testFlow\": \"2000000002,485058.875;2000000001,8021050.012;2000000006,260869.313;2000000003,990325.800;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 795,\r\n                    \"threeTime\": \"2023-12-08 00:00:00\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1132.800000000745\",\r\n                    \"threshold\": \"0.14160000000009312\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-08 00:00:00\",\r\n                    \"totalFlowRate\": 9756197.6,\r\n                    \"testFlow\": \"2000000003,989216.100;2000000001,8021053.312;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 794,\r\n                    \"threeTime\": \"2023-12-07 00:00:01\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1117.5\",\r\n                    \"threshold\": \"0.1396875\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-07 00:00:01\",\r\n                    \"totalFlowRate\": 9755064.799999999,\r\n                    \"testFlow\": \"2000000003,988082.700;2000000001,8021053.912;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 793,\r\n                    \"threeTime\": \"2023-12-06 00:00:39\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1105.199999999255\",\r\n                    \"threshold\": \"0.13814999999990688\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-06 00:00:39\",\r\n                    \"totalFlowRate\": 9753947.299999999,\r\n                    \"testFlow\": \"2000000001,8021050.012;2000000006,260869.313;2000000002,485058.875;2000000003,986969.100;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 792,\r\n                    \"threeTime\": \"2023-12-05 00:00:10\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1137.5999999996275\",\r\n                    \"threshold\": \"0.14219999999995345\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-05 00:00:10\",\r\n                    \"totalFlowRate\": 9752842.1,\r\n                    \"testFlow\": \"2000000001,8021053.612;2000000003,985860.300;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 791,\r\n                    \"threeTime\": \"2023-12-04 00:00:00\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1127.4000000003725\",\r\n                    \"threshold\": \"0.14092500000004657\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-04 00:00:00\",\r\n                    \"totalFlowRate\": 9751704.5,\r\n                    \"testFlow\": \"2000000001,8021054.212;2000000003,984722.100;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                },\r\n                {\r\n                    \"id\": 790,\r\n                    \"threeTime\": \"2023-12-03 00:00:00\",\r\n                    \"collectionArea\": \"东达标区\",\r\n                    \"dailyWaterSupply\": \"8000\",\r\n                    \"dailySewageSupply\": \"1133.1000000014901\",\r\n                    \"threshold\": \"0.14163750000018627\",\r\n                    \"isStandard\": 0,\r\n                    \"responsePersonId\": null,\r\n                    \"responsePersonName\": null,\r\n                    \"telephone\": null,\r\n                    \"threeOperating\": null,\r\n                    \"createTime\": \"2023-12-03 00:00:00\",\r\n                    \"totalFlowRate\": 9750577.1,\r\n                    \"testFlow\": \"2000000003,983594.400;2000000001,8021054.512;2000000006,260869.313;2000000002,485058.875;\",\r\n                    \"noticeTime\": null\r\n                }\r\n            ],//33行动\r\n            sewageData: {\r\n                title: '',\r\n                style: {\r\n                    width: \"94%\",\r\n                    height: \"400px\"\r\n                },\r\n                data: [\r\n\r\n                ]\r\n            },\r\n            tabsList: [\"水务设施分析\", \"水环境分析\"],\r\n            tabsIndex: 0,\r\n            equipmentData: [],\r\n            perIndex: null,\r\n            //设备种类\r\n            equipmentPer: [\r\n                // {\r\n                //     name: \"单兵执法仪\",\r\n                //     num: 8,\r\n                // },\r\n                {\r\n                    name: \"物联设备\",\r\n                    num: 382,\r\n                    per: '42%',\r\n                },\r\n                {\r\n                    name: \"视频监控设备\",\r\n                    num: 294,\r\n                    per: '40%',\r\n                }\r\n            ],\r\n            waterQualityTYpe: {\r\n                name: '水质检测',\r\n                data: [\r\n                    {\r\n                        prop: 'siinName',\r\n                        label: '监测名称'\r\n                    },\r\n                    {\r\n                        prop: 'para',\r\n                        label: \"监测类别\"\r\n                    },\r\n                    {\r\n                        prop: 'evaluation',\r\n                        label: '监测级别'\r\n                    },\r\n                    {\r\n                        prop: 'valueUnit',\r\n                        label: '监测单位'\r\n                    }\r\n                ]\r\n            },\r\n            // 水质检测数据\r\n            WaterMonitorData: [],\r\n            isFalse: true,\r\n            qualityItems: [\r\n                {\r\n                    name: \"浊度\",\r\n                    per: '26%'\r\n                },\r\n                {\r\n                    name: '高锰酸盐',\r\n                    per: '20%'\r\n                },\r\n                {\r\n                    name: \"总磷\",\r\n                    per: '19%'\r\n                }\r\n            ],\r\n            config: { //工单\r\n                header: ['监测名称', '监测类别', '监测级别', '办结结果'],\r\n                data: [],\r\n                headerBGC: \"rgba(18, 76, 111, .45)\",\r\n                evenRowBGC: 'rgba(0,0,0,0)'\r\n\r\n            },\r\n            tableData: [],\r\n            MonitorName: '',\r\n            dialogVisible: false,\r\n            dialogType: {},\r\n            wgList: [],\r\n            siinTypeList: [],\r\n            showSiinType: false,\r\n        };\r\n    },\r\n    components: {\r\n        WaterHistory,\r\n        EquipmentTrend,\r\n        AreaPro\r\n    },\r\n    watch: {\r\n\r\n    },\r\n    computed: {\r\n\r\n    },\r\n\r\n    mounted() {\r\n        // 天福湿地监测点\r\n        this.TFPartyInfoList();\r\n        this.WaterMonitor();\r\n        // this.ThreeBigDataActionList()\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        // 33行动分析数据\r\n        async ThreeBigDataActionList() {\r\n            await getWaterThreeBigDataActionList().then(res => {\r\n                let list = res.data.data;\r\n                this.ThreeBigData = list\r\n            })\r\n        },\r\n        toggleDatePanel() {\r\n            this.rankTxt == '33行动分析' ? this.rankTxt = \"排污溯源\" : this.rankTxt = \"33行动分析\";\r\n        },\r\n        isShowFn() {\r\n            this.$store.commit('warningType/getIsShowAccumulationArr', true)\r\n            this.Accumulation = { name: '防汛值班表', data: ' ' }\r\n            this.$store.commit('warningType/getAccumulationData', this.Accumulation)\r\n        },\r\n        // 天福湿地监测点\r\n        async TFPartyInfoList() {\r\n            await forThirdPartyInfo().then(res => {\r\n                this.equipmentData = res.data.extra;\r\n            })\r\n        },\r\n        //删除点位\r\n        deleteFn() {\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n\r\n        async ChangeSiinType(val) {\r\n            this.siinType = val\r\n            await forThirdPartyInfoList({ siinType: val }).then(res => {\r\n                this.siinTypeList = res.data.extra;\r\n                this.showSiinType = true\r\n            })\r\n        },\r\n        closeSiinType() {\r\n            this.showSiinType = false\r\n        },\r\n        sendUeFn(name) {\r\n            this.$eventBus.$emit('senTxtToUe', name);\r\n        },\r\n        formatInteger(row, column, cellValue) {\r\n            return Math.round(cellValue); // 使用 Math.round 进行四舍五入取整\r\n        },\r\n        //查询点位并撒点\r\n        perEventFn(item, index) {\r\n            this.deleteFn();\r\n            if (this.perIndex == index) {\r\n                this.perIndex = null\r\n            } else {\r\n                this.perIndex = index;\r\n                getkeyArea({ name: item.name }).then(res => {\r\n                    if (res.data.code == 200) {\r\n                        this.wgList = res.data.extra.data;\r\n                        let params = {\r\n                            \"mode\": \"add\",\r\n                            \"sources\": JSON.parse(this.wgList[0].points)\r\n                        }\r\n                        this.$eventBus.$emit('senTxtToUe', params);//撒点\r\n                        this.$eventBus.$emit('senTxtToUe', this.wgList[0].angle);//聚焦到当前位置\r\n                    }\r\n                })\r\n            }\r\n        },\r\n        setUrl(name, index) {\r\n            return require(`@/assets/images/smartWater/${name}${index + 1}${this.perIndex == index && name == 'per-bg' ? '-active' : ''}.png`);\r\n        },\r\n        tabsFn(index) {\r\n            this.tabsIndex = index;\r\n        },\r\n        // 水质检测弹出框\r\n        closeDialog() {//弹框的关闭方法\r\n            this.dialogVisible = false\r\n        },\r\n        // 水质监测类型列表弹出框\r\n        ChangeWaterMonitorName(val) {\r\n            this.MonitorName = val\r\n            if (this.MonitorName) {\r\n                this.isFalse = false\r\n                this.dialogVisible = true\r\n                let params = {\r\n                    siinName: val\r\n                }\r\n                this.dialogType = this.waterQualityTYpe\r\n                this.getWaterMonitorList(params)\r\n\r\n            }\r\n        },\r\n        // 水质检测\r\n        async WaterMonitor() {\r\n            await WaterMonitor().then(res => {\r\n                this.WaterMonitorData = res.data.extra;\r\n                this.MonitorName = this.WaterMonitorData[0].siinName;\r\n                this.getWaterMonitorList({\r\n                    siinName: this.MonitorName\r\n                })\r\n                let newDataList = [];\r\n                let current = 0\r\n                if (this.WaterMonitorData && this.WaterMonitorData?.length > 0) {\r\n                    for (let i = 0; i <= this.WaterMonitorData.length - 1; i++) {\r\n                        if (i % 3 !== 0 || i === 0) {\r\n                            if (!newDataList[current]) {\r\n                                newDataList.push([this.WaterMonitorData[i]])\r\n                            } else {\r\n                                newDataList[current].push(this.WaterMonitorData[i])\r\n                            }\r\n                        } else {\r\n                            current++;\r\n                            newDataList.push([this.WaterMonitorData[i]])\r\n                        }\r\n                    }\r\n                }\r\n                this.WaterMonitorData = [...newDataList]\r\n            }).catch(err => {\r\n                console.log(err);\r\n            })\r\n        },\r\n        async getWaterMonitorList(params) {\r\n            await WaterMonitorList(params).then(res => {\r\n                let list = res.data.extra\r\n                this.tableData = list\r\n                let configList = {\r\n                    header: ['监测名称', '监测类别', '监测级别', '办结结果'],\r\n                    data: [],\r\n                    headerBGC: \"rgba(18, 76, 111, .45)\",\r\n                    evenRowBGC: 'rgba(0,0,0, 0)',\r\n                    oddRowBGC: 'rgba(0,0,0, 0)'\r\n\r\n                }\r\n                list.forEach((item, index) => {\r\n                    if (index < 10) configList.data.push([item.siinName, item.para, item.evaluation, item.valueUnit])\r\n\r\n                })\r\n                this.config = configList\r\n\r\n            }).catch(err => {\r\n                console.log(err);\r\n            })\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n::v-deep .threeAction {\r\n    margin-top: 30px;\r\n\r\n    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {\r\n        background-color: transparent !important;\r\n    }\r\n\r\n    .el-table,\r\n    .el-table__expanded-cell {\r\n        background-color: transparent;\r\n        color: #fff;\r\n        font-size: 20px;\r\n\r\n    }\r\n\r\n    .el-table__row:hover {\r\n        background-color: transparent;\r\n    }\r\n\r\n    .el-table thead {\r\n        font-weight: bold;\r\n        background-color: rgba(27, 137, 225, 0.4);\r\n        color: #fff;\r\n        font-size: 22px;\r\n        // text-shadow: 0px 0px 8px rgba(41, 218, 250,.4);\r\n    }\r\n\r\n    .el-table th,\r\n    .el-table tr,\r\n    .el-table td {\r\n        background-color: transparent;\r\n        // color: #fff;\r\n\r\n    }\r\n\r\n}\r\n\r\n::v-deep .dv-scroll-board .header {\r\n    font-size: 24px;\r\n\r\n}\r\n\r\n::v-deep .dv-scroll-board .rows .row-item {\r\n    font-size: 24px;\r\n    cursor: pointer;\r\n}\r\n\r\n.siinTypeList {\r\n    ::v-deep .el-dialog__header {\r\n        padding: 5rem;\r\n    }\r\n\r\n    /deep/ .el-table,\r\n    /deep/ .el-table tr,\r\n    /deep/ .el-table td,\r\n    /deep/ .el-table th {\r\n        color: #fff;\r\n        background-color: transparent !important;\r\n        border: 0;\r\n    }\r\n\r\n    /deep/ .el-table__cell.is-leaf {\r\n        border: 0;\r\n\r\n    }\r\n\r\n    /deep/ .el-table::before {\r\n        height: 0; // 将高度修改为0\r\n    }\r\n\r\n    ::v-deep .el-dialog {\r\n        /* background-color: transparent; */\r\n        background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n        background-size: 100% 100%;\r\n\r\n        .el-dialog__header {\r\n            padding: 3rem 4rem 0;\r\n\r\n            .el-dialog__title {\r\n                color: #fff;\r\n                font-size: 1.4rem;\r\n\r\n            }\r\n\r\n            .el-dialog__close {\r\n                color: #fff;\r\n                padding: 1.6rem 1rem 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.waterDaLog {\r\n\r\n    /deep/.el-table,\r\n    /deep/ .el-table tr,\r\n    /deep/ .el-table td,\r\n    /deep/ .el-table th {\r\n        color: #fff;\r\n        background-color: transparent !important;\r\n        border: 0;\r\n    }\r\n\r\n    /deep/ .el-table__cell.is-leaf {\r\n        border: 0;\r\n\r\n    }\r\n\r\n    /deep/ .el-table::before {\r\n        height: 0; // 将高度修改为0\r\n    }\r\n\r\n    ::v-deep .el-dialog {\r\n        /* background-color: transparent; */\r\n        background: url('@/assets/images/mainPics/videoBg.png') no-repeat center;\r\n        background-size: 100% 100%;\r\n\r\n        .el-dialog__header {\r\n            padding: 3rem 4rem 0;\r\n\r\n            .el-dialog__title {\r\n                color: #fff;\r\n                font-size: 1.4rem;\r\n\r\n            }\r\n\r\n            .el-dialog__close {\r\n                color: #fff;\r\n                padding: 1.6rem 1rem 0;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n\r\n    ::v-deep .el-table {\r\n\r\n        .el-table__body {\r\n            height: 100%;\r\n\r\n        }\r\n\r\n    }\r\n\r\n}\r\n\r\n.two-title {\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    width: 700px;\r\n    box-sizing: border-box;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #89FFFE;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    display: flex;\r\n    // justify-content: space-between;\r\n    position: relative;\r\n    align-items: center;\r\n\r\n    >i {\r\n        position: absolute;\r\n        cursor: pointer;\r\n        right: 75px;\r\n    }\r\n\r\n    // >img:nth-child(1) {\r\n    //     width: 30px;\r\n    //     height: 30px;\r\n    //     right: 190px;\r\n    // }\r\n\r\n    >img:nth-child(1) {\r\n        right: 125px;\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n\r\n    >img:nth-child(3) {\r\n        right: 20px;\r\n    }\r\n\r\n    >img {\r\n        width: 40px;\r\n        height: 40px;\r\n        cursor: pointer;\r\n        position: absolute;\r\n    }\r\n}\r\n\r\n.leftView {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 0 25px;\r\n\r\n    .title-box {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        >span:nth-child(1) {\r\n            width: 620px;\r\n            padding-left: 50px;\r\n            margin-left: -25px;\r\n            height: 67px;\r\n            background-size: 100% 100%;\r\n            background-image: url(\"@/assets/images/comprehensiveSituation/header-bg.png\");\r\n            font-family: Source Han Sans CN, Source Han Sans CN;\r\n            font-weight: bold;\r\n            font-size: 45px;\r\n            color: #FFFFFF;\r\n            line-height: 66px;\r\n            text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.57);\r\n            text-align: left;\r\n            font-style: normal;\r\n            text-transform: none;\r\n        }\r\n    }\r\n\r\n    .wg-box {\r\n        width: 700px;\r\n        margin-top: 16px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .wg-types {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            padding: 24px 85px 0 85px;\r\n\r\n            >p {\r\n                width: 111px;\r\n                height: 280px;\r\n                position: relative;\r\n                cursor: pointer;\r\n\r\n                >img {\r\n                    width: 111px;\r\n                    height: 280px;\r\n                    position: absolute;\r\n                    top: 0;\r\n                    left: 0;\r\n                    z-index: -1;\r\n                }\r\n\r\n                >span:nth-child(1) {\r\n\r\n                    font-family: Source Han Sans CN, Source Han Sans CN;\r\n                    font-weight: 400;\r\n                    font-size: 30px;\r\n                    color: #FFFFFF;\r\n                    position: absolute;\r\n                    top: 126px;\r\n                    left: 27px;\r\n                }\r\n\r\n                >span:nth-child(2) {\r\n\r\n                    font-family: DIN, DIN;\r\n                    font-weight: bold;\r\n                    font-size: 35px;\r\n                    color: #FFFFFF;\r\n                    position: absolute;\r\n                    top: 176px;\r\n                    left: 38px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .per-box {\r\n        display: flex;\r\n        margin-top: 17px;\r\n        justify-content: space-around;\r\n\r\n        >p {\r\n            width: 210px;\r\n            height: 217px;\r\n            position: relative;\r\n            cursor: pointer;\r\n\r\n            >img {\r\n                width: 210px;\r\n                height: 217px;\r\n                position: absolute;\r\n                left: 0;\r\n                top: 0;\r\n                z-index: -1;\r\n            }\r\n\r\n            >span:nth-child(1) {\r\n                width: 66px;\r\n                white-space: nowrap;\r\n                text-align: center;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 500;\r\n                font-size: 38px;\r\n                position: absolute;\r\n                left: 81px;\r\n                top: 61px;\r\n            }\r\n\r\n            >span:nth-child(2) {\r\n                width: 180px;\r\n                text-align: center;\r\n                white-space: nowrap;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: bold;\r\n                font-size: 30px;\r\n                color: #CAFBFF;\r\n                position: absolute;\r\n                top: 129px;\r\n                left: 24px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .tabs {\r\n        display: flex;\r\n        margin-top: 18px;\r\n        margin-bottom: 25px;\r\n\r\n        >p {\r\n            width: 338px;\r\n            height: 98px;\r\n            position: relative;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            margin-right: 44px;\r\n\r\n            >img {\r\n                position: absolute;\r\n                width: 338px;\r\n                height: 98px;\r\n                left: 0;\r\n                top: 0;\r\n                z-index: -1;\r\n            }\r\n\r\n            >span {\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 500;\r\n                font-size: 36px;\r\n                line-height: 108px;\r\n                font-style: normal;\r\n                text-transform: none;\r\n            }\r\n        }\r\n    }\r\n\r\n    .qualityItems {\r\n        width: 700px;\r\n\r\n        .carouselBox {\r\n            width: 94%;\r\n            height: 120px;\r\n            padding: 0 20px;\r\n            background: url(\"@/assets/images/right_slices/water/box_bg32.png\") no-repeat center;\r\n            background-size: 100%;\r\n\r\n            .carousel {\r\n                width: 100%;\r\n                height: 120px;\r\n                margin-bottom: 200px;\r\n\r\n                .el-car-item {\r\n                    width: 100%;\r\n                    height: 120px;\r\n                    display: flex;\r\n                    justify-content: space-around;\r\n\r\n                    .divSrc {\r\n                        width: 30%;\r\n                        height: 80px;\r\n                        cursor: pointer;\r\n\r\n                        .num {\r\n                            font-size: 24px;\r\n                            color: #fff;\r\n                            text-align: center;\r\n                            line-height: 50px;\r\n                        }\r\n\r\n                        .title {\r\n                            width: 100%;\r\n                            margin: 0 auto;\r\n                            text-align: center;\r\n                            font-size: 24px;\r\n                            font-weight: bold;\r\n                            color: #fff;\r\n                            overflow: hidden;\r\n                            white-space: nowrap;\r\n                            text-overflow: ellipsis;\r\n                        }\r\n                    }\r\n\r\n                    .line {\r\n                        position: relative;\r\n                        margin: 8px auto 0;\r\n                        width: 100%;\r\n                        /* height: 0px; */\r\n                        border: 2.5px solid #29DEFF;\r\n\r\n                    }\r\n\r\n                    .line::after {\r\n                        content: '';\r\n                        position: absolute;\r\n                        left: 50%;\r\n                        top: 3px;\r\n                        transform: translateX(-50%);\r\n                        display: block;\r\n                        width: 0;\r\n                        height: 0;\r\n                        border-top: .7rem solid #29DEFF;\r\n                        border-left: .7rem solid transparent;\r\n                        border-right: .7rem solid transparent;\r\n                        border-bottom: .7rem solid transparent;\r\n                    }\r\n\r\n                }\r\n\r\n                /deep/.el-carousel__arrow {\r\n                    display: none;\r\n                    font-size: 20px;\r\n                }\r\n\r\n                /deep/.el-carousel__container {\r\n                    position: relative;\r\n                    height: 100px;\r\n                }\r\n\r\n            }\r\n\r\n            .el-car-item {\r\n                width: 100%;\r\n                display: flex;\r\n\r\n                .img {\r\n                    width: 284px;\r\n                    height: 184px;\r\n                    margin-right: 20px;\r\n                    cursor: pointer;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAoIA,SAAAA,8BAAA;AACA,SAAAC,YAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,UAAA;AACA,OAAAC,YAAA;AACA,OAAAC,cAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,YAAA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EACA;MAAA;MACAC,UAAA;QACAC,KAAA;QACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAP,IAAA;MAGA;MACAQ,QAAA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACA;MACA;MACA;MACA;QACAb,IAAA;QACAc,GAAA;QACAC,GAAA;MACA,GACA;QACAf,IAAA;QACAc,GAAA;QACAC,GAAA;MACA,EACA;MACAC,gBAAA;QACAhB,IAAA;QACAC,IAAA,GACA;UACAgB,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA,GACA;UACAD,IAAA;UACAC,KAAA;QACA;MAEA;MACA;MACAC,gBAAA;MACAC,OAAA;MACAC,YAAA,GACA;QACArB,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,GACA;QACAf,IAAA;QACAe,GAAA;MACA,EACA;MACAO,MAAA;QAAA;QACAC,MAAA;QACAtB,IAAA;QACAuB,SAAA;QACAC,UAAA;MAEA;MACAC,SAAA;MACAC,WAAA;MACAC,aAAA;MACAC,UAAA;MACAC,MAAA;MACAC,YAAA;MACAC,YAAA;IACA;EACA;EACAC,UAAA;IACApC,YAAA;IACAC,cAAA;IACAC;EACA;EACAmC,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA;IACA,KAAAC,eAAA;IACA,KAAA7C,YAAA;IACA;EACA;EAEA8C,QAAA,GAEA;EAEAC,OAAA;IACA;IACA,MAAAC,uBAAA;MACA,MAAAjD,8BAAA,GAAAkD,IAAA,CAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAzC,IAAA,CAAAA,IAAA;QACA,KAAAE,YAAA,GAAAwC,IAAA;MACA;IACA;IACAC,gBAAA;MACA,KAAA1C,OAAA,oBAAAA,OAAA,iBAAAA,OAAA;IACA;IACA2C,SAAA;MACA,KAAAC,MAAA,CAAAC,MAAA;MACA,KAAAC,YAAA;QAAAhD,IAAA;QAAAC,IAAA;MAAA;MACA,KAAA6C,MAAA,CAAAC,MAAA,yCAAAC,YAAA;IACA;IACA;IACA,MAAAX,gBAAA;MACA,MAAA3C,iBAAA,GAAA+C,IAAA,CAAAC,GAAA;QACA,KAAA/B,aAAA,GAAA+B,GAAA,CAAAzC,IAAA,CAAAgD,KAAA;MACA;IACA;IACA;IACAC,SAAA;MACA,IAAAC,MAAA;QACA;MACA;MACA,KAAAC,SAAA,CAAAC,KAAA,eAAAF,MAAA;IACA;IAEA,MAAAG,eAAAC,GAAA;MACA,KAAAC,QAAA,GAAAD,GAAA;MACA,MAAA5D,qBAAA;QAAA6D,QAAA,EAAAD;MAAA,GAAAd,IAAA,CAAAC,GAAA;QACA,KAAAX,YAAA,GAAAW,GAAA,CAAAzC,IAAA,CAAAgD,KAAA;QACA,KAAAjB,YAAA;MACA;IACA;IACAyB,cAAA;MACA,KAAAzB,YAAA;IACA;IACA0B,SAAA1D,IAAA;MACA,KAAAoD,SAAA,CAAAC,KAAA,eAAArD,IAAA;IACA;IACA2D,cAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,SAAA;IACA;IACA;IACAG,WAAAC,IAAA,EAAAC,KAAA;MACA,KAAAjB,QAAA;MACA,SAAAtC,QAAA,IAAAuD,KAAA;QACA,KAAAvD,QAAA;MACA;QACA,KAAAA,QAAA,GAAAuD,KAAA;QACAvE,UAAA;UAAAI,IAAA,EAAAkE,IAAA,CAAAlE;QAAA,GAAAyC,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAzC,IAAA,CAAAmE,IAAA;YACA,KAAAtC,MAAA,GAAAY,GAAA,CAAAzC,IAAA,CAAAgD,KAAA,CAAAhD,IAAA;YACA,IAAAkD,MAAA;cACA;cACA,WAAAkB,IAAA,CAAAC,KAAA,MAAAxC,MAAA,IAAAyC,MAAA;YACA;YACA,KAAAnB,SAAA,CAAAC,KAAA,eAAAF,MAAA;YACA,KAAAC,SAAA,CAAAC,KAAA,oBAAAvB,MAAA,IAAA0C,KAAA;UACA;QACA;MACA;IACA;IACAC,OAAAzE,IAAA,EAAAmE,KAAA;MACA,OAAAO,OAAA,+BAAA1E,IAAA,GAAAmE,KAAA,YAAAvD,QAAA,IAAAuD,KAAA,IAAAnE,IAAA;IACA;IACA2E,OAAAR,KAAA;MACA,KAAAzD,SAAA,GAAAyD,KAAA;IACA;IACA;IACAS,YAAA;MAAA;MACA,KAAAhD,aAAA;IACA;IACA;IACAiD,uBAAAtB,GAAA;MACA,KAAA5B,WAAA,GAAA4B,GAAA;MACA,SAAA5B,WAAA;QACA,KAAAP,OAAA;QACA,KAAAQ,aAAA;QACA,IAAAuB,MAAA;UACA2B,QAAA,EAAAvB;QACA;QACA,KAAA1B,UAAA,QAAAb,gBAAA;QACA,KAAA+D,mBAAA,CAAA5B,MAAA;MAEA;IACA;IACA;IACA,MAAA3D,aAAA;MACA,MAAAA,YAAA,GAAAiD,IAAA,CAAAC,GAAA;QACA,KAAAvB,gBAAA,GAAAuB,GAAA,CAAAzC,IAAA,CAAAgD,KAAA;QACA,KAAAtB,WAAA,QAAAR,gBAAA,IAAA2D,QAAA;QACA,KAAAC,mBAAA;UACAD,QAAA,OAAAnD;QACA;QACA,IAAAqD,WAAA;QACA,IAAAC,OAAA;QACA,SAAA9D,gBAAA,SAAAA,gBAAA,EAAA+D,MAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,SAAAhE,gBAAA,CAAA+D,MAAA,MAAAC,CAAA;YACA,IAAAA,CAAA,cAAAA,CAAA;cACA,KAAAH,WAAA,CAAAC,OAAA;gBACAD,WAAA,CAAAI,IAAA,OAAAjE,gBAAA,CAAAgE,CAAA;cACA;gBACAH,WAAA,CAAAC,OAAA,EAAAG,IAAA,MAAAjE,gBAAA,CAAAgE,CAAA;cACA;YACA;cACAF,OAAA;cACAD,WAAA,CAAAI,IAAA,OAAAjE,gBAAA,CAAAgE,CAAA;YACA;UACA;QACA;QACA,KAAAhE,gBAAA,OAAA6D,WAAA;MACA,GAAAK,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;IACA,MAAAP,oBAAA5B,MAAA;MACA,MAAA1D,gBAAA,CAAA0D,MAAA,EAAAV,IAAA,CAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAzC,IAAA,CAAAgD,KAAA;QACA,KAAAvB,SAAA,GAAAiB,IAAA;QACA,IAAA8C,UAAA;UACAlE,MAAA;UACAtB,IAAA;UACAuB,SAAA;UACAC,UAAA;UACAiE,SAAA;QAEA;QACA/C,IAAA,CAAAgD,OAAA,EAAAzB,IAAA,EAAAC,KAAA;UACA,IAAAA,KAAA,OAAAsB,UAAA,CAAAxF,IAAA,CAAAmF,IAAA,EAAAlB,IAAA,CAAAY,QAAA,EAAAZ,IAAA,CAAA0B,IAAA,EAAA1B,IAAA,CAAA2B,UAAA,EAAA3B,IAAA,CAAA4B,SAAA;QAEA;QACA,KAAAxE,MAAA,GAAAmE,UAAA;MAEA,GAAAJ,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}