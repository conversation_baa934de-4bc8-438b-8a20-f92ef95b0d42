{"ast": null, "code": "import { getStandardApi } from '@/api/userMenu';\nimport DiaLogPdf from '@/components/DiaLogPdf.vue';\nexport default {\n  name: 'standardSpecification',\n  data() {\n    return {\n      pageNum: 1,\n      pageSize: 10,\n      total: 0,\n      inputValue: \"\",\n      standerdList: [],\n      pdfDialogFlag: false,\n      PdfUrl: ''\n    };\n  },\n  components: {\n    DiaLogPdf\n  },\n  watch: {},\n  computed: {},\n  mounted() {\n    this.getStandardApiList();\n  },\n  updated() {},\n  methods: {\n    resetQuery() {\n      this.inputValue = \"\";\n      this.pageNum = 1;\n      this.getStandardApiList();\n    },\n    // 关闭pdf弹窗\n    pdfPopWindow(nv) {\n      this.pdfDialogFlag = nv;\n    },\n    // 文件预览\n    handlePosition(item) {\n      this.pdfDialogFlag = true;\n      this.PdfUrl = item.pdfUrl;\n    },\n    // 文件下载\n    downPdf(row) {\n      var a = document.createElement(\"a\");\n      a.href = row.pdfUrl;\n      a.download = row.name;\n      a.style.display = \"none\";\n      document.body.appendChild(a);\n      a.click();\n      a.remove();\n    },\n    // 监听滚动事件\n    // onScroll(event) {\n    //     const container = event.target;\n    //     if (container.scrollTop + container.clientHeight >= container.scrollHeight) {\n    //         this.pageNum++;\n    //         this.getStandardApiList();\n    //     }\n    // },\n    // 获取标准规范列表数据\n    getStandardApiList() {\n      // if (this.standerdList.length >= this.total && this.standerdList.length > 0) return;\n      getStandardApi({\n        pageNum: this.pageNum,\n        pageSize: this.pageSize,\n        name: this.inputValue\n      }).then(res => {\n        if (res.data.code == 200) {\n          this.standerdList = res.data.extra.data;\n          this.pageNum = res.data.extra.pageNum;\n          this.pageSize = res.data.extra.pageSize;\n          this.total = res.data.extra.total;\n        }\n      });\n    },\n    // 搜索\n    searchFn() {\n      this.pageNum = 1;\n      this.standerdList = [];\n      this.getStandardApiList();\n    },\n    // 监听页码变化\n    handleCurrentChange(val) {\n      this.pageNum = val;\n      this.standerdList = [];\n      this.getStandardApiList();\n    }\n  }\n};", "map": {"version": 3, "names": ["getStandardApi", "DiaLogPdf", "name", "data", "pageNum", "pageSize", "total", "inputValue", "standerdList", "pdfDialogFlag", "PdfUrl", "components", "watch", "computed", "mounted", "getStandardApiList", "updated", "methods", "reset<PERSON><PERSON>y", "pdfPopWindow", "nv", "handlePosition", "item", "pdfUrl", "downPdf", "row", "a", "document", "createElement", "href", "download", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "then", "res", "code", "extra", "searchFn", "handleCurrentChange", "val"], "sources": ["src/views/mainPage/components/resource-center/components/standardSpecification.vue"], "sourcesContent": ["<template>\n    <div class=\"standard-specification\">\n        <div class=\"header-search\">\n            <div class=\"title\">标准查询</div>\n            <el-input placeholder=\"请输入关键词搜索\" v-model=\"inputValue\" style=\"caret-color: #fff;\">\n                <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\n            </el-input>\n            <span class=\"search-btn\" @click=\"searchFn\">查询</span>\n            <span class=\"refresh-btn\" @click=\"resetQuery\">重置</span>\n        </div>\n        <div class=\"content\">\n            <div class=\"list\" v-for=\"item in standerdList\" :key=\"item.id\">\n                <div class=\"title\">\n                    <span>{{ item.name }}</span>\n                    <span>发布时间：{{ item.rtime }}</span>\n                </div>\n                <p><b>摘要：</b> {{ item.abstractInfo }}</p>\n                <p><b>关键词：</b> {{ item.keyWords }}</p>\n                <div class=\"btns\">\n                    <span @click=\"handlePosition(item)\">文档预览</span>\n                    <span @click=\"downPdf(item)\"> <i class=\" el-icon-download\"></i>文档下载</span>\n                </div>\n            </div>\n\n        </div>\n        <DiaLogPdf v-if=\"pdfDialogFlag\" ref=\"diaLogPdf\" :url=\"PdfUrl\" :dialogVisible=\"pdfDialogFlag\"\n            @updateFun=\"pdfPopWindow\">\n        </DiaLogPdf>\n        <el-pagination @current-change=\"handleCurrentChange\" :current-page=\"pageNum\" :page-size=\"pageSize\"\n            layout=\"total, prev, pager, next, jumper\" :total=\"total\">\n        </el-pagination>\n    </div>\n</template>\n<script>\nimport { getStandardApi } from '@/api/userMenu'\nimport DiaLogPdf from '@/components/DiaLogPdf.vue'\nexport default {\n    name: 'standardSpecification',\n    data() {\n        return {\n            pageNum: 1,\n            pageSize: 10,\n            total: 0,\n            inputValue: \"\",\n            standerdList: [],\n            pdfDialogFlag: false,\n            PdfUrl: '',\n        };\n    },\n    components: {\n        DiaLogPdf,\n    },\n    watch: {\n\n    },\n    computed: {\n\n    },\n\n    mounted() {\n        this.getStandardApiList();\n    },\n\n    updated() {\n\n    },\n    methods: {\n        resetQuery() {\n            this.inputValue = \"\";\n            this.pageNum = 1;\n            this.getStandardApiList();\n        },\n        // 关闭pdf弹窗\n        pdfPopWindow(nv) {\n            this.pdfDialogFlag = nv\n        },\n        // 文件预览\n        handlePosition(item) {\n            this.pdfDialogFlag = true\n            this.PdfUrl = item.pdfUrl\n        },\n        // 文件下载\n        downPdf(row) {\n            var a = document.createElement(\"a\");\n            a.href = row.pdfUrl;\n            a.download = row.name;\n            a.style.display = \"none\";\n            document.body.appendChild(a);\n            a.click();\n            a.remove();\n        },\n        // 监听滚动事件\n        // onScroll(event) {\n        //     const container = event.target;\n        //     if (container.scrollTop + container.clientHeight >= container.scrollHeight) {\n        //         this.pageNum++;\n        //         this.getStandardApiList();\n        //     }\n        // },\n        // 获取标准规范列表数据\n        getStandardApiList() {\n            // if (this.standerdList.length >= this.total && this.standerdList.length > 0) return;\n            getStandardApi({ pageNum: this.pageNum, pageSize: this.pageSize, name: this.inputValue }).then(res => {\n                if (res.data.code == 200) {\n                    this.standerdList = res.data.extra.data;\n                    this.pageNum = res.data.extra.pageNum;\n                    this.pageSize = res.data.extra.pageSize;\n                    this.total = res.data.extra.total;\n                }\n            })\n        },\n        // 搜索\n        searchFn() {\n            this.pageNum = 1;\n            this.standerdList = []\n            this.getStandardApiList();\n        },\n        // 监听页码变化\n        handleCurrentChange(val) {\n            this.pageNum = val;\n            this.standerdList = []\n            this.getStandardApiList();\n        }\n    },\n};\n</script>\n<style lang=\"less\" scoped>\n.standard-specification {\n    width: 100%;\n    height: 100%;\n    color: #fff;\n\n    .header-search {\n        margin-left: 180px;\n        margin-top: 73px;\n        margin-bottom: 10px;\n        width: 100%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        .title {\n            width: 168px;\n            height: 33px;\n            font-family: GLJ-GBT;\n            font-weight: 400;\n            font-size: 40px;\n            color: #6FBBFC;\n            line-height: 18px;\n        }\n\n        :deep(.el-input) {\n            margin-left: 76px;\n            width: 762px;\n            height: 51px;\n            background: rgba(0, 180, 255, 0.24);\n            border-radius: 4px;\n\n            .el-input__inner {\n                height: 50px !important;\n                border: none;\n                background-color: transparent;\n                color: #fff;\n            }\n\n            .el-input__inner::-webkit-input-placeholder {\n                color: #fff\n            }\n\n            .el-icon-search {\n                color: #fff;\n                cursor: pointer;\n            }\n        }\n\n\n        >span {\n            width: 122px;\n            height: 51px;\n\n            font-family: Source Han Sans CN;\n            font-weight: 500;\n            font-size: 21px;\n            color: #FFFFFF;\n            line-height: 51px;\n            text-align: center;\n            margin-left: 17px;\n            cursor: pointer;\n        }\n\n        .search-btn {\n            background: #007FEA;\n        }\n\n        .refresh-btn {\n            background: #07c9b2;\n        }\n\n    }\n\n    .content {\n        width: calc(100% - 40px - 66px);\n        margin: 0 20px;\n        height: 829px;\n        background: rgba(40, 104, 141, 0.23);\n        border-radius: 5px 5px 5px 5px;\n        border: 2px solid #4E8FC0;\n        padding: 0 33px;\n        overflow-x: hidden;\n        overflow-y: scroll;\n\n        .list {\n            margin-top: 35px;\n            height: 233px;\n            background: rgba(40, 104, 141, 0.23);\n            box-shadow: 0px 2 2px 0px rgba(0, 0, 0, 0.6);\n            border-radius: 5px 5px 5px 5px;\n            border: 2px solid rgba(171, 218, 223, 0.23);\n            padding: 0 43px;\n            position: relative;\n\n            .title {\n                padding: 16px 0;\n                display: flex;\n                justify-content: space-between;\n                font-family: Source Han Sans CN, Source Han Sans CN;\n                font-weight: bold;\n                font-size: 17px;\n                color: #FFFFFF;\n                line-height: 22px;\n                text-align: left;\n                font-style: normal;\n                text-transform: none;\n            }\n\n            >p {\n                line-height: 22px;\n                font-family: Source Han Sans CN, Source Han Sans CN;\n                font-size: 16px;\n                text-align: left;\n                font-style: normal;\n                text-transform: none;\n                color: #ffffff9d;\n\n\n                b {\n                    color: #FFFFFF;\n                    font-size: 18px;\n\n                    font-weight: bold;\n                }\n            }\n\n            >p:nth-child(2) {\n                margin-bottom: 35px;\n                height: 105px;\n                line-height: 40px;\n\n            }\n\n            .btns {\n                display: flex;\n                align-items: center;\n                position: absolute;\n                bottom: 21px;\n                right: 58px;\n\n                >span:nth-child(1) {\n                    width: 79px;\n                    height: 30px;\n                    background: rgba(147, 162, 167, 0.2);\n                    border-radius: 4px 4px 4px 4px;\n                    border: 1px solid #D1DEE2;\n                    line-height: 30px;\n                    text-align: center;\n                    font-family: Source Han Sans CN, Source Han Sans CN;\n                    font-weight: 400;\n                    font-size: 15px;\n                    color: #63E5FF;\n                    font-style: normal;\n                    text-transform: none;\n                    cursor: pointer;\n                }\n\n                >span:nth-child(2) {\n                    cursor: pointer;\n                    margin-left: 27px;\n                    font-family: Source Han Sans CN, Source Han Sans CN;\n                    font-weight: 400;\n                    font-size: 15px;\n                    color: #75B9FF;\n                    line-height: 22px;\n                    text-align: left;\n                    font-style: normal;\n                    text-transform: none;\n                }\n            }\n\n        }\n    }\n\n\n    :deep(.el-pagination) {\n        text-align: center;\n        // margin-right: 33px;\n        margin-top: 20px;\n\n        .el-pagination__total {\n            color: #fff;\n        }\n\n        .el-pagination__sizes {\n            input {\n                background: #072C44;\n                border-radius: 4px;\n                border: 1px solid #4BDBFF;\n                color: #fff;\n            }\n        }\n\n        >button {\n            color: #fff;\n            background-color: transparent;\n        }\n\n        ul {\n            li {\n                background-color: transparent;\n                color: #fff;\n            }\n        }\n\n        .el-pager .active {\n            color: #4BDBFF;\n        }\n\n        .el-pagination__jump {\n            color: #fff;\n\n            input {\n                background: #072C44;\n                border-radius: 4px;\n                border: 1px solid #4BDBFF;\n                color: #4BDBFF;\n            }\n        }\n    }\n\n}\n</style>"], "mappings": "AAkCA,SAAAA,cAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MACAC,MAAA;IACA;EACA;EACAC,UAAA;IACAV;EACA;EACAW,KAAA,GAEA;EACAC,QAAA,GAEA;EAEAC,QAAA;IACA,KAAAC,kBAAA;EACA;EAEAC,QAAA,GAEA;EACAC,OAAA;IACAC,WAAA;MACA,KAAAX,UAAA;MACA,KAAAH,OAAA;MACA,KAAAW,kBAAA;IACA;IACA;IACAI,aAAAC,EAAA;MACA,KAAAX,aAAA,GAAAW,EAAA;IACA;IACA;IACAC,eAAAC,IAAA;MACA,KAAAb,aAAA;MACA,KAAAC,MAAA,GAAAY,IAAA,CAAAC,MAAA;IACA;IACA;IACAC,QAAAC,GAAA;MACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAJ,GAAA,CAAAF,MAAA;MACAG,CAAA,CAAAI,QAAA,GAAAL,GAAA,CAAAvB,IAAA;MACAwB,CAAA,CAAAK,KAAA,CAAAC,OAAA;MACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAR,CAAA;MACAA,CAAA,CAAAS,KAAA;MACAT,CAAA,CAAAU,MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArB,mBAAA;MACA;MACAf,cAAA;QAAAI,OAAA,OAAAA,OAAA;QAAAC,QAAA,OAAAA,QAAA;QAAAH,IAAA,OAAAK;MAAA,GAAA8B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAnC,IAAA,CAAAoC,IAAA;UACA,KAAA/B,YAAA,GAAA8B,GAAA,CAAAnC,IAAA,CAAAqC,KAAA,CAAArC,IAAA;UACA,KAAAC,OAAA,GAAAkC,GAAA,CAAAnC,IAAA,CAAAqC,KAAA,CAAApC,OAAA;UACA,KAAAC,QAAA,GAAAiC,GAAA,CAAAnC,IAAA,CAAAqC,KAAA,CAAAnC,QAAA;UACA,KAAAC,KAAA,GAAAgC,GAAA,CAAAnC,IAAA,CAAAqC,KAAA,CAAAlC,KAAA;QACA;MACA;IACA;IACA;IACAmC,SAAA;MACA,KAAArC,OAAA;MACA,KAAAI,YAAA;MACA,KAAAO,kBAAA;IACA;IACA;IACA2B,oBAAAC,GAAA;MACA,KAAAvC,OAAA,GAAAuC,GAAA;MACA,KAAAnC,YAAA;MACA,KAAAO,kBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}