/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.23/esri/copyright.txt for details.
*/
import{FeatureServiceDatabaseType as e}from"./shared.js";import{WhereClause as r}from"../../../core/sql/WhereClause.js";import{DateTime as t}from"luxon";function n(e,r){return c(e.parseTree,r,e.parameters)}function a(e,r,t){return c(e,r,t)}function s(t,n,a,s){return r.create(c(t.parseTree,e.Standardised,t.parameters,n,a),s)}function o(t,a,s="AND"){return r.create("(("+n(t,e.Standardised)+")"+s+"("+n(a,e.Standardised)+"))",t.fieldsIndex)}function c(e,r,t,n=null,a=null){let s,o,f,d;switch(e.type){case"interval":return T(c(e.value,r,t,n,a),e.qualifier,e.op);case"case_expression":{let s=" CASE ";"simple"===e.format&&(s+=c(e.operand,r,t,n,a));for(let o=0;o<e.clauses.length;o++)s+=" WHEN "+c(e.clauses[o].operand,r,t,n,a)+" THEN "+c(e.clauses[o].value,r,t,n,a);return null!==e.else&&(s+=" ELSE "+c(e.else,r,t,n,a)),s+=" END ",s}case"param":{const n=t[e.value.toLowerCase()];if("string"==typeof n){return"'"+t[e.value.toLowerCase()].toString().replace(/'/g,"''")+"'"}if(n instanceof Date)return u(n,r);if(n instanceof Array){const e=[];for(let t=0;t<n.length;t++)"string"==typeof n[t]?e.push("'"+n[t].toString().replace(/'/g,"''")+"'"):n[t]instanceof Date?e.push(u(n[t],r)):e.push(n[t].toString());return e}return n.toString()}case"expr_list":o=[];for(const s of e.value)o.push(c(s,r,t,n,a));return o;case"unary_expr":return" ( NOT "+c(e.expr,r,t,n,a)+" ) ";case"binary_expr":switch(e.operator){case"AND":return" ("+c(e.left,r,t,n,a)+" AND "+c(e.right,r,t,n,a)+") ";case"OR":return" ("+c(e.left,r,t,n,a)+" OR "+c(e.right,r,t,n,a)+") ";case"IS":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return" ("+c(e.left,r,t,n,a)+" IS NULL )";case"ISNOT":if("null"!==e.right.type)throw new Error("Unsupported RHS for IS");return" ("+c(e.left,r,t,n,a)+" IS NOT NULL )";case"IN":return s=[],"expr_list"===e.right.type?(s=c(e.right,r,t,n,a)," ("+c(e.left,r,t,n,a)+" IN ("+s.join(",")+")) "):(d=c(e.right,r,t,n,a),d instanceof Array?" ("+c(e.left,r,t,n,a)+" IN ("+d.join(",")+")) ":" ("+c(e.left,r,t,n,a)+" IN ("+d+")) ");case"NOT IN":return s=[],"expr_list"===e.right.type?(s=c(e.right,r,t,n,a)," ("+c(e.left,r,t,n,a)+" NOT IN ("+s.join(",")+")) "):(d=c(e.right,r,t,n,a),d instanceof Array?" ("+c(e.left,r,t,n,a)+" NOT IN ("+d.join(",")+")) ":" ("+c(e.left,r,t,n,a)+" NOT IN ("+d+")) ");case"BETWEEN":return f=c(e.right,r,t,n,a)," ("+c(e.left,r,t,n,a)+" BETWEEN "+f[0]+" AND "+f[1]+" ) ";case"NOTBETWEEN":return f=c(e.right,r,t,n,a)," ("+c(e.left,r,t,n,a)+" NOT BETWEEN "+f[0]+" AND "+f[1]+" ) ";case"LIKE":return""!==e.escape?" ("+c(e.left,r,t,n,a)+" LIKE "+c(e.right,r,t,n,a)+" ESCAPE '"+e.escape+"') ":" ("+c(e.left,r,t,n,a)+" LIKE "+c(e.right,r,t,n,a)+") ";case"NOT LIKE":return""!==e.escape?" ("+c(e.left,r,t,n,a)+" NOT LIKE "+c(e.right,r,t,n,a)+" ESCAPE '"+e.escape+"') ":" ("+c(e.left,r,t,n,a)+" NOT LIKE "+c(e.right,r,t,n,a)+") ";case"<>":case"<":case">":case">=":case"<=":case"=":case"*":case"-":case"+":case"/":return" ("+c(e.left,r,t,n,a)+" "+e.operator+" "+c(e.right,r,t,n,a)+") "}throw new Error("Not Supported Operator "+e.operator);case"null":return"null";case"bool":return!0===e.value?"1":"0";case"string":return"'"+e.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return u(e.value,r);case"number":return e.value.toString();case"current_time":return i("date"===e.mode,r);case"column_ref":return n&&n.toLowerCase()===e.column.toLowerCase()?"("+a+")":e.column;case"function":{const s=c(e.args,r,t,n,a);return l(e.name,s,r)}}throw new Error("Unsupported sql syntax "+e.type)}function l(r,t,n){switch(r.toLowerCase().trim()){case"abs":if(1!==t.length)throw new Error("Invalid Parameter for call to ABS");return"abs("+t[0]+")";case"ceiling":case"ceil":if(1!==t.length)throw new Error("Invalid Parameter for call to CEILING");switch(n){case e.Standardised:case e.StandardisedNoInterval:}return"CEILING("+t[0]+")";case"floor":if(1!==t.length)throw new Error("Invalid Parameter for call to Floor");return"FLOOR("+t[0]+")";case"log":if(1!==t.length)throw new Error("Invalid Parameter for call to LOG");return"LOG("+t[0]+")";case"log10":if(1!==t.length)throw new Error("Invalid Parameter for call to LOG10");return"LOG10("+t[0]+")";case"power":if(2!==t.length)throw new Error("Invalid Parameter for call to POWER");return"POWER("+t[0]+","+t[1]+")";case"round":if(2===t.length)return"ROUND("+t[0]+","+t[1]+")";if(1===t.length)return"ROUND("+t[0]+")";throw new Error("Invalid Parameter for call to ROUND");case"truncate":if(t.length<1||t.length>2)throw new Error("Invalid Parameter for TRUNCATE function");return n===e.SqlServer?"ROUND("+t[0]+(1===t.length?"0":","+t[1])+",1)":"TRUNCATE("+t[0]+(1===t.length?")":","+t[1]+")");case"char_length":case"len":if(1!==t.length)throw new Error("Invalid Parameter for CHAR_LENGTH function");switch(n){case e.SqlServer:return"LEN("+t[0]+")";case e.Oracle:return"LENGTH("+t[0]+")";default:return"CHAR_LENGTH("+t[0]+")"}case"concat":if(t.length<1)throw new Error("Invalid Parameter for CONCAT function");{let e="CONCAT(";for(let r=0;r<t.length;r++)0!==r&&(e+=","),e+=t[r];return e+=")",e}case"lower":case"lcase":if(1!==t.length)throw new Error("Invalid Parameter for Lower function");return"LOWER("+t[0]+")";case"upper":case"ucase":if(1!==t.length)throw new Error("Invalid Parameter for Upper function");return"UPPER("+t[0]+")";case"substring":{let r="";switch(n){case e.Oracle:return r="SUBSTR("+t[0]+","+t[1],3===t.length&&(r+=","+t[2]),r+=")",r;case e.SqlServer:return r=3===t.length?"SUBSTRING("+t[0]+","+t[1]+","+t[2]+")":"SUBSTRING("+t[0]+",  "+t[1]+", LEN("+t[0]+") - "+t[1]+")",r;default:return r="SUBSTRING("+t[0]+" FROM "+t[1],3===t.length&&(r+=" FOR "+t[2]),r+=")",r}}case"extract":return"EXTRACT("+t[0].replace(/\'/g,"")+" FROM "+t[1]+")"}throw new Error("Function Not Recognised")}function u(r,n){const a=t.fromJSDate(r),s=0===a.hour&&0===a.minute&&0===a.second&&0===a.millisecond;switch(n){case e.FILEGDB:case e.Standardised:case e.StandardisedNoInterval:return s?`date '${a.toFormat("yyyy-LL-dd")}'`:`date '${a.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case e.Oracle:return s?`TO_DATE('${a.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${a.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case e.SqlServer:return`'${a.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case e.PGDB:return`#${a.toFormat(s?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case e.Postgres:return`TIMESTAMP '${a.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`date '${a.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function i(r,t){switch(t){case e.FILEGDB:case e.Standardised:case e.StandardisedNoInterval:case e.Oracle:return r?"CURRENT_DATE":"CURRENT_TIMESTAMP";case e.SqlServer:return r?"CAST(GETDATE() AS DATE)":"GETDATE()";case e.PGDB:case e.Postgres:default:return r?"CURRENT_DATE":"CURRENT_TIMESTAMP"}}function f(e,r,t={}){const n={},a={},s={esriFieldTypeSmallInteger:"integer",esriFieldTypeInteger:"integer",esriFieldTypeSingle:"double",esriFieldTypeDouble:"double",esriFieldTypeString:"string",esriFieldTypeDate:"date",esriFieldTypeOID:"integer",oid:"integer",long:"integer","small-integer":"integer",integer:"integer",single:"double",double:"double",date:"date",string:"string"};for(const o of r){const e=s[o.type];n[o.name.toLowerCase()]=void 0===e?"":e}for(const o in t){const e=s[t[o]];a[o.toLowerCase()]=void 0===e?"":e}switch(d(n,e.parseTree,e.parameters,a)){case"double":return"double";case"integer":return"integer";case"date":return"date";case"string":return"string"}return""}function d(e,r,t,n){let a;switch(r.type){case"interval":return"integer";case"case_expression":{const a=[];if("simple"===r.format){for(let s=0;s<r.clauses.length;s++)a.push(d(e,r.clauses[s].value,t,n));null!==r.else&&a.push(d(e,r.else,t,n))}else{for(let s=0;s<r.clauses.length;s++)a.push(d(e,r.else,t,n));null!==r.else&&a.push(d(e,r.else,t,n))}return g(a)}case"param":{const e=n[r.value.toLowerCase()];if(void 0===e&&t){const e=t[r.value.toLowerCase()];if(void 0===e)return"";if(null===e)return"";if("string"==typeof e||e instanceof String)return"string";if("boolean"==typeof e)return"boolean";if(e instanceof Date)return"date";if("number"==typeof e)return e%1==0?"integer":"double"}return void 0===e?"":e}case"expr_list":{const a=[];for(const s of r.value)a.push(d(e,s,t,n));return a}case"unary_expr":return"boolean";case"binary_expr":switch(r.operator){case"AND":case"OR":case"IN":case"NOT IN":case"BETWEEN":case"NOTBETWEEN":case"LIKE":case"NOT LIKE":case"<>":case"<":case">":case">=":case"<=":case"=":return"boolean";case"IS":case"ISNOT":if("null"!==r.right.type)throw new Error("Unsupported RHS for IS");return"boolean";case"*":case"-":case"+":case"/":return g([d(e,r.left,t,n),d(e,r.right,t,n)]);default:throw new Error("Not Supported Operator "+r.operator)}case"null":return"";case"bool":return"boolean";case"string":return"string";case"number":return null===r.value?"":r.value%1==0?"integer":"double";case"date":case"timestamp":case"current_time":return"date";case"column_ref":{const t=e[r.column.toLowerCase()];return void 0===t?"":t}case"function":switch(r.name.toLowerCase()){case"position":case"extract":case"char_length":return"integer";case"round":return a=d(e,r.args,t,n),a instanceof Array?a.length>0?a[0]:"":a;case"sign":return a=d(e,r.args,t,n),a instanceof Array&&(a=g(a)),"integer"===a||"double"===a?a:"double";case"ceiling":case"floor":case"abs":{const a=d(e,r.args,t,n);return a instanceof Array?g(a):a}case"area":case"length":case"log":case"log10":case"sin":case"cos":case"tan":case"asin":case"acos":case"atan":case"power":case"truncate":return"double";case"substring":case"trim":case"concat":case"lower":case"upper":return"string"}return""}throw new Error("Unsupported sql syntax "+r.type)}const p={boolean:1,string:2,integer:3,double:4,date:5};function g(e){if(e){let r="";for(const t of e)""!==t&&(r=""===r||p[r]<p[t]?t:r);return r}return""}function h(e,r){return y(e.parseTree,r)}function E(e){return"column_ref"===e.parseTree.type}function y(e,r){if(null==e)return!1;switch(e.type){case"when_clause":return y(e.operand,r)||y(e.value,r);case"case_expression":for(const t of e.clauses)if(y(t,r))return!0;return!("simple"!==e.format||!y(e.operand,r))||!(null===e.else||!y(e.else,r));case"param":case"null":case"bool":case"date":case"timestamp":case"string":case"number":return!1;case"expr_list":for(const t of e.value)if(y(t,r))return!0;return!1;case"unary_expr":return y(e.expr,r);case"binary_expr":return y(e.left,r)||y(e.right,r);case"column_ref":return r.toLowerCase()===e.column.toLowerCase();case"function":return y(e.args,r)}return!1}function m(e){let r="";return r+=e.period.toUpperCase(),r}function T(e,r,t){let n="";return n="interval-period"===r.type?m(r):m(r.start)+" TO "+m(r.end),"INTERVAL "+t+" "+e+" "+n}export{o as combine,T as convertIntervalToSql,E as isSingleField,u as makeDateString,i as makeToday,f as predictType,s as reformulateWithoutField,h as scanForField,n as toWhereClause,a as toWhereClauseFromTree,l as translateFunctionToDatabaseSpecific};
