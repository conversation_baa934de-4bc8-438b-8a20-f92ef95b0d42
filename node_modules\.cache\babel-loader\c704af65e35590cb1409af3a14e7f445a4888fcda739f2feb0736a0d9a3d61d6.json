{"ast": null, "code": "import { getkeyArea } from '@/api/index.js';\nimport { mapState } from 'vuex';\nimport BaseLayer from '@/views/mainPage/components/timeBaseLayer';\nexport default {\n  name: 'rightView',\n  data() {\n    return {\n      buildData: '',\n      list: [],\n      activeIndex: null,\n      flag: false,\n      PreliminaryAndManage: '重点区域'\n    };\n  },\n  components: {\n    BaseLayer\n  },\n  watch: {\n    ueTxt(nv) {\n      if (nv && nv.POIType == '视频监控') {\n        this.$emit('showEvent', false);\n      } else if (nv?.typename == '数据图层') {\n        this.$store.commit(\"action/getIsShowData\", true);\n        // this.isShowData=true\n        this.buildData = nv.data;\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      ueTxt: state => state.dataChannelText,\n      isShowData: state => state.action.isShowData\n    })\n  },\n  beforeDestroy() {\n    this.$store.commit(\"action/clearAll\", false);\n    this.deleteFn();\n  },\n  mounted() {\n    this.getkeyAreaList();\n  },\n  updated() {},\n  methods: {\n    tabsFn(name) {\n      this.deleteFn();\n      this.PreliminaryAndManage = name;\n    },\n    closeData() {\n      this.$store.commit(\"action/getIsShowData\", false);\n      // this.isShowData=false\n      this.buildData = '';\n    },\n    // 数据图层ue交互\n    getDataLayer(val) {\n      this.deleteFn();\n      if (val) {\n        let {\n          query,\n          url\n        } = val;\n        let params = {\n          \"mode\": \"add\",\n          \"sources\": JSON.parse(url)\n        };\n        this.$eventBus.$emit('senTxtToUe', params); // 撒点\n        this.$eventBus.$emit('senTxtToUe', query); // 视角\n      }\n    },\n    showList() {\n      this.flag = !this.flag;\n      this.getkeyAreaList();\n    },\n    // 打开监控\n    openPlay(id) {\n      // this.getLatLon(, 'play')\n      this.$store.commit(\"action/getVideoPlayerList\", {\n        channalCode: id\n      });\n      this.$store.commit(\"action/getIsShowNumInitChange\", true);\n      this.$store.commit(\"action/getVideoPlayerBox\", true);\n    },\n    setUrl(index) {\n      return require(`@/assets/images/comprehensiveSituation/key-area${this.activeIndex == index ? '-active' : ''}.png`);\n    },\n    sendUeFn(item, index) {\n      this.deleteFn();\n      if (this.activeIndex == index) {\n        this.activeIndex = null;\n      } else {\n        this.activeIndex = index;\n        this.$store.commit(\"action/clearAll\", false); //清除视频弹框\n        let params = {\n          \"mode\": \"add\",\n          \"sources\": JSON.parse(item.points)\n        };\n        this.$eventBus.$emit('senTxtToUe', params); //撒点\n        this.$eventBus.$emit('senTxtToUe', item.angle); //聚焦到当前位置\n      }\n    },\n    //删除点位\n    deleteFn() {\n      // this.flag = false;\n      let params = {\n        \"mode\": \"delete\"\n      };\n      this.$eventBus.$emit('senTxtToUe', params);\n    },\n    //获取重点区域列表\n    getkeyAreaList() {\n      getkeyArea({\n        pageNum: 1,\n        pageSize: 100\n      }).then(res => {\n        if (res.data.code == 200) {\n          if (!this.flag) {\n            this.list = res.data.extra.data.slice(0, 12);\n          } else {\n            this.list = res.data.extra.data;\n          }\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["getkeyArea", "mapState", "Base<PERSON><PERSON>er", "name", "data", "buildData", "list", "activeIndex", "flag", "PreliminaryAndManage", "components", "watch", "ueTxt", "nv", "POIType", "$emit", "typename", "$store", "commit", "computed", "state", "dataChannelText", "isShowData", "action", "<PERSON><PERSON><PERSON><PERSON>", "deleteFn", "mounted", "getkeyAreaList", "updated", "methods", "tabsFn", "closeData", "get<PERSON>ata<PERSON><PERSON><PERSON>", "val", "query", "url", "params", "JSON", "parse", "$eventBus", "showList", "openPlay", "id", "channalCode", "setUrl", "index", "require", "sendUeFn", "item", "points", "angle", "pageNum", "pageSize", "then", "res", "code", "extra", "slice"], "sources": ["src/views/comprehensiveSituation/components/city-manage/right-view.vue"], "sourcesContent": ["<template>\r\n    <div class=\"rightView\">\r\n        <div class=\"two-title\" @click=\"showList\">\r\n            <div :class=\"PreliminaryAndManage == '重点区域' ? 'action' : ''\" @click.stop=\"tabsFn('重点区域')\">\r\n                重点区域\r\n            </div>\r\n            |\r\n            <div :class=\"PreliminaryAndManage == '重点资源' ? 'action' : ''\" @click.stop=\"tabsFn('重点资源')\">\r\n                重点场景\r\n            </div>\r\n        </div>\r\n        <div class=\"right-list\" v-if=\"PreliminaryAndManage == '重点区域'\">\r\n            <div class=\"list\" v-for=\"(item, index) in list\" @click=\"sendUeFn(item, index)\">\r\n                <img class=\"bg\" ref=\"imgbg\" :src=\"setUrl(index)\" alt=\"\">\r\n                <p>{{ item.name }}</p>\r\n                <img class=\"img\" :src=\"item.imgPath\" alt=\"\">\r\n            </div>\r\n        </div>\r\n        <div class=\"baseLayer\" v-else>\r\n            <BaseLayer @changeDataLayer='getDataLayer'></BaseLayer>\r\n            <div class=\"dataBox\" v-if=\"isShowData\">\r\n                <div class=\"delete\" @click=\"closeData\">\r\n                    <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n                </div>\r\n\r\n                <ul>\r\n                    <li>\r\n                        <span class=\"dataT\">名称:</span>\r\n                        <span>{{ buildData.name }}</span>\r\n                    </li>\r\n                    <li>\r\n                        <span class=\"dataT\">地址:</span>\r\n                        <span>{{ buildData.address }}</span>\r\n                    </li>\r\n                    <li>\r\n                        <span class=\"dataT\">所属派出所:</span>\r\n                        <span>{{ buildData.local }}</span>\r\n                    </li>\r\n                    <li>\r\n                        <span class=\"dataT\">社区:</span>\r\n                        <span>{{ buildData.society }}</span>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getkeyArea } from '@/api/index.js'\r\nimport { mapState } from 'vuex'\r\nimport BaseLayer from '@/views/mainPage/components/timeBaseLayer'\r\nexport default {\r\n    name: 'rightView',\r\n    data() {\r\n        return {\r\n            buildData: '',\r\n            list: [],\r\n            activeIndex: null,\r\n            flag: false,\r\n            PreliminaryAndManage: '重点区域',\r\n        };\r\n    },\r\n    components: {\r\n        BaseLayer\r\n    },\r\n    watch: {\r\n        ueTxt(nv) {\r\n            if (nv && nv.POIType == '视频监控') {\r\n                this.$emit('showEvent', false);\r\n            } else if (nv?.typename == '数据图层') {\r\n                this.$store.commit(\"action/getIsShowData\", true)\r\n                // this.isShowData=true\r\n                this.buildData = nv.data\r\n            }\r\n        },\r\n    },\r\n    computed: {\r\n        ...mapState({\r\n            ueTxt: state => state.dataChannelText,\r\n            isShowData: state => state.action.isShowData,\r\n        }),\r\n    },\r\n    beforeDestroy() {\r\n        this.$store.commit(\"action/clearAll\", false)\r\n        this.deleteFn();\r\n    },\r\n    mounted() {\r\n        this.getkeyAreaList();\r\n    },\r\n\r\n    updated() {\r\n\r\n    },\r\n\r\n    methods: {\r\n        tabsFn(name) {\r\n            this.deleteFn();\r\n            this.PreliminaryAndManage = name;\r\n        },\r\n        closeData() {\r\n            this.$store.commit(\"action/getIsShowData\", false)\r\n            // this.isShowData=false\r\n            this.buildData = ''\r\n        },\r\n        // 数据图层ue交互\r\n        getDataLayer(val) {\r\n            this.deleteFn();\r\n            if (val) {\r\n                let { query, url } = val;\r\n                let params = {\r\n                    \"mode\": \"add\",\r\n                    \"sources\": JSON.parse(url)\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params)// 撒点\r\n                this.$eventBus.$emit('senTxtToUe', query)// 视角\r\n            }\r\n\r\n        },\r\n        showList() {\r\n            this.flag = !this.flag;\r\n            this.getkeyAreaList();\r\n        },\r\n        // 打开监控\r\n        openPlay(id) {\r\n            // this.getLatLon(, 'play')\r\n            this.$store.commit(\"action/getVideoPlayerList\", { channalCode: id })\r\n            this.$store.commit(\"action/getIsShowNumInitChange\", true)\r\n            this.$store.commit(\"action/getVideoPlayerBox\", true)\r\n\r\n        },\r\n        setUrl(index) {\r\n            return require(`@/assets/images/comprehensiveSituation/key-area${this.activeIndex == index ? '-active' : ''}.png`)\r\n        },\r\n        sendUeFn(item, index) {\r\n            this.deleteFn();\r\n            if (this.activeIndex == index) {\r\n                this.activeIndex = null;\r\n            } else {\r\n                this.activeIndex = index;\r\n                this.$store.commit(\"action/clearAll\", false);//清除视频弹框\r\n                let params = {\r\n                    \"mode\": \"add\",\r\n                    \"sources\": JSON.parse(item.points)\r\n                }\r\n                this.$eventBus.$emit('senTxtToUe', params);//撒点\r\n                this.$eventBus.$emit('senTxtToUe', item.angle);//聚焦到当前位置\r\n            }\r\n\r\n        },\r\n        //删除点位\r\n        deleteFn() {\r\n            // this.flag = false;\r\n            let params = {\r\n                \"mode\": \"delete\",\r\n            }\r\n            this.$eventBus.$emit('senTxtToUe', params);\r\n        },\r\n        //获取重点区域列表\r\n        getkeyAreaList() {\r\n            getkeyArea({ pageNum: 1, pageSize: 100 }).then(res => {\r\n                if (res.data.code == 200) {\r\n                    if (!this.flag) {\r\n                        this.list = res.data.extra.data.slice(0, 12);\r\n                    } else {\r\n                        this.list = res.data.extra.data;\r\n                    }\r\n\r\n                }\r\n            })\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n.two-title {\r\n    display: flex;\r\n    height: 56px;\r\n    padding-left: 50px;\r\n    background-size: 100% 100%;\r\n    background-image: url(@/assets/images/comprehensiveSituation/two-header.png);\r\n    font-family: Source Han Sans CN, Source Han Sans CN;\r\n    font-weight: bold;\r\n    font-size: 36px;\r\n    color: #fff;\r\n    line-height: 56px;\r\n    letter-spacing: 3px;\r\n    font-style: normal;\r\n    text-transform: none;\r\n\r\n    >div {\r\n        margin: 0 10px;\r\n        cursor: pointer;\r\n\r\n    }\r\n\r\n    >div.action {\r\n        color: #43FFF4;\r\n\r\n    }\r\n}\r\n\r\n// /deep/ .el-tree-node__children .el-tree-node__content:hover {\r\n//     background: #43FFF4 !important;\r\n// }\r\n\r\n.dataBox {\r\n    width: 260px;\r\n    height: 220px;\r\n    position: absolute;\r\n    top: 200px;\r\n    left: 52%;\r\n    background-color: rgba(46, 101, 167, 0.5);\r\n\r\n    .delete {\r\n        float: right;\r\n        margin-right: 20px;\r\n        margin-top: 10px;\r\n    }\r\n\r\n    li {\r\n        margin-top: 28px;\r\n        margin-left: 5px;\r\n        width: 230px;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        text-overflow: ellipsis;\r\n    }\r\n\r\n    .dataT {\r\n        font-size: 20px;\r\n        margin-right: 5px;\r\n    }\r\n\r\n}\r\n\r\n.rightView {\r\n    width: 100%;\r\n    height: 100%;\r\n    // padding: 0 25px;\r\n\r\n    .right-list {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        // flex-direction: column;\r\n        padding: 14px 5px 0 5px;\r\n        height: 1876px;\r\n        justify-content: space-between;\r\n        overflow-y: scroll;\r\n\r\n        .list {\r\n            width: 359px;\r\n            height: 252px;\r\n            // background-image: url(@/assets/images/comprehensiveSituation/key-area.png);\r\n            // background-size: 100% 100%;\r\n            margin-bottom: 67px;\r\n            cursor: pointer;\r\n            position: relative;\r\n\r\n            .bg {\r\n                position: absolute;\r\n                width: 359px;\r\n                height: 252px;\r\n                top: 0;\r\n                left: 0;\r\n                z-index: -1;\r\n\r\n            }\r\n\r\n            >p {\r\n                padding-left: 31px;\r\n                height: 44px;\r\n                font-family: Source Han Sans CN, Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                line-height: 50px;\r\n                text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);\r\n                font-style: normal;\r\n                text-transform: none;\r\n            }\r\n\r\n            .img {\r\n                width: 328px;\r\n                height: 184px;\r\n                margin-top: 13px;\r\n                margin-left: 20px;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n</style>"], "mappings": "AAiDA,SAAAA,UAAA;AACA,SAAAC,QAAA;AACA,OAAAC,SAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,IAAA;MACAC,WAAA;MACAC,IAAA;MACAC,oBAAA;IACA;EACA;EACAC,UAAA;IACAR;EACA;EACAS,KAAA;IACAC,MAAAC,EAAA;MACA,IAAAA,EAAA,IAAAA,EAAA,CAAAC,OAAA;QACA,KAAAC,KAAA;MACA,WAAAF,EAAA,EAAAG,QAAA;QACA,KAAAC,MAAA,CAAAC,MAAA;QACA;QACA,KAAAb,SAAA,GAAAQ,EAAA,CAAAT,IAAA;MACA;IACA;EACA;EACAe,QAAA;IACA,GAAAlB,QAAA;MACAW,KAAA,EAAAQ,KAAA,IAAAA,KAAA,CAAAC,eAAA;MACAC,UAAA,EAAAF,KAAA,IAAAA,KAAA,CAAAG,MAAA,CAAAD;IACA;EACA;EACAE,cAAA;IACA,KAAAP,MAAA,CAAAC,MAAA;IACA,KAAAO,QAAA;EACA;EACAC,QAAA;IACA,KAAAC,cAAA;EACA;EAEAC,QAAA,GAEA;EAEAC,OAAA;IACAC,OAAA3B,IAAA;MACA,KAAAsB,QAAA;MACA,KAAAhB,oBAAA,GAAAN,IAAA;IACA;IACA4B,UAAA;MACA,KAAAd,MAAA,CAAAC,MAAA;MACA;MACA,KAAAb,SAAA;IACA;IACA;IACA2B,aAAAC,GAAA;MACA,KAAAR,QAAA;MACA,IAAAQ,GAAA;QACA;UAAAC,KAAA;UAAAC;QAAA,IAAAF,GAAA;QACA,IAAAG,MAAA;UACA;UACA,WAAAC,IAAA,CAAAC,KAAA,CAAAH,GAAA;QACA;QACA,KAAAI,SAAA,CAAAxB,KAAA,eAAAqB,MAAA;QACA,KAAAG,SAAA,CAAAxB,KAAA,eAAAmB,KAAA;MACA;IAEA;IACAM,SAAA;MACA,KAAAhC,IAAA,SAAAA,IAAA;MACA,KAAAmB,cAAA;IACA;IACA;IACAc,SAAAC,EAAA;MACA;MACA,KAAAzB,MAAA,CAAAC,MAAA;QAAAyB,WAAA,EAAAD;MAAA;MACA,KAAAzB,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;IAEA;IACA0B,OAAAC,KAAA;MACA,OAAAC,OAAA,wDAAAvC,WAAA,IAAAsC,KAAA;IACA;IACAE,SAAAC,IAAA,EAAAH,KAAA;MACA,KAAApB,QAAA;MACA,SAAAlB,WAAA,IAAAsC,KAAA;QACA,KAAAtC,WAAA;MACA;QACA,KAAAA,WAAA,GAAAsC,KAAA;QACA,KAAA5B,MAAA,CAAAC,MAAA;QACA,IAAAkB,MAAA;UACA;UACA,WAAAC,IAAA,CAAAC,KAAA,CAAAU,IAAA,CAAAC,MAAA;QACA;QACA,KAAAV,SAAA,CAAAxB,KAAA,eAAAqB,MAAA;QACA,KAAAG,SAAA,CAAAxB,KAAA,eAAAiC,IAAA,CAAAE,KAAA;MACA;IAEA;IACA;IACAzB,SAAA;MACA;MACA,IAAAW,MAAA;QACA;MACA;MACA,KAAAG,SAAA,CAAAxB,KAAA,eAAAqB,MAAA;IACA;IACA;IACAT,eAAA;MACA3B,UAAA;QAAAmD,OAAA;QAAAC,QAAA;MAAA,GAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAlD,IAAA,CAAAmD,IAAA;UACA,UAAA/C,IAAA;YACA,KAAAF,IAAA,GAAAgD,GAAA,CAAAlD,IAAA,CAAAoD,KAAA,CAAApD,IAAA,CAAAqD,KAAA;UACA;YACA,KAAAnD,IAAA,GAAAgD,GAAA,CAAAlD,IAAA,CAAAoD,KAAA,CAAApD,IAAA;UACA;QAEA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}