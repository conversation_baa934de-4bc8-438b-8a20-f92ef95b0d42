{"ast": null, "code": "import request from '@/utils/api/getVideo.js';\n\n// 获取视频token\nexport function getVideoOneToken() {\n  return request.get('/water/app/index/getVideoOneToken');\n}", "map": {"version": 3, "names": ["request", "getVideoOneToken", "get"], "sources": ["D:/Project/HuaQiaoSanQi/src/api/waterVideo.js"], "sourcesContent": ["import request from '@/utils/api/getVideo.js'\r\n\r\n// 获取视频token\r\nexport function getVideoOneToken() {\r\n   return request.get('/water/app/index/getVideoOneToken')\r\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,yBAAyB;;AAE7C;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EAChC,OAAOD,OAAO,CAACE,GAAG,CAAC,mCAAmC,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}