{"ast": null, "code": "import hlsCreated from '@/components/hlvJsVideo/hlsCreated.vue';\nimport { mapState } from 'vuex';\nexport default {\n  name: 'singleHlsVideo',\n  components: {\n    hlsCreated\n  },\n  props: {\n    allStyle: {\n      type: Object,\n      default: () => {\n        return {\n          width: '1050px',\n          height: '800px',\n          startX: 0,\n          startY: 0,\n          offsetX: 0,\n          offsetY: 0\n        };\n      }\n    },\n    minWidth: String,\n    // sizeStyle: {\n    //     type: Object,\n    //     default: () => {\n    //         return {\n    //             transform: ' scale(1)',\n    //             left: \"calc(50%)\"\n    //         }\n    //     }\n    // },\n    videomBox: Boolean\n  },\n  data() {\n    return {\n      sizeStyle: {\n        transform: 'scale(1)',\n        right: \"calc(25px)\",\n        top: 'calc(120px)'\n      },\n      isFull: true,\n      fullScreenFlag: false,\n      playerList: [],\n      tableList: {\n        ape_id: \"32058368001310147287\",\n        name: \"rxsb_通达广场(3#）东门南侧朝外\"\n      },\n      winPlayer: 1,\n      isAction: false,\n      areaTitInfo: null\n    };\n  },\n  created() {},\n  mounted() {\n    this.$nextTick(() => {\n      const resizeObserver = new ResizeObserver(() => {\n        this.getSize(); // 每次尺寸变化时调用 getSize 获取宽高\n      });\n      resizeObserver.observe(this.$refs.resizableDiv); // 监听该 div 的变化\n    });\n  },\n  beforeDestroy() {\n    if (this.$refs.resizableDiv) {\n      const resizeObserver = new ResizeObserver(() => {\n        this.getSize();\n      });\n      resizeObserver.unobserve(this.$refs.resizableDiv);\n    }\n  },\n  computed: {\n    ...mapState('action', ['videoPlayerList', 'isShowNumInitChange']),\n    ...mapState(['aspectRatio'])\n  },\n  watch: {\n    winPlayer: {\n      handler(nv) {\n        this.$store.commit(\"action/getwinPlayerNum\", nv);\n      },\n      immediate: true\n    },\n    videoPlayerList: {\n      handler(nv) {\n        this.$nextTick(() => {\n          this.areaTitInfo = nv;\n        });\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    getSize() {\n      const element = this.$refs.resizableDiv;\n      if (element) {\n        // console.log(`实时获取宽度: ${element.offsetWidth}px, 高度: ${element.offsetHeight}px`);\n        let width = `${element.offsetWidth}px`;\n        let height = `${parseInt((element.offsetWidth + 150) / this.aspectRatio)}px`;\n        // let height=`${element.offsetHeight}px`\n        this.$emit('updateStyle', {\n          width,\n          height\n        });\n      }\n    },\n    fullScreen(val) {\n      let element = document.documentElement;\n      if (val) {\n        // if (element.requestFullscreen) {\n        //     element.requestFullscreen();\n        // } else if (element.webkitRequestFullScreen) {\n        //     element.webkitRequestFullScreen();\n        // } else if (element.mozRequestFullScreen) {\n        //     element.mozRequestFullScreen();\n        // } else if (element.msRequestFullscreen) {\n        //     // IE11\n        //     element.msRequestFullscreen();\n        // }\n        this.$emit('updateStyle', {\n          width: \"100%\",\n          height: \"80%\"\n        });\n        this.sizeStyle = {\n          transform: 'scale(1)',\n          left: \"0\",\n          top: '0'\n        };\n        this.$store.commit(\"action/getisFull\", false);\n      } else {\n        // if (document.exitFullscreen) {\n        //     document.exitFullscreen();\n        // } else if (document.webkitCancelFullScreen) {\n        //     document.webkitCancelFullScreen();\n        // } else if (document.mozCancelFullScreen) {\n        //     document.mozCancelFullScreen();\n        // } else if (document.msExitFullscreen) {\n        //     document.msExitFullscreen();\n        // }\n        this.$emit('updateStyle', {\n          width: \"1050px\",\n          height: \"800px\"\n        });\n        this.sizeStyle = {\n          transform: 'scale(1)',\n          right: \"calc(25px)\",\n          top: 'calc(120px)'\n        };\n        this.$store.commit(\"action/getisFull\", true);\n      }\n      this.isFull = !val;\n    },\n    closeBox() {\n      // if (document.exitFullscreen) {\n      //     document.exitFullscreen();\n      // } else if (document.webkitCancelFullScreen) {\n      //     document.webkitCancelFullScreen();\n      // } else if (document.mozCancelFullScreen) {\n      //     document.mozCancelFullScreen();\n      // } else if (document.msExitFullscreen) {\n      //     document.msExitFullscreen();\n      // }\n      // this.$emit('updateStyle', { width: \"42rem\", height: \"25rem\" })\n      this.getSize();\n      this.sizeStyle = {\n        transform: 'scale(1)',\n        left: \"calc(50%)\",\n        top: 'calc(10%)'\n      };\n      this.$store.commit(\"action/getisFull\", true);\n      this.$emit('changeVideomBox', false);\n      this.$store.commit(\"action/getVideoPlayerList\", null);\n      this.$store.commit(\"action/getVideoPlayerBox\", false);\n    },\n    changeIsAction(val) {\n      this.isAction = val;\n    },\n    // 几路视频切换\n    ChangeWinPlayer(val) {\n      this.winPlayer = val;\n    },\n    move(e) {\n      let odiv = e.target.offsetParent; //获取目标元素\n      //算出鼠标相对元素的位置\n      let disX = e.clientX - odiv.offsetLeft;\n      let disY = e.clientY - odiv.offsetTop;\n      document.onmousemove = e => {\n        //鼠标按下并移动的事件\n        //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置\n        let left = e.clientX - disX;\n        let top = e.clientY - disY;\n\n        //绑定元素位置到positionX和positionY上面\n        this.positionX = top;\n        this.positionY = left;\n\n        //移动当前元素\n        odiv.style.left = left + 'px';\n        odiv.style.top = top + 'px';\n      };\n      document.onmouseup = e => {\n        document.onmousemove = null;\n        document.onmouseup = null;\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["hlsCreated", "mapState", "name", "components", "props", "allStyle", "type", "Object", "default", "width", "height", "startX", "startY", "offsetX", "offsetY", "min<PERSON><PERSON><PERSON>", "String", "videomBox", "Boolean", "data", "sizeStyle", "transform", "right", "top", "isFull", "fullScreenFlag", "playerList", "tableList", "ape_id", "winPlayer", "isAction", "areaTitInfo", "created", "mounted", "$nextTick", "resizeObserver", "ResizeObserver", "getSize", "observe", "$refs", "resizableDiv", "<PERSON><PERSON><PERSON><PERSON>", "unobserve", "computed", "watch", "handler", "nv", "$store", "commit", "immediate", "videoPlayerList", "methods", "element", "offsetWidth", "parseInt", "aspectRatio", "$emit", "fullScreen", "val", "document", "documentElement", "left", "closeBox", "changeIsAction", "ChangeWinPlayer", "move", "e", "odiv", "target", "offsetParent", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "<PERSON><PERSON><PERSON><PERSON>", "positionX", "positionY", "style", "onmouseup"], "sources": ["src/components/hlvJsVideo/singleHlsVideo.vue"], "sourcesContent": ["<template>\r\n    <div class=\"eventSubscriptionCon boxBgStyle\" :style=\"{ ...sizeStyle, 'width': allStyle.width, minWidth: minWidth }\"\r\n        id=\"box\">\r\n        <div class=\"box-card\" ref=\"resizableDiv\">\r\n            <div class=\"topMain\" @mousedown.self=\"move\">\r\n                <div class=\"title\">\r\n                    <div class=\"txt\" style=\"font-size: 26px;\">实时监控</div>\r\n                </div>\r\n                <div class=\"second\">\r\n                    <div class=\"numInitChange\" v-show=\"isShowNumInitChange\">\r\n                        <span @click=\"ChangeWinPlayer(1)\" :class=\"{ 'selectedSpn': winPlayer == 1 }\">1路</span>\r\n                        <span @click=\"ChangeWinPlayer(4)\" :class=\"{ 'selectedSpn': winPlayer == 4 }\">4路</span>\r\n                        <span @click=\"ChangeWinPlayer(9)\" :class=\"{ 'selectedSpn': winPlayer == 9 }\">9路</span>\r\n                    </div>\r\n                    <div class=\"screenbt\" v-show=\"isShowNumInitChange\">\r\n                        <span @click=\"fullScreen(isFull)\" v-if=\"isFull\">全屏</span>\r\n                        <span @click=\"fullScreen(isFull)\" v-else>退出全屏</span>\r\n                        <div class=\"delete\" style=\" padding: 3px 10px;\" @click=\"closeBox\">\r\n                            <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <!-- <div class=\"second\">\r\n                <div class=\"numInitChange\" v-show=\"isShowNumInitChange\">\r\n                    <span @click=\"ChangeWinPlayer(1)\" :class=\"{ 'selectedSpn': winPlayer == 1 }\">1路</span>\r\n                    <span @click=\"ChangeWinPlayer(4)\" :class=\"{ 'selectedSpn': winPlayer == 4 }\">4路</span>\r\n                    <span @click=\"ChangeWinPlayer(9)\" :class=\"{ 'selectedSpn': winPlayer == 9 }\">9路</span>\r\n                </div>\r\n                <div class=\"screenbt\" v-show=\"isShowNumInitChange\">\r\n                    <span @click=\"fullScreen(isFull)\" v-if=\"isFull\">全屏</span>\r\n                    <span @click=\"fullScreen(isFull)\" v-else>退出全屏</span>\r\n                    <div class=\"delete\" style=\" padding: 3px 10px;\" @click=\"closeBox\">\r\n                        <img src=\"@/assets/images/mainPics/i_delete.png\" alt=\"\">\r\n                    </div>\r\n                </div>\r\n            </div> -->\r\n\r\n            <div class=\"videoShow\">\r\n                <hlsCreated @sendIsAction=\"changeIsAction\" :areaTitInfo=\"areaTitInfo\" :winPlayer=\"winPlayer\"\r\n                    :isAction=isAction :height=allStyle.height>\r\n                </hlsCreated>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport hlsCreated from '@/components/hlvJsVideo/hlsCreated.vue'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n    name: 'singleHlsVideo',\r\n    components: {\r\n        hlsCreated\r\n    },\r\n    props: {\r\n        allStyle: {\r\n            type: Object,\r\n            default: () => {\r\n                return {\r\n                    width: '1050px',\r\n                    height: '800px',\r\n                    startX: 0,\r\n                    startY: 0,\r\n                    offsetX: 0,\r\n                    offsetY: 0,\r\n                }\r\n            }\r\n        },\r\n        minWidth: String,\r\n        // sizeStyle: {\r\n        //     type: Object,\r\n        //     default: () => {\r\n        //         return {\r\n        //             transform: ' scale(1)',\r\n        //             left: \"calc(50%)\"\r\n        //         }\r\n        //     }\r\n        // },\r\n        videomBox: Boolean\r\n    },\r\n    data() {\r\n        return {\r\n            sizeStyle: {\r\n                transform: 'scale(1)',\r\n                right: \"calc(25px)\",\r\n                top: 'calc(120px)'\r\n            },\r\n            isFull: true,\r\n            fullScreenFlag: false,\r\n            playerList: [],\r\n            tableList: {\r\n                ape_id: \"32058368001310147287\",\r\n                name: \"rxsb_通达广场(3#）东门南侧朝外\"\r\n\r\n            },\r\n            winPlayer: 1,\r\n            isAction: false,\r\n            areaTitInfo: null,\r\n\r\n        }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            const resizeObserver = new ResizeObserver(() => {\r\n                this.getSize(); // 每次尺寸变化时调用 getSize 获取宽高\r\n            });\r\n\r\n            resizeObserver.observe(this.$refs.resizableDiv); // 监听该 div 的变化\r\n        })\r\n\r\n    },\r\n    beforeDestroy() {\r\n\r\n        if (this.$refs.resizableDiv) {\r\n            const resizeObserver = new ResizeObserver(() => {\r\n                this.getSize();\r\n            });\r\n            resizeObserver.unobserve(this.$refs.resizableDiv);\r\n\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState('action', ['videoPlayerList', 'isShowNumInitChange']),\r\n        ...mapState(['aspectRatio'])\r\n    },\r\n    watch: {\r\n        winPlayer: {\r\n            handler(nv) {\r\n                this.$store.commit(\"action/getwinPlayerNum\", nv)\r\n\r\n            },\r\n            immediate: true,\r\n        },\r\n        videoPlayerList: {\r\n\r\n            handler(nv) {\r\n                this.$nextTick(() => {\r\n                    this.areaTitInfo = nv\r\n                })\r\n            },\r\n            immediate: true\r\n        },\r\n\r\n    },\r\n    methods: {\r\n        getSize() {\r\n            const element = this.$refs.resizableDiv;\r\n            if (element) {\r\n                // console.log(`实时获取宽度: ${element.offsetWidth}px, 高度: ${element.offsetHeight}px`);\r\n                let width = `${element.offsetWidth}px`\r\n                let height = `${parseInt((element.offsetWidth + 150) / this.aspectRatio)}px`\r\n                // let height=`${element.offsetHeight}px`\r\n                this.$emit('updateStyle', { width, height })\r\n            }\r\n        },\r\n        fullScreen(val) {\r\n            let element = document.documentElement;\r\n            if (val) {\r\n                // if (element.requestFullscreen) {\r\n                //     element.requestFullscreen();\r\n                // } else if (element.webkitRequestFullScreen) {\r\n                //     element.webkitRequestFullScreen();\r\n                // } else if (element.mozRequestFullScreen) {\r\n                //     element.mozRequestFullScreen();\r\n                // } else if (element.msRequestFullscreen) {\r\n                //     // IE11\r\n                //     element.msRequestFullscreen();\r\n                // }\r\n                this.$emit('updateStyle', { width: \"100%\", height: \"80%\" })\r\n                this.sizeStyle = {\r\n                    transform: 'scale(1)',\r\n                    left: \"0\",\r\n                    top: '0',\r\n                }\r\n                this.$store.commit(\"action/getisFull\", false)\r\n            } else {\r\n                // if (document.exitFullscreen) {\r\n                //     document.exitFullscreen();\r\n                // } else if (document.webkitCancelFullScreen) {\r\n                //     document.webkitCancelFullScreen();\r\n                // } else if (document.mozCancelFullScreen) {\r\n                //     document.mozCancelFullScreen();\r\n                // } else if (document.msExitFullscreen) {\r\n                //     document.msExitFullscreen();\r\n                // }\r\n                this.$emit('updateStyle', { width: \"1050px\", height: \"800px\" })\r\n                this.sizeStyle = {\r\n                    transform: 'scale(1)',\r\n                    right: \"calc(25px)\",\r\n                    top: 'calc(120px)',\r\n                }\r\n                this.$store.commit(\"action/getisFull\", true)\r\n            }\r\n            this.isFull = !val\r\n        },\r\n        closeBox() {\r\n            // if (document.exitFullscreen) {\r\n            //     document.exitFullscreen();\r\n            // } else if (document.webkitCancelFullScreen) {\r\n            //     document.webkitCancelFullScreen();\r\n            // } else if (document.mozCancelFullScreen) {\r\n            //     document.mozCancelFullScreen();\r\n            // } else if (document.msExitFullscreen) {\r\n            //     document.msExitFullscreen();\r\n            // }\r\n            // this.$emit('updateStyle', { width: \"42rem\", height: \"25rem\" })\r\n            this.getSize()\r\n            this.sizeStyle = {\r\n                transform: 'scale(1)',\r\n                left: \"calc(50%)\",\r\n                top: 'calc(10%)'\r\n            }\r\n            this.$store.commit(\"action/getisFull\", true)\r\n            this.$emit('changeVideomBox', false);\r\n            this.$store.commit(\"action/getVideoPlayerList\", null)\r\n            this.$store.commit(\"action/getVideoPlayerBox\", false)\r\n        },\r\n        changeIsAction(val) {\r\n            this.isAction = val\r\n        },\r\n        // 几路视频切换\r\n        ChangeWinPlayer(val) {\r\n            this.winPlayer = val\r\n        },\r\n        move(e) {\r\n            let odiv = e.target.offsetParent;        //获取目标元素\r\n            //算出鼠标相对元素的位置\r\n            let disX = e.clientX - odiv.offsetLeft;\r\n            let disY = e.clientY - odiv.offsetTop;\r\n            document.onmousemove = (e) => {\r\n                //鼠标按下并移动的事件\r\n                //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置\r\n                let left = e.clientX - disX;\r\n                let top = e.clientY - disY;\r\n\r\n                //绑定元素位置到positionX和positionY上面\r\n                this.positionX = top;\r\n                this.positionY = left;\r\n\r\n                //移动当前元素\r\n                odiv.style.left = left + 'px';\r\n                odiv.style.top = top + 'px';\r\n            };\r\n            document.onmouseup = (e) => {\r\n                document.onmousemove = null;\r\n                document.onmouseup = null;\r\n            };\r\n        }\r\n    },\r\n\r\n}\r\n\r\n\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.eventSubscriptionCon {\r\n    pointer-events: stroke;\r\n    position: fixed;\r\n    top: calc(10%);\r\n    // left: 60%;\r\n    z-index: 100;\r\n    overflow: hidden;\r\n    resize: horizontal;\r\n    background: rgba(18, 76, 111, 0.7) !important;\r\n}\r\n\r\n.box-card {\r\n\r\n    padding-bottom: 15px;\r\n\r\n    .topMain {\r\n        cursor: move;\r\n    }\r\n\r\n\r\n    .videoShow {\r\n        width: 100%;\r\n        height: 100%;\r\n        padding: 0 15px;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .second {\r\n        width: 85%;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        // /* 左右两端对齐 */\r\n        // align-items: center;\r\n\r\n        /* 垂直居中 */\r\n        .numInitChange {\r\n            // flex: left; \r\n            display: inline-block;\r\n            color: #fff;\r\n            text-align: center;\r\n            margin-top: 13px;\r\n\r\n            >span {\r\n                display: inline-block;\r\n                padding: 2px 3px;\r\n                border: 1px solid #fff;\r\n                cursor: pointer;\r\n                font-size: 26px;\r\n            }\r\n\r\n            .selectedSpn {\r\n                background-color: rgba(9, 171, 212, .3);\r\n            }\r\n        }\r\n\r\n        .screenbt {\r\n            color: #fff;\r\n            text-align: center;\r\n            margin-top: 10px;\r\n\r\n            >span {\r\n                border: 1px solid #fff;\r\n                cursor: pointer;\r\n                font-size: 26px;\r\n            }\r\n        }\r\n\r\n        .delete {\r\n            display: inline-block;\r\n            margin-right: 30px;\r\n            margin-left: 50px;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAgDA,OAAAA,UAAA;AACA,SAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAA,CAAA;QACA;UACAC,KAAA;UACAC,MAAA;UACAC,MAAA;UACAC,MAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;IACA;IACAC,QAAA,EAAAC,MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,SAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAC,SAAA;QACAC,SAAA;QACAC,KAAA;QACAC,GAAA;MACA;MACAC,MAAA;MACAC,cAAA;MACAC,UAAA;MACAC,SAAA;QACAC,MAAA;QACA1B,IAAA;MAEA;MACA2B,SAAA;MACAC,QAAA;MACAC,WAAA;IAEA;EACA;EACAC,QAAA,GAEA;EACAC,QAAA;IACA,KAAAC,SAAA;MACA,MAAAC,cAAA,OAAAC,cAAA;QACA,KAAAC,OAAA;MACA;MAEAF,cAAA,CAAAG,OAAA,MAAAC,KAAA,CAAAC,YAAA;IACA;EAEA;EACAC,cAAA;IAEA,SAAAF,KAAA,CAAAC,YAAA;MACA,MAAAL,cAAA,OAAAC,cAAA;QACA,KAAAC,OAAA;MACA;MACAF,cAAA,CAAAO,SAAA,MAAAH,KAAA,CAAAC,YAAA;IAEA;EACA;EACAG,QAAA;IACA,GAAA1C,QAAA;IACA,GAAAA,QAAA;EACA;EACA2C,KAAA;IACAf,SAAA;MACAgB,QAAAC,EAAA;QACA,KAAAC,MAAA,CAAAC,MAAA,2BAAAF,EAAA;MAEA;MACAG,SAAA;IACA;IACAC,eAAA;MAEAL,QAAAC,EAAA;QACA,KAAAZ,SAAA;UACA,KAAAH,WAAA,GAAAe,EAAA;QACA;MACA;MACAG,SAAA;IACA;EAEA;EACAE,OAAA;IACAd,QAAA;MACA,MAAAe,OAAA,QAAAb,KAAA,CAAAC,YAAA;MACA,IAAAY,OAAA;QACA;QACA,IAAA3C,KAAA,MAAA2C,OAAA,CAAAC,WAAA;QACA,IAAA3C,MAAA,MAAA4C,QAAA,EAAAF,OAAA,CAAAC,WAAA,eAAAE,WAAA;QACA;QACA,KAAAC,KAAA;UAAA/C,KAAA;UAAAC;QAAA;MACA;IACA;IACA+C,WAAAC,GAAA;MACA,IAAAN,OAAA,GAAAO,QAAA,CAAAC,eAAA;MACA,IAAAF,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAAF,KAAA;UAAA/C,KAAA;UAAAC,MAAA;QAAA;QACA,KAAAU,SAAA;UACAC,SAAA;UACAwC,IAAA;UACAtC,GAAA;QACA;QACA,KAAAwB,MAAA,CAAAC,MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAAQ,KAAA;UAAA/C,KAAA;UAAAC,MAAA;QAAA;QACA,KAAAU,SAAA;UACAC,SAAA;UACAC,KAAA;UACAC,GAAA;QACA;QACA,KAAAwB,MAAA,CAAAC,MAAA;MACA;MACA,KAAAxB,MAAA,IAAAkC,GAAA;IACA;IACAI,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAzB,OAAA;MACA,KAAAjB,SAAA;QACAC,SAAA;QACAwC,IAAA;QACAtC,GAAA;MACA;MACA,KAAAwB,MAAA,CAAAC,MAAA;MACA,KAAAQ,KAAA;MACA,KAAAT,MAAA,CAAAC,MAAA;MACA,KAAAD,MAAA,CAAAC,MAAA;IACA;IACAe,eAAAL,GAAA;MACA,KAAA5B,QAAA,GAAA4B,GAAA;IACA;IACA;IACAM,gBAAAN,GAAA;MACA,KAAA7B,SAAA,GAAA6B,GAAA;IACA;IACAO,KAAAC,CAAA;MACA,IAAAC,IAAA,GAAAD,CAAA,CAAAE,MAAA,CAAAC,YAAA;MACA;MACA,IAAAC,IAAA,GAAAJ,CAAA,CAAAK,OAAA,GAAAJ,IAAA,CAAAK,UAAA;MACA,IAAAC,IAAA,GAAAP,CAAA,CAAAQ,OAAA,GAAAP,IAAA,CAAAQ,SAAA;MACAhB,QAAA,CAAAiB,WAAA,GAAAV,CAAA;QACA;QACA;QACA,IAAAL,IAAA,GAAAK,CAAA,CAAAK,OAAA,GAAAD,IAAA;QACA,IAAA/C,GAAA,GAAA2C,CAAA,CAAAQ,OAAA,GAAAD,IAAA;;QAEA;QACA,KAAAI,SAAA,GAAAtD,GAAA;QACA,KAAAuD,SAAA,GAAAjB,IAAA;;QAEA;QACAM,IAAA,CAAAY,KAAA,CAAAlB,IAAA,GAAAA,IAAA;QACAM,IAAA,CAAAY,KAAA,CAAAxD,GAAA,GAAAA,GAAA;MACA;MACAoC,QAAA,CAAAqB,SAAA,GAAAd,CAAA;QACAP,QAAA,CAAAiB,WAAA;QACAjB,QAAA,CAAAqB,SAAA;MACA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}