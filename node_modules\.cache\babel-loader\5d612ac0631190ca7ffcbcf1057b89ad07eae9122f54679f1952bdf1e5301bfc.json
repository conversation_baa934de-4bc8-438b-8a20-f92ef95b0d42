{"ast": null, "code": "import * as echarts from 'echarts';\nimport { getDailyDemand } from '@/api/bigScreen';\nexport default {\n  name: 'PopulationFlow',\n  data() {\n    return {\n      popTrend: '',\n      dataX: [],\n      data2: [12000, 9400, 20000, 8120, 14540],\n      data: []\n    };\n  },\n  beforeDestroy() {\n    if (this.popTrend) {\n      this.popTrend.clear();\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.myChart();\n    });\n  },\n  methods: {\n    myChart() {\n      new Promise(resolve => {\n        resolve();\n      }).then(() => {\n        if (this.popTrend != null && this.popTrend != \"\" && this.popTrend != undefined) {\n          this.popTrend.dispose();\n        }\n        this.popTrend = echarts.init(this.$refs.popTrend); // 要引用的页面的元素或id值为\n        this.popTrend.clear();\n        getDailyDemand().then(res => {\n          let dataList = res.data.data;\n          for (var i of dataList) {\n            this.dataX.push(i.details_tim);\n            this.data.push(i.demand);\n          }\n          this.setOption();\n        });\n      });\n    },\n    setOption() {\n      let option = {\n        grid: {\n          top: \"23%\",\n          right: '6%',\n          left: '15%',\n          bottom: '30%'\n        },\n        tooltip: {\n          trigger: 'axis',\n          formatter: function (params) {\n            return params[0].name + '<br>' + params[0].marker + '诉求量: ' + params[0].data;\n          },\n          backgroundColor: \"rgba(34, 154, 255, .4)\",\n          borderWidth: \"1\",\n          //边框宽度设置1\n          borderColor: \"rgba(33, 242, 196, 1)\",\n          //设置边框颜色\n          textStyle: {\n            color: \"#fff\",\n            //设置文字颜色\n            fontSize: 20\n          }\n        },\n        title: {\n          top: \"3%\",\n          left: '0',\n          text: '每日诉求量',\n          textStyle: {\n            color: \"#fff\",\n            fontSize: 24,\n            fontWeight: 500\n          }\n        },\n        dataZoom: [{\n          type: 'inside',\n          start: 0,\n          end: 50\n        }, {}],\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: this.dataX,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: true,\n            lineStyle: {\n              color: '#ddd',\n              // 颜色\n              width: 3 // 粗细\n            }\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 20,\n            margin: 20\n          }\n        },\n        yAxis: {\n          type: 'value',\n          // max:0,\n          // min: 0,\n          // boundaryGap: [0, '100%'],\n          interval: 100,\n          axisTick: {\n            show: false\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            //多条横线\n            show: false\n          },\n          axisLabel: {\n            color: \"#fff\",\n            fontSize: 20\n          }\n        },\n        series: [{\n          type: 'line',\n          smooth: true,\n          symbol: 'circle',\n          symbolSize: [10, 10],\n          showSymbol: false,\n          sampling: 'lttb',\n          stack: \"总量\",\n          itemStyle: {\n            color: 'rgba(34, 255, 204, 1)'\n          },\n          areaStyle: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: \"rgba(34, 255, 204, .5)\"\n            }, {\n              offset: 1,\n              color: \"rgba(0,0, 0, .3)\"\n            }])\n          },\n          data: this.data\n        }]\n      };\n      if (option && typeof option === 'object') {\n        this.popTrend.setOption(option);\n      }\n      window.onresize = this.popTrend.resize;\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "data", "popTrend", "dataX", "data2", "<PERSON><PERSON><PERSON><PERSON>", "clear", "mounted", "$nextTick", "myChart", "methods", "Promise", "resolve", "then", "undefined", "dispose", "init", "$refs", "res", "dataList", "i", "push", "details_tim", "demand", "setOption", "option", "grid", "top", "right", "left", "bottom", "tooltip", "trigger", "formatter", "params", "marker", "backgroundColor", "borderWidth", "borderColor", "textStyle", "color", "fontSize", "title", "text", "fontWeight", "dataZoom", "type", "start", "end", "xAxis", "boundaryGap", "axisTick", "show", "axisLine", "lineStyle", "width", "axisLabel", "margin", "yAxis", "interval", "splitLine", "series", "smooth", "symbol", "symbolSize", "showSymbol", "sampling", "stack", "itemStyle", "areaStyle", "graphic", "LinearGradient", "offset", "window", "onresize", "resize"], "sources": ["src/components/peopleDem/PopulationFlow.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div id=\"popTrend\" ref=\"popTrend\"></div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import * as echarts from 'echarts';\r\n    import {getDailyDemand} from '@/api/bigScreen'\r\n    export default {\r\n        name: 'PopulationFlow',\r\n\r\n        data() {\r\n            return {\r\n                popTrend: '',\r\n                dataX: [],\r\n                data2: [12000, 9400, 20000, 8120, 14540],\r\n                data: []\r\n            };\r\n        },\r\n        beforeDestroy(){\r\n        if(this.popTrend){\r\n            this.popTrend.clear()\r\n        }\r\n    },\r\n        mounted() {\r\n            this.$nextTick(() => {\r\n\r\n                this.myChart()\r\n\r\n            })\r\n\r\n        },\r\n\r\n        methods: {\r\n\r\n            myChart() {\r\n                new Promise((resolve) => {\r\n                    resolve()\r\n                }).then(() => {\r\n                    if (this.popTrend != null && this.popTrend != \"\" && this.popTrend != undefined) {\r\n                        this.popTrend.dispose();\r\n                    }\r\n                    this.popTrend = echarts.init(this.$refs.popTrend); // 要引用的页面的元素或id值为\r\n                    this.popTrend.clear()\r\n                    getDailyDemand().then(res=>{\r\n                        let dataList = res.data.data;\r\n                        for(var i of dataList){\r\n                            this.dataX.push(i.details_tim);\r\n                            this.data.push(i.demand)\r\n                        }\r\n                        this.setOption()\r\n                    })\r\n                })\r\n            },\r\n            setOption(){\r\n                \r\n                let option = {\r\n                        grid: {\r\n                            top: \"23%\",\r\n                            right: '6%',\r\n                            left: '15%',\r\n                            bottom: '30%',\r\n\r\n                        },\r\n                        tooltip: {\r\n                            trigger: 'axis',\r\n                            formatter: function (params) {\r\n                                return params[0].name+'<br>'+params[0].marker + '诉求量: '+params[0].data;\r\n\r\n                            },\r\n                            backgroundColor: \"rgba(34, 154, 255, .4)\",\r\n                            borderWidth: \"1\", //边框宽度设置1\r\n                            borderColor: \"rgba(33, 242, 196, 1)\", //设置边框颜色\r\n                            textStyle: {\r\n                                color: \"#fff\", //设置文字颜色\r\n                                fontSize:20\r\n                            },\r\n                        },\r\n                        title: {\r\n                            top:\"3%\",\r\n                            left: '0',\r\n                            text: '每日诉求量',\r\n                            textStyle: {\r\n                                color: \"#fff\",\r\n                                fontSize: 24,\r\n                                fontWeight: 500,\r\n\r\n                            }\r\n\r\n                        },\r\n                        dataZoom: [{\r\n                            type: 'inside',\r\n                            start: 0,\r\n                            end: 50\r\n                        },{}],\r\n\r\n                        xAxis: {\r\n                            type: 'category',\r\n                            boundaryGap: false,\r\n                            data: this.dataX,\r\n                            axisTick: {\r\n                                show: false,\r\n                            },\r\n                            axisLine: {\r\n                                show: true,\r\n                                lineStyle: {\r\n                                    color: '#ddd', // 颜色\r\n                                    width: 3 // 粗细\r\n                                }\r\n                            },\r\n                            axisLabel: {\r\n                                color: \"#fff\",\r\n                                fontSize:20,\r\n                                margin: 20,\r\n\r\n                            },\r\n                        },\r\n                        yAxis: {\r\n                            type: 'value',\r\n                            // max:0,\r\n                            // min: 0,\r\n                            // boundaryGap: [0, '100%'],\r\n                            interval: 100,\r\n                            axisTick: {\r\n                                show: false,\r\n                            },\r\n                            axisLine: {\r\n                                show: false,\r\n                            },\r\n                            splitLine: { //多条横线\r\n                                show: false\r\n                            },\r\n\r\n                            axisLabel: {\r\n                                color: \"#fff\",\r\n                                fontSize:20,\r\n                            },\r\n\r\n                        },\r\n\r\n                        series: [\r\n\r\n                            {\r\n                                type: 'line',\r\n                                smooth: true,\r\n                                symbol: 'circle',\r\n                                symbolSize:[10,10],\r\n                                showSymbol: false,\r\n                                sampling: 'lttb',\r\n                                stack: \"总量\",\r\n                                itemStyle: {\r\n                                    color: 'rgba(34, 255, 204, 1)'\r\n\r\n\r\n                                },\r\n                                areaStyle: {\r\n\r\n                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                        {\r\n                                            offset: 0,\r\n                                            color: \"rgba(34, 255, 204, .5)\",\r\n                                        },\r\n                                        {\r\n                                            offset: 1,\r\n                                            color: \"rgba(0,0, 0, .3)\",\r\n                                        }\r\n                                    ])\r\n                                },\r\n                                data: this.data\r\n                            }\r\n\r\n                        ]\r\n                    };\r\n                 \r\n                    if (option && typeof option === 'object') {\r\n                        this.popTrend.setOption(option);\r\n                    }\r\n                    window.onresize = this.popTrend.resize;\r\n            }\r\n        },\r\n    };\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n    #popTrend {\r\n        width: 94%;\r\n        height: 300px;\r\n    }\r\n</style>"], "mappings": "AAOA,YAAAA,OAAA;AACA,SAAAC,cAAA;AACA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,QAAA;MACAC,KAAA;MACAC,KAAA;MACAH,IAAA;IACA;EACA;EACAI,cAAA;IACA,SAAAH,QAAA;MACA,KAAAA,QAAA,CAAAI,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;MAEA,KAAAC,OAAA;IAEA;EAEA;EAEAC,OAAA;IAEAD,QAAA;MACA,IAAAE,OAAA,CAAAC,OAAA;QACAA,OAAA;MACA,GAAAC,IAAA;QACA,SAAAX,QAAA,iBAAAA,QAAA,eAAAA,QAAA,IAAAY,SAAA;UACA,KAAAZ,QAAA,CAAAa,OAAA;QACA;QACA,KAAAb,QAAA,GAAAJ,OAAA,CAAAkB,IAAA,MAAAC,KAAA,CAAAf,QAAA;QACA,KAAAA,QAAA,CAAAI,KAAA;QACAP,cAAA,GAAAc,IAAA,CAAAK,GAAA;UACA,IAAAC,QAAA,GAAAD,GAAA,CAAAjB,IAAA,CAAAA,IAAA;UACA,SAAAmB,CAAA,IAAAD,QAAA;YACA,KAAAhB,KAAA,CAAAkB,IAAA,CAAAD,CAAA,CAAAE,WAAA;YACA,KAAArB,IAAA,CAAAoB,IAAA,CAAAD,CAAA,CAAAG,MAAA;UACA;UACA,KAAAC,SAAA;QACA;MACA;IACA;IACAA,UAAA;MAEA,IAAAC,MAAA;QACAC,IAAA;UACAC,GAAA;UACAC,KAAA;UACAC,IAAA;UACAC,MAAA;QAEA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,CAAAC,MAAA;YACA,OAAAA,MAAA,IAAAlC,IAAA,YAAAkC,MAAA,IAAAC,MAAA,aAAAD,MAAA,IAAAjC,IAAA;UAEA;UACAmC,eAAA;UACAC,WAAA;UAAA;UACAC,WAAA;UAAA;UACAC,SAAA;YACAC,KAAA;YAAA;YACAC,QAAA;UACA;QACA;QACAC,KAAA;UACAf,GAAA;UACAE,IAAA;UACAc,IAAA;UACAJ,SAAA;YACAC,KAAA;YACAC,QAAA;YACAG,UAAA;UAEA;QAEA;QACAC,QAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA;QAEAC,KAAA;UACAH,IAAA;UACAI,WAAA;UACAjD,IAAA,OAAAE,KAAA;UACAgD,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;YACAE,SAAA;cACAd,KAAA;cAAA;cACAe,KAAA;YACA;UACA;UACAC,SAAA;YACAhB,KAAA;YACAC,QAAA;YACAgB,MAAA;UAEA;QACA;QACAC,KAAA;UACAZ,IAAA;UACA;UACA;UACA;UACAa,QAAA;UACAR,QAAA;YACAC,IAAA;UACA;UACAC,QAAA;YACAD,IAAA;UACA;UACAQ,SAAA;YAAA;YACAR,IAAA;UACA;UAEAI,SAAA;YACAhB,KAAA;YACAC,QAAA;UACA;QAEA;QAEAoB,MAAA,GAEA;UACAf,IAAA;UACAgB,MAAA;UACAC,MAAA;UACAC,UAAA;UACAC,UAAA;UACAC,QAAA;UACAC,KAAA;UACAC,SAAA;YACA5B,KAAA;UAGA;UACA6B,SAAA;YAEA7B,KAAA,MAAA1C,OAAA,CAAAwE,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACAhC,KAAA;YACA,GACA;cACAgC,MAAA;cACAhC,KAAA;YACA,EACA;UACA;UACAvC,IAAA,OAAAA;QACA;MAGA;MAEA,IAAAwB,MAAA,WAAAA,MAAA;QACA,KAAAvB,QAAA,CAAAsB,SAAA,CAAAC,MAAA;MACA;MACAgD,MAAA,CAAAC,QAAA,QAAAxE,QAAA,CAAAyE,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}